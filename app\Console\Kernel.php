<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{

    protected $commands = [
        Commands\SubscriptionBillCron::class,
        Commands\overdueCron::class,
        Commands\SendBookingReminders::class,
    ];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        // $schedule->command('inspire')->hourly();
        $schedule->command('subscriptionBill:cron')->everyMinute();
        $schedule->command('overdue:cron')->daily();
        $schedule->command('recurringInvoice:cron')->daily();
        $schedule->command('getEinvoiceSubmission:cron')->everyFiveMinutes();
        $schedule->command('verifyFiuuPayment:cron')->everyMinute();
        
        $schedule->command('bookings:send-reminders')->everyFiveMinutes();
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
