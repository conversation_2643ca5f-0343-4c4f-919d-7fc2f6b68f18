<?php

namespace App\Http\Controllers;

use App\Models\Language;
use App\Models\School;
use App\Models\SchoolSetting;
use App\Models\User;
use App\Repositories\Guidance\GuidanceInterface;
use App\Repositories\Package\PackageInterface;
use App\Repositories\School\SchoolInterface;
use App\Repositories\SchoolSetting\SchoolSettingInterface;
use App\Repositories\User\UserInterface;
use App\Services\BootstrapTableService;
use App\Services\CachingService;
use App\Services\ResponseService;
use App\Services\SchoolDataService;
use App\Services\SubscriptionService;
use Auth;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Stripe\Review;
use Throwable;
use TypeError;
use App\Models\Mediums;
use App\Models\Section;

class SchoolController extends Controller {

    // Initializing the schools Repository
    private SchoolInterface $schoolsRepository;
    private UserInterface $userRepository;
    private PackageInterface $package;
    private CachingService $cache;
    private SubscriptionService $subscriptionService;
    private SchoolSettingInterface $schoolSettings;
    private GuidanceInterface $guidance;

    public function __construct(SchoolInterface $school, UserInterface $user, PackageInterface $package, CachingService $cache, SubscriptionService $subscriptionService, SchoolSettingInterface $schoolSettings, GuidanceInterface $guidance) {
        $this->schoolsRepository = $school;
        $this->userRepository = $user;
        $this->package = $package;
        $this->cache = $cache;
        $this->subscriptionService = $subscriptionService;
        $this->schoolSettings = $schoolSettings;
        $this->guidance = $guidance;
    }


    public function index() {
        ResponseService::noPermissionThenRedirect('schools-list');
        $packages = $this->package->builder()->orderBy('rank')->get()->pluck('package_with_type','id')->toArray();

        $baseUrl = url('/');
        // Remove the scheme (http:// or https://)
        $baseUrlWithoutScheme = preg_replace("(^https?://)", "", $baseUrl);
        $baseUrlWithoutScheme = str_replace("www.", "", $baseUrlWithoutScheme);
        return view('schools.index', compact('packages','baseUrlWithoutScheme'));
    }

    public function store(Request $request) {
        ResponseService::noAnyPermissionThenRedirect(['schools-create']);
        $validator = Validator::make($request->all(), [
            'school_name'          => 'required',
            'school_support_email' => 'required|unique:schools,support_email',
            'school_support_phone' => 'required|numeric|digits_between:1,16',
            'school_address'       => 'required',
            'business_registration_no'=> 'nullable',
            'remark'                  => 'nullable',
            'capacity'                => 'required|numeric',
            'school_image'         => 'required|mimes:jpg,jpeg,png,svg,svg+xml|max:2048',
            'admin_first_name'     => 'required',
            'admin_last_name'      => 'required',
            'admin_contact'        => 'required|digits_between:1,16',
            'admin_email'          => 'required|unique:users,email',
            'admin_image'          => 'required|mimes:jpg,jpeg,png,svg,svg+xml|max:2048',
            'domain'               => 'nullable|unique:schools,domain'
        ]);
        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            DB::beginTransaction();
            $school_data = array(
                'name'          => $request->school_name,
                'address'       => $request->school_address,
                'support_email' => $request->school_support_email,
                'support_phone' => $request->school_support_phone,
                'tagline'       => $request->school_tagline ?? '',
                'logo'          => $request->file('school_image'),
                'domain'        => $request->domain,
                'business_registration_no'=> $request->business_registration_no ?? '',
                'remark'        => $request->remark ?? '',
                'capacity'      => 50,
                'logo'          => $request->file('school_image')
            );
            // Call store function of Schools Repository
            $schoolData = $this->schoolsRepository->create($school_data);

            $admin_data = array(
                'first_name' => $request->admin_first_name,
                'last_name'  => $request->admin_last_name,
                'mobile'     => $request->admin_contact,
                'email'      => $request->admin_email,
                'password'   => Hash::make($request->admin_contact),
                'school_id'  => $schoolData->id,
                'image'      => $request->file('admin_image')
            );

            //Call store function of User Repository and get the admin data
            $user = $this->userRepository->create($admin_data);
            $user->assignRole('School Admin');

            // Update Admin id to School Data
            $schoolData = $this->schoolsRepository->update($schoolData->id, ['admin_id' => $user->id]);

            $medium = ['name' => '', 'school_id' => $schoolData->id];
            Mediums::create($medium);
    
            $sections = ['name' => '', 'school_id' => $schoolData->id];
            Section::create($sections);

            $schoolService = app(SchoolDataService::class);
            // Add Pre School Settings By Default
            $schoolService->preSettingsSetup($schoolData);

            // Assign package
            if ($request->assign_package) {
                // Create subscription plan
                $this->subscriptionService->createSubscription($request->assign_package, $schoolData->id, null, 1);
                $this->cache->removeSchoolCache(config('constants.CACHE.SCHOOL.SETTINGS'),$schoolData->id);

            }

            DB::commit();

            $settings = $this->cache->getSystemSettings();
            $email_body = $this->replacePlaceholders($request, $user, $settings);

            $data = [
                'subject'                 => 'Welcome to ' . $request->school_name ?? 'Schola',
                'name'                    => $request->admin_first_name,
                'email'                   => $request->admin_email,
                'email_body'              => $email_body,
                'password'                => $request->admin_contact,
                'school_name'             => $request->school_name,
                'business_registration_no'=> $request->business_registration_no,
                'remark'                  => $request->remark,
                'capacity'                => 50,
            ];

            Mail::send('schools.email', $data, static function ($message) use ($data) {
                $message->to($data['email'])->subject($data['subject']);
            });

            ResponseService::successResponse('Data Stored Successfully');

        } catch (Throwable $e) {
            if (Str::contains($e->getMessage(), ['Failed', 'Mail', 'Mailer', 'MailManager'])) {
                ResponseService::warningResponse("School Registered successfully. But Email not sent.");
            } else {
                DB::rollBack();
                ResponseService::logErrorResponse($e, "School Controller -> Store method");
                ResponseService::errorResponse();
            }

        }
    }


    private function replacePlaceholders($request, $user, $settings)
    {
        $templateContent = $settings['email_template_school_registration'] ?? '';
        // Define the placeholders and their replacements
        $placeholders = [
            '{school_admin_name}' => $user->full_name,
            '{email}' => $user->email,
            '{password}' => $user->mobile,
            '{school_name}' => $request->school_name,

            '{super_admin_name}' => $settings['super_admin_name'] ?? 'Super Admin',
            '{support_email}' => $settings['mail_username'] ?? '',
            '{contact}' => $settings['mobile'] ?? '',
            '{system_name}' => $settings['system_name'] ?? 'eSchool Saas',
            '{url}' => url('/'),
            // Add more placeholders as needed
        ];

        // Replace the placeholders in the template content
        foreach ($placeholders as $placeholder => $replacement) {
            $templateContent = str_replace($placeholder, $replacement, $templateContent);
        }

        return $templateContent;
    }

    public function show() {
        ResponseService::noPermissionThenRedirect('schools-list');
        $offset = request('offset', 0);
        $limit = request('limit', 10);
        $sort = request('sort', 'id');
        $order = request('order', 'ASC');
        $search = request('search');
        $package_id = request('package_id');
        $showDeleted = request('show_deleted');
        $today_date = Carbon::now()->format('Y-m-d');

        $sql = $this->schoolsRepository->builder()->with('user:id,first_name,last_name,email,image,mobile')->with(['subscription' => function($q) use($today_date){
            $q->whereDate('start_date','<=',$today_date)->whereDate('end_date','>=',$today_date);
        }])->with('subscription.package')
            //search query
            ->where(function ($query) use ($search) {
                $query->when($search, function ($query) use ($search) {
                    $query->where(function ($query) use ($search) {
                        $query->where('name', 'LIKE', "%$search%")
                            ->orWhere('support_email', 'LIKE', "%$search%")
                            ->orWhere('support_phone', 'LIKE', "%$search%")
                            ->orWhere('tagline', 'LIKE', "%$search%")
                            ->orWhere('address', 'LIKE', "%$search%")
                            ->orWhere('business_registration_no', 'LIKE', "%$search%")
                            ->orWhere('remark', 'LIKE', "%$search%")
                            ->orWhere('capacity', 'LIKE', "%$search%")
                            // ->orWhere('Branch', 'LIKE', "%$search%")
                            ->orWhereHas('user', function ($query) use ($search) {
                                $query->whereRaw("CONCAT(first_name, ' ', last_name) LIKE ?", "%$search%");
                            });
                    });
                    $query->where(function ($query) use ($search) {
                        $query->where('name', 'LIKE', "%$search%")
                            ->orWhere('support_email', 'LIKE', "%$search%")
                            ->orWhere('support_phone', 'LIKE', "%$search%")
                            ->orWhere('tagline', 'LIKE', "%$search%")
                            ->orWhere('address', 'LIKE', "%$search%")
                            ->orWhere('business_registration_no', 'LIKE', "%$search%")
                            ->orWhere('remark', 'LIKE', "%$search%")
                            ->orWhere('capacity', 'LIKE', "%$search%")
                            ->orWhereHas('user', function ($query) use ($search) {
                                $query->whereRaw("CONCAT(first_name, ' ', last_name) LIKE ?", "%$search%");
                            });
                    });
                });
            })->when(!empty($showDeleted), function ($query) {
                $query->onlyTrashed();
            });

        if ($package_id) {
            $sql->whereHas('subscription',function($q) use($package_id, $today_date) {
                $q->where('package_id',$package_id)->whereDate('start_date','<=',$today_date)->whereDate('end_date','>=',$today_date);
            });
        }


        $total = $sql->count();

        $sql->orderBy($sort, $order)->skip($offset)->take($limit);
        $res = $sql->get();

        // Fetch the additional data
        $schoolStats = DB::select(
            "SELECT 
                s.id AS school_id,
                s.name AS school_name,
                SUM(CASE WHEN u.status = 0 THEN 1 ELSE 0 END) AS inactive_count,
                SUM(CASE WHEN u.status = 1 THEN 1 ELSE 0 END) AS active_count,
                COUNT(*) AS total_students
            FROM 
                students stu
            JOIN 
                users u ON u.id = stu.user_id
            JOIN 
                schools s ON u.school_id = s.id
            GROUP BY 
                s.id, s.name"
        );

        // Merge the data into a unified format
        $mergedData = [];
        foreach ($res as $key => $row) {
            $mergedData[$key] = $row;
            foreach ($schoolStats as $stat) {
                if ($stat->school_id === $row->id) {
                    $mergedData[$key]->active_count = $stat->active_count;
                    $mergedData[$key]->inactive_count = $stat->inactive_count;
                    $mergedData[$key]->total_students = $stat->total_students;
                    break;
                }
            }
        }

        $bulkData = array();
        $bulkData['total'] = $total;
        $bulkData['rows'] = $mergedData;
        $rows = array();
        $no = 1;

        foreach ($res as $row) {
            $operate = '';

            if ($showDeleted) {
                //Show Restore and Hard Delete Buttons
                $operate = BootstrapTableService::menuRestoreButton('restore',route('schools.restore', $row->id));
                $operate .= BootstrapTableService::menuTrashButton('delete',route('schools.trash', $row->id));
            } else {
                $operate = BootstrapTableService::menuButton('change_admin',"#",['update-admin-data'],['data-toggle' => "modal", 'data-target' => "#editAdminModal"]);

                if ($row->status == 0) {
                    $operate .= BootstrapTableService::menuButton('activate_school',"#",["change-school-status"],['data-id' => $row->id]);
                } else {
                    $operate .= BootstrapTableService::menuButton('inactive_school',"#",["change-school-status"],['data-id' => $row->id]);
                }
                $operate .= BootstrapTableService::menuButton('edit_branch_school',route('school.branch',$row->id));
                $operate .= BootstrapTableService::menuButton('login_as_school', route('school.login_to', $row['id']));
                $operate .= BootstrapTableService::menuEditButton('edit',route('schools.update', $row->id));
                $operate .= BootstrapTableService::menuDeleteButton('delete',route('schools.destroy', $row->id));
            }

            $tempRow = $row->toArray();
            $tempRow['no'] = $no++;
            $tempRow['active_plan'] = '-';
            $e_invoice_status = false;
            $e_invoice_exist = DB::table('e_invoice')->where('school_id', $row->id)->exists();
            $eInvoiceValidate = DB::table('e_invoice')->where('school_id',$row->id)->where('status',1)->first();
            if($e_invoice_exist){
                $incompleteDataCount = DB::table('e_invoice')
                ->where('school_id', $row->id)
                ->where(function ($query) {
                    $query->whereNull('new_company_registration_number')
                    ->orWhere('new_company_registration_number', '')
                    ->orWhereNull('company_business_activity')
                    ->orWhere('company_business_activity', '')
                    ->orWhereNull('tax_identification_number')
                    ->orWhere('tax_identification_number', '')
                    ->orWhereNull('address_line1')
                    ->orWhere('address_line1', '')
                    ->orWhereNull('city')
                    ->orWhere('city', '')
                    ->orWhereNull('postal_code')
                    ->orWhere('postal_code', '')
                    ->orWhereNull('country')
                    ->orWhere('country', '')
                    ->orWhereNull('state')
                    ->orWhere('state', '');
                })
                ->count();
                if($incompleteDataCount === 0 && $eInvoiceValidate) {
                    $e_invoice_status = true;
                }
            }
            $tempRow['e_invoice_status'] = $e_invoice_status;
            if(!$eInvoiceValidate){
                $operate .= BootstrapTableService::menuButton('validate e-invoice', route('schools.validate-einvoice', $row->id), ['validate-einvoice']);
            }

            if (count($row->subscription)) {
                $package = $row->subscription()->whereDate('start_date','<=',$today_date)->whereDate('end_date','>=',$today_date)->latest()->first();
                if ($package) {
                    $tempRow['active_plan'] = $package->name;
                }
            } else {
                $tempRow['active_plan'] = '-';
            }
            $schoolBranch = DB::table('school_branch')
                            ->join('schools','schools.id','=','school_branch.branch_id')
                            ->where('school_branch.school_id',$row->id)
                            ->pluck('name')
                            ->toArray();
            $tempRow['school_branch'] = implode(', ',$schoolBranch);
            // $tempRow['operate'] = $operate;
            $tempRow['operate'] = BootstrapTableService::menuItem($operate);
            $rows[] = $tempRow;
        }

        $bulkData['rows'] = $rows;
        return response()->json($bulkData);
    }

    public function update(Request $request, $id) {
        ResponseService::noPermissionThenSendJson(['schools-edit']);
        $validator = Validator::make($request->all(), [
            'edit_school_name'          => 'required',
            'edit_school_support_email' => 'required|unique:schools,support_email,' . $id,
            'edit_school_support_phone' => 'required|numeric|digits_between:1,16',
            'edit_school_tagline'       => 'nullable',
            'edit_school_address'       => 'required',
            'edit_business_registration_no'  => 'nullable',
            'edit_remark'                    => 'nullable',
            'edit_capacity'                  => 'nullable|numeric',
            'edit_school_image'         => 'nullable|mimes:jpg,jpeg,png,svg,svg+xml|max:2048',
            'edit_domain'               => 'nullable|unique:schools,domain,'.$id
        ]);
        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            $school_data = array(
                'name'          => $request->edit_school_name,
                'address'       => $request->edit_school_address,
                'support_email' => $request->edit_school_support_email,
                'support_phone' => $request->edit_school_support_phone,
                'tagline'       => $request->edit_school_tagline ?? '',
                'business_registration_no'=> $request->edit_business_registration_no ?? '',
                'capacity'                => $request->edit_capacity,
                'remark'                  => $request->edit_remark ?? '',
                'domain'        => $request->edit_domain
            );

            if ($request->hasFile('edit_school_image')) {
                $school_data['logo'] = $request->file('edit_school_image');
            }

            $this->schoolsRepository->update($request->edit_id, $school_data); // Call update function of Schools Repository
            // Update school settings
            $schoolSettingData = array(
                [
                    'name'      => 'school_name',
                    'data'      => $request->edit_school_name,
                    'type'      => 'string',
                    'school_id' => $request->edit_id,
                ],
                [
                    'name'      => 'school_email',
                    'data'      => $request->edit_school_support_email,
                    'type'      => 'string',
                    'school_id' => $request->edit_id
                ],
                [
                    'name'      => 'school_phone',
                    'data'      => $request->edit_school_support_phone,
                    'type'      => 'number',
                    'school_id' => $request->edit_id
                ],
                [
                    'name'      => 'school_tagline',
                    'data'      => $request->edit_school_tagline,
                    'type'      => 'string',
                    'school_id' => $request->edit_id
                ],
                [
                    'name'      => 'school_address',
                    'data'      => $request->edit_school_address,
                    'type'      => 'string',
                    'school_id' => $request->edit_id
                ],
                [
                    'name'      => 'business_registration_no',
                    'data'      => $request->edit_business_registration_no,
                    'type'      => 'string',
                    'school_id' => $request->edit_id
                ],
                [
                    'name'      => 'capacity',
                    'data'      => $request->edit_capacity,
                    'type'      => 'number',
                    'school_id' => $request->edit_id
                ],
                [
                    'name'      => 'remark',
                    'data'      => $request->edit_remark,
                    'type'      => 'string',
                    'school_id' => $request->edit_id
                ],
                [
                    'name'      => 'domain',
                    'data'      => $request->edit_domain,
                    'type'      => 'string',
                    'school_id' => $request->edit_id
                ]);

                if ($request->hasFile('edit_school_image')) {
                    $schoolSettingData[] = [
                        'name'      => 'vertical_logo',
                        'data'      => $request->file('edit_school_image')->store('school','public'),
                        'type'      => 'file',
                        'school_id' => $request->edit_id
                    ];
                }
                SchoolSetting::upsert($schoolSettingData,['name','school_id'],['data','school_id','type']);
                $this->cache->removeSchoolCache(config('constants.CACHE.SCHOOL.SETTINGS'),$request->edit_id);

                // Assign package
            if ($request->assign_package) {
                // Create subscription plan
                $this->subscriptionService->createSubscription($request->assign_package, $request->edit_id, null, 1);
                $this->cache->removeSchoolCache(config('constants.CACHE.SCHOOL.SETTINGS'),$request->edit_id);

            }

            ResponseService::successResponse('Data Updated Successfully');
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e, "School Controller -> Update method");
            ResponseService::errorResponse();
        }
    }

    public function destroy($id) {
        ResponseService::noPermissionThenSendJson('schools-delete');
        try {
            $school = $this->schoolsRepository->update($id,['status' => 0]);
            User::withTrashed()->where('id',$school->admin_id)->delete();
            $this->schoolsRepository->deleteById($id);
            ResponseService::successResponse('Data Deleted Successfully');
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e, "School Controller -> Delete method");
            ResponseService::errorResponse();
        }
    }

    public function restore(int $id) {
        ResponseService::noPermissionThenSendJson('schools-delete');
        try {
            $this->schoolsRepository->findOnlyTrashedById($id)->restore();
            $school = $this->schoolsRepository->findById($id);
            User::onlyTrashed()->where('id', $school->admin_id)->restore();

            ResponseService::successResponse("Data Restored Successfully");
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function trash($id) {
        ResponseService::noPermissionThenSendJson('schools-delete');
        try {
            ResponseService::errorResponse("cannot_delete_because_data_is_associated_with_other_data");
            $this->schoolsRepository->findOnlyTrashedById($id)->forceDelete();
            ResponseService::successResponse("Data Deleted Permanently");
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e,'','cannot_delete_because_data_is_associated_with_other_data');
            ResponseService::errorResponse();
        }
    }

    public function adminSearch(Request $request) {
        $adminData = $this->userRepository->getTrashedAdminData($request->email);
        if (!empty($adminData)) {
            $response = ['error' => false, 'data' => $adminData];
        } else {
            $response = ['error' => true, 'message' => trans('no_data_found')];
        }
        return response()->json($response);
    }

    public function updateAdmin(Request $request) {
        ResponseService::noAnyPermissionThenRedirect(['schools-edit']);
        $validator = Validator::make($request->all(), [
            "edit_id"               => 'required',
            "edit_admin_email"      => 'required',
            "edit_admin_first_name" => 'required',
            "edit_admin_last_name"  => 'required',
            "edit_admin_contact"    => 'required|digits_between:1,16',
        ]);
        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            DB::beginTransaction();

            // If Email is not the ID then Check the following requirements
            if (!is_numeric($request->edit_admin_email)) {
                $validator = Validator::make($request->all(), [
                    "edit_admin_email" => 'required|email|unique:users,email',
                ],
                    [
                        "edit_admin_email.required" => trans('email_is_required'),
                        "edit_admin_email.email"    => trans('enter_valid_email'),
                        "edit_admin_email.unique"   => trans('email_already_in_use'),
                    ]);
                if ($validator->fails()) {
                    ResponseService::validationError($validator->errors()->first());
                }
            }

            $admin_data = array(
                'school_id'  => $request->edit_id,
                'email'      => $request->edit_admin_email,
                'first_name' => $request->edit_admin_first_name,
                'last_name'  => $request->edit_admin_last_name,
                'contact'    => $request->edit_admin_contact,
                'reset_password'    => $request->reset_password,
            );
            $this->schoolsRepository->updateSchoolAdmin($admin_data, $request->edit_admin_image); // Call updateSchoolAdmin function of Schools Repository
            DB::commit();
            ResponseService::successResponse('Data Updated Successfully');
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "School Controller -> Update Admin method");
            ResponseService::errorResponse();
        }
    }

    public function changeStatus($id) {
        ResponseService::noAnyPermissionThenRedirect(['schools-edit']);
        try {
            DB::beginTransaction();
            $school = $this->schoolsRepository->findById($id);
            $status = ['status' => $school->status == 0 ? 1 : 0];
            $this->schoolsRepository->update($id, $status);
            DB::commit();
            ResponseService::successResponse('Data updated successfully');
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "School Controller -> Change Status");
            ResponseService::errorResponse();
        }
    }

    public function searchAdmin(Request $request) {
        ResponseService::noAnyPermissionThenRedirect(['schools-create', 'schools-edit']);
        $parent = $this->userRepository->builder()->role('School Admin')->withTrashed()->where(function ($query) use ($request) {
            $query->where('email', 'like', '%' . $request->email . '%')
                ->orWhere('first_name', 'like', '%' . $request->email . '%')
                ->orWhere('last_name', 'like', '%' . $request->email . '%');
        })->get();

        if (!empty($parent)) {
            $response = [
                'error' => false,
                'data'  => $parent
            ];
        } else {
            $response = [
                'error'   => true,
                'message' => trans('no_data_found')
            ];
        }
        return response()->json($response);
    }

    public function registrationForm()
    {
        $settings = app(CachingService::class)->getSystemSettings();
        $guidances = $this->guidance->builder()->get();
        $languages = Language::get();
        return view('registration_form',compact('settings','guidances','languages'));
    }

    public function registration(Request $request) {
        $validator = Validator::make($request->all(), [
            'school_name'          => 'required',
            'school_image'                      => 'required|mimes:jpg,jpeg,png,svg,svg+xml',
            'school_support_email' => 'required',
            'school_support_phone' => 'required|numeric|digits_between:1,16',
            'school_address'       => 'required',
            'admin_first_name'     => 'required',
            'admin_last_name'      => 'required',
            'admin_contact'        => 'required|numeric|digits_between:1,16',
            'admin_email'          => 'required',
            'new_company_registration_number'   => 'required',
            'old_company_registration_number'   => 'required',
            'company_business_activity'         => 'required',
            'address_line1'                     => 'required',
            'city'                              => 'required',
            'postal_code'                       => 'required',
            'state'                             => 'required',
            'country'                           => 'required',
        ]);

        if ($validator->fails()) {
            // ResponseService::validationError($validator->errors()->first());
            return Redirect::back()->withErrors($validator);
        }

        $school = $this->schoolsRepository->builder()->where('support_email', $request->school_support_email)->withTrashed()->first();
        $user = $this->userRepository->builder()->where('email', $request->admin_email)->withTrashed()->first();
        if ($school || $user) {
            return redirect('/login')->with('error', 'School or User email already exists');
        }

        try {
            DB::beginTransaction();

            $school_data = array(
                'name'          => $request->school_name,
                'address'       => $request->school_address,
                'support_email' => $request->school_support_email,
                'support_phone' => $request->school_support_phone,
                'tagline'                 => $request->school_tagline ?? '',
                'business_registration_no'=> $request->business_registration_no ?? '',
                'remark'                  => $request->remark ?? '',
                // 'capacity'                => $request->capacity,
                'logo'                    => $request->file('school_image'),
                'status'                  => 1,
            );
            // Call store function of Schools Repository
            $schoolData = $this->schoolsRepository->create($school_data);

            $medium = ['name' => '', 'school_id' => $schoolData->id];
            Mediums::create($medium);
    
            $sections = ['name' => '', 'school_id' => $schoolData->id];
            Section::create($sections);

            $admin_data = array(
                'first_name' => $request->admin_first_name,
                'last_name'  => $request->admin_last_name,
                'mobile'     => $request->admin_contact,
                'email'      => $request->admin_email,
                'password'   => Hash::make($request->admin_contact),
                'school_id'  => $schoolData->id,
                'image'      => 'dummy_logo.jpg'
            );

            $e_invoice_data = array(
                'new_company_registration_number'   => $request->new_company_registration_number,
                'old_company_registration_number'   => $request->old_company_registration_number,
                'company_business_activity'         => $request->company_business_activity,
                'tax_identification_number'         => $request->tax_identification_number,
                'sst_registration_number'           => $request->sst_registration_number,
                'tourism_tax_registration_number'   => $request->tourism_tax_registration_number,
                'company_msic_code'                 => $request->company_msic_code,
                'address_line1'                     => $request->address_line1,
                'address_line2'                     => $request->address_line2,
                'address_line3'                     => $request->address_line3,
                'city'                              => $request->city,
                'postal_code'                       => $request->postal_code,
                'country'                           => $request->country,
                'state'                             => $request->state,
                'school_id'                         => $schoolData->id,
            );

            $e_invoice = DB::table("e_invoice")->insert($e_invoice_data);

            //Call store function of User Repository and get the admin data
            $user = $this->userRepository->create($admin_data);
            $user->assignRole('School Admin');

            // Update Admin id to School Data
            $schoolData = $this->schoolsRepository->update($schoolData->id, ['admin_id' => $user->id]);

            $schoolService = app(SchoolDataService::class);
            // Add Pre School Settings By Default
            $schoolService->preSettingsSetup($schoolData);

            // Assign trail package if selected
            if ($request->trial_package) {
                // Create subscription plan
                $this->subscriptionService->createSubscription($request->trial_package, $schoolData->id, null, 1);
                $this->cache->removeSchoolCache(config('constants.CACHE.SCHOOL.SETTINGS'),$schoolData->id);
            }

             // Set Default reward categories
             $rewardCategories = [
                [
                    'category_name' => 'Clock In',
                    'points_amount' => 0,
                    'remark' => 'Default Clock In',
                    'is_default' => 1,
                    'school_id' => $schoolData->id,
                    'mode' => 1,
                ],
                [
                    'category_name' => 'Clock Out',
                    'points_amount' => 0,
                    'remark' => 'Default Clock Out',
                    'is_default' => 1,
                    'school_id' => $schoolData->id,
                    'mode' => 1,
                ]
            ];

            DB::table('rewards_category')->insert($rewardCategories);


            DB::commit();

            $settings = $this->cache->getSystemSettings();
            $email_body = $this->replacePlaceholders($request, $user, $settings);

            $data = [
                'subject'     => 'Welcome to ' . $settings['system_name'] ?? 'eSchool Saas',
                'email'       => $request->admin_email,
                'email_body'  => $email_body
            ];

            $data = [
                'subject'     => 'Welcome to ' . $request->school_name,
                'name'        => $request->admin_first_name,
                'email'       => $request->admin_email,
                'password'    => $request->admin_contact,
                'email_body'  => $email_body,
                'school_name' => $request->school_name
            ];

            Mail::send('schools.email', $data, static function ($message) use ($data) {
                $message->to($data['email'])->subject($data['subject']);
            });

            return redirect('/login')->with('success', 'School Registration Successful');

        } catch (Throwable $e) {
            if (Str::contains($e->getMessage(), ['Failed', 'Mail', 'Mailer', 'MailManager'])) {
                return redirect('/login')->with('success', 'School Registration Successfully. But Email not sent');
            } else {
                DB::rollBack();
                ResponseService::logErrorResponse($e, "School Controller -> Registration method");
                ResponseService::errorResponse();
            }
            
            
        }
    }
    public function editSchoolBranch($id)
    {
        $school = DB::table('schools')->where('id', $id)->first();
        $schoollist = DB::table('schools')->get();
        $schoolBranch = DB::table('school_branch')->where('school_id',$id)->get();
        // dd($schoolBranch);
        return view('schools.school_branch',compact('school','schoollist','schoolBranch','id'));

    }

    public function updateSchoolBranch(Request $request)
    {
        $validatedData = $request->validate([
            'school_id' => 'required|integer',
            'student_fee_types.*.branch_id' => 'required|integer',
            'student_fee_types.*.id' => 'nullable|integer',
        ]);

        foreach ($validatedData['student_fee_types'] as $branch) {
            // If id exists, update existing branch, otherwise create a new one
            if (!empty($branch['id'])) {
                DB::table('school_branch')->where('id', $branch['id'])->update([
                    'school_id' => $validatedData['school_id'],
                    'branch_id' => $branch['branch_id'],
                    'updated_at' => now(), ]);
            } else {
                DB::table('school_branch')->insert([
                    'school_id' => $validatedData['school_id'],
                    'branch_id' => $branch['branch_id'],
                    'created_at' => now(),  // Set the created_at field
                    'updated_at' => now(), // Set the updated_at field
                ]);
            }
        }
        DB::commit();
        return redirect()->route('schools.index')->with('success', 'School branch updated successfully!');
    }
    public function deleteSchoolBranch($id) {

        try {
            DB::beginTransaction();
            DB::table('school_branch')->where("id",$id)->delete();
            DB::commit();
            ResponseService::successResponse("Data Deleted Successfully");
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "SchoolController -> Store Method");
            ResponseService::errorResponse();
        }
    }
    public function loginToSchool($schoolId)
    {
        // Validate schoolId (e.g., ensure it exists and the current user has permission)
        $school = School::findOrFail($schoolId);
        
        // Find a user to log in as (or create logic to determine the appropriate user)
        $user = User::where('school_id', $schoolId)->first(); // Example logic; adjust as needed

        if ($user) {
            Auth::login($user);

            return redirect()->route('dashboard'); // Redirect to a dashboard or home page after login
        }

        // Handle case where no user is found
        return redirect()->back()->withErrors(['error' => 'No user found for the selected school.']);
    }

    public function validateEInvoicing($id){
        try {
            $schoolEInvoice = DB::table('e_invoice')
                                ->where('school_id',$id)
                                ->first();
            $accessToken = null;
            if(!$schoolEInvoice || empty($schoolEInvoice->tax_identification_number)){
                ResponseService::errorResponse("No TIN number found");
            } else {
                $schoolSettings = $this->cache->getSchoolSettings(['*'],$id);
                if($schoolSettings) {
                    $clientId = '';
                    $client_secret = array();
                    if($schoolSettings['client_id']) {
                        $clientId = $schoolSettings['client_id'];
                    }
                    if($schoolSettings['client_secret_1']){
                        $client_secret[] = $schoolSettings['client_secret_1'];
                    }
                    if($schoolSettings['client_secret_2']){
                        $client_secret[] = $schoolSettings['client_secret_2'];
                    }
                    if(!empty($clientId) && count($client_secret) > 0) {
                        foreach ($client_secret as $secret){
                            $url = 'https://api.myinvois.hasil.gov.my/connect/token';
                            // $headers = ['onbehalfof:'.$schoolEInvoice->tax_identification_number];
                            $fields = [
                                'client_id' => $clientId,
                                'client_secret' => $secret,
                                'grant_type'    => 'client_credentials',
                                'scope'         => 'InvoicingAPI'
                            ];
                            $encodedFields = http_build_query($fields);
                            $ch = curl_init();
                            curl_setopt($ch, CURLOPT_URL, $url);
                            curl_setopt($ch, CURLOPT_POST, true);
                            // curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
                            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
                            curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
                            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                            curl_setopt($ch,CURLOPT_POSTFIELDS, $encodedFields);
                            $result = curl_exec($ch);
                            if($result == false){
                                die('Curl failed: ' . curl_error($ch));
                            }else {
                                $data = json_decode($result);
                                if(!isset($data->error)){
                                    if(!empty($data->access_token)){
                                        $accessToken = $data->access_token;
                                        break;
                                    }
                                }
                            }
                            curl_close($ch);
                        }
                        if($accessToken){
                            if(!empty($schoolEInvoice->tax_identification_number) && !empty($schoolEInvoice->old_company_registration_number)){
                                $url = 'https://api.myinvois.hasil.gov.my/api/v1.0/taxpayer/validate/'.$schoolEInvoice->tax_identification_number.'?idType=BRN&idValue='.$schoolEInvoice->old_company_registration_number;
                                $headers = [
                                    'authorization: Bearer '.$accessToken,
                                    'Content-Type:application/json',
                                    'Accept:application/json',
                                    'Accept-Language:en'
                                ];
                                $ch = curl_init();
                                curl_setopt($ch, CURLOPT_URL, $url);
                                curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
                                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                                curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
                                curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
                                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                                $result = curl_exec($ch);
                                $httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                                curl_close($ch);
                                
                                if($httpcode == 200){
                                    DB::table('e_invoice')->where('id',$schoolEInvoice->id)->update(['status' => 1]);
                                    ResponseService::successResponse("TIN number has been validated");
                                } else {
                                    ResponseService::errorResponse("Unable to validate TIN number");
                                }
                            }
                        }
                    }
                }
            }
        } catch (\Throwable $e) {
            DB::rollBack();
            ResponseService::errorResponse();
        }
    }
    
}
    

