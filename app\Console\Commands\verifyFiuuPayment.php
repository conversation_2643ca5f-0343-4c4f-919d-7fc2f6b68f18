<?php

namespace App\Console\Commands;

use chillerlan\QRCode\QRCode;
use Illuminate\Console\Command;
use App\Services\CachingService;
use App\Services\ResponseService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use \Datetime;
use Carbon\Carbon;

class verifyFiuuPayment extends Command
{
    private CachingService $cache;
    public function __construct(CachingService $cache)
    {
        parent::__construct();
        $this->cache = $cache;
    }
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'verifyFiuuPayment:cron';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $paymentConfigs = DB::table('payment_configurations')
                    ->select('secret_key', 'school_id', 'merchant_id')
                    ->where('payment_method', 'Fiuu')
                    ->whereRaw("merchant_id IS NOT NULL AND merchant_id != ''")
                    ->whereRaw("secret_key IS NOT NULL AND secret_key != ''")
                    ->whereRaw("school_id IS NOT NULL AND school_id != ''")
                    ->get();
        
            date_default_timezone_set('Asia/Kuala_Lumpur');
            $currentDatetime = new DateTime();
            $pastDatetime = new DateTime();
            $pastDatetime->modify('-5 minutes');
            //echo $currentDatetime->format('Y-m-d H:i:s') . " " . $pastDatetime->format('Y-m-d H:i:s');
            $date = $pastDatetime->format('Y-m-d');
            $allRecords = [];
            foreach($paymentConfigs as $item) {
                $count=0;
                $url = 'https://api.fiuu.com/RMS/API/PSQ/psq-daily.php';
                $fields = [
                    'merchantID' => $item->merchant_id,
                    'skey'       => md5($date.$item->merchant_id.$item->secret_key),
                    'rdate'      => $date,
                    'version'    => 4.0,
                    'additional_fields' => 'all'
                ];
                $encodedFields = http_build_query($fields);
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $url);
                curl_setopt($ch, CURLOPT_POST, true);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
                curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                curl_setopt($ch, CURLOPT_POSTFIELDS, $encodedFields);
                
                $result = curl_exec($ch);
                if ($result === false) {
                    die('Curl failed: ' . curl_error($ch));
                } else {
                    $lines = explode("\n", trim($result));
                    $header = explode("\t", array_shift($lines));
                    if (end($header) === "") {
                        array_pop($header);  
                    }
                    $header[]='school_id';
                    foreach($header as $key=>$head) {
                        if($header[$key]==='Channel'){
                            $count++;
                            if($count>1) {      
                                $header[$key] = 'Channel' . $count;
                            } else {
                                $header[$key] = 'Channel1';
                            }
                        }
                    }
                    //echo md5($date.$item->merchant_id.$item->secret_key);
                    foreach ($lines as $line) {
                        if (!empty($line)) {
                            $values = explode("\t", $line);
                            $values[]=$item->school_id;          
                            $record = array_combine($header, $values);
                            $fieldsToRemove = [
                                'StatCode', 'StatName', 'BIN', 'BillingInfo', 'Channel2', 'GST', 'BankName', 
                                'ExpiryDate', 'SettlementDate', 'TerminalID', 'PayTransactionID', 'ResponseCode',
                                'ApprovalCode', 'Bin4', 'ECIValue', 'CAVV', 'XID', 'AcquirerName', 'BankMID',
                                'CardScheme', 'CardType', 'CardCountry', 'CaptureRefID', 'RefundRefID', 'school_id'
                            ];

                            $filteredRecord = array_diff_key($record, array_flip($fieldsToRemove));

                            $recordString = implode("\n", array_map(function($key, $value) {
                                return "$key: $value";
                            }, array_keys($filteredRecord), $filteredRecord));
                            if ($record) {
                                $exists = DB::table('fiuu_daily_transaction')
                                    ->where('TranId', $values[2])
                                    ->exists();
                                if ($exists) {
                                    //echo "TranId: $values[2] exists.";
                                    //Record exist so we can skip it
                                } else {
                                    //echo "TranId $values[2] not found";
                                    //Record does not exit, insert and verify the data
                                    DB::table('fiuu_daily_transaction')->insert($record);
                                    $orderId = (int)str_replace("INV", "", $values[1]);
                                    if($values[5] == '0' && ($values[30] == '00' || $values[30] == 'SUCCESS')){
                                        //Process the following record
                                        $studentFeesResults = DB::select("
                                            SELECT * FROM student_fees
                                            WHERE uid = ?
                                            AND school_id = ?
                                            AND status = 'published'
                                        ", [$orderId, $item->school_id]);
                                        if (COUNT($studentFeesResults) == 0) {
                                            if ($orderId > 10000) {
                                                $studentFeesResults = DB::select("
                                                    SELECT * FROM student_fees
                                                    WHERE id = ?
                                                    AND school_id = ?
                                                    AND status = 'published'
                                                ", [$orderId, $item->school_id]);
                                            }
                                            // After potential re-query, check again if still no results
                                            if (COUNT($studentFeesResults) == 0) {
                                                echo 'UID/ID: '.$orderId.' not found';
                                                //If student fee not found, something is not right
                                            }
                                        }
                                        if (COUNT($studentFeesResults) == 1) {
                                            //echo "Record found: " . json_encode($studentFeesResults[0]);
                                            //Student fee exist, check their payment status before process
                                            //Calculate the student fees
                                            $res = DB::select("
                                                SELECT      sf.id AS student_fees_id,
                                                            s.id AS student_id,
                                                            s.school_id,
                                                            CONCAT(u.first_name , u.last_name) AS full_name,
                                                            s.guardian_id,
                                                            (
                                                                SELECT  SUM( (fees_type_amount - (fees_type_amount * COALESCE(discount,0)/100) + (fees_type_amount * COALESCE(tax,0)/100) ) * quantity ) 
                                                                FROM    student_fees_details
                                                                WHERE   student_fees_id = sf.id
                                                                AND     fees_type_name NOT IN ('Early Discount', 'Overdue Fees')
                                                            )    AS total_compulsory_fees,
                                                            (
                                                                SELECT  SUM(fees_type_amount * discount / 100) 
                                                                FROM    student_fees_details
                                                                WHERE   student_fees_id = sf.id
                                                                AND     fees_type_name NOT IN ('Early Discount', 'Overdue Fees')
                                                            )   AS sumDiscount,
                                                            (
                                                                SELECT  SUM(fees_type_amount * tax / 100) 
                                                                FROM    student_fees_details
                                                                WHERE   student_fees_id = sf.id
                                                                AND     fees_type_name NOT IN ('Early Discount', 'Overdue Fees')
                                                            )   AS sumTax,
                                                            c.name AS class_name,
                                                            sfp.is_fully_paid,
                                                            sfp.mode,
                                                            sfp.cheque_no,
                                                            sfp.date
                                                FROM        users u
                                                JOIN        students s
                                                ON          u.id = s.user_id
                                                JOIN        class_sections cs
                                                ON          cs.id = s.class_section_id
                                                JOIN        classes c
                                                ON          c.id = cs.class_id
                                                JOIN        student_fees sf
                                                ON          sf.student_id = s.id
                                                AND         sf.status = 'published'
                                                LEFT JOIN   student_fees_paids sfp
                                                ON          sfp.student_fees_id = sf.id
                                                WHERE       s.id = ?
                                                AND         sf.id = ?
                                                AND         sf.deleted_at IS NULL
                                                ORDER BY    sfp.is_fully_paid, sf.created_at desc
                                            ", [$studentFeesResults[0]->student_id, $studentFeesResults[0]->id]);
                                            $student = $res[0];
                                            if ($student) {
                                                $student->due_charges = 0;
                                                $currentDate = date('Y-m-d');
                                                $due_date = DB::select('SELECT due_date FROM student_fees WHERE id= ?', [$student->student_fees_id]);
                                                $due_date = $due_date[0]->due_date;
                                                $due_date = date("Y-m-d", strtotime($due_date));
                                                $early_date = DB::select('SELECT early_date FROM student_fees WHERE id= ?', [$student->student_fees_id]);
                                                $early_date = $early_date[0]->early_date;
                                                $early_date = date("Y-m-d", strtotime($early_date));
                                                $total_compulsory_fees = floatval(str_replace(',', '', $student->total_compulsory_fees));
                                                $earlyFee = 0;
                                                $dueFee = 0;
                                                $studentFeesCharges = DB::table('student_fees')->where('id',$student->student_fees_id)->first();
                                                if ($student->is_fully_paid == 0 && $currentDate > $due_date) {
                                                    $student_overdue_fees = DB::table("student_fees_details")
                                                    ->select("fees_type_amount")
                                                    ->where("fees_type_name", "Overdue Fees")
                                                    ->where("student_fees_id", $student->student_fees_id) 
                                                    ->get();
                                                    if ($student_overdue_fees->isNotEmpty()) {
                                                        $total_compulsory_fees += $student_overdue_fees->first()->fees_type_amount;
                                                        $student->due_charges = number_format($student_overdue_fees->first()->fees_type_amount, 2);
                                                        $student->total_compulsory_fees = $total_compulsory_fees;   
                                                    } else {
                                                        if ($currentDate > $due_date) {
                                                            if (!$studentFeesCharges->due_charges) {
                                                                $dueFee = $studentFeesCharges->due_charges_amount;
                                                                $total_compulsory_fees += $dueFee;
                                                            } else {
                                                                $dueFee =($total_compulsory_fees * ($studentFeesCharges->due_charges / 100));
                                                                $total_compulsory_fees += $dueFee;
                                                            }
                                                            $student->due_charges = number_format($dueFee);
                                                            $student->total_compulsory_fees = $total_compulsory_fees;   
                                                        } 
                                                    }
                                                }
                                                // Only apply the early discount if there are no overdue fees
                                                if ($student->is_fully_paid == 0 && $currentDate <= $early_date && $currentDate <= $due_date) {
                                                    $student_early_discount = DB::table("student_fees_details")
                                                        ->select("fees_type_amount")
                                                        ->where("fees_type_name", "Early Discount")
                                                        ->where("student_fees_id", $student->student_fees_id) 
                                                        ->get();
                                                    if ($student_early_discount->isNotEmpty()) {
                                                        $total_compulsory_fees += $student_early_discount->first()->fees_type_amount;
                                                        $student->early_offer = number_format($student_early_discount->first()->fees_type_amount, 2);
                                                        $student->total_compulsory_fees = $total_compulsory_fees;   
                                                    } else {
                                                        if ($currentDate <= $early_date) {
                                                            if (!$studentFeesCharges->early_offer) {
                                                                $earlyFee = $studentFeesCharges->early_offer_amount;
                                                                $total_compulsory_fees -= $earlyFee;
                                                            } else {
                                                                $earlyFee = ($total_compulsory_fees * ($studentFeesCharges->early_offer / 100));
                                                                $total_compulsory_fees -= $earlyFee;
                                                            }
                                                            $student->early_offer = number_format($earlyFee);
                                                            $student->total_compulsory_fees = $total_compulsory_fees;
                                                        } 
                                                    }
                                                }
                                            }
                                            $sumDiscount= $student->sumDiscount;
                                            $sumTax = $student->sumTax;

                                            $studentFeesPaidResults = DB::select("
                                                SELECT * FROM student_fees_paids
                                                WHERE student_fees_id = ?
                                                AND school_id = ?
                                            ", [$studentFeesResults[0]->id, $item->school_id]);
                                            if (COUNT($studentFeesPaidResults) == 0) {
                                                echo 'student_fees_id:'.$studentFeesResults[0]->id.' not found';
                                                //Calculation the fees and due charges
                                                //Fee payment not found, insert new payment record
                                                
                                                //Insert a new payment record
                                                $data = [
                                                    [
                                                        'student_fees_id' => $studentFeesResults[0]->id,
                                                        'school_id'       => $item->school_id,
                                                        'mode'            => 3,
                                                        'is_fully_paid'   => 1,
                                                        'amount'          => $student->total_compulsory_fees,
                                                        'due_charges'     => $student->due_charges,
                                                        'status'          => 1,
                                                        'date'            => Carbon::now()->toDateString(),
                                                        'created_at'      => Carbon::now(),
                                                        'updated_at'      => Carbon::now(),
                                                        'payment_detail'  => $recordString,
                                                    ],
                                                ];
                                                DB::table('student_fees_paids')->insert($data);
                                                
                                                $userId = DB::table('users')
                                                    ->join('students', 'students.user_id', '=', 'users.id')
                                                    ->join('student_fees', 'student_fees.student_id', '=', 'students.id')
                                                    ->where('students.id', $student->student_id)
                                                    ->where('students.school_id', $student->school_id)
                                                    ->whereNull('students.deleted_at') // Ensure soft-deleted students are excluded
                                                    ->select('users.id') 
                                                    ->first();

                                                    $data = [
                                                        'user_id'=> $userId->id,
                                                        'school_id' => $student->school_id,
                                                        'type'      => 4,
                                                        'date' => now(),
                                                        'status'    => 0,
                                                    ];
                                                DB::table('admission_notification')->insert($data);
                                            } else if (COUNT($studentFeesPaidResults) == 1) {
                                                //Fee payment found, update only if status is pending or mode is not online
                                                //echo "student_fees_id: ".$studentFeesResults[0]->id.' '.json_encode($studentFeesPaidResults[0]);
                                                echo "student_fees_id: ".$studentFeesResults[0]->id.' found.';
                                                if($studentFeesPaidResults[0]->status == 2 || $studentFeesPaidResults[0]->mode != 3){
                                                    echo 'Update fees status';
                                                    //Update the payment record
                                                    $data = [
                                                        'status'          => 1,
                                                        'mode'            => 3,
                                                        'updated_at'      => Carbon::now(),
                                                        'payment_detail'  => $recordString,
                                                    ];
                                                    DB::table('student_fees_paids')->where('id', $studentFeesPaidResults[0]->id)->update($data);

                                                    $userId = DB::table('users')
                                                    ->join('students', 'students.user_id', '=', 'users.id')
                                                    ->join('student_fees', 'student_fees.student_id', '=', 'students.id')
                                                    ->where('students.id', $student->student_id)
                                                    ->where('students.school_id', $student->school_id)
                                                    ->whereNull('students.deleted_at') // Ensure soft-deleted students are excluded
                                                    ->select('users.id') 
                                                    ->first();

                                                    $data = [
                                                        'user_id'=> $userId->id,
                                                        'school_id' => $student->school_id,
                                                        'type'      => 4,
                                                        'date' => now(),
                                                        'status'    => 0,
                                                    ];
                                                    DB::table('admission_notification')->insert($data);
                                                }
                                            } else {
                                                echo 'student_fees_id:'.$studentFeesResults[0]->id.' found '.COUNT($studentFeesPaidResults).' records';
                                                //Found more than 1 records. Delete all records and insert new one
                                                DB::table('student_fees_paids')->where('student_fees_id', $studentFeesResults[0]->id)->delete();

                                                $data = [
                                                    [
                                                        'student_fees_id' => $studentFeesResults[0]->id,
                                                        'school_id'       => $item->school_id,
                                                        'mode'            => 3,
                                                        'is_fully_paid'   => 1,
                                                        'amount'          => $student->total_compulsory_fees,
                                                        'due_charges'     => $student->due_charges,
                                                        'status'          => 1,
                                                        'date'            => Carbon::now()->toDateString(),
                                                        'created_at'      => Carbon::now(),
                                                        'updated_at'      => Carbon::now(),
                                                        'payment_detail'  => $recordString,
                                                    ],
                                                ];
                                                DB::table('student_fees_paids')->insert($data);
                                            }
                                        } else {
                                            //echo 'UID/ID: '.$orderId.' found '.COUNT($studentFeesResults).' records';
                                            //More than 1 types of fee found. Ignore it.
                                        }
                                    }
                                    else{
                                        //Payment failure. Need to remove the paid record only if there is no payment success record
                                        $fiuuTransactionResults = DB::select("
                                            SELECT * FROM fiuu_daily_transaction
                                            WHERE OrderId = ?
                                            AND school_id = ?
                                            AND StatCode = '0'
                                            AND (ResponseCode = '00' OR ResponseCode = 'SUCCESS')
                                        ", [$values[1], $item->school_id]);
                                        if (COUNT($fiuuTransactionResults) == 0) {
                                            //Check if the fee exist
                                            $studentFeesResults = DB::select("
                                                SELECT * FROM student_fees
                                                WHERE uid = ?
                                                AND school_id = ?
                                                AND status = 'published'
                                            ", [$orderId, $item->school_id]);
                                            if (COUNT($studentFeesResults) == 1) {
                                                //echo "Record found: " . json_encode($studentFeesResults[0]);
                                                //Student fee exist, check their payment status before process
                                                $studentFeesPaidResults = DB::select("
                                                    SELECT * FROM student_fees_paids
                                                    WHERE student_fees_id = ?
                                                    AND school_id = ?
                                                ", [$studentFeesResults[0]->id, $item->school_id]);
                                                if (COUNT($studentFeesPaidResults) > 0) {
                                                    echo 'student_fees_id:'.$studentFeesResults[0]->id.' found '.COUNT($studentFeesPaidResults).' records';
                                                    //Found more than 1 records. Delete all records and insert new one
                                                    DB::table('student_fees_paids')->where('student_fees_id', $studentFeesResults[0]->id)->delete();
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                curl_close($ch);
            }
            echo 'Process successful';

        } catch (\Throwable $th) {
            Log::error('Error in verify fiuu payment cron job: ' . $th->getMessage(), [
                'exception' => $th,
                'stack_trace' => $th->getTraceAsString(),
            ]);
        }
    }
}
