<?php

namespace App\Http\Controllers;

use App\Models\AdmissionNotification;
use App\Models\Announcement;
use App\Models\ClassGroup;
use App\Models\ClassSchool;
use App\Models\StudentFeeType;
use App\Models\StudentFeesDetail;
use App\Models\StudentFee;
use App\Models\Subject;
use App\Models\Faq;
use App\Models\Feature;
use App\Models\FeatureSection;
use App\Models\Gallery;
use App\Models\Language;
use App\Models\Package;
use App\Models\School;
use App\Models\SchoolSetting;
use App\Models\TeacherAttendance;
use App\Models\Slider;
use App\Models\Stream;
use App\Models\Students;
use App\Models\User;
use App\Repositories\Guidance\GuidanceInterface;
use App\Repositories\SystemSetting\SystemSettingInterface;
use App\Repositories\StudentFeeType\StudentFeeTypeInterface;
use App\Repositories\Subscription\SubscriptionInterface;
use App\Repositories\StudentFeesDetail\StudentFeesDetailInterface;
use App\Repositories\User\UserInterface;
use App\Services\CachingService;
use App\Services\ResponseService;
use App\Services\GeneralFunctionService;
use App\Services\SubscriptionService;
use App\Services\UploadService;
use Carbon\Carbon;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Validator;
use Throwable;
use TypeError;
use DateTime;

class Controller extends BaseController
{
    use AuthorizesRequests, DispatchesJobs, ValidatesRequests;

    private SystemSettingInterface $systemSettings;
    private GuidanceInterface $guidance;
    private UserInterface $user;
    private StudentFeesDetailInterface $studentFeesDetail;
    private SubscriptionInterface $subscription;
    private SubscriptionService $subscriptionService;
    private CachingService $cache;
    private StudentFeeTypeInterface $studentFeeType;

    public function __construct(SystemSettingInterface $systemSettings, GuidanceInterface $guidance, SubscriptionService $subscriptionService, SubscriptionInterface $subscription, CachingService $cache, UserInterface $user, StudentFeeTypeInterface $studentFeeType, StudentFeesDetailInterface $studentFeesDetail)
    {
        $this->systemSettings = $systemSettings;
        $this->guidance = $guidance;
        $this->subscription = $subscription;
        $this->user = $user;
        $this->subscriptionService = $subscriptionService;
        $this->cache = $cache;
        $this->studentFeeType = $studentFeeType;
        $this->studentFeesDetail = $studentFeesDetail;
    }

    /**
     * Get school ID from domain name
     * Checks both custom domain and schola.one subdomain
     * 
     * @return int|null School ID if found, null if not found
     */
    private function getSchoolIdFromDomain() 
    {
        $fullDomain = $_SERVER['HTTP_HOST'];
        
        // First try to find school by custom domain
        $school = School::where('custom_domain', $fullDomain)->first();
        
        if (!$school) {
            // If no school found by custom domain, check if this is a schola.one domain
            if (str_contains($fullDomain, 'schola.one')) {
                // Extract subdomain from schola.one domain
                $parts = explode('.', $fullDomain);
                $subdomain = $parts[0];
                
                // Look up school by subdomain in domain field
                $school = School::where('domain', $subdomain)->first();
            }
        }

        return $school;
    }

    public function index()
    {
        if (Auth::user()) {
            return redirect('dashboard');
        }

        $school = $this->getSchoolIdFromDomain();

        if ($school) {
            // Get current subscription features
            $subscription = $this->subscriptionService->active_subscription($school->id);
            if ($subscription) {
                $features = $subscription->subscription_feature->pluck('feature.name')->toArray();
                $addons = $subscription->addons->pluck('feature.name')->toArray();
                $features = array_merge($features, $addons);
                // Check website management feature
                if (in_array('Website Management', $features)) {
                    return $this->school_website();
                }
            }
        }
        // End school website

        $features = Feature::activeFeatures()->get();
        $settings = app(CachingService::class)->getSystemSettings();
        $schoolSettings = SchoolSetting::where('name', 'horizontal_logo')->get();

        $about_us_lists = $settings['about_us_points'] ?? 'Affordable price, Easy to manage admin panel, Data Security';
        $about_us_lists = explode(",", $about_us_lists);
        $faqs = Faq::where('school_id', null)->get();
        $featureSections = FeatureSection::with('feature_section_list')->orderBy('rank', 'ASC')->get();
        $guidances = $this->guidance->builder()->get();
        $languages = Language::get();

        $school = School::count();

        try {
            $student = User::role('Student')->whereHas('school', function ($q) {
                $q->whereNull('deleted_at')->where('status', 1);
            })->count();
            $teacher = User::role('Teacher')->whereHas('school', function ($q) {
                $q->whereNull('deleted_at')->where('status', 1);
            })->count();
        } catch (Throwable) {
            // If role does not exist in fresh installation then set the counter to 0
            $student = 0;
            $teacher = 0;
        }


        $counter = [
            'school'  => $school,
            'student' => $student,
            'teacher' => $teacher,
        ];

        $packages = Package::where('status', 1)->with('package_feature.feature')->where('status', 1)->orderBy('rank', 'ASC')->get();

        $trail_package = $packages->where('is_trial', 1)->first();
        if ($trail_package) {
            $trail_package = $trail_package->id;
        }
        // Fetch social media links from settings
        $socialMedia = [
            'facebook'  => $settings['facebook'] ?? null,
            'instagram' => $settings['instagram'] ?? null,
            'linkedin'  => $settings['linkedin'] ?? null,
        ];

        $videoLinks = \DB::table('video_settings')->orderBy('id', 'desc')->get();

        $pictureLinks = \DB::table('picture_settings')->orderBy('id', 'desc')->get();
        return view('home', compact('features', 'packages', 'settings', 'faqs', 'guidances', 'languages', 'schoolSettings', 'featureSections', 'about_us_lists', 'counter', 'trail_package', 'socialMedia', 'videoLinks', 'pictureLinks'));
    }

    public function school_website()
    {
        $school = $this->getSchoolIdFromDomain();
        
        $schoolId = $school->id;
        $sliders = Slider::where('school_id', $school->id)->whereIn('type', [2, 3])->get();
        if (!count($sliders)) {
            $sliders = [
                url('assets/school/images/heroImg1.jpg'),
                url('assets/school/images/heroImg2.jpg'),
            ];
        }
        $faqs = Faq::where('school_id', $school->id)->get();

        $students = Students::where('school_id', $school->id)->whereHas('user', function ($q) {
            $q->where('status', 1);
        })->count();

        $classes = ClassSchool::where('school_id', $school->id)->count();
        $streams = Subject::where('school_id', $school->id)->count();

        $counters = [
            'students' => $students,
            'classes' => $classes,
            'streams' => $streams,
        ];

        $announcements = Announcement::where('school_id', $school->id)->whereHas('announcement_class', function ($q) {
            $q->where('class_subject_id', null);
        })->with('announcement_class.class_section.class.stream', 'announcement_class.class_section.section', 'announcement_class.class_section.medium')->orderBy('id', 'DESC')->take(10)->get();

        $class_groups = ClassGroup::where('school_id', $school->id)->get();
        $languages = Language::get();
 
        return view('school-website.index', compact('sliders', 'faqs','languages','counters', 'announcements', 'class_groups', 'schoolId'));
    }

    public function contact(Request $request)
    {
        try {
            $admin_email = app(CachingService::class)->getSystemSettings('mail_username');
            $data = [
                'name'        => $request->name,
                'email'       => $request->email,
                'description' => $request->message,
                'admin_email' => '<EMAIL>'//$admin_email
            ];

             if (env('RECAPTCHA_SECRET_KEY') ?? '') {
                $validator = Validator::make($request->all(), [
                    'g-recaptcha-response' => 'required',
                ]);
    
                if ($validator->fails()) {
                    ResponseService::errorResponse($validator->errors()->first());
                }
    
                $googleCaptcha = app(GeneralFunctionService::class)->reCaptcha($request);
    
                if (!$googleCaptcha) {
                    ResponseService::errorResponse(trans('reCAPTCHA verification failed. Please try again.'));
                }
            }

            Mail::send('contact', $data, static function ($message) use ($data) {
                $message->to($data['admin_email'])->subject('Get In Touch');
            });

            return redirect()->to('/#contact')->with('success', "Message send successfully");
        } catch (Throwable $e) {
            return redirect()->to('/#contact')->with('error', "Apologies for the Inconvenience: Please Try Again Later");
        }
    }

    public function privacy(Request $request, CachingService $cache)
    {
        $test = '';
        $privacyPolicy = htmlspecialchars_decode($cache->getSystemSettings('privacy_policy'));
        $settings = app(CachingService::class)->getSystemSettings();
        return view('privacy-policy', compact('test', 'settings'), [
            'privacyPolicy' => $privacyPolicy,
        ]);
    }

    public function terms(Request $request, CachingService $cache)
    {
        $test = '';
        $termsCondition = htmlspecialchars_decode($cache->getSystemSettings('terms_condition'));
        $settings = app(CachingService::class)->getSystemSettings();
        return view('terms-conditions', compact('test', 'settings'), [
            'termsCondition' => $termsCondition,
        ]);
    }

    public function cron_job()
    {
        Artisan::call('schedule:run');
    }

    public function relatedDataIndex($table, $id)
    {
        $databaseName = config('database.connections.mysql.database');

        //Fetch all the tables in which current table's id used as foreign key
        $relatedTables = DB::select("SELECT TABLE_NAME,COLUMN_NAME
            FROM information_schema.KEY_COLUMN_USAGE
            WHERE REFERENCED_TABLE_NAME = ? AND TABLE_SCHEMA = ?", [$table, $databaseName]);
        $data = [];
        foreach ($relatedTables as $relatedTable) {
            $q = DB::table($relatedTable->TABLE_NAME)->where($relatedTable->TABLE_NAME . "." . $relatedTable->COLUMN_NAME, $id);
            $data[$relatedTable->TABLE_NAME] = $this->buildRelatedJoinStatement($q, $relatedTable->TABLE_NAME)->get()->toArray();
        }

        $currentDataQuery = DB::table($table);

        $currentData = $this->buildRelatedJoinStatement($currentDataQuery, $table)->first();
        return view('related-data.index', compact('data', 'currentData', 'table'));
    }

    public function packageCronJob()
    {
        $packageStatus = DB::table('purchase_package')->get();
        foreach ($packageStatus as $row) {
            $pp_id = $row->id;

            $remaining_days = DB::table('purchase_package as pp')
                ->join('subject_package as sp', 'pp.package_id', '=', 'sp.id')
                ->select(
                    'pp.student_id',
                    'sp.name',
                    'sp.expiry_days',
                    'pp.date as purchase_date'
                )
                ->where('pp.id', $pp_id)
                ->first();

            if ($remaining_days) {
                $purchaseDate = Carbon::parse($remaining_days->purchase_date);
                $expiryDays = $remaining_days->expiry_days;

                $expirationDate = $purchaseDate->addDays($expiryDays);
                $formattedExpirationDate = $expirationDate->format('Y-m-d');

                $remainingDays = $expirationDate->diffInDays(Carbon::now());

                if (Carbon::now() >= $expirationDate) {
                    $expirationDate->diffInDays(Carbon::now(), false);

                    DB::table('purchase_package')
                        ->where('id', $pp_id)
                        ->update(['status' => 1]);
                } else if ($remainingDays <= 30) {
                    DB::table('purchase_package')
                        ->where('id', $pp_id)
                        ->update(['status' => 2]);
                } else {
                    DB::table('purchase_package')
                        ->where('id', $pp_id)
                        ->update(['status' => 0]);
                }
            }
        }
    }

    public function recurringCronJob()
    {
        try {
            $studentFees = StudentFee::whereNotNull('recurring_invoice')->where('status', 'published')->get();
            foreach ($studentFees as $fee) {
                if (isset($fee->total_cycles) && $fee->current_cycle < $fee->total_cycles) {
                    $recurringDate = $this->calculateNextRecurringDate($fee->created_at, $fee->current_cycle, $fee->recurring_invoice);
                    $earlyDate = $this->calculateEarlyDate($fee->early_date, $fee->current_cycle, $fee->recurring_invoice);
                    $dueDate = $this->calculateDueDate($fee->due_date, $fee->current_cycle, $fee->recurring_invoice);
    
                    if (Carbon::parse($recurringDate)->lte(Carbon::now())) {
                        
                        $fee->current_cycle += 1;
                        $currentCycle = $fee->current_cycle;
                        $fee->save();
                        $newFee = StudentFee::create([
                            'name'               => 'Auto-generated',
                            'due_date'           => $dueDate,
                            'due_charges'        => $fee->due_charges ?? 0,
                            'due_charges_amount' => $fee->due_charges_amount ?? 0,
                            'early_date'         => $earlyDate ? Carbon::parse($earlyDate)->format('Y-m-d') : null,
                            'early_offer'        => $fee->early_offer ?? 0,
                            'early_offer_amount' => $fee->early_offer_amount ?? 0,
                            'class_id'           => $fee->class_id,
                            'school_id'          => $fee->school_id,
                            'session_year_id'    => $fee->session_year_id,
                            'student_id'         => $fee->student_id,
                            'status'             => 'published',
                            'uid'                => $this->generateNextUid($fee->school_id),
                            'current_cycle'      => $currentCycle,
                            'total_cycles'        => $fee->total_cycles,
                            'recurring_reference' => $fee->id,
                            'created_at'         => now(),
                            'created_at_draft'   => now(),
                            'updated_at'         => now(),
                        ]);
    
                        $feeTypes = DB::table('student_fees_details')->where('student_fees_id',$fee->id)->whereNotIn('fees_type_name', ['Overdue Fees', 'Early Discount'])->get();
                        // $this->studentFeeType->builder()->where("student_id", $fee->student_id)->get();
    
                        $feeDetails = [];
                        foreach ($feeTypes as $feeType) {
                            $feeDetails[] = [
                                "student_fees_id"   => $newFee->id,
                                "fees_type_name"    => $feeType->fees_type_name,
                                "fees_type_amount"  => $feeType->fees_type_amount,
                                "classification_code" => $feeType->classification_code,
                                "quantity"          => $feeType->quantity ?? 1,
                                "unit"              => $feeType->unit,
                                "discount"          => $feeType->discount ?? null,
                                "tax"               => $feeType->tax ?? null,
                                "optional"          => 0,
                                "school_id"         => $fee->school_id,
                                "created_at"        => now(),   
                                "updated_at"        => now(),
                            ];
                        }
                        if (count($feeDetails) > 0) {
                            // Log::info('Fee Details: ' . json_encode($feeDetails));
                            StudentFeesDetail::insert($feeDetails);
                            // $this->studentFeesDetail->upsert($feeDetails, ['student_fees_id','school_id'], ['fees_type_name', 'fees_type_amount', 'classification_code', 'unit', 'quantity', 'optional']);
                        }
                    }
                }
            }
        } catch (\Exception $e) {
            Log::error('Error in overdue cron job: ' . $e->getMessage(), [
                'exception' => $e,
                'stack_trace' => $e->getTraceAsString(),
            ]);
        }
    }
    
    public function addMonth($date, $count) {
        $start = new DateTime($date);
        $checkDate = new DateTime($date);
        $checkDate->modify('last day of +'.$count.' month');
        if ($start->format('Y-m-d') === $start->format('Y-m-t')) {
            $start->modify('last day of +'.$count.' month');
        } else {
            if (($start->format('d') > $checkDate->format('d'))) {
                $start = $checkDate;
            } else {
                $start->modify('+'.$count.' month');
            }   
        }
        return $start;
    }

    private function calculateNextRecurringDate($created_at, $current_cycle, $recurring_invoice)
    {
        $createdAt = Carbon::parse($created_at);

        $nextRecurringDate = $this->addMonth($createdAt,($current_cycle + 1) * $recurring_invoice);
        return $nextRecurringDate->format('Y-m-d');
    }
    private function calculateDueDate($due_date, $current_cycle, $recurring_invoice)
    {
        $createdAt = Carbon::parse($due_date);
        $nextRecurringDate = $this->addMonth($createdAt,($current_cycle + 1) * $recurring_invoice);

        return $nextRecurringDate->format('Y-m-d');
    }
    private function calculateEarlyDate($early_date, $current_cycle, $recurring_invoice)
    {
        if (!$early_date) {
            return null;
        }
        $createdAt = Carbon::parse($early_date);

        $nextRecurringDate = $this->addMonth($createdAt,($current_cycle + 1) * $recurring_invoice);

        return $nextRecurringDate->format('Y-m-d');
    }

    public function generateNextUid($schoolId)
    {
        $latestUID = DB::table('student_fees')->where('school_id', $schoolId)->where('status', 'published')->whereNull('deleted_at')->select('uid')->orderBy('uid', 'desc')->value('uid');
        $uid = $latestUID ? $latestUID + 1 : 1;

        while (DB::table('student_fees')
            ->where('uid', $uid)
            ->where('school_id', Auth::user()->school_id)
            ->where('status', 'published')
            ->exists()
        ) {
            $uid++;
        } 

        return $uid;
    }

    private function buildSelectStatement($query, $table)
    {
        $select = [
            "classes"        => "classes.*,CONCAT(classes.name,'(',mediums.name,')') as name,streams.name as stream_name,shifts.name as shift_name",
            "class_sections" => "class_sections.*,CONCAT(classes.name,' ',sections.name,'(',mediums.name,')') as class_section",
            "users"          => "users.first_name,users.last_name",
            //            "student_subjects" => "student_subjects.*,CONCAT(users.first_name,' ',users.last_name) as student,"
        ];
        return $query->select(DB::raw($select[$table] ?? "*," . $table . ".id as id"));
    }


    private function buildRelatedJoinStatement($query, $table)
    {
        $databaseName = config('database.connections.mysql.database');
        // If all the child tables further have foreign keys than fetch that table also
        $getTableSchema = DB::select("SELECT CONSTRAINT_NAME, COLUMN_NAME, REFERENCED_TABLE_NAME, REFERENCED_COLUMN_NAME
            FROM information_schema.KEY_COLUMN_USAGE
            WHERE TABLE_NAME = ? AND TABLE_SCHEMA = ? AND REFERENCED_TABLE_NAME IS NOT NULL", [$table, $databaseName]);

        $tableAlias = [];
        //Build Join query for all the foreign key using the Table Schema
        foreach ($getTableSchema as $foreignKey) {
            //            , 'edited_by', 'created_by', 'guardian_id'
            if ($foreignKey->REFERENCED_TABLE_NAME == $table) {
                //If Related table has foreign key of the same table then no need to add that in join to reduce the query load
                continue;
            }

            // Sometimes there will be same table is used in multiple foreign key at that time alias of the table should be different
            if (in_array($foreignKey->REFERENCED_TABLE_NAME, $tableAlias)) {
                $count = array_count_values($tableAlias)[$foreignKey->REFERENCED_TABLE_NAME] + 1;
                $currentAlias = $foreignKey->REFERENCED_TABLE_NAME . $count;
            } else {
                $currentAlias = $foreignKey->REFERENCED_TABLE_NAME;
            }
            $tableAlias[] = $foreignKey->REFERENCED_TABLE_NAME;

            if (!in_array($foreignKey->COLUMN_NAME, ['school_id', 'session_year_id'])) {
                $query->leftJoin($foreignKey->REFERENCED_TABLE_NAME . " as " . $currentAlias, $foreignKey->REFERENCED_TABLE_NAME . "." . $foreignKey->REFERENCED_COLUMN_NAME, '=', $table . "." . $foreignKey->COLUMN_NAME);
            }
        }

        return $this->buildSelectStatement($query, $table);
    }

    public function relatedDataDestroy($table, $id)
    {
        try {
            DB::table($table)->where('id', $id)->delete();
            ResponseService::successResponse("Data Deleted Permanently");
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e, "Controller -> relatedDataDestroy Method", 'cannot_delete_because_data_is_associated_with_other_data');
            ResponseService::errorResponse();
        }
    }

    public function admissionFormPublic($schoolCode, Request $request)
    {
        try {
            if ($schoolCode === "domain") {
                $domain = $request->getHost();
                $domainParts = explode('.', $domain);
                $school = DB::select('SELECT * FROM schools WHERE domain = ?', [$domainParts[0]]);
            } else {
                $school = DB::select('SELECT * FROM schools WHERE school_code = ' . $schoolCode);

                if (!$school) {
                    return response()->json([
                        'error' => true,
                        'message' => 'School not found',
                    ], 404);
                }
            }
            $schoolId = $school[0]->id ?? '';

            $classSubjectSections = DB::select(
                "SELECT
                    class_sections.*,
                    classes.name AS class_name,
                    streams.name AS stream_name,
                    sections.name AS section_name,
                    mediums.name AS medium_name
                FROM
                    class_sections
                LEFT JOIN
                    classes ON class_sections.class_id = classes.id
                LEFT JOIN
                    streams ON classes.stream_id = streams.id
                LEFT JOIN
                    sections ON class_sections.section_id = sections.id
                LEFT JOIN
                    mediums ON class_sections.medium_id = mediums.id
                WHERE
                    class_sections.school_id = ?
                    AND class_sections.deleted_at IS NULL",
                [$schoolId]
            );

            $sessionYears = DB::select('SELECT * FROM session_years WHERE school_id=' . $schoolId);

            $extraFields = DB::select("SELECT * FROM form_fields WHERE school_id = ? AND deleted_at IS NULL ORDER BY `rank`", [$schoolId]);

            $defaultSession = DB::select('SELECT * FROM session_years WHERE `school_id`= ? AND `default` = 1 LIMIT 1', [$schoolId]);
            $sessionName = $defaultSession[0]->name;
            $studentList = DB::select('SELECT COUNT(id) AS total FROM students WHERE school_id = ' . $schoolId);
            $totalStudent = $studentList[0]->total + 1;
            $admission_no = $sessionName.$schoolId.str_pad(($totalStudent), 4, '0', STR_PAD_LEFT);
            while(true){
                $studentList = DB::select('SELECT id FROM users WHERE email = ?',[$admission_no]);
                if(COUNT($studentList)){
                    $totalStudent = $totalStudent + 1;
                    $admission_no = $sessionName . $schoolId . str_pad(($totalStudent + 1), 4, '0', STR_PAD_LEFT);
                } else {
                    break;
                }
            }

            return response()->json([
                'error' => false,
                'school_id' => $schoolId,
                'class_sections' => $classSubjectSections,
                'session_years' => $sessionYears,
                'extra_fields' => $extraFields,
                'admission_no' => $admission_no,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => true,
                'message' => 'Error occurred while fetching data',
            ], 500);
        }
    }

    public function getTermsCondition($schoolId, Request $request)
    {
        $termsCondition = SchoolSetting::where('school_id', $schoolId)
            ->where('name', 'terms_condition')
            ->first();

        if ($termsCondition) {
            return response()->json([
                'terms_condition' => $termsCondition->data
            ]);
        } else {
            return response()->json([
                'terms_condition' => null,
                'message' => 'No terms and conditions available for this school.'
            ]);
        }
    }

    public function admission(Request $request)
    {
        $school_id = $request->school_id;
        // Validate the TIN format
        if (isset($request->tax_identification_number)) {
            if (strlen($request->tax_identification_number) < 11 || strlen($request->tax_identification_number) > 13) {
                session()->flash('errorAlert', "Invalid TIN format. Individual TIN: IGXXXXXXXXX (11 to 13 characters).");
                return redirect()->back();
            }
        }

        // Validate the email format
        if (isset($request->guardian_email)) {
            if (!filter_var($request->guardian_email, FILTER_VALIDATE_EMAIL)) {
                session()->flash('errorAlert', "Invalid email format.");
                return redirect()->back();
            }
        }

        try {
            DB::beginTransaction();
            // Check free trial package
            $today_date = Carbon::now()->format('Y-m-d');

            $subscription = DB::select("
                SELECT sb.*, s.*
                FROM subscription_bills as sb
                JOIN subscriptions as s ON sb.subscription_id = s.id
                WHERE s.school_id = ?
                AND DATE(s.start_date) <= ?
                AND DATE(s.end_date) >= ?
                AND EXISTS (
                    SELECT 1
                    FROM packages
                    WHERE packages.id = s.package_id
                        AND packages.is_trial = 1
                )
            ", [$school_id, $today_date, $today_date]);

            // If free trail package
            if ($subscription) {
                $systemSettings = $this->cache->getSystemSettings();
                $student = DB::select("SELECT COUNT(*) as student_count FROM users WHERE school_id =" . $school_id);
                $studentCount = $student[0]->student_count;
                if ($studentCount >= $systemSettings['student_limit']) {
                    $message = "The free trial allows only " . $systemSettings['student_limit'] . " students.";
                    session()->flash('errorAlert', $message . $subscription);
                    return redirect()->back();
                }
            } else {
                // Regular package? Check Postpaid or Prepaid
                $subscription = $this->subscriptionService->active_subscription($school_id);
                // If prepaid plan check student limit
                if ($subscription && $subscription->package_type == 0) {
                    $status = $this->subscriptionService->check_user_limit($subscription, "Students");

                    if (!$status) {
                        session()->flash('errorAlert', 'You reach out limits');
                        return redirect()->back();
                    }
                }
            }

            // Get the user details from the guardian details & identify whether that user is guardian or not. if not the guardian and has some other role then show appropriate message in response
            $guardianUser = $this->user->builder()->whereHas('roles', function ($q) {
                $q->where('name', '!=', 'Guardian');
            })->where('email', $request->guardian_email)->first();

            if ($guardianUser) {
                session()->flash('errorAlert', 'The guardian email is already taken for another role.');
                return redirect()->back();
            }


            //Create Guardian User
            $guardianPassword = $request->guardian_mobile;
            $guardian_image = null;
            if ($request->hasFile('guardian_image')) {
                $guardian_image = $request->file('guardian_image');
            }

            $parent = array(
                'first_name' => $request->guardian_first_name,
                'last_name'  => $request->guardian_last_name,
                'mobile'     => $request->guardian_mobile,
                'gender'     => $request->guardian_gender,
                'school_id'  => $school_id,
                'updated_at' => now()
            );

            //NOTE : This line will return the old values if the user is already exists
            $user = DB::table('users')->where('email', $request->guardian_email)->first();
            if (!empty($guardian_image)) {
                $parent['image'] = UploadService::upload($guardian_image, 'guardian');
            }
            if (!empty($user)) {
                if (isset($parent['image'])) {
                    // if (Storage::disk('public')->exists($user->getRawOriginal('image'))) {
                    //     Storage::disk('public')->delete($user->getRawOriginal('image'));
                    // }
                }
                DB::table('users')->where('id', $user->id)->update($parent);
                $userId = $user->id;
            } else {
                $parent['password'] = Hash::make($guardianPassword);
                $parent['email'] = $request->guardian_email;
                $parent['deleted_at'] = now();
                $user = User::create($parent);
                $userId = $user->id;
                // $userId = DB::table('users')->insertGetId($parent);
                $guardianRoleId = DB::table('roles')->where('name', 'Guardian')->where('school_id', $school_id)->value('id');
                // DB::table('model_has_roles')->insert(['role_id' => $guardianRoleId, 'model_type' => 'App\\Models\\User', 'model_id' => $userId]);
                DB::table('model_has_roles')->insert(['role_id' => 3, 'model_type' => 'App\\Models\\User', 'model_id' => $userId]);
            }
            if ($userId) {
                $guardian_id = $userId;
            }


            //Regenrate Admission no as student count might change
            $defaultSession = DB::select('SELECT * FROM session_years WHERE `school_id`= ? AND `default` = 1 LIMIT 1', [$school_id]);
            $sessionName = $defaultSession[0];
            $studentList = DB::select('SELECT COUNT(id) AS total FROM students WHERE school_id = ' . $school_id);
            $totalStudent = $studentList[0]->total + 1;
            $admission_no = $sessionName->name.$school_id.str_pad(($totalStudent), 4, '0', STR_PAD_LEFT);
            while(true){
                $studentList = DB::select('SELECT id FROM users WHERE email = ?',[$admission_no]);
                if(COUNT($studentList)){
                    $totalStudent = $totalStudent + 1;
                    $admission_no = $sessionName->name . $school_id . str_pad(($totalStudent + 1), 4, '0', STR_PAD_LEFT);
                } else {
                    break;
                }
            }
            if ($request->hasFile('image')) {
                $student_image = $request->file('image');
                if (!empty($student_image)) {
                    $student_image_url = UploadService::upload($student_image, 'user');
                }
            }
            //Create Student User
            $status = 0;
            $studentPassword = str_replace('-', '', date('d-m-Y', strtotime($request->dob)));
            $user = [
                'first_name'        => $request->first_name,
                'last_name'         => $request->last_name,
                'email'             => $admission_no,
                'mobile'            => $request->mobile,
                'dob'               => date('Y-m-d', strtotime($request->dob)),
                'gender'            => $request->gender,
                'password'          => Hash::make($studentPassword),
                'school_id'         => $school_id,
                'image'             => $student_image_url ?? null,
                'status'            => $status,
                'current_address'   => $request->current_address,
                'permanent_address' => $request->permanent_address,
                'created_at'        => now(),
                'updated_at'        => now(),
                'deleted_at'        => $status == 1 ? null : '1970-01-01 01:00:00'
            ];
            $studentUserId = DB::table('users')->insertGetId($user);

            $studentRoleId = DB::table('roles')->where('name', 'Student')->where('school_id', $school_id)->value('id');
            // DB::table('model_has_roles')->insert(['role_id' => $studentRoleId, 'model_type' => 'App\\Models\\User', 'model_id' => $studentUserId]);
            DB::table('model_has_roles')->insert(['role_id' => 4, 'model_type' => 'App\\Models\\User', 'model_id' => $studentUserId]);

            $roll_number_db = DB::table('students')->where('class_section_id', $request->class_section_id)->max('roll_number');
            $roll_number = $roll_number_db + 1;

            DB::table('students')->insert([
                'user_id' => $studentUserId,
                'class_section_id' => $request->class_section_id,
                'ic_no_2' => $request->ic_no_2,
                'admission_no' => $admission_no,
                'roll_number' => $roll_number,
                'admission_date' => date('Y-m-d', strtotime($request->admission_date)),
                'guardian_id' => $guardian_id,
                'session_year_id' => $defaultSession[0]->id,
                'school_id' => $school_id,
                'created_at' => now(),
                'updated_at' => now()
            ]);

            // Store Extra Details
            $extraDetails = array();
            $extraFields = $request->extra_fields ?? [];

            foreach ($extraFields as $index => $fields) {

                $data = null;
                if (isset($fields['data']) && $fields['data'] instanceof UploadedFile) {
                    $data = UploadService::upload($fields['data'], 'extra-data');
                } else if (isset($fields['data'])) {
                    $data = (is_array($fields['data']) ? json_encode($fields['data'], JSON_THROW_ON_ERROR) : $fields['data']);
                }
                $extraDetails[] = array(
                    'student_id'    => $studentUserId,
                    'form_field_id' => $fields['form_field_id'],
                    'data'          => $data,
                    'school_id'     => $school_id,
                    'created_at'    => now(),
                    'updated_at'    => now()
                );
            }
            if (!empty($extraDetails)) {
                DB::table('extra_student_datas')->insert($extraDetails);
            }
            $e_invoice_data = [
                'name'                => $request->name,
                'ic_no'               => $request->ic_no,
                'tax_identification_number' => preg_replace('/\D/', '', $request->tax_identification_number),
                'email'               => $request->email,
                'address'             => $request->address,
                'city'                => $request->city,
                'postal_code'         => $request->postal_code,
                'country'             => $request->country,
                'state'               => $request->state,
                'guardian_id'         => $guardian_id,
                'school_id'           => $school_id,
                'contact_no'          => $request->contact_no ?? '',
            ];
            $e_invoice = DB::table('e_invoice_guardian')->where('guardian_id', $guardian_id)->where('school_id',$school_id)->first();
            if(!$e_invoice){
                DB::table('e_invoice_guardian')->insert($e_invoice_data);
            }

            //Admission Notification
            $data = [
                'user_id'   => $studentUserId,
                'school_id' => $school_id,
                'date'      => now(),
                'type'      => 1
            ];
            AdmissionNotification::create($data);
            DB::commit();

            // $guardian = DB::table('users')
            // ->select('id', 'email', DB::raw("CONCAT(first_name, ' ', last_name) as full_name"),'mobile')
            // ->where('id', $guardian_id)
            // ->first();
            // $child = DB::table('users')
            //     ->select('id','email',DB::raw("CONCAT(first_name, ' ', last_name) as full_name"))
            //     ->where('id', $studentUserId)
            //     ->first();
            // $childPassword =str_replace('-', '', date('d-m-Y', strtotime($request->dob)));               
            // $email_body = $this->replacePlaceholders($guardian,$child,$admission_no,$childPassword,$school_id);
            // $school_name = DB::table('schools')->where('id',$school_id)->value('name');

            // $data = [
            //     'subject'                => 'Welcome to ' . $school_name,
            //     'email'                  => $guardian->email,
            //     'email_body'             => $email_body
            // ];

            // Mail::send('students.email', $data, static function ($message) use ($data) {
            //     $message->to($data['email'])->subject($data['subject']);
            // });

            return redirect()->back()->with('admissionSuccess', 'Admission Form Successfully Submitted');
        } catch (Throwable $e) {
            // IF Exception is TypeError and message contains Mail keywords then email is not sent successfully
            // if ($e instanceof TypeError && Str::contains($e->getMessage(), [
            //     'Failed',
            //     'Mail',
            //     'Mailer',
            //     'MailManager'
            // ])) {
            //     DB::commit();
            //     ResponseService::warningResponse("Student Registered successfully. But Email not sent.");
            // } else {
            //     DB::rollBack();
            //     ResponseService::logErrorResponse($e, "Student Controller -> Store method");
            //     session()->flash('errorAlert','Error occurred while saving data');
            //     // ResponseService::errorResponse();
            // }
            
            session()->flash('errorAlert', "Error occurred while saving data: ".$e->getMessage());
            return redirect()->back();
        }
    }

    private function replacePlaceholders($guardian, $child, $childAdmissionNumber, $childPlainTextPassword, $schoolId)
    {
        $cache = app(CachingService::class);
        $schoolSettings = $cache->getSchoolSettings('*', $schoolId);
        $systemSettings = $cache->getSystemSettings();
        $templateContent = $schoolSettings['email-template-parent'] ?? '';
        // Define the placeholders and their replacements
        $placeholders = [
            '{parent_name}' => $guardian->full_name,
            '{email}' => $guardian->email,
            '{password}' => $guardian->mobile,
            '{school_name}' => $schoolSettings['school_name'],

            '{child_name}' => $child->full_name,
            '{grno}' => $child->email,
            '{child_password}' => $childPlainTextPassword,
            '{admission_no}' => $childAdmissionNumber,

            '{support_email}' => $schoolSettings['school_email'] ?? '',
            '{support_contact}' => $schoolSettings['school_phone'] ?? '',

            '{android_app}' => $systemSettings['app_link'] ?? '',
            '{ios_app}' => $systemSettings['ios_app_link'] ?? '',

            // Add more placeholders as needed
        ];

        // Replace the placeholders in the template content
        foreach ($placeholders as $placeholder => $replacement) {
            $templateContent = str_replace($placeholder, $replacement, $templateContent);
        }
        return $templateContent;
    }

    public function attendanceSchoolSub($schoolCode, Request $request)
    {
        try {
            if ($schoolCode === "domain") {
                $domain = $request->getHost();
                $domainParts = explode('.', $domain);
                $school = DB::table('schools')->where('domain', $domainParts[0])->first();
            } else {
                $school = DB::table('schools')->where('school_code', $schoolCode)->first();
            }

            if (!$school) {
                return response()->json([
                    'error' => true,
                    'message' => 'School not found',
                ], 404);
            }

            $subjects = DB::table('subjects')
                ->select('id', 'name')
                ->where('school_id', $school->id)
                ->get();


            $teachers = DB::table('staffs as s')
                ->join('users as u', 's.user_id', '=', 'u.id')
                ->where('u.school_id', $school->id)
                ->whereNull('u.deleted_at')
                ->select('u.id', DB::raw("CONCAT(u.first_name, ' ', u.last_name) AS full_name"))
                ->get();

            return response()->json([
                'error' => false,
                'options' => $subjects,
                'school_id' => $school->id,
                'teachers' => $teachers
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => true,
                'message' => 'Error occurred while fetching data',
            ], 500);
        }
    }

    public function attendanceTracker($school_id, $subject_id, $teacher_id, $role)
    {
        $test = '';
        $school = School::find($school_id);
        $teacher = null;
        $subject = null;
        if ($subject_id !== 'all') {
            $subject = DB::table('subjects')->where('id', $subject_id)->where('school_id', $school_id)->first();
        }
        if ($teacher_id !== 'all') {
            $teacher = DB::table('users')->where('id', $teacher_id)->where('school_id', $school_id)->first();
        }


        $settings = app(CachingService::class)->getSystemSettings();
        return view('attendance', compact('test', 'settings', 'school', 'subject', 'teacher', 'role'));
    }

    public function takeAttendance(Request $request)
    {
        $request->validate([
            'school_id'  => 'required',
            'subjects_id' => 'nullable',
            'teacher_id' => 'nullable',
            'rfid_id'    => 'required',
        ]);

        $role = $request->role;
        $rfidId = $request->rfid_id;
        $modePrefix = 'Mode:01-0001-1000';
        if (strpos($rfidId, $modePrefix) === 0) {
            $rfidId = substr($rfidId, strlen($modePrefix));
        }

        $rfidIsValid = DB::select('SELECT * FROM rfid_whitelist WHERE rfid_id = ? AND status = 1', [$rfidId]);

        if (!$rfidIsValid) {
            return redirect()->back()->with('error', 'Unauthorized RFID ID.');
        }

        $subjectId = $request->subjects_id;
        $teacherId = $request->teacher_id;
        $schoolId = $request->school_id;

        // Fetch user, school, subject, and teacher details
        $user = DB::table('users')->select('id', 'first_name', 'last_name')->where('rfid_id', $rfidId)->first();
        $school = DB::table('schools')->where('id', $schoolId)->first();
        $subject = DB::table('subjects')->select('name', 'commission')->where('id', $subjectId)->first();
        $teacher = DB::table('users')->select(DB::raw("CONCAT(first_name, ' ', last_name) AS full_name"))->where('id', $teacherId)->where('school_id', $schoolId)->first();

        if (!$user) {
            return redirect()->back()->with('error', 'RFID ID does not belong to a valid user.');
        }
        if ($role == 'teacher') {
            $isStaff = DB::table('staffs')->where('user_id', $user->id)->exists();
            if (!$isStaff) {
                return redirect()->back()->with('error', 'RFID ID does not belong to teacher');
            }
        }
        if ($role == 'student') {
            $isStaff = DB::table('students')->where('user_id', $user->id)->exists();
            if (!$isStaff) {
                return redirect()->back()->with('error', 'RFID ID does not belong to student');
            }
        }

        $schoolName = $school->name;
        $userName = $user->first_name . "" . $user->last_name;
        $subjectName = $subject->name ?? "";
        $teacherName = $teacher->full_name ?? "";


        // Retrieve commission type and amount
        $commissionType = null;
        $commissionAmount = null;
        $userGroupDetail = DB::table('user_group_details')->where('subject_id', $request->subjects_id)->where('teacher_id', $teacherId)->first();
        if ($userGroupDetail) {
            $groupId = $userGroupDetail->group_id;
            $userGroup = DB::table('user_groups')->where('id', $groupId)->first();
            if ($userGroup) {
                $commissionType = $userGroup->commission_type;
                $commissionAmount = $userGroup->commission_amount;
            }
        }
        $userID = DB::table('users')
            ->join('staffs', 'users.id', '=', 'staffs.user_id')
            ->where('users.rfid_id', $rfidId)
            ->select('staffs.user_id')
            ->first();

        $existingAttendance = DB::table('subject_attendances')
            ->where('user_id', $user->id)
            ->where('school_id', $schoolId)
            ->where('subjects_id', $subjectId)
            ->whereDate('date', now())
            ->orderByDesc('created_at')
            ->first();

        $existingAttendancee = DB::table('teacher_attendance')
            ->where('school_id', $schoolId)
            ->where('user_id', $user->id)
            ->where('subject_id', $subjectId)
            ->whereDate('date', now())
            ->orderByDesc('created_at')
            ->first();

        if ($role === 'teacher' && !empty($userID)) {
            if ($existingAttendancee && empty($existingAttendancee->clock_out) && !empty($existingAttendancee->clock_in)) {
                // Handle existing attendance logic (clock in/out)
                if ($existingAttendancee->clock_in && !$existingAttendancee->clock_out) {
                    try {
                        DB::beginTransaction();

                        $clockIn = Carbon::parse($existingAttendancee->clock_in);
                        $clockOut = now();
                        $diffInMinutes = $clockOut->diffInMinutes($clockIn);
                        if ($diffInMinutes < 15) {
                            return redirect()->back()->with('error', 'You can only clock out after 15 minutes.');
                        }

                        $interval = $clockOut->diff($clockIn);
                        $totalTime = $interval->format('%H:%I:%S');

                        // Update the attendance record
                        DB::table('teacher_attendance')
                            ->where('id', $existingAttendancee->id)
                            ->update([
                                'clock_out' => $clockOut,
                                'total_time' => $totalTime,
                                'updated_at' => now(),
                            ]);

                        DB::commit();

                        // Prepare notification data
                        // $notifyUser = [$teacherId]; // Assuming the teacher is to be notified
                        $checkedOutTime = now()->format('h:iA');
                        $todayDate = now()->format('d/m/Y');
                        $title = 'Clocked Out';
                        $body = "{$schoolName}\n{$userName} clocked out at {$checkedOutTime}, {$todayDate}.";

                        send_notification($notifyUser, $title, $body, 'Attendance'); // Send Notification

                        return redirect()->back()->with('success', "{$user->first_name} clocked out at " . now()->format('h:iA'));
                    } catch (\Throwable $e) {
                        DB::rollback();
                        return redirect()->back()->with('error', 'Failed to update attendance: ' . $e->getMessage());
                    }
                } elseif ($existingAttendancee->status == 1) {
                    return redirect()->back()->with('error', 'Attendance has already been taken');
                } else {
                    return redirect()->back()->with('error', 'Unexpected attendance state. Please contact support.');
                }
            } elseif (isset($existingAttendancee->clock_in) && isset($existingAttendancee->clock_out)) {
                return redirect()->back()->with('error', 'Attendance already recorded with both in and out times today.');
            } else {
                // Record new attendance
                try {
                    DB::beginTransaction();

                    $attendanceDataa = [
                        'school_id' => $schoolId,
                        'user_id' => $user->id, // Use teacher ID for attendance
                        'status' => 1,
                        'subject_id' => $subjectId,
                        'date' => now(),
                        'clock_in' => now(),
                        // Add any additional fields needed
                    ];

                    $id = DB::table('teacher_attendance')->insertGetId($attendanceDataa);

                    DB::commit();

                    return redirect()->back()->with('success', 'Attendance recorded successfully.');
                } catch (\Throwable $e) {
                    DB::rollback();
                    return redirect()->back()->with('error', 'Failed to record attendance: ' . $e->getMessage());
                }
            }
        } else {
            if ($existingAttendance && empty($existingAttendance->clock_out) && !empty($existingAttendance->clock_in)) {
                // Handle existing attendance logic (clock in/out)
                if ($existingAttendance->clock_in && !$existingAttendance->clock_out) {
                    try {
                        DB::beginTransaction();

                        $clockIn = Carbon::parse($existingAttendance->clock_in);
                        $clockOut = now();
                        $diffInMinutes = $clockOut->diffInMinutes($clockIn);
                        if ($diffInMinutes < 15) {
                            return redirect()->back()->with('error', 'You can only clock out after 15 minutes.');
                        }

                        $interval = $clockOut->diff($clockIn);
                        $totalTime = $interval->format('%H:%I:%S');

                        // Update the attendance record
                        DB::table('subject_attendances')
                            ->where('id', $existingAttendance->id)
                            ->update([
                                'clock_out' => $clockOut,
                                'total_time' => $totalTime,
                                'updated_at' => now(),
                            ]);

                        DB::commit();

                        // Check if user is a student
                        $checkUser = DB::select('SELECT * FROM students s, users u WHERE s.user_id = u.id AND u.id = ?', [$user->id]);
                        if (count($checkUser) > 0) {
                            $allUser = DB::select('SELECT s.guardian_id, sa.user_id FROM subject_attendances sa 
                                                        JOIN users u ON sa.user_id = u.id 
                                                        JOIN students s ON u.id = s.user_id 
                                                        WHERE sa.user_id = ? AND sa.id = ?', [$user->id, $existingAttendance->id]);
                        } else {
                            $allUser = DB::select('SELECT u.id FROM subject_attendances sa, users u 
                                                        WHERE sa.user_id = u.id AND sa.user_id = ? AND sa.id = ?', [$user->id, $existingAttendance->id]);
                        }

                        $notifyUser = [];
                        foreach ($allUser as $data) {
                            if (isset($data->user_id)) {
                                $notifyUser[] = $data->user_id; // student
                            }
                            if (isset($data->id)) {
                                $notifyUser[] = $data->id; // user
                            }
                            if (isset($data->guardian_id)) {
                                $notifyUser[] = $data->guardian_id; // guardian
                            }
                        }

                        $checkedOutTime = now()->format('h:iA');
                        $todayDate = now()->format('d/m/Y');
                        if ($notifyUser) {
                            $title = 'Clocked Out';
                            $type = 'Attendance'; // Get The Type for Notification
                            $body = "{$schoolName}\n{$userName} clocked out {$subjectName} at {$checkedOutTime}, {$todayDate}."; // Get The Body for Notification
                            send_notification($notifyUser, $title, $body, $type); // Send Notification
                        }

                        $alertMessage = "{$user->first_name} clocked out at " . now()->format('h:iA');
                        return redirect()->back()->with('success', $alertMessage);
                    } catch (\Throwable $e) {
                        DB::rollback();
                        return redirect()->back()->with('error', 'Failed to update attendance: ' . $e->getMessage());
                    }
                } elseif ($existingAttendance->status == 1) {
                    return redirect()->back()->with('error', 'Attendance has already been taken');
                } else {
                    return redirect()->back()->with('error', 'Unexpected attendance state. Please contact support.');
                }
            } elseif (isset($existingAttendance->clock_in) && isset($existingAttendance->clock_out) && $subjectId == null) {
                return redirect()->back()->with('error', 'Attendance already recorded with both in and out times today.');
            } else {
                // Record new attendance
                try {
                    DB::beginTransaction();
                    $attendanceData = [
                        'school_id' => $schoolId,
                        'teacher_id' => $teacherId,
                        'subjects_id' => $subjectId,
                        'user_id' => $user->id,
                        'status' => 1,
                        'date' => now(),
                        'clock_in' => now(),
                        'fees_per_section' => $subject->commission ?? null,      // For section fees
                        'fees_per_month' => $subject->commission_month ?? null,  // For monthly fees
                        'commission_typee' => $commissionType,
                        'commission_amountt' => $commissionAmount ?? 0, // Default to 0 if not found
                    ];

                    $id = DB::table('subject_attendances')->insertGetId($attendanceData);

                    if (empty($subjectId)) {
                        $student = DB::select('SELECT * FROM students JOIN users ON users.id = students.user_id WHERE users.id = ?', [$user->id]);
                        $defaultSession = DB::select('SELECT * FROM session_years WHERE `school_id` = ? AND `default` = 1 LIMIT 1', [$schoolId]);
                        if ($student) {
                            $oldAttendanceTable = [
                                'class_section_id' => $student[0]->class_section_id,
                                'student_id' => $user->id,
                                'session_year_id' => $defaultSession[0]->id,
                                'type' => 1,
                                'date' => now(),
                                'school_id' => $schoolId,
                                'subject_attendance_id' => $id,
                                'created_at' => now(),
                                'updated_at' => now(),
                            ];
                            DB::table('attendances')->insert($oldAttendanceTable);
                        }
                    } else {
                        $subject = DB::table('subjects')
                        ->where('id',$subject_id)
                        ->first();

                    }
                    // } catch (\Throwable $e) {
                    //     DB::rollback();
                    //     return redirect()->back()->with('error', 'Failed to record attendance: ' . $e->getMessage());
                    // }



                    $deductionMethod = DB::table('school_settings')
                        ->select('name', 'data')
                        ->where('name', 'deduction_method')
                        ->where('school_id', $schoolId)
                        ->first();

                    $userId = DB::table('students as s')
                        ->join('users as u', 'u.id', '=', 's.user_id')
                        ->select('s.id', DB::raw("CONCAT(u.first_name, ' ', u.last_name) AS full_name"))
                        ->where('u.id', $user->id)
                        ->first();

                    if ($deductionMethod && $userId) {
                        $creditStatus = DB::table('students')
                            ->where('user_id', $user->id)
                            ->value('credit_status');

                        if ($creditStatus != 1) {
                        if ($deductionMethod->data === "1") {
                            if ($attendanceData) {

                                $query = DB::table('subject_attendances as sa')
                                    ->join('users as u', 'sa.user_id', '=', 'u.id')
                                    ->join('credit_system as cs', 'sa.user_id', '=', 'cs.user_id')
                                    ->join('students as s', 's.user_id', '=', 'sa.user_id')
                                    ->join('class_sections as cls','cls.id','=','s.class_section_id')
                                    ->join('classes as c','c.id','=','cls.class_id')
                                    ->join('subjects as sub', 'sa.subjects_id', '=', 'sub.id',)
                                    ->select('cs.user_id as credit_user', DB::raw("CONCAT(u.first_name, ' ', u.last_name) AS full_name"), 'c.id as class_id', 'cs.balance', 'cs.credit_amount', 'sub.commission')
                                    ->where('sa.date', date('Y-m-d', strtotime($request->date)))
                                    ->where('sa.subjects_id', $subjectId)
                                    ->where('sa.status', 1)
                                    ->where('cs.school_id', Auth::user()->school_id)
                                    ->where('cs.user_id', $user->id)
                                    ->first();

                                $subjectPrice = -$query->commission;

                                $totalBalance = DB::table('credit_system')
                                    ->where('user_id', $query->credit_user)
                                    ->sum('credit_amount');

                                $balance = $totalBalance + $subjectPrice;


                                DB::table('credit_system')->insert([
                                    'school_id' => $schoolId,
                                    'class_id' => $query->class_id,
                                    'user_id' => $query->credit_user,
                                    'credit_amount' => $subjectPrice,
                                    'balance' => $balance,
                                    'detail' => empty($subjectId)?'Attended 1 Class':'Attended 1 Class' .'('.$subject->name. ')',
                                    'created_at' => Carbon::now(),
                                    'updated_at' => Carbon::now()
                                ]);

                                if ($balance <= 0) {
                                    $studentsInsufficientFunds[] = $query->full_name;
                                }
                            }
                        } else if ($deductionMethod->data === "2") {

                            $userId = DB::table('students as s')
                                ->join('users as u', 'u.id', '=', 's.user_id')
                                ->select('s.id', DB::raw("CONCAT(u.first_name, ' ', u.last_name) AS full_name"))
                                ->where('u.id', $user->id)
                                ->first();

                            $package = DB::table('purchase_package as pp')
                                ->join('subject_package as sp', 'pp.package_id', '=', 'sp.id')
                                ->select('pp.id as purchase_id')
                                ->where('pp.student_id', $userId->id)
                                ->whereIn('pp.status', [0, 2])
                                ->whereRaw("FIND_IN_SET(?, sp.subject_id)", [$subjectId])
                                ->first();

                            if ($package) {
                                $packageUsage = DB::table('package_usage as pu')
                                    ->where('pu.student_id', $userId->id)
                                    ->where('pu.purchase_package_id', $package->purchase_id)
                                    ->orderBy('created_at', 'DESC')
                                    ->first();

                                if ($packageUsage && $packageUsage->remaining_session > 0) {
                                    // dd($userId, $package, $packageUsage);
                                    DB::table('package_usage')->insert([
                                        'school_id' => $schoolId,
                                        'purchase_package_id' => $package->purchase_id,
                                        'student_id' => $userId->id,
                                        'deduct' => -1,
                                        'remaining_session' => $packageUsage->remaining_session - 1,
                                        'created_at' => Carbon::now(),
                                        'updated_at' => Carbon::now(),
                                    ]);

                                    if ($packageUsage && $packageUsage->remaining_session = 0) {
                                        DB::table('purchase_package as pp')
                                            ->where('pp.id', $package->purchase_id)
                                            ->update([
                                                'status' => 1
                                            ]);
                                    }
                                } else {
                                    $studentsInsufficientFunds[] = $userId->full_name;
                                }
                            }
                        } else if ($deductionMethod->data === "3") {

                            $userId = DB::table('students as s')
                                ->join('users as u', 'u.id', '=', 's.user_id')
                                ->select('s.id')
                                ->where('u.id', $user->id)
                                ->first();

                            $package = DB::table('purchase_package as pp')
                                ->join('subject_package as sp', 'pp.package_id', '=', 'sp.id')
                                ->select('pp.id as purchase_id')
                                ->where('pp.student_id', $userId->id)
                                ->whereIn('pp.status', [0, 2])
                                ->whereRaw("FIND_IN_SET(?, sp.subject_id)", [$subjectId])
                                ->first();

                            if ($package) {
                                $packageUsage = DB::table('package_usage as pu')
                                    ->where('pu.student_id', $userId->id)
                                    ->where('pu.purchase_package_id', $package->purchase_id)
                                    ->orderBy('created_at', 'DESC')
                                    ->first();

                                if ($packageUsage && $packageUsage->remaining_session > 0) {
                                    DB::table('package_usage')->insert([
                                        'school_id' => $schoolId,
                                        'purchase_package_id' => $package->purchase_id,
                                        'student_id' => $userId->id,
                                        'deduct' => -1,
                                        'remaining_session' => $packageUsage->remaining_session - 1,
                                        'created_at' => Carbon::now(),
                                        'updated_at' => Carbon::now(),
                                    ]);
                                } else {
                                    $query = DB::table('subject_attendances as sa')
                                        ->join('users as u', 'sa.user_id', '=', 'u.id')
                                        ->join('credit_system as cs', 'sa.user_id', '=', 'cs.user_id')
                                        ->join('students as s', 's.user_id', '=', 'sa.user_id')
                                        ->join('class_sections as cls','cls.id','=','s.class_section_id')
                                        ->join('classes as c','c.id','=','cls.class_id')
                                        ->join('subjects as sub', 'sa.subjects_id', '=', 'sub.id',)
                                        ->select('cs.user_id as credit_user', DB::raw("CONCAT(u.first_name, ' ', u.last_name) AS full_name"), 'c.id as class_id', 'cs.balance', 'cs.credit_amount', 'sub.commission')
                                        ->where('sa.date', date('Y-m-d', strtotime($request->date)))
                                        ->where('sa.subjects_id', $subjectId)
                                        ->where('sa.status', 1)
                                        ->where('cs.school_id', Auth::user()->school_id)
                                        ->where('cs.user_id', $user->id)
                                        ->first();

                                    $subjectPrice = -$query->commission;

                                    $totalBalance = DB::table('credit_system')
                                        ->where('user_id', $query->credit_user)
                                        ->sum('credit_amount');

                                    $balance = $totalBalance + $subjectPrice;

                                    DB::table('credit_system')->insert([
                                        'school_id' => Auth::user()->school_id,
                                        'class_id' => $query->class_id,
                                        'user_id' => $query->credit_user,
                                        'credit_amount' => $subjectPrice,
                                        'balance' => $balance,
                                        'detail' => empty($subjectId)?'Attended 1 Class':'Attended 1 Class' .'('.$subject->name. ')',
                                        'created_at' => Carbon::now(),
                                        'updated_at' => Carbon::now()
                                    ]);

                                    if ($balance <= 0) {
                                        $studentsInsufficientFunds[] = $query->full_name;
                                    }
                                }
                            }
                        }
                    }
                }

                    DB::commit();

                    $checkUser = DB::select('SELECT * FROM students s,users u WHERE s.user_id = u.id AND u.id = ?', [$user->id]);
                    if (count($checkUser) > 0) {
                        $allUser = DB::select('SELECT s.guardian_id,sa.user_id FROM subject_attendances sa JOIN users u ON sa.user_id = u.id JOIN students s ON u.id = s.user_id WHERE sa.user_id = ? AND sa.id = ?', [$user->id, $id]);
                    } else {
                        $allUser = DB::select('SELECT u.id FROM subject_attendances sa, users u WHERE sa.user_id=u.id AND sa.user_id = ? AND sa.id = ?', [$user->id, $id]);
                    }

                    $notifyUser = [];
                    foreach ($allUser as $data) {
                        if (isset($data->user_id)) {
                            $notifyUser[] = $data->user_id; //student
                        }
                        if (isset($data->id)) {
                            $notifyUser[] = $data->id; //user
                        }
                        if (isset($data->guardian_id)) {
                            $notifyUser[] = $data->guardian_id; //guardian
                        }
                    }
                    $checkedInTime = now()->format('h:iA');
                    $todayDate = now()->format('d/m/Y');
                    if ($notifyUser !== null) {
                        $title = 'Clocked In';
                        $type = 'Attendance'; // Get The Type for Notification
                        $body = "{$schoolName}\n{$userName} clocked in {$subjectName} at {$checkedInTime},{$todayDate}."; // Get The Body for Notification
                        send_notification($notifyUser, $title, $body, $type); // Send Notification
                    }

                    $alertMessage = "{$user->first_name} clocked in at " . now()->format('h:iA');
                    return redirect()->back()->with('success', $alertMessage);
                } catch (\Throwable $e) {
                    DB::rollback();
                    return redirect()->back()->with('error', 'Failed to record attendance: ' . $e->getMessage());
                }
            }
        }
    }

    // public function rewardSchoolSub()
    // {
    //     $schoolID=Auth::user()->school_id;
    //     try {
    //         if ($schoolID) {
    //             $school = DB::table('schools')->where('id', $schoolID)->first();
    //         } 
    //         if (!$school) {
    //             return response()->json([
    //                 'error' => true,
    //                 'message' => 'School not found',
    //             ], 404);
    //         }
            
    //         // $teachers = DB::table('staffs as s')
    //         //     ->join('users as u', 's.user_id', '=', 'u.id')
    //         //     ->where('u.school_id', $school->id)
    //         //     ->whereNull('u.deleted_at')
    //         //     ->select('u.id', DB::raw("CONCAT(u.first_name, ' ', u.last_name) AS full_name"))
    //         //     ->get();


    //         return response()->json([
    //             'error' => false,
    //             'school_id' => $school->id,
    //         ]);
    //     } catch (\Exception $e) {

    //         return response()->json([
    //             'error' => true,
    //             'message' => 'Error occurred while fetching data',
    //             'details' => $e->getMessage(),
    //         ], 500);
    //     }
    // }

    public function rewardPoint()
    {
        $school = School::find(Auth::user()->school_id);

        if(!$school){
            return redirect()->back()->with('error','school NOT Found');
        }

        $settings = app(CachingService::class)->getSystemSettings();
        return view('rewardPointRfid', compact( 'settings', 'school'));
    }

    public function grantRewardPoint(Request $request)
    {
        $validated = $request->validate([
            'school_id' => 'required',
            'class_id' => 'required',
            'student_id' => 'required',
            'rfid_id' => 'required',
        ]);


        $categoryId = $request->input('performance_id');
        $studentId = $request->input('student_id');
        $pointRedeem = $request->input('points-redeem')*-1;
        $redeemRemark = $request->input('redeem-remark');
        $mode=DB::table('rewards_category')->where('id',$categoryId)->whereNull('deleted_at')->value('mode');

        try {
            DB::begintransaction();
            $performanceSelected = DB::table('rewards_category')->where('id', $categoryId)->whereNull('deleted_at')->first();
            $score = DB::table('rewards')->where('student_id', $studentId)->whereNull('deleted_at')->select('score_total')->orderby('updated_at', 'desc')->first();
            $rewardPoint = DB::table('rewards')->where('student_id', $studentId)->whereNull('deleted_at')->select('reward_point_total')->orderby('updated_at', 'desc')->first();

            if (!$score) {
                $scoreNow = 0;
                $score = (object)[
                    'score_total' => 0
                ];
            }
            if (!$rewardPoint) {
                $rewardPointNow = 0;
                $rewardPoint = (object)[
                    'reward_point_total' => 0
                ];
            }



            if (!$pointRedeem) {
                if ($mode == 1) {
                    $scoreNow = (int)($score->score_total + $performanceSelected->points_amount);
                    $rewardPointNow = (int)($rewardPoint->reward_point_total + $performanceSelected->points_amount);
                    $point_amount = $performanceSelected->points_amount;
                } else {
                    $scoreNow = (int)($score->score_total + $performanceSelected->points_amount);
                    $rewardPointNow = $rewardPoint->reward_point_total;
                    $point_amount = 0;
                }

                $data = [
                    'school_id' => $request->input('school_id'),
                    'class_id'  => $request->input('class_id'),
                    'student_id' => $studentId,
                    'score_amount'   => $performanceSelected->points_amount,
                    'score_total' => $scoreNow,
                    'reward_point_amount' => $point_amount,
                    'reward_point_total' => $rewardPointNow,
                    'remark' => $performanceSelected->remark,
                    'category_id' => $categoryId
                ];
            } else {
                $rewardPointNow = (int)($rewardPoint->reward_point_total + $pointRedeem);

                $data = [
                    'school_id' => $request->input('school_id'),
                    'class_id'  => $request->input('class_id'),
                    'student_id' => $studentId,
                    'score_amount'   => 0,
                    'score_total' => $score->score_total,
                    'reward_point_amount' => $pointRedeem,
                    'reward_point_total' => $rewardPointNow,
                    'remark' => $redeemRemark,
                    'category_id' => 0
                ];
            }


            DB::table('rewards')->insert($data);
            DB::commit();
            return redirect()->back()->with('success', 'Reward Updated Successfully');
        } catch (Throwable $e) {
            DB::rollBack();
            return redirect()->back()->with('error', 'Reward Updated Unsuccessfully' . $e->getMessage());
        }
    }

    public function getInformationByRfid(Request $request)
    {
        $rfidId = $request->rfid_id;
        $school_id = $request->school_id;
        $modePrefix = 'Mode:01-0001-1000';
        if (strpos($rfidId, $modePrefix) === 0) {
            $rfidId = substr($rfidId, strlen($modePrefix));
        }
        try {
            $isValid = DB::select('SELECT * FROM rfid_whitelist WHERE rfid_id = ? AND status = 1', [$rfidId]);

            if ($isValid) {

                $student = DB::table('students as s')
                    ->join('users as u', 'u.id', '=', 's.user_id')
                    ->where('u.rfid_id', $rfidId)
                    ->whereNull('s.deleted_at')
                    ->select('s.id', DB::raw("CONCAT(u.last_name, ' ', u.first_name) as full_name"), 'u.image')
                    ->first();

                if (!$student) {
                    return response()->json([
                        'error' => true,
                        'message' => 'Student NOT Found',
                    ]);
                }

                $classes = DB::table('classes as c')
                    ->join('class_sections as cs', 'cs.class_id', '=', 'c.id')
                    ->join('students as s', 's.class_section_id', '=', 'cs.id')
                    ->where('s.id', $student->id)
                    ->whereNull('c.deleted_at')
                    ->select('c.id', 'c.name')
                    ->first();

                if (!$classes) {
                    return response()->json([
                        'error' => true,
                        'message' => 'Class NOT Found',
                    ]);
                }

                $academicPerformance = DB::table('rewards_category')
                    ->where('school_id', $school_id)
                    ->whereNull('deleted_at')
                    ->get();

                if (!$academicPerformance) {
                    return response()->json([
                        'error' => true,
                        'message' => 'Academic Perofmance NOT Found',
                    ]);
                }

                $score = DB::table('rewards')->where('student_id', $student->id)->whereNull('deleted_at')->select('score_total', 'score_amount')->orderby('updated_at', 'desc')->first();

                if (!$score) {
                    $score = (object)[
                        'score_total' => 0,
                        'score_amount' => 0
                    ];
                }
                $rewardPoint = DB::table('rewards')->where('student_id', $student->id)->whereNull('deleted_at')->select('reward_point_amount', 'reward_point_total')->orderby('updated_at', 'desc')->first();

                if (!$rewardPoint) {
                    $rewardPoint = (object)[
                        'reward_point_amount' => 0,
                        'reward_point_total' => 0
                    ];
                }
            } else {
                return response()->json([
                    'error' => true,
                    'message' => 'Unauthorized RFID ID',
                ]);
            }

            return response()->json([
                'error' => false,
                'student' => $student,
                'classes' => $classes,
                'performance' => $academicPerformance,
                'score' => $score,
                'rewardPoint' => $rewardPoint
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => true,
                'message' => 'Error occurred while fetching data',
            ]);
        }
    }

    public function overdueCronjob()
    {
        $this->recurringCronJob();
        $studentFees = StudentFee::whereNotNull('recurring_invoice')->get();
        foreach ($studentFees as $fee) {
            if (isset($fee->total_cycles) && $fee->current_cycle < $fee->total_cycles) {

                $recurringDate = $this->calculateNextRecurringDate($fee->created_at, $fee->current_cycle, $fee->recurring_invoice);
                $earlyDate = $this->calculateEarlyDate($fee->early_date, $fee->current_cycle, $fee->recurring_invoice);
                $dueDate = $this->calculateDueDate($fee->due_date, $fee->current_cycle, $fee->recurring_invoice);


                if (Carbon::parse($recurringDate)->lte(Carbon::now())) {

                    $fee->current_cycle += 1;
                    $fee->save();

                    $newFee = StudentFee::create([
                        'name'               => 'Auto-generated',
                        'due_date'           => $dueDate,
                        'due_charges'        => $fee->due_charges ?? 0,
                        'due_charges_amount' => $fee->due_charges_amount ?? 0,
                        'early_date'         => $earlyDate ? Carbon::parse($earlyDate)->format('Y-m-d') : null,
                        'early_offer'        => $fee->early_offer ?? 0,
                        'early_offer_amount' => $fee->early_offer_amount ?? 0,
                        'class_id'           => $fee->class_id,
                        'school_id'          => $fee->school_id,
                        'session_year_id'    => $fee->session_year_id,
                        'student_id'         => $fee->student_id,
                        'status'             => 'draft',
                        'uid'                => $fee->uid,
                        'created_at'         => now(),
                        'updated_at'         => now(),
                    ]);

                    $feeTypes = $this->studentFeeType->builder()->where("student_id", $fee->student_id)->get();

                    $feeDetails = [];
                    foreach ($feeTypes as $feeType) {
                        $feeDetails[] = [
                            "student_fees_id"   => $newFee->id,
                            "fees_type_name"    => $feeType->fees_type_name,
                            "fees_type_amount"  => $feeType->unit_price,
                            "classification_code" => $feeType->classification_code,
                            "quantity"          => $feeType->quantity ?? 1,
                            "unit"              => $feeType->unit,
                            "optional"          => 0,
                            "school_id"         => $fee->school_id,
                        ];
                    }

                    if (count($feeDetails) > 0) {
                        $this->studentFeesDetail->upsert($feeDetails, ['student_fees_id'], ['fees_type_name', 'fees_type_amount', 'classification_code', 'unit', 'quantity', 'optional']);
                    }
                }
            }
        }

        $packageStatus = DB::table('purchase_package')->get();
        foreach ($packageStatus as $row) {
            $pp_id = $row->id;

            $remaining_days = DB::table('purchase_package as pp')
                ->join('subject_package as sp', 'pp.package_id', '=', 'sp.id')
                ->select(
                    'pp.student_id',
                    'sp.name',
                    'sp.expiry_days',
                    'pp.date as purchase_date'
                )
                ->where('pp.id', $pp_id)
                ->first();

            if ($remaining_days) {
                $purchaseDate = Carbon::parse($remaining_days->purchase_date);
                $expiryDays = $remaining_days->expiry_days;

                $expirationDate = $purchaseDate->addDays($expiryDays);
                $formattedExpirationDate = $expirationDate->format('Y-m-d');

                $remainingDays = $expirationDate->diffInDays(Carbon::now());

                if (Carbon::now() >= $expirationDate) {
                    $expirationDate->diffInDays(Carbon::now(), false);

                    DB::table('purchase_package')
                        ->where('id', $pp_id)
                        ->update(['status' => 1]);
                } else if ($remainingDays <= 30) {
                    DB::table('purchase_package')
                        ->where('id', $pp_id)
                        ->update(['status' => 2]);
                } else {
                    DB::table('purchase_package')
                        ->where('id', $pp_id)
                        ->update(['status' => 0]);
                }
            }
        }
        $this->handleTomorrowDueDates();
        $this->overDueStudents();
        return response()->json(['status' => 'success', 'message' => 'Cron job executed']);
    }

    public function overdueStudents()
    {
        $overdueStudents = DB::table('students')
        ->join('student_fees', 'students.id', '=', 'student_fees.student_id')
        ->leftJoin('student_fees_paids', 'student_fees.id', '=', 'student_fees_paids.student_fees_id')
        ->where('student_fees.due_date', '<', date('Y-m-d')) 
        ->whereNull('student_fees.deleted_at')
        ->where('student_fees.status','=','published')
        ->whereNull('student_fees_paids.is_fully_paid')
        ->select('students.*', 'student_fees.due_date','student_fees.id as student_fee_id','student_fees.uid as student_fee_uid') 
        ->get();
        $sevenDaysAgo = date('Y-m-d', strtotime('-7 days'));
        foreach($overdueStudents as $student){
            $invoiceNumber = "INV" . sprintf('%08d', $student->student_fee_uid ?? '');
            $recent = DB::table('duefees_reminder')->where('student_id',$student->id)->where('student_fee_id',$student->student_fee_id)->where('reminder_date','>',$sevenDaysAgo)->where('type','=',2)->exists();
            if(!$recent){
                $guardian = DB::select('SELECT s.guardian_id, CONCAT(u.first_name, " ", u.last_name) AS full_name FROM students s JOIN users u ON u.id = s.user_id WHERE s.id ='. $student->id);
                if(!empty($guardian)){
                    $title = 'Reminder';
                    $body = $guardian[0]->full_name."'s school fees (Invoice #" . $invoiceNumber . ") are overdue. Please make the payment at your earliest convenience";
                    $type = "Fees Overdue";
                    send_notification([$guardian[0]->guardian_id], $title, $body, $type);

                    DB::table('duefees_reminder')->insert(['student_id' => $student->id, 'student_fee_id' => $student->student_fee_id, 'type' => 2, 'reminder_date' => date('Y-m-d')]);
                }
            }
        }
    }

    private function handleTomorrowDueDates()
    {
        $tomorrow = date('Y-m-d', strtotime('+1 day'));

        $studentsWithDueDateTomorrow = DB::table('students')
            ->join('student_fees', 'students.id', '=', 'student_fees.student_id')
            ->leftJoin('student_fees_paids', 'student_fees.id', '=', 'student_fees_paids.student_fees_id')
            ->where('student_fees.due_date', '=', $tomorrow)
            ->whereNull('student_fees.deleted_at')
            ->where('student_fees.status','=','published')
            ->whereNull('student_fees_paids.is_fully_paid')
            ->select('students.id', 'student_fees.id as student_fee_id','student_fees.uid as student_fee_uid')
            ->get();

        foreach ($studentsWithDueDateTomorrow as $student) {
            $invoiceNumber = "INV" . sprintf('%08d', $student->student_fee_uid ?? '');
            $guardian = DB::select('SELECT s.guardian_id, CONCAT(u.first_name, " ", u.last_name) AS student_full_name FROM students s JOIN users u ON u.id = s.user_id WHERE s.id ='. $student->id);
            if(!empty($guardian)){
                $title = 'Reminder';
                $body = $guardian[0]->student_full_name."'s school fees (Invoice #" . $invoiceNumber . ") are due tomorrow. Please make payment before the due date to avoid due charges.";
                $type = "Fees Due Tomorrow";
                send_notification([$guardian[0]->guardian_id], $title, $body, $type);
                
                DB::table('duefees_reminder')->insert(['student_id' => $student->id,'student_fee_id' => $student->student_fee_id,'`type`'=>1,'reminder_date' => date('Y-m-d')]);
            }
         
        }
    }

    public function about_us()
    {  // School website
        $school = $this->getSchoolIdFromDomain();
        $schoolId = $school->id;
        return view('school-website.about_us', compact('schoolId'));
    }

    public function contact_us()
    {
        return view('school-website.contact');
    }

    public function contact_form(Request $request)
    {
        $school = $this->getSchoolIdFromDomain();
        // Verify google captcha
        $schoolSettings = $this->cache->getSchoolSettings('*', $school->id);
        if ($schoolSettings['SCHOOL_RECAPTCHA_SITE_KEY'] ?? '') {
            $validator = Validator::make($request->all(), [
                'g-recaptcha-response' => 'required',
            ]);
            if ($validator->fails()) {
                ResponseService::errorResponse($validator->errors()->first());
            }
            $googleCaptcha = app(GeneralFunctionService::class)->schoolreCaptcha($request, $schoolSettings);

            if (!$googleCaptcha) {
                ResponseService::errorResponse('reCAPTCHA verification failed. Please try again.');
            }
        }
        
        try {
            $admin_email = app(CachingService::class)->getSystemSettings('mail_username');
            $data = [
                'name'        => $request->name,
                'email'       => $request->email,
                'subject'     => $request->subject,
                'description' => $request->message,
                'admin_email' => $admin_email,
                'school_email' => $request->school_email
            ];

            Mail::send('contact', $data, static function ($message) use ($data) {
                $message->to($data['school_email'])->subject($data['subject']);
            });

            return redirect()->to('school/contact-us')->with('success', "Message send successfully");
        } catch (Throwable) {
            return redirect()->to('school/contact-us')->with('error', "Apologies for the Inconvenience: Please Try Again Later");
        }
    }

    public function photo()
    {
        return view('school-website.photo');
    }

    public function photo_file($id)
    {
        try {
            $photos = Gallery::with(['file' => function ($q) {
                $q->where('type', 1);
            }])->find($id);
            if ($photos) {
                return view('school-website.photo_file', compact('photos'));
            } else {
                return redirect('school/photos');
            }
        } catch (\Throwable $th) {
            return redirect('school/photos');
        }
    }

    public function video()
    {
        return view('school-website.video');
    }

    public function video_file($id)
    {
        try {
            $videos = Gallery::with(['file' => function ($q) {
                $q->whereIn('type', [2, 3]);
            }])->find($id);
            if ($videos) {
                return view('school-website.video_file', compact('videos'));
            } else {
                return redirect('school/videos');
            }
        } catch (\Throwable $th) {
            return redirect('school/videos');
        }
    }

    public function terms_conditions()
    {
        return view('school-website.terms_conditions');
    }

    public function privacy_policy()
    {
        return view('school-website.privacy_policy');
    }

    public function attendanceRFIDTimetable(Request $request)
    {
        $schoolId = base64_decode($request->s);
        $school = School::find($schoolId);
        $settings = app(CachingService::class)->getSystemSettings();
        $getAttdSettings = DB::table('school_settings')
            ->where('name', 'attendance_setting')
            ->where('school_id', $school->id)
            ->value('data');
        $attendance_settings = [];
        if (isset($getAttdSettings)) {
            $attendance_settings = explode(',', $getAttdSettings);
        }
        return view('attendance-timetable', compact('school', 'settings', 'attendance_settings'));
    }

    public function takeAttendanceRFIDTimetable(Request $request)
    {
        $rfidId = $request->rfid_id;
        $modePrefix = 'Mode:01-0001-1000';
        if (strpos($rfidId, $modePrefix) === 0) {
            $rfidId = substr($rfidId, strlen($modePrefix));
        }

        $rfidIsValid = DB::select('SELECT * FROM rfid_whitelist WHERE rfid_id = ? AND status = 1', [$rfidId]);
        if (!$rfidIsValid) {
            return response()->json(['error' => 'Unauthorized RFID ID.']);
        }

        $school = DB::table('schools')->where('id', $request->school_id)->first();
        $todayDate = now();
        $user = DB::table('users')->select('id', 'first_name', 'last_name')->where('rfid_id', $rfidId)->first();
        if (!$user) {
            return response()->json(['error' => 'RFID ID does not belong to a valid user.']);
        }
        $student = DB::table('students')->where('user_id', $user->id)->first();

        $type = null;
        $notifyUser = array();
        try {
            DB::beginTransaction();
            if ($request->role == "student") {
                $classSections = DB::table('class_sections')
                ->where('id',$student->class_section_id)
                ->select('class_id')
                ->first();

                $classId = $classSections->class_id;    
                if ($request->type == 'allDay') {
                    $allDayAttendance = DB::table('attendances')
                        ->where('date', $todayDate->format('Y-m-d'))
                        ->where('student_id', $user->id)
                        ->exists();
                    $sessionYear = DB::select('SELECT * FROM session_years WHERE `school_id` = ? AND `default` = 1 LIMIT 1', [$school->id]);
                    if (!$allDayAttendance) {
                        $oldAttendanceTable = [
                            'class_section_id' => $student->class_section_id,
                            'student_id' => $user->id,
                            'session_year_id' => $sessionYear[0]->id,
                            'type' => 1,
                            'date' => $todayDate->format('Y-m-d'),
                            'school_id' => $school->id,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ];
                        $result = DB::table('attendances')->insert($oldAttendanceTable);
                        $data = [
                            'school_id' => $school->id,
                            'subjects_id' => null,
                            'user_id' => $user->id,
                            'status' => 1,
                            'date' => $todayDate->format('Y-m-d'),
                            'clock_in' => $todayDate,
                            'in_temperature' => $request->temperature
                        ];
                        $result = DB::table('subject_attendances')->insert($data);
                        if ($result) {
                            $attendanceStatus = 'Clocked In';
                            $notifyUser[] = $user->id;
                            $notifyUser[] = $student->guardian_id;
                        }

                        //Add Default Clock in Points
                        $clockInPoints = DB::table('rewards_category')
                        ->where('school_id', $school->id)
                        ->where('category_name','Clock In')
                        ->where('is_default',1)
                        ->select('points_amount','mode','id')
                        ->first();

                        if ($clockInPoints && $clockInPoints->points_amount > 0){
                            $points = DB::table('rewards')
                            ->where('school_id',$school->id)
                            ->where('student_id',$student->id)
                            ->where('class_id',$classId)
                            ->whereNull('deleted_at')
                            ->select('score_total','reward_point_total')
                            ->orderBy('updated_at', 'desc')
                            ->first();

                            // If student doesnt have previous reward records
                            if ($points) {
                                $scoreTotal = $points->score_total;
                                $rewardPointTotal = $points->reward_point_total;
                            } else {
                                $scoreTotal = 0;
                                $rewardPointTotal = 0;
                            }
                            

                            // update score and reward points
                            $data = array(
                                'school_id' => $school->id,
                                'class_id' =>$classId,
                                'student_id' => $student->id,
                                'score_amount' => $clockInPoints->points_amount,
                                'score_total' => $scoreTotal + $clockInPoints->points_amount,
                                'remark' => 'Clock In',
                                'category_id' => $clockInPoints->id
                            );

                            // update reward points if mode 1
                            $data['reward_point_amount'] = $clockInPoints->mode == 1 ? $clockInPoints->points_amount : 0;
                            $data['reward_point_total'] = $clockInPoints->mode == 1 ? $rewardPointTotal + $clockInPoints->points_amount : $rewardPointTotal;
                            $result = DB::table('rewards')->insert($data);

                        }
                    } else {
                        $scanAttendanceIfExists = DB::table('subject_attendances')
                            ->where('date', $todayDate->format('Y-m-d'))
                            ->where('user_id', $user->id)
                            ->whereNull('subjects_id')
                            ->whereNotNull('clock_in')
                            ->whereNull('clock_out')
                            ->first();
                        if ($scanAttendanceIfExists) {
                            $clockIn = Carbon::parse($scanAttendanceIfExists->clock_in);
                            $clockOut = Carbon::parse($todayDate);
                            $diffInMinutes = $clockOut->diffInMinutes($clockIn);
                            if ($diffInMinutes < 15) {
                                return response()->json(['error' => 'You can only clock out after 15 minutes.']);
                            }
                            $interval = $clockOut->diff($clockIn);
                            $totalTime = $interval->format('%H:%I:%S');
                            $data = [
                                'clock_out'  => $clockOut,
                                'total_time' => $totalTime,
                                'out_temperature' => $request->temperature
                            ];
                            $result = DB::table('subject_attendances')->where('id', $scanAttendanceIfExists->id)->update($data);
                            if ($result) {
                                $attendanceStatus = 'Clocked Out';
                                $notifyUser[] = $user->id;
                                $notifyUser[] = $student->guardian_id;
                            }

                            // Add Default Clock Out Points
                            $clockOutPoints = DB::table('rewards_category')
                            ->where('school_id', $school->id)
                            ->where('category_name','Clock Out')
                            ->where('is_default',1)
                            ->select('points_amount','mode','id')
                            ->orderBy('updated_at', 'desc')
                            ->first();

                            if ($clockOutPoints && $clockOutPoints->points_amount > 0){
                                $points = DB::table('rewards')
                                ->where('school_id',$school->id)
                                ->where('class_id',$classId)
                                ->where('student_id',$student->id)
                                ->whereNull('deleted_at')
                                ->select('score_total','reward_point_total')
                                ->orderBy('updated_at', 'desc')
                                ->first();

                                // update score and reward points
                                $data = array(
                                    'school_id' => $school->id,
                                    'class_id' =>$classId,
                                    'student_id' => $student->id,
                                    'score_amount' => $clockOutPoints->points_amount,
                                    'score_total' => $points->score_total + $clockOutPoints->points_amount,
                                    'remark' => 'Clock Out',
                                    'category_id' => $clockOutPoints->id
                                );

                                // update reward points if mode 1
                                $data['reward_point_amount'] = $clockOutPoints->mode == 1 ? $clockOutPoints->points_amount : 0;
                                $data['reward_point_total'] = $clockOutPoints->mode == 1 ? $points->reward_point_total + $clockOutPoints->points_amount : $points->reward_point_total;
                                $result = DB::table('rewards')->insert($data);

                            }
                        } else {
                            return response()->json(['error' => 'All Day Attendance has already been recorded']);
                        }
                    }
                } elseif ($request->type == 'subject') {
                    $classesToday = DB::table('timetables as t')
                        ->join('class_sections as cs', 'cs.id', '=', 't.class_section_id')
                        ->where('cs.id', $student->class_section_id)
                        ->where('t.school_id', $school->id)
                        ->where('t.day', $todayDate->format('l'))
                        ->whereNotNull('t.subject_id')
                        ->select('t.subject_teacher_id', 't.subject_id', 't.start_time', 't.end_time')
                        ->get();
                    $currentClass = null;
                    $currentTime = $todayDate->format('H:i:s');
                    $upComingClasses = $todayDate->copy()->addHours(1)->format('H:i:s');

                    //Handling of one time attendance checking
                    $schoolSetting = DB::table('school_settings')
                        ->where('school_id', $request -> school_id)
                        ->where('name', 'attendance_setting')
                        ->where('data', 'LIKE', '%4%')
                        ->exists();

                    if ($schoolSetting) {
                        // Check if there's already an attendance entry for today
                        $scanAttendanceIfExists = DB::table('subject_attendances')
                            ->where('school_id', $school->id)
                            ->where('user_id', $user->id)
                            ->where('subjects_id', $request->subjects_id)
                            ->whereDate('date', date('Y-m-d'))
                            ->first();
                        // One-time attendance is enabled
                        if ($scanAttendanceIfExists) {
                            return response()->json([
                                'error' => 'Your attendance has already been marked for today.'
                            ]);
                        }
                    }

                    //Check Current Class
                    foreach ($classesToday as $class) {
                        if (($currentTime >= $class->start_time && $currentTime <= $class->end_time) || $class->start_time >= $currentTime && $class->start_time <= $upComingClasses) {
                            $currentClass = $class;
                            break;
                        }
                    }

                    //Existing Attendance
                    $existingAttendance = DB::table('subject_attendances')
                        ->where('date', $todayDate->format('Y-m-d'))
                        ->where('user_id', $user->id)
                        ->whereNotNull('subjects_id')
                        ->whereNull('clock_out')
                        ->where('clock_in', '<=', $todayDate)
                        ->where('status', '!=', 0)
                        ->first();

                    if ($existingAttendance) {

                        $subject = DB::table('subjects')->where('id', $existingAttendance->subjects_id)->first();
                        $clockIn = Carbon::parse($existingAttendance->clock_in);
                        $clockOut = Carbon::parse($currentTime);
                        $diffInMinutes = $clockOut->diffInMinutes($clockIn);
                        if ($diffInMinutes < 15) {
                            return response()->json(['error' => 'You can only clock out after 15 minutes.']);
                        }
                        $interval = $clockOut->diff($clockIn);
                        $totalTime = $interval->format('%H:%I:%S');
                        $data = [
                            'clock_out'  => $clockOut,
                            'total_time' => $totalTime
                        ];
                        $result = DB::table('subject_attendances')
                            ->where('id', $existingAttendance->id)
                            ->update($data);
                        if ($result) {
                            $attendanceStatus = 'Clocked Out';
                            $notifyUser[] = $user->id;
                            $notifyUser[] = $student->guardian_id;
                        }
                    } else {
                        if ($currentClass == null) {
                            return response()->json(['error' => 'There are currently no classes scheduled during this period.']);
                        }
                        if ($currentClass) {
                            // Retrieve commission type and amount
                            $subject = DB::table('subjects')->where('id', $currentClass->subject_id)->first();
                            $commissionType = null;
                            $commissionAmount = null;
                            $teacher = DB::table('subject_teachers')->where('id', $currentClass->subject_teacher_id)->first();
                            if ($teacher) {
                                $userGroupDetail = DB::table('user_group_details')->where('subject_id', $request->subjects_id)->where('teacher_id', $teacher->teacher_id)->first();
                                if ($userGroupDetail) {
                                    $groupId = $userGroupDetail->group_id;
                                    $userGroup = DB::table('user_groups')->where('id', $groupId)->first();
                                    if ($userGroup) {
                                        $commissionType = $userGroup->commission_type;
                                        $commissionAmount = $userGroup->commission_amount;
                                    }
                                }
                            }
                            $attendanceData = [
                                'school_id' => $school->id,
                                'teacher_id' => $teacher->teacher_id ?? null,
                                'subjects_id' => $subject->id,
                                'user_id' => $user->id,
                                'status' => 1,
                                'date' => $todayDate->format('Y-m-d'),
                                'clock_in' => $todayDate,
                                'fees_per_section' => $subject->commission ?? null,
                                'fees_per_month' => $subject->commission_month ?? null,
                                'commission_typee' => $commissionType ?? null,
                                'commission_amountt' => $commissionAmount ?? 0, // Default to 0 if not found
                            ];
                            $result = DB::table('subject_attendances')->insert($attendanceData);
                            if ($result) {
                                $attendanceStatus = 'Clocked In';
                                $notifyUser[] = $user->id;
                                $notifyUser[] = $student->guardian_id;

                                $deductionMethod = DB::table('school_settings')
                                ->select('name', 'data')
                                ->where('name', 'deduction_method')
                                ->where('school_id', $school->id)
                                ->first();
        
                                $userFullName = DB::table('users')
                                                ->where('id',$user->id)
                                                ->value(DB::raw("CONCAT(first_name, ' ', last_name) AS full_name"));
                                $studentDetail = DB::table('students as s')
                                            ->join('class_sections as cs','cs.id','=','s.class_section_id')
                                            ->where('s.user_id',$user->id)
                                            ->first();
                                if ($deductionMethod) {
                                    $creditStatus = DB::table('students')
                                        ->where('user_id', $user->id)
                                        ->value('credit_status');

                                if ($creditStatus != 1) {
                                    if ($deductionMethod->data === "1") {
                                        // if ($subject_attendance_id) {
                                            $query = DB::table('subject_attendances as sa')
                                                ->join('users as u', 'sa.user_id', '=', 'u.id')
                                                ->join('credit_system as cs', 'sa.user_id', '=', 'cs.user_id')
                                                ->join('students as s', 's.user_id', '=', 'sa.user_id')
                                                ->join('class_sections as cls','cls.id','=','s.class_section_id')
                                                ->join('classes as c','c.id','=','cls.class_id')
                                                ->join('subjects as sub', 'sa.subjects_id', '=', 'sub.id',)
                                                ->select('cs.user_id as credit_user', DB::raw("CONCAT(u.first_name, ' ', u.last_name) AS full_name"), 'c.id as class_id', 'cs.balance', 'cs.credit_amount', 'sub.commission')
                                                ->where('sa.date', $todayDate->format('Y-m-d'))
                                                ->where('sa.subjects_id', $subject->id)
                                                ->where('sa.status', 1)
                                                ->where('cs.school_id', $school->id)
                                                ->where('cs.user_id', $user->id)
                                                ->first();
                                            if($query){
                                                $subjectPrice = -$query->commission;
                                                $totalBalance = DB::table('credit_system')
                                                ->where('user_id', $query->credit_user)
                                                ->sum('credit_amount');
                                                // $className=DB::table('classes')->where('id',$class_id).whereNull('deleted_at')->value('name');
                                                
                                                $balance = $totalBalance + $subjectPrice;
                
                                                DB::table('credit_system')->insert([
                                                    'school_id' => $school->id,
                                                    'class_id' => $query->class_id,
                                                    'user_id' => $query->credit_user,
                                                    'credit_amount' => $subjectPrice,
                                                    'balance' => $balance,
                                                    'detail' => empty($subjectId)?'Attended 1 Class':'Attended 1 Class' .'('.$subject->name. ')',
                                                    'created_at' => Carbon::now(),
                                                    'updated_at' => Carbon::now()
                                                ]);
                
                                                if ($balance <= 0) {
                                                    $studentsInsufficientFunds[] = $query->full_name;
                                                }
                                            } else {
                                                $subjectPrice = -$subject->commission;
                                                $value = [
                                                    'school_id' => $school->id,
                                                    'class_id'  => $studentDetail->class_id,
                                                    'user_id'   => $user->id,
                                                    'credit_amount' => $subjectPrice,
                                                    'balance'       => $subjectPrice,
                                                    'detail' => empty($subjectId)?'Attended 1 Class':'Attended 1 Class' .'('.$subject->name. ')',
                                                    'created_at' => Carbon::now(),
                                                    'updated_at' => Carbon::now()
                                                ];
                                                DB::table('credit_system')->insert($value);
                                                $studentsInsufficientFunds[] = $userFullName;
                                            }
                                            $notifyDeduct=$subjectPrice;
                                        // }
                                    } else if ($deductionMethod->data === "2") {
                                        $userId = DB::table('students as s')
                                            ->join('users as u', 'u.id', '=', 's.user_id')
                                            ->select('s.id', DB::raw("CONCAT(u.first_name, ' ', u.last_name) AS full_name"))
                                            ->where('u.id', $user->id)
                                            ->first();
            
                                        $package = DB::table('purchase_package as pp')
                                            ->join('subject_package as sp', 'pp.package_id', '=', 'sp.id')
                                            ->select('pp.id as purchase_id')
                                            ->where('pp.student_id', $userId->id)
                                            ->whereIn('pp.status', [0, 2])
                                            ->whereRaw("FIND_IN_SET(?, sp.subject_id)", [$subject->id])
                                            ->first();
                                        if ($package) {
                                            $packageUsage = DB::table('package_usage as pu')
                                                ->where('pu.student_id', $userId->id)
                                                ->where('pu.purchase_package_id', $package->purchase_id)
                                                ->orderBy('created_at', 'DESC')
                                                ->first();
                                            if ($packageUsage && $packageUsage->remaining_session > 0) {
                                                if($packageUsage->remaining_session != 0){
                                                    $remain = $packageUsage->remaining_session - 1;
                                                }
            
                                                DB::table('package_usage')->insert([
                                                    'school_id' => $school->id,
                                                    'purchase_package_id' => $package->purchase_id,
                                                    'student_id' => $userId->id,
                                                    'deduct' => -1,
                                                    'remaining_session' => $remain ?? 0,
                                                    'created_at' => Carbon::now(),
                                                    'updated_at' => Carbon::now(),
                                                ]);
                                                $notifysessionDeduct=1;
            
            
                                                if (in_array($remain, [1,2,3])) {
                                                    $lowSessionCount[] = ['student_id' => $packageUsage->student_id, 'remaining_session' => $remain];
                                                }else if ($remain == 0) {
                                                    $noSessionCount[] =['student_id' => $packageUsage->student_id, 'remaining_session' => $remain];
                                                }
                                                if ($packageUsage && $remain == 0) {
                                                    DB::table('purchase_package as pp')
                                                        ->where('pp.id', $package->purchase_id)
                                                        ->update([
                                                            'status' => 1
                                                        ]);
                                                }
                                            } else {
                                                $studentsInsufficientFunds[] = $userId->full_name;
                                            }
                                        } else {
                                            $studentsInsufficientFunds[] = $userFullName;
                                        }
                                     
                                    } else if ($deductionMethod->data === "3") {
                                        $packageUsed = false;
                                        $userId = DB::table('students as s')
                                            ->join('users as u', 'u.id', '=', 's.user_id')
                                            ->select('s.id')
                                            ->where('u.id', $user->id)
                                            ->first();
            
                                        $package = DB::table('purchase_package as pp')
                                            ->join('subject_package as sp', 'pp.package_id', '=', 'sp.id')
                                            ->select('pp.id as purchase_id', 'pp.package_id', 'pp.student_id')
                                            ->where('pp.student_id', $userId->id)
                                            ->whereIn('pp.status', [0, 2])
                                            ->whereRaw("FIND_IN_SET(?, sp.subject_id)", [$subject->id])
                                            ->first();
            
                                        if ($package) {
                                            $packageUsage = DB::table('package_usage as pu')
                                            ->where('pu.student_id', $userId->id)
                                            ->where('pu.purchase_package_id', $package->purchase_id)
                                            ->orderBy('created_at', 'DESC')
                                            ->first();
                                            if ($packageUsage && $packageUsage->remaining_session > 0) {
                                                if($packageUsage->remaining_session != 0){
                                                    $remain = $packageUsage->remaining_session - 1;
                                                    $packageUsed = true;
                                                }
            
                                                DB::table('package_usage')->insert([
                                                    'school_id' => $school->id,
                                                    'purchase_package_id' => $package->purchase_id,
                                                    'student_id' => $userId->id,
                                                    'deduct' => -1,
                                                    'remaining_session' => $remain ?? 0,
                                                    'created_at' => Carbon::now(),
                                                    'updated_at' => Carbon::now(),
                                                ]);
                      
                                                $notifysessionDeduct=1;

                                                if (in_array($remain, [1,2,3])) {
                                                    $lowSessionCount[] = ['student_id' => $packageUsage->student_id, 'remaining_session' => $remain];
                                                }else if ($remain == 0) {
                                                    $noSessionCount[] =['student_id' => $packageUsage->student_id, 'remaining_session' => $remain];
                                                }
                                                if ($packageUsage && $remain == 0) {
                                                    DB::table('purchase_package as pp')
                                                        ->where('pp.id', $package->purchase_id)
                                                        ->update([
                                                            'status' => 1
                                                        ]);
                                                }
                                            } 
                                        } 
                                        
                                        $query = DB::table('subject_attendances as sa')
                                            ->join('users as u', 'sa.user_id', '=', 'u.id')
                                            ->join('credit_system as cs', 'sa.user_id', '=', 'cs.user_id')
                                            ->join('subjects as sub', 'sa.subjects_id', '=', 'sub.id')
                                            ->select('cs.user_id as credit_user', DB::raw("CONCAT(u.first_name, ' ', u.last_name) AS full_name"), 'cs.class_id', 'cs.balance', 'cs.credit_amount', 'sub.commission')
                                            ->where('sa.date', $todayDate->format('Y-m-d'))
                                            ->where('sa.subjects_id', $subject->id)
                                            ->where('sa.status', 1)
                                            ->where('cs.school_id', $school->id)
                                            ->where('cs.user_id', $user->id)
                                            ->first();
                                        if(!$packageUsed){
                                            if($query){
                                                $subjectPrice = -$query->commission;
                                                $totalBalance = DB::table('credit_system')
                                                ->where('user_id', $query->credit_user)
                                                ->sum('credit_amount');
                                               
                                                
                                                $balance = $totalBalance + $subjectPrice;
                
                                                DB::table('credit_system')->insert([
                                                    'school_id' => $school->id,
                                                    'class_id' => $query->class_id,
                                                    'user_id' => $query->credit_user,
                                                    'credit_amount' => $subjectPrice,
                                                    'balance' => $balance,
                                                    'detail' => 'Attended 1 Class' . '('.$subject->name.')',
                                                    'created_at' => Carbon::now(),
                                                    'updated_at' => Carbon::now()
                                                ]);
                
                                                if ($balance <= 0) {
                                                    $studentsInsufficientFunds[] = $query->full_name;
                                                }
                                                $notifyDeduct=$subjectPrice;
                                            } else {
                                                $subjectPrice = -$subject->commission;
                                                $value = [
                                                    'school_id' => $school->id,
                                                    'class_id'  => $studentDetail->class_id,
                                                    'user_id'   => $user->id,
                                                    'credit_amount' => $subjectPrice,
                                                    'balance'       => $subjectPrice,
                                                    'detail' => 'Attended 1 Class' . '('.$subject->name.')',
                                                    'created_at' => Carbon::now(),
                                                    'updated_at' => Carbon::now()
                                                ];
                                                DB::table('credit_system')->insert($value);
                                                $studentsInsufficientFunds[] = $userFullName;
                                                $notifyDeduct=$subjectPrice;
                                            }
                                        }
                                    }
                                }
                                }  
                            }
                        }
                    }
                }
            } else if ($request->role == "teacher") {
                $allTaken = DB::table('teacher_attendance')
                    ->where('user_id', $user->id)
                    ->whereNull('subject_id')
                    ->whereNotNull('clock_in')
                    ->whereNotNull('clock_out')
                    ->where('status', '!=', 0)
                    ->where('date', $todayDate->format('Y-m-d'))
                    ->first();
                if ($allTaken) {
                     $oneTimeAttendance = DB::table('school_settings')
                    ->where('school_id', $school->id)
                    ->where('name', 'attendance_setting')
                    ->where('data', 'LIKE', '%4%')
                    ->first();

                    if ($oneTimeAttendance) {
                        return redirect()->back()->with('error', 'Attendance already recorded with both in and out times today.');
                    }
                    //return response()->json(['error' => 'Attendance has been taken']);
                }
                $existingAttendance = DB::table('teacher_attendance')
                    ->where('user_id', $user->id)
                    ->whereNull('subject_id')
                    ->whereNotNull('clock_in')
                    ->whereNull('clock_out')
                    ->where('status', '!=', 0)
                    ->where('date', $todayDate->format('Y-m-d'))
                    ->first();
                if ($existingAttendance) {
                    $clockIn = Carbon::parse($existingAttendance->clock_in);
                    $clockOut = Carbon::parse($todayDate);
                    $diffInMinutes = $clockOut->diffInMinutes($clockIn);
                    if ($diffInMinutes < 15) {
                        return response()->json(['error' => 'You can only clock out after 15 minutes.']);
                    }
                    $interval = $clockOut->diff($clockIn);
                    $totalTime = $interval->format('%H:%I:%S');
                    $data = [
                        'clock_out'  => $clockOut,
                        'total_time' => $totalTime,
                        'updated_at' => $todayDate
                    ];
                    $result = DB::table('teacher_attendance')
                        ->where('id', $existingAttendance->id)
                        ->update($data);
                    if ($result) {
                        $attendanceStatus = 'Clocked Out';
                        $notifyUser[] = $user->id;
                    }
                } else {
                    $data = [
                        'user_id'    => $user->id,
                        'clock_in'   => $todayDate,
                        'status'     => 1,
                        'date'       => $todayDate->format('Y-m-d'),
                        'school_id'  => $school->id,
                        'created_at' => $todayDate,
                        'updated_at' => $todayDate
                    ];
                    $result = DB::table('teacher_attendance')->insert($data);
                    if ($result) {
                        $attendanceStatus = 'Clocked In';
                        $notifyUser[] = $user->id;
                    }
                }
            }
            DB::commit();
        } catch (\Throwable $e) {
            DB::rollback();
            return response()->json(['error' => 'An error occured',
                                    'message' => $e->getMessage()]);
        }

        if (isset($attendanceStatus) && isset($notifyUser)) {
            $userName = $user->first_name . " " . $user->last_name;
            $notifyTime = $todayDate->format('h:iA');
            $notifyDate = $todayDate->format('d/m/Y');
            $subjectName = $subject->name ?? '';
            $title = $attendanceStatus;
            $type = 'Attendance'; // Get The Type for Notification
            $body = "{$school->name}\n{$userName} {$attendanceStatus} {$subjectName} at {$notifyTime}, {$notifyDate}.";
            send_notification($notifyUser, $title, $body, $type); // Send Notification

            if(isset($notifyDeduct)) {
                // Check credit_status before sending credit deduction notification
                $creditStatus = DB::table('students')
                    ->where('user_id', $user->id)
                    ->value('credit_status');

                if ($creditStatus != 1) {  // Only send if credit_status is not 1
                    if($notifyDeduct < 0){
                        $deductMark=$notifyDeduct*-1;
                    } else{
                        $deductMark=$notifyDeduct;
                    }

                    $title = 'Credit Deduction';
                    $type = 'Credit Deduction Notification'; // Get The Type for Notification
                    $body = "{$deductMark} credits deducted for clocking in to {$subjectName} on {$notifyDate},{$notifyTime} .";
                    send_notification($notifyUser, $title, $body, $type); // Send Notification
                }
            }

            if(isset($notifysessionDeduct)) {
                try {
                    $packageInfo = DB::table('purchase_package as pp')
                            ->join('subject_package as sp', 'pp.package_id', '=', 'sp.id')
                            ->join('students as s', 'pp.student_id', '=', 's.id')
                            ->where('s.user_id', $user->id)
                            ->whereRaw("FIND_IN_SET(?, sp.subject_id)", [$subject->id])
                            ->select('pp.date as purchase_date', 'sp.expiry_days', 'pp.id as pp_id', 'pp.status')
                            ->orderBy('pp.id', 'DESC')
                            ->first();

                    $title = 'Session Updated';
                    $type = 'session_update';

                    if ($packageInfo) {
                        $packageUsage = DB::table('package_usage')
                            ->where('purchase_package_id', $packageInfo->pp_id)
                            ->orderBy('created_at', 'DESC')
                            ->first();

                        $remainingSessions = $packageUsage ? $packageUsage->remaining_session : 0;

                        $purchaseDate = Carbon::parse($packageInfo->purchase_date);
                        $expiryDays = $packageInfo->expiry_days;
                        $expirationDate = $purchaseDate->addDays($expiryDays);
                        $formattedExpirationDate = $expirationDate->format('d-m-Y');

                        $title = 'Session Updated';
                        $type = 'session_update';

                        if ($remainingSessions == 0) {
                            $body = "Clock-in successful! Your sessions have finished.";
                        } else {
                            $body = "Clock-in successful! You have {$remainingSessions} sessions left (expires {$formattedExpirationDate})";
                        }
                    } else {
                        $body = "Clock-in successful!";
                    }
                    send_notification($notifyUser, $title, $body, $type);

                } catch (\Exception $e) {
                    \Log::error('Error in session deduction process:', [
                        'error' => $e->getMessage(),
                        'file' => $e->getFile(),
                        'line' => $e->getLine(),
                        'user_id' => $user->id
                    ]);
                }
            }

            if (!empty($studentsInsufficientFunds)) {
                $creditStatus = DB::table('students')
                    ->where('user_id', $user->id)
                    ->value('credit_status');

                if ($creditStatus == 1) {
                    $alertMessage = "{$userName} {$attendanceStatus} at " . $notifyTime;
                    return response()->json(['success' => $alertMessage]);
                } else {
                    $namesInsufficient = implode(', ', $studentsInsufficientFunds);
                    return response()->json(['warning' => $namesInsufficient]);
                }
            } else {
                $alertMessage = "{$userName} {$attendanceStatus} at " . $notifyTime;
                return response()->json(['success' => $alertMessage]);
            }
        }
    }

    public function checkTeacherOrStudent(Request $request)
    {
        $rfidId = $request->rfid_id;
        $modePrefix = 'Mode:01-0001-1000';
        if (strpos($rfidId, $modePrefix) === 0) {
            $rfidId = substr($rfidId, strlen($modePrefix));
        }

        $rfidIsValid = DB::select('SELECT * FROM rfid_whitelist WHERE rfid_id = ? AND status = 1', [$rfidId]);
        if (!$rfidIsValid) {
            return response()->json(['proceed' => true]);
        }
        $teacher = DB::table('staffs as s')
            ->join('users as u', 'u.id', '=', 's.user_id')
            ->where('u.rfid_id', $rfidId)
            ->first();

        if ($teacher) {
            return response()->json(['success' => true]);
        }
        $user = DB::table('users')->select('id', 'first_name', 'last_name')->where('rfid_id', $rfidId)->first();
        if($user){
            $existingAttendance = DB::table('subject_attendances')
            ->where('date', now()->format('Y-m-d'))
            ->where('user_id', $user->id)
            ->whereNull('subjects_id')
            ->where('clock_in', '<=', now())
            ->where('status', '!=', 0)
            ->first();
            if($existingAttendance && !isset($existingAttendance->clock_out)){
                $clockIn = Carbon::parse($existingAttendance->clock_in);
                $clockOut = Carbon::parse(now());
                $diffInMinutes = $clockOut->diffInMinutes($clockIn);
                if ($diffInMinutes < 15) {
                    return response()->json(['proceed' => true]);
                }                
                return response()->json(['allDay' => true]);
            } else if (isset($existingAttendance->clock_in) && isset($existingAttendance->clock_out)){
                return response()->json(['proceed' => true]);
            }
        }
    }

    public function admissionFormPage(Request $request){
        $school = $this->getSchoolIdFromDomain();
        if($school){
            $schoolId = $school->id ?? '';
            
            $classSubjectSections = DB::select(
                "SELECT
                    class_sections.*,
                    classes.name AS class_name,
                    streams.name AS stream_name,
                    sections.name AS section_name,
                    mediums.name AS medium_name
                FROM
                    class_sections
                LEFT JOIN
                    classes ON class_sections.class_id = classes.id
                LEFT JOIN
                    streams ON classes.stream_id = streams.id
                LEFT JOIN
                    sections ON class_sections.section_id = sections.id
                LEFT JOIN
                    mediums ON class_sections.medium_id = mediums.id
                WHERE
                    class_sections.school_id = ?
                    AND class_sections.deleted_at IS NULL",
                [$schoolId]
            );

            $sessionYears = DB::select('SELECT * FROM session_years WHERE school_id = ' . $schoolId);

            $extraFields = DB::select("SELECT * FROM form_fields WHERE school_id = ? AND deleted_at IS NULL ORDER BY `rank`", [$schoolId]);
            foreach ($extraFields as $key => $e) {
                // dd($e);
                if ($e->default_values) {
                    $e = (array) $e;
                    $extraFields[$key]->default_values = json_decode($e['default_values']);
                }
            }

            $defaultSession = DB::select('SELECT * FROM session_years WHERE `school_id`= ? AND `default` = 1 LIMIT 1', [$schoolId]);
            $sessionName = $defaultSession[0]->name;
            $studentList = DB::select('SELECT COUNT(id) AS total FROM students WHERE school_id = ' . $schoolId);
            $totalStudent = $studentList[0]->total + 1;
            $admission_no = $sessionName.$schoolId.str_pad(($totalStudent), 4, '0', STR_PAD_LEFT);
            while(true){
                $studentList = DB::select('SELECT id FROM users WHERE email = ?',[$admission_no]);
                if(COUNT($studentList)){
                    $totalStudent = $totalStudent + 1;
                    $admission_no = $sessionName . $schoolId . str_pad(($totalStudent + 1), 4, '0', STR_PAD_LEFT);
                } else {
                    break;
                }
            }
        }
        $languages = Language::get();

        $settings = app(CachingService::class)->getSystemSettings();
        $schoolSettings = SchoolSetting::where('name', 'horizontal_logo')->get();
        return view('admission_form_page', compact('classSubjectSections', 'sessionYears', 'extraFields', 'admission_no', 'schoolId', 'languages', 'settings', 'schoolSettings'));
    }
}
