<?php

namespace App\Http\Controllers;

use Throwable;
use Carbon\Carbon;
use App\Models\Timetable;
use Illuminate\Http\Request;
use App\Services\CachingService;
use App\Services\ResponseService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use App\Services\FileSizeLimitService;
use App\Services\BootstrapTableService;
use Illuminate\Support\Facades\Storage;
use App\Repositories\User\UserInterface;
use App\Repositories\Medium\MediumInterface;
use App\Repositories\Subject\SubjectInterface;
use App\Repositories\Timetable\TimetableInterface;
use App\Repositories\ClassSchool\ClassSchoolInterface;
use App\Repositories\ClassSection\ClassSectionInterface;
use App\Repositories\SchoolSetting\SchoolSettingInterface;
use App\Repositories\SubjectTeacher\SubjectTeacherInterface;

class TimetableController extends Controller {
    private SubjectTeacherInterface $subjectTeacher;
    private SubjectInterface $subject;
    private TimetableInterface $timetable;
    private ClassSectionInterface $classSection;
    private UserInterface $user;
    private SchoolSettingInterface $schoolSettings;
    private CachingService $cache;
    private ClassSchoolInterface $class;
    private MediumInterface $medium;

    public function __construct(SubjectTeacherInterface $subjectTeacher, SubjectInterface $subject, TimetableInterface $timetable, ClassSectionInterface $classSection, UserInterface $user, SchoolSettingInterface $schoolSettings, CachingService $cache, ClassSchoolInterface $class, MediumInterface $medium) {
        $this->subjectTeacher = $subjectTeacher;
        $this->subject = $subject;
        $this->timetable = $timetable;
        $this->classSection = $classSection;
        $this->user = $user;
        $this->schoolSettings = $schoolSettings;
        $this->cache = $cache;
        $this->class = $class;
        $this->medium = $medium;
    }

    public function index() {
        ResponseService::noFeatureThenRedirect('Timetable Management');
        ResponseService::noPermissionThenRedirect('timetable-list');

        // Get Timetable Settings Data
        $timetableData = $this->schoolSettings->getBulkData([
            'timetable_start_time', 'timetable_end_time', 'timetable_duration'
        ]);

        // Convert Timetable Duration time to number
        $timetableData['timetable_duration'] = Carbon::parse($timetableData['timetable_duration'] ?? "00:00:00")->diffInMinutes(Carbon::parse('00:00:00'));

        $classes = $this->class->builder()->with('stream')->get()->pluck('full_name','id');
        $mediums = $this->medium->builder()->pluck('name','id');
        

        return view('timetable.index', compact('timetableData','classes','mediums'));
    }

    public function store(Request $request) {
        ResponseService::noFeatureThenRedirect('Timetable Management');
        ResponseService::noPermissionThenRedirect(['timetable-create']);
        $request->validate([
            'subject_teacher_id' => 'nullable|numeric',
            'class_section_id'   => 'required|numeric',
            'subject_id'         => 'nullable|numeric',
            'start_time' => 'required',
            'end_time'   => 'required',
            'day'                => 'required',
            'note'               => 'nullable',
            'timetable_configurations_id' => 'nullable'
        ]);
        $confirmed = false;

        if(!empty($request->confirmed) && $request->confirmed == true){
            $confirmed = true;
        } 
        if($confirmed == false) {
            $teacherid = "SELECT teacher_id, school_id FROM subject_teachers where id = ?";
            $teacherResult = DB::select($teacherid,[$request->subject_teacher_id]);
            
            if(!empty($teacherResult[0])){
                $sessionSql = "SELECT id, start_date, end_date FROM session_years WHERE school_id = ? AND `default` = 1";
                $sessionResult = DB::select($sessionSql, [$teacherResult[0]->school_id]);  
                if(!empty($sessionResult[0])){
                   
                    $conflictTimetableSql = "SELECT timetables.*, subject_teachers.teacher_id FROM timetables JOIN subject_teachers ON subject_teachers.id = timetables.subject_teacher_id WHERE subject_teachers.teacher_id = ? 
                        AND timetables.day = ? 
                        AND (
                        (start_time <= ? AND end_time > ?) OR
                        (start_time < ? AND end_time >= ?) OR
                        (start_time >= ? AND end_time <= ?) 
                        ) AND timetables.subject_id IN
                        (SELECT subject_id FROM class_subjects WHERE id IN (SELECT DISTINCT class_subject_id FROM `student_subjects` WHERE session_year_id = ?))
                        ";
                    $conflictTimetableResult = DB::select($conflictTimetableSql, [
                        $teacherResult[0]->teacher_id,
                        $request->day,
                        $request->start_time,
                        $request->start_time,
                        $request->end_time,
                        $request->end_time,
                        $request->start_time,
                        $request->end_time, $sessionResult[0]->id]);
                    if(!empty($conflictTimetableResult)){
                        return response()->json([
                            'message' => 'conflict',
                            'subject_teacher_id' => $request->subject_teacher_id,
                            'class_section_id' => $request->class_section_id,
                            'subject_id' => $request->subject_id,
                            'type' => (!empty($request->subject_id)) ? "Lecture" : "Break",
                        'start_time' => $request->start_time,'end_time' => $request->end_time,'day' => $request->day]);
                    }
                }
                else{
                    return response()->json([
                        'message' => 'No default session year found']);
                }
            }
        }
      

        try {
            $timetable = $this->timetable->create([
                ...$request->all(),
                'type' => (!empty($request->subject_id)) ? "Lecture" : "Break"
            ]);
            return response()->json([
                'message' => 'success',
                'id' => $timetable->id,  
            ]);
            ResponseService::successResponse('Data Stored Successfully', $timetable);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function edit($classSectionID) {
        ResponseService::noFeatureThenRedirect('Timetable Management');
        ResponseService::noPermissionThenRedirect('timetable-edit');
        $currentSemester = $this->cache->getDefaultSemesterData();
        $classSection = $this->classSection->findById($classSectionID, ['*'], ['class', 'class.stream', 'section', 'medium']);

        $subjectTeachers = $this->subjectTeacher->builder()->with('subject:id,name,type,bg_color', 'teacher:id,first_name,last_name')->whereHas('class_section', function ($q) use ($classSectionID) {
            $q->where('id', $classSectionID);
        })->whereHas('class_subject',function($q) {
            $q->whereNull('deleted_at');
        })->orderBy('subject_id', 'ASC')->CurrentSemesterData()->get();

        $subjectWithoutTeacherAssigned = $this->subject->builder()->whereHas('class_subjects', function ($q) use ($classSection) {
            $q->where('class_id', $classSection->class_id)->CurrentSemesterData();
        })->select(['id', 'name', 'type', 'bg_color'])->whereNotIn('id', $subjectTeachers->pluck('subject_id'))->get();

        $timetables = $this->timetable->builder()
                    ->where('class_section_id', $classSectionID)
                    ->with('teacher:users.id,first_name,last_name', 'subject')
                    ->CurrentSemesterData()
                    ->get();
        // Get Timetable Settings Data
        $timetableSettingsData = $this->schoolSettings->getBulkData([
            'timetable_start_time', 'timetable_end_time', 'timetable_duration'
        ]);
        $timetable_configurations = DB::table('timetable_configurations')->where('school_id',Auth::user()->school_id)->whereNull('deleted_at')->get();
        return view('timetable.edit', compact('subjectTeachers', 'subjectWithoutTeacherAssigned', 'classSection', 'timetables', 'timetableSettingsData', 'currentSemester','timetable_configurations'));
    }

    public function update(Request $request, $id) {
        ResponseService::noFeatureThenRedirect('Timetable Management');
        ResponseService::noPermissionThenRedirect(['timetable-edit']);
        $request->validate([
            'start_time' => 'required',
            'end_time'   => 'required',
            'day'        => 'required',
        ]);
        $start_time = $request->start_time;
        $end_time = $request->end_time;
        $start_time = Carbon::createFromFormat('H:i:s',$start_time)->format('H:i:s');
        $end_time = Carbon::createFromFormat('H:i:s',$end_time)->format('H:i:s');

        $schoolSettings = $this->cache->getSchoolSettings();
        $timetable_start_time = Carbon::createFromFormat('H:i:s',$schoolSettings['timetable_start_time'])->format('H:i:s');
        $timetable_end_time = Carbon::createFromFormat('H:i:s',$schoolSettings['timetable_end_time'])->format('H:i:s');
        
        // if($start_time < $timetable_start_time || $end_time > $timetable_end_time){
        //     return response()->json(['message' => 'timeError']);
        // }

        $existingTimetable = DB::table('timetables')->where('id',$id)->first();

        $confirmed = false;

        if(!empty($request->confirmed) && $request->confirmed == true){
            $confirmed = true;
        } 
        if($confirmed == false) {
            $teacherid = "SELECT teacher_id FROM subject_teachers where id = ?";
            $teacherResult = DB::select($teacherid,[$existingTimetable->subject_teacher_id]);

            if(!empty($teacherResult[0])){
            
                $check = "SELECT timetables.*, subject_teachers.teacher_id FROM timetables 
                JOIN subject_teachers ON subject_teachers.id = timetables.subject_teacher_id WHERE subject_teachers.teacher_id = ?
                AND timetables.day = ? 
                AND timetables.id != ?
                AND (
                (start_time <= ? AND end_time > ?) OR
                (start_time < ? AND end_time >= ?) OR
                (start_time >= ? AND end_time <= ?) 
                )";

                $teacherResult = DB::select($check, 
                [$teacherResult[0]->teacher_id,
                $request->day,
                $id,
                $request->start_time,
                $request->start_time,
                $request->end_time,
                $request->end_time,
                $request->start_time,
                $request->end_time]);   

                // if(!empty($teacherResult)){
                //     return response()->json(['message' => 'updateConflict', 'id' => $id,
                //     'start_time' => $request->start_time,'end_time' => $request->end_time,'day' => $request->day]);
                // }
            }

            $noTeacherid = "SELECT timetables.* FROM timetables
                LEFT JOIN subject_teachers ON subject_teachers.id = timetables.subject_teacher_id 
                WHERE subject_teachers.teacher_id IS NULL
                AND timetables.subject_id = ?
                AND timetables.day = ? 
                AND timetables.id != ?
                    AND (
                    (start_time <= ? AND end_time > ?) OR
                    (start_time < ? AND end_time >= ?) OR
                    (start_time >= ? AND end_time <= ?) 
                    )";

            $checkNoTeacher = DB::select($noTeacherid,
                                    [$existingTimetable->subject_id,
                                    $request->day,
                                    $id,
                                    $request->start_time,
                                    $request->start_time,
                                    $request->end_time,
                                    $request->end_time,
                                    $request->start_time,
                                    $request->end_time]);
        
            // if(!empty($checkNoTeacher)) {            
            //     return response()->json(['message' =>'updateNoTeacherConflict','id' => $id, 'start_time' => $request->start_time, 'end_time' => $request->end_time, 'day' => $request->day]);
            // }
        }
        
        try {
            if ($timetable_start_time <= $start_time && $timetable_end_time >= $end_time) {
                $this->timetable->updateOrCreate(['id' => $id,], $request->all());
                ResponseService::successResponse('Data Stored Successfully');
            } else {
                ResponseService::errorResponse('Please select a valid time');
            }
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function show(Request $request) {
        ResponseService::noFeatureThenRedirect('Timetable Management');
        ResponseService::noPermissionThenRedirect('timetable-list');
        $offset = request('offset', 0);
        $limit = request('limit', 10);
        $sort = request('sort', 'id');
        $order = request('order', 'DESC');

        $sql = $this->classSection->builder()->with(['class:id,name,stream_id', 'class.stream', 'section:id,name', 'medium:id,name', 'timetable' => function ($query) {
            $query->CurrentSemesterData()->with('subject:id,name,type');
        }]);
        if (!empty($request->search)) {
            $search = $request->search;
            $sql->where(function ($query) use ($search) {
                $query->orWhereHas('section', function ($q) use ($search) {
                    $q->where('name', 'LIKE', "%$search%");
                })->orWhereHas('medium', function ($q) use ($search) {
                    $q->where('name', 'LIKE', "%$search%");
                })->orWhereHas('class',function($q) use($search){
                    $q->where('name', 'LIKE', "%$search%");
                })->orWhereHas('class.stream',function($q) use($search){
                    $q->where('name', 'LIKE', "%$search%");
                });
            });
        }
        if (!empty($request->medium_id)) {
            $sql = $sql->where('medium_id', $request->medium_id);
        }
        $total = $sql->count();

        $sql->orderBy($sort, $order)->skip($offset)->take($limit);
        $res = $sql->get();

        $bulkData = array();
        $bulkData['total'] = $total;
        $rows = array();
        $no = 1;
        foreach ($res as $row) {
            $operate = BootstrapTableService::editButton(route('timetable.edit', $row->id), false);
            $operate .= BootstrapTableService::button('fa fa-trash', '#', ['delete-class-timetable', 'btn-gradient-danger'], ['title' => trans("Delete Class Timetable"), 'data-id' => $row->id]);
            $tempRow = $row->toArray();
            $timetable = $row->timetable->groupBy('day')->sortBy('start_time');
            $tempRow['no'] = $no++;
            $tempRow['Monday'] = $timetable['Monday'] ?? [];
            $tempRow['Tuesday'] = $timetable['Tuesday'] ?? [];
            $tempRow['Wednesday'] = $timetable['Wednesday'] ?? [];
            $tempRow['Thursday'] = $timetable['Thursday'] ?? [];
            $tempRow['Friday'] = $timetable['Friday'] ?? [];
            $tempRow['Saturday'] = $timetable['Saturday'] ?? [];
            $tempRow['Sunday'] = $timetable['Sunday'] ?? [];
            $tempRow['operate'] = $operate;
            $rows[] = $tempRow;
        }
        $bulkData['rows'] = $rows;
        return response()->json($bulkData);
    }

    public function destroy($id) {
        ResponseService::noFeatureThenRedirect('Timetable Management');
        ResponseService::noPermissionThenSendJson('timetable-delete');
        try {
            Timetable::find($id)->delete();
            ResponseService::successResponse('Data Deleted Successfully');
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function teacherIndex() {
        ResponseService::noFeatureThenRedirect('Timetable Management');
        ResponseService::noPermissionThenRedirect('timetable-list');

        // Get Timetable Settings Data
        $timetableSettingsData = $this->schoolSettings->getBulkData([
            'timetable_start_time', 'timetable_end_time', 'timetable_duration'
        ]);
        return view('timetable.teacher.index', compact('timetableSettingsData'));
    }

    public function teacherList(Request $request) {
        ResponseService::noFeatureThenRedirect('Timetable Management');
        ResponseService::noPermissionThenRedirect('timetable-list');
        $offset = request('offset', 0);
        $limit = request('limit', 10);
        $sort = request('sort', 'id');
        $order = request('order', 'DESC');
        $sql = $this->user->builder()->role('Teacher')->with(['timetable' => function ($query) {
            $query->CurrentSemesterData()->with('subject:id,name', 'class_section.class', 'class_section.section');
        }]);
        if (!empty($request->search)) {
            $search = $request->search;
            $sql->where(function ($query) use ($search) {
                $query->where('id', 'LIKE', "%$search%")->orwhereRaw("concat(first_name,' ',last_name) LIKE '%" . $search . "%'");
            });
        }

        $total = $sql->count();

        $sql->orderBy($sort, $order)->skip($offset)->take($limit);
        $res = $sql->get();

        $bulkData = array();
        $bulkData['total'] = $total;
        $rows = array();
        $no = 1;
        foreach ($res as $row) {
            $operate = BootstrapTableService::button('fa fa-eye', route('timetable.teacher.show', $row->id), ['btn-gradient-success'], ['title' => "View Timetable"]);
            $tempRow = $row->toArray();
            $timetable = $row->timetable->filter(function ($item) {
                return $item->class_section && is_null($item->class_section->deleted_at); 
            })->groupBy('day')->sortBy('start_time');
            $tempRow['no'] = $no++;
            $tempRow['Monday'] = $timetable['Monday'] ?? [];
            $tempRow['Tuesday'] = $timetable['Tuesday'] ?? [];
            $tempRow['Wednesday'] = $timetable['Wednesday'] ?? [];
            $tempRow['Thursday'] = $timetable['Thursday'] ?? [];
            $tempRow['Friday'] = $timetable['Friday'] ?? [];
            $tempRow['Saturday'] = $timetable['Saturday'] ?? [];
            $tempRow['Sunday'] = $timetable['Sunday'] ?? [];
            $tempRow['operate'] = $operate;
            $rows[] = $tempRow;
        }
        $bulkData['rows'] = $rows;
        return response()->json($bulkData);
    }

    public function teacherShow($teacherID) {
        ResponseService::noFeatureThenRedirect('Timetable Management');
        $teacher = $this->user->findById($teacherID, ['id', 'first_name', 'last_name']);
        $timetables = $this->timetable->builder()->whereHas('subject_teacher', function ($q) use ($teacherID) {
            $q->where('teacher_id', $teacherID);
        })->whereHas('class_section', function ($q) {
            $q->whereNull('deleted_at');
        })->with('subject:id,name,bg_color', 'class_section.class', 'class_section.section','class_section.medium')->get();

        // Get Timetable Settings Data
        $timetableSettingsData = $this->schoolSettings->getBulkData([
            'timetable_start_time', 'timetable_end_time', 'timetable_duration'
        ]);
        return view('timetable.teacher.view', compact('timetables', 'teacher', 'timetableSettingsData'));
    }

    public function updateTimetableSettings(Request $request) {
        ResponseService::noFeatureThenRedirect('Timetable Management');
        ResponseService::noPermissionThenRedirect('timetable-list');
        try {
            DB::beginTransaction();
            $settings = array(
                'timetable_start_time', 'timetable_end_time', 'timetable_duration'
            );

            $timeTableExistsBeforeStartTime = $this->timetable->builder()->where('start_time', '<', date('H:i:s', strtotime($request->timetable_start_time)))->get();
            if (!empty($timeTableExistsBeforeStartTime->toArray())) {
                ResponseService::errorResponse("Updates are prohibited as there are pre-existing lectures scheduled before " . $request->timetable_start_time);
            }

            $timeTableExistsAfterEndTime = $this->timetable->builder()->where('end_time', '>', date('H:i:s', strtotime($request->timetable_end_time)))->get();
            if (!empty($timeTableExistsAfterEndTime->toArray())) {
                ResponseService::errorResponse("Updates are prohibited as there are pre-existing lectures scheduled after " . $request->timetable_end_time);
            }

            $data = array();
            foreach ($settings as $row) {
                $data[] = [
                    "name" => $row,
                    "data" => $row == 'timetable_duration' ? Carbon::createFromTimestampUTC($request->$row * 60)->format('H:i:s') : date("H:i:s", strtotime($request->$row)),
                    "type" => 'time'
                ];
            }
            $this->schoolSettings->upsert($data, ["name"], ["data", "type"]);
            $this->cache->removeSchoolCache(config('constants.CACHE.SCHOOL.SETTINGS'));
            DB::commit();
            ResponseService::successResponse('Data Updated Successfully');
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "Timetable Controller -> updateTimetableSettings");
            ResponseService::errorResponse();
        }
    }

    public function deleteClassTimetable($id)
    {
        ResponseService::noFeatureThenRedirect('Timetable Management');
        ResponseService::noPermissionThenSendJson('timetable-delete');
        try {
            $this->timetable->builder()->where('class_section_id',$id)->delete();
            ResponseService::successResponse('Data Deleted Successfully');
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function timetable_configurations(){
        ResponseService::noFeatureThenRedirect('Timetable Management');
        ResponseService::noPermissionThenRedirect('timetable-list');
        return view('timetable.timetable_configurations');
    }

    public function timetable_configurations_store(Request $request){
        ResponseService::noFeatureThenRedirect('Timetable Management');
        ResponseService::noPermissionThenRedirect('timetable-list');
        $request->validate([
            'name'      => 'required',
            'bg_color'  => 'required|not_in:transparent',
        ]);
        try{
            $data = [
                'name' => $request->name,
                'bg_color' => $request->bg_color,
                'school_id' => Auth::user()->school_id
            ];
            if($request->hasFile('image')){
                $imageFile = $request->file('image');
                //Get Storage Size
                $imageSize = $imageFile->getSize();
                $imageSizeKB = round($imageSize / 1024, 2); // Convert size to KB
                $path = $imageFile->store('timetable_configurations', 'public');
                $data['image'] = $path;
                $data['file_size'] = $imageSizeKB;
            }
            DB::table('timetable_configurations')->insert($data);
            ResponseService::successResponse('Data Stored Successfully');
        }catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();  
        }
    }

    public function timetable_configurations_show(Request $request) {
        ResponseService::noFeatureThenRedirect('Timetable Management');
        ResponseService::noPermissionThenRedirect('timetable-list');
        $offset = request('offset', 0);
        $limit = request('limit', 10);
        $sort = request('sort', 'id');
        $order = request('order', 'DESC');
        $search = $_GET['search'];
        $showDeleted = $request->show_deleted;

        $query = DB::table('timetable_configurations')
            ->select('*') 
            ->when($search, function ($q) use ($search) {
                $q->where(function ($query) use ($search) {
                    $query->where('id', 'LIKE', "%$search%")
                        ->orWhere('name', 'LIKE', "%$search%")
                        ->orWhere('bg_color', 'LIKE', "%$search%");
                });
            })
            ->where('school_id',Auth::user()->school_id)
            ->when($showDeleted, function ($q) {
                $q->whereNotNull('deleted_at');
            },function ($q) {
                $q->whereNull('deleted_at');
            });

        $total = $query->count();

        $sql = $query->orderBy($sort, $order)->skip($offset)->take($limit);
        $res = $sql->get();

        $bulkData = array();
        $bulkData['total'] = $total;
        $rows = array();
        $no = 1;

        foreach ($res as $row) {
            if ($request->show_deleted) {
                $operate = BootstrapTableService::restoreButton(route('timetable.timetable_configurations.restore', $row->id));
                $operate .= BootstrapTableService::deleteButton(route('timetable.timetable_configurations.destroy', $row->id));
            } else {
                $operate = BootstrapTableService::editButton(route('timetable.timetable_configurations.update', $row->id));
                $operate .= BootstrapTableService::deleteButton(route('timetable.timetable_configurations.delete', $row->id));
            }
            $tempRow = (array) $row;
            $tempRow['no'] = $no++;
            $tempRow['operate'] = $operate;
            $rows[] = $tempRow;
        }

        $bulkData['rows'] = $rows;
        return response()->json($bulkData);
    }

    public function timetable_configurations_update(Request $request,$id){
        ResponseService::noFeatureThenRedirect('Timetable Management');
        ResponseService::noPermissionThenRedirect('timetable-list');
        $request->validate([
            'name'      => 'required',
            'bg_color'  => 'required|not_in:transparent',
        ]);
        try{
            $data = [
                'name' => $request->name,
                'bg_color' => $request->bg_color,
            ];

            if($request->hasFile('image')){
                if (FileSizeLimitService::checkFileSizeLimit(Auth::user()->school_id)) {
                    return ResponseService::errorResponse('storage capacity not enough');
                }
                $imageFile = $request->file('image');
                //Get Storage Size
                $imageSize = $imageFile->getSize();
                $imageSizeKB = round($imageSize / 1024, 2); // Convert size to KB
                $path = $imageFile->store('timetable_configurations', 'public');
                $data['image'] = $path;
                $data['file_size'] = $imageSizeKB;
                if($path){
                    $oldImagePath = DB::table('timetable_configurations')->where('id',$id)->value('image');
                    if ($oldImagePath && Storage::disk('public')->exists($oldImagePath)) {
                        Storage::disk('public')->delete($oldImagePath);
                        // dd($oldImagePath);
                    }
                }
            }
            DB::table('timetable_configurations')->where('id',$id)->update($data);
            ResponseService::successResponse('Data Updated Successfully');
        }catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();  
        }
    }

    public function timetable_configurations_delete($id){
        ResponseService::noFeatureThenRedirect('Timetable Management');
        ResponseService::noPermissionThenRedirect('timetable-list');
        try{
            DB::table('timetable_configurations')->where('id',$id)->update(['deleted_at' => now()]);
            ResponseService::successResponse('Data Deleted Successfully');
        }catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();  
        }
    }

    public function timetable_configurations_restore($id){
        ResponseService::noFeatureThenRedirect('Timetable Management');
        ResponseService::noPermissionThenRedirect('timetable-list');
        try{
            DB::table('timetable_configurations')->where('id',$id)->update(['deleted_at' => null]);
            ResponseService::successResponse('Data Restored Successfully');
        }catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();  
        }
    }
    public function timetable_configurations_destroy($id){
        ResponseService::noFeatureThenRedirect('Timetable Management');
        ResponseService::noPermissionThenRedirect('timetable-list');
        try{
            $oldImagePath = DB::table('timetable_configurations')->where('id',$id)->value('image');
            if ($oldImagePath && Storage::disk('public')->exists($oldImagePath)) {
                Storage::disk('public')->delete($oldImagePath);
            }
            DB::table('timetable_configurations')->where('id',$id)->delete();
            ResponseService::successResponse('Data Deleted Successfully');
        }catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();  
        }
    }
}
