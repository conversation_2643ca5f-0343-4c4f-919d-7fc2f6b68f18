<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class LeaveCategory extends Model
{
    use HasFactory;
    protected $table = 'leave_category';

    protected $fillable = ['name','description','school_id','created_id','updated_at','deleted_at'];
    public function leaves()
    {
        return $this->hasMany(Leave::class);
    }
}
