<?php

namespace App\Exports;

use App\Models\StudentDeposit;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class StudentDepositExport implements FromCollection, WithHeadings, WithMapping, ShouldAutoSize
{
    public function collection()
    {
        return StudentDeposit::with(['student', 'depositType'])->get();
    }

    public function headings(): array
    {
        return [
            'Student Name',
            'IC Number',
            'Deposit Type',
            'Amount',
            'Remaining Amount',
            'Status',
            'Created At'
        ];
    }

    public function map($deposit): array
    {
        return [
            $deposit->student->name ?? '',
            $deposit->student->ic_number ?? '',
            $deposit->depositType->name ?? '',
            number_format($deposit->amount, 2),
            number_format($deposit->remaining_amount, 2),
            ucfirst($deposit->status),
            $deposit->created_at->format('Y-m-d H:i:s')
        ];
    }
}