<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class StudentFeeType extends Model
{
    use HasFactory;

    protected $fillable = [
        'class_id',
        'school_id',
        'student_id',
        'item_code',
        'fees_type_name',
        'unit_price',
        'classification_code',
        'unit',
        'quantity',
        'discount',
        'tax',
        'remarks',
    ];

    public function student() {
        return $this->belongsTo(Students::class,'student_id')->withTrashed();
    }

    public function class() {
        return $this->belongsTo(ClassSchool::class)->withTrashed();
    }

    public function scopeOwner($query) {
        if (Auth::check()) {
            if (Auth::user()->hasRole('Super Admin')) {
                return $query;
            }

            if (Auth::user()->hasRole('School Admin') || Auth::user()->hasRole('Teacher')) {
                return $query->where('school_id', Auth::user()->school_id);
            }

            if (Auth::user()->hasRole('Student')) {
                return $query->where('school_id', Auth::user()->school_id);
            }
        }

        return $query;
    }
}
