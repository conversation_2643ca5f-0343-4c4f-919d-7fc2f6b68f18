<?php

namespace App\Http\Controllers;

use PDO;
use Exception;
use Throwable;
use Carbon\Carbon;
use App\Models\Credit;
use App\Models\Reward;
use Illuminate\Http\Request;

use App\Models\StudentFeeType;
use App\Services\CachingService;
use App\Services\ResponseService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\StudentFeeTypesExport;
use App\Imports\StudentFeeTypesImport;

use App\Services\BootstrapTableService;
use App\Repositories\Reward\RewardInterface;
use App\Repositories\Student\StudentInterface;
use App\Repositories\FeesType\FeesTypeInterface;
use App\Repositories\ClassSchool\ClassSchoolInterface;
use App\Repositories\StudentFees\StudentFeesInterface;
use App\Repositories\ClassSection\ClassSectionInterface;
use App\Repositories\StudentFeesDetail\StudentFeesDetailInterface;

class CreditController extends Controller
{
    // Initializing the schools Repository
    // private CreditInterface $creditTable;
    private StudentInterface $student;
    private FeesTypeInterface $feesType;
    private ClassSchoolInterface $class;
    private CachingService $cache;
    private ClassSectionInterface $classSection;
    private StudentFeesInterface $studentFees;
    private StudentFeesDetailInterface $studentFeesDetail;



    public function __construct(
        // CreditInterface $creditTable,
        CachingService $cache,
        ClassSchoolInterface $classSchool,
        StudentInterface $student,
        ClassSectionInterface $classSection,
        StudentFeesInterface $studentFees,
        StudentFeesDetailInterface $studentFeesDetail
    ) {

        // $this->credit_system = $creditTable;
        $this->cache = $cache;
        $this->class = $classSchool;
        $this->student = $student;
        $this->classSection = $classSection;
        $this->studentFees = $studentFees;
        $this->studentFeesDetail = $studentFeesDetail;

    }

    public function manageCreditIndex($id = null)
    {

        $classes = $this->class->all(['*'], ['stream', 'medium']);
        $simpleClassDropdown = $this->class->builder()->pluck('name', 'id');

        $query = DB::table('credit_system as c')
            ->join('students as s', 'c.user_id', '=', 's.user_id')
            ->join('users as u', 's.user_id', '=', 'u.id')
            ->select(
                'c.id',
                's.id',
                DB::raw("CONCAT(u.first_name, ' ', u.last_name) AS full_name"),
                'c.updated_at',
                'c.detail',
                DB::raw('SUM(c.credit_amount) AS balance')
            )
            ->groupBy('c.id', 'u.first_name', 'u.last_name', 'c.updated_at', 'c.detail');

        if (isset($id)) {
            $query->where('s.id', $id);
            $id = $query->pluck('s.id')->first();
        }


        return response(view('credit.manage_credit', compact('simpleClassDropdown', 'classes', 'id')));
    }


    public function viewCreditIndex()
    {
        $classes = $this->class->all(['*'], ['stream', 'medium', 'stream']);
        $simpleClassDropdown = $this->class->builder()->pluck('name', 'id');
        return view('credit.view_credit', compact('simpleClassDropdown', 'classes'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'class_id' => 'required|numeric',
            'student_id' => 'required|array',
            'student_id.*' => 'required|numeric',
            'credit_amount' => 'required|numeric',
            'detail' => 'nullable|string'
        ]);

        try {
            // Check if any selected student has credit_status = 1
            $invalidStudents = DB::table('students')
                ->whereIn('students.id', $request->student_id)  // Specify students.id
                ->where('credit_status', 1)
                ->select(DB::raw("CONCAT(users.first_name, ' ', users.last_name) AS name"))
                ->join('users', 'students.user_id', '=', 'users.id')
                ->pluck('name');

            if ($invalidStudents->isNotEmpty()) {
                return ResponseService::errorResponse('Cannot process credits for students: ' . $invalidStudents->implode(', '));
            }

            $sessionYear = $this->cache->getDefaultSessionYear();
            // Loop through student_id and store data
            foreach ($request->student_id as $studentId) {
                $userId = DB::table('students')
                    ->where('id', $studentId)
                    ->pluck('user_id')
                    ->first();

                $updatedAt = Carbon::now();
                $type = $request->type;
                $credits_amount = (float) $request->credit_amount;


                if ($type === 'debit') {
                    $credits_amount = $credits_amount * -1;
                }

                $totalBalance = DB::table('credit_system')
                    ->where('user_id', $userId)
                    ->sum('credit_amount');

                $balance = $totalBalance + $credits_amount;

                Credit::create([
                    'school_id' => Auth::user()->school_id,
                    'class_id' => $request->class_id,
                    'user_id' => $userId,
                    'credit_amount' => $credits_amount,
                    'detail' => $request->detail ?? '',
                    'balance' => $balance
                ]);

                if (empty($request->student_id)) {
                    return response()->json(['message' => 'No student IDs provided']);
                }

                if($type != 'debit' && $request->generate_invoice == '1'){
                    $studentId = DB::table('students')->where('user_id',$userId)->value('id');
                    $fees = $this->studentFees->create([
                        'name'               => "Credit Topup",
                        'class_id'           => $request->class_id,
                        'school_id'          => Auth::user()->school_id,
                        'session_year_id'    => $sessionYear->id,
                        'due_date'           => Carbon::now(),
                        'student_id'         => $studentId,
                        'status'             => 'published',
                        'created_at_draft'   => now()
                    ]);

                    $latestUID = DB::table('student_fees')->where('school_id', Auth::user()->school_id)->where('status', 'published')->whereNull('deleted_at')->select('uid')->orderBy('uid', 'desc')->value('uid');
                    $uid = $latestUID ? $latestUID + 1 : 1;
                    // dd($uid);
                    while (DB::table('student_fees')
                        ->where('uid', $uid)
                        ->where('school_id', Auth::user()->school_id)
                        ->where('status', 'published')
                        ->whereNull('deleted_at')
                        ->exists()
                    ) {
                        $uid++;
                    }
                    DB::table('student_fees')->where('id', $fees->id)->update(["uid" => $uid]);
                    $feeDetail = array();
                    $feeDetail[] = array(
                        "student_fees_id"       => $fees->id,
                        "fees_type_name"        => "Credit",
                        "fees_type_amount"      => $credits_amount,
                        'classification_code'   => '010 - Education fees',
                        'unit'                  => 'E48 - service unit',
                        'quantity'              => 1,
                        // "school_id"             => Auth::user()->school_id,
                    );
                    
                    if (COUNT($feeDetail) > 0) {
                        $this->studentFeesDetail->upsert($feeDetail, ['student_fees_id'], ['fees_type_name', 'fees_type_amount', 'classification_code', 'unit', 'quantity']);
                    }

                    DB::table('student_fees_paids')
                    ->insert([
                        'school_id' => Auth::user()->school_id,
                        'student_fees_id' => $fees->id,
                        'mode' => 1,
                        'is_fully_paid' => 1,
                        'amount' => $credits_amount,
                        'due_charges' => 0,
                        'status' => 1,
                        'date' => Carbon::now(),
                        'created_at' => Carbon::now(),
                        'updated_at' => Carbon::now(),
                    ]);
                }

                $notifyUser = DB::table('students')
                ->where('id', $studentId)
                ->select('user_id')
                ->get()
                ->pluck('user_id');
        
         
                if ($notifyUser !== null){
                    $guardianIds = collect();
                    foreach ($notifyUser as $user){
                        $guardianId = DB::table('students')
                        ->where('user_id', $user)
                        ->pluck('guardian_id');
                        $guardianIds = $guardianIds->merge($guardianId);
                    }

                    $notifyUser = $notifyUser->merge($guardianIds);
                    if($credits_amount > 0){
                    $title = 'Credits Added!';
                    $body = $credits_amount . ' credits have been successfully added to your account.';
                    $type = "Notification";
                    send_notification($notifyUser, $title, $body, $type);
                    }else{
                    $title = 'Credits Deducted!';
                    $body = $credits_amount . ' credits have been successfully deducted from your account.';
                    $type = "Notification";
                    send_notification($notifyUser, $title, $body, $type);
                    }
                  
                }

            }
            ResponseService::successResponse('Data Stored Successfully');
        } catch (Throwable $e) {
            DB::rollback();
            ResponseService::logErrorResponse($e, "CreditController -> Store Method");
            ResponseService::errorResponse();
        }
    }

    public function manageCreditShow(Request $request,$id = null)
    {
        $offset = request('offset', 0);
        $limit = request('limit', 10);
        $sort = request('sort', 'id');
        $order = request('order', 'DESC');
        $search = request('search');
        $showDeleted = request('show_deleted');
        $classId = (int)request('class_id');
        $studentId = (int)request('student_id');
        $startDate = request('start_date');
        $endDate = request('end_date');

        $userId = DB::table('students')
            ->where('id', $studentId)
            ->pluck('user_id')
            ->first();

        $query = DB::table('credit_system as c')
            ->join('students as s', 'c.user_id', '=', 's.user_id')
            ->join('users as u', 's.user_id', '=', 'u.id')
            ->select(
                'c.id',
                DB::raw('ROW_NUMBER() OVER(ORDER BY c.updated_at DESC) as no'),
                DB::raw("concat(u.first_name, ' ', u.last_name) as full_name"),
                'c.updated_at',
                'c.detail',
                'c.credit_amount',
                'c.balance'
            )
            ->where('c.school_id', Auth::user()->school_id);
            
            if ($studentId) {
                $query->where('s.id', $studentId);
            } else if (isset($id)) {
                $query->where('s.id', $id);
                $id = $query->pluck('s.id')->first();
            }
            
            if ($classId) { 
                $query->where('class_id',$classId);
            }

        if ($startDate && $endDate) {
            $query->whereBetween(DB::raw('DATE(c.updated_at)'), [date('Y-m-d', strtotime($startDate)), date('Y-m-d', strtotime($endDate))]);
        }

        $total = $query->count(); // Get total records based on current filters

        $query->orderBy('c.created_at', $order);

        $records = $query->offset($offset)
            ->limit($limit)
            ->get();


        $bulkData = [];
        $bulkData['total'] = $total;
        $rows = [];
        $no = $offset + 1;


        foreach ($records as $row) {
            $operate = '';
            $operate .= BootstrapTableService::editButton(route('credit.edit', $row->id), false);
            $operate .= BootstrapTableService::deleteButton(route('credit.delete', $row->id));
            $tempRow = (array) $row; // Convert object to array
            $tempRow['operate'] = $operate;
            $tempRow['no'] = $no++; // Add row number
            $rows[] = $tempRow; // Add the row to the array
        }

        $bulkData['rows'] = $rows;

        // dd($bulkData);
        return response()->json($bulkData);
    }

    public function viewCreditShow(Request $request)
    {
        $offset = request('offset', 0);
        $limit = request('limit', 10);
        $order = request('order', 'DESC');
        $classId = (int)request('class_id');
        $studentId = (int)request('student_id');

        $latestTime = Carbon::now();

        $latestCredits = DB::table('credit_system')
            ->select('user_id', DB::raw('MAX(id) as latest_id'))
            ->groupBy('user_id');

        $query = DB::table('credit_system as c')
            ->join('students as s', 'c.user_id', '=', 's.user_id')
            ->join('users as u', 's.user_id', '=', 'u.id')
            ->joinSub($latestCredits, 'latest', function ($join) {
                $join->on('c.id', '=', 'latest.latest_id');
            })
            ->select(
                'c.id',
                's.id as student_id',
                DB::raw("CONCAT(u.first_name, ' ', u.last_name) AS full_name"),
                'c.updated_at',
                'c.detail',
                'c.balance'  
            )
            ->where('c.school_id', Auth::user()->school_id);

        if ($studentId) {
            $query->where('s.id', $studentId);
        }
        
        if ($classId) {
            $query->where('c.class_id', $classId); 
        }

        $total = $query->count();

        $records = $query->orderBy('c.updated_at', $order)
            ->offset($offset)
            ->limit($limit)
            ->get();

        $bulkData = [];
        $bulkData['total'] = $total;
        $rows = [];
        $no = $offset + 1;

        foreach ($records as $row) {
            $operate = '';
            $operate .= BootstrapTableService::editButton(route('credit.edit', $row->id), false);
            $operate .= BootstrapTableService::deleteButton(route('credit.delete', $row->id));
            $tempRow = (array) $row;
            $tempRow['operate'] = $operate;
            $tempRow['no'] = $no++;
            $rows[] = $tempRow;
        }

        $bulkData['rows'] = $rows;

        return response()->json($bulkData);
    }


    public function edit($id)
    {

        $credit = DB::table('credit_system')->where('id', $id)->first();
        if (!$credit) {
            ResponseService::logErrorResponse($credit, 'Credit entry not found.');
            return redirect()->route('credit.index')->withErrors('Credit entry not found.');
        }
        $classes = $this->class->all(['*'], ['stream', 'medium', 'stream']);

        $userId = $credit->user_id;
        $student = DB::table('students as s')
            ->join('users as u', 's.user_id', '=', 'u.id')
            ->join('class_sections as cs', 's.class_section_id', '=', 'cs.id')
            ->join('classes as c', 'cs.class_id', '=', 'c.id')
            ->select('s.*', 'u.first_name', 'u.last_name', 'c.name as class_name')
            ->where('s.user_id', $userId)
            ->first();

        $selectedClass = DB::table('classes')->where('id', $credit->class_id)->first();

        return view('credit.edit', compact('credit', 'classes', 'student', 'selectedClass'));
    }

    public function update(Request $request, $id)
    {

        $request->validate([
            'credit_amount' => 'required|numeric',
            'detail' => 'nullable|string',
            'type' => 'required|in:credit,debit',
        ]);

        try {
            DB::beginTransaction();

            $credit = DB::table('credit_system')->where('id', $id)->first();
            if (!$credit) {
                ResponseService::logErrorResponse($credit, 'Credit entry not found.');
                return redirect()->route('credit.index')->withErrors('Credit entry not found.');
            }


            $userId = $credit->user_id;

            $current_balance = DB::table('credit_system')
                ->where('user_id', $userId)
                ->sum('credit_amount');


            $initial_type = $request->type;
            $initial_points = $credit->credit_amount;

            if ($initial_type == 'debit' && $initial_points > 0) {
                $initial_points *= -1;
            }

            $new_type = $request->type;
            $new_points = $request->credit_amount;

            if ($new_type == 'debit' && $new_points > 0) {
                $new_points *= -1;
            }

            $current_balance = $current_balance - $initial_points + $new_points;

            DB::table('credit_system')->where('id', $id)->update([
                'credit_amount' => $new_points,
                'detail' => $request->detail,
                'balance' => $current_balance,
                'updated_at' => Carbon::now()
            ]);

            DB::commit();
            ResponseService::successRedirectResponse(route('credit.manage-credit'), 'Data Update Successfully');
        } catch (Throwable $e) {
            DB::rollback();
            ResponseService::logErrorResponse($e, 'Update failed: ' . $e->getMessage());
            ResponseService::errorRedirectResponse();
        }

        
    }

    public function getStudent(Request $request, $class_id)
    {
        $classStudents = DB::table('students as s')
            ->join('class_sections as cs', 's.class_section_id', '=', 'cs.id')
            ->join('users as u', 's.user_id', '=', 'u.id')
            ->select(
                's.id',
                DB::raw("CONCAT(u.first_name,' ', u.last_name) AS fullname"),
                's.school_id'
            )
            ->where('cs.class_id', $class_id)
            ->where('s.session_year_id', $this->cache->getDefaultSessionYear()->id)
            ->where('u.status', 1)
            ->get();

        return response()->json($classStudents);
    }

    public function deleteCredit($id)
    {
        try {
            DB::beginTransaction();
            DB::table('credit_system')->where('id', $id)->delete();
            DB::commit();
            return ResponseService::successResponse("Data Deleted Successfully");
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "CreditController -> deleteCredit Method");
            return ResponseService::errorResponse();
        }
    }
}
