<?php

namespace App\Console\Commands;

use chillerlan\QRCode\QRCode;
use App\Helpers\EInvoiceHelper;
use Illuminate\Console\Command;
use App\Services\CachingService;
use App\Services\ResponseService;
use Illuminate\Support\Facades\DB;
use App\Models\SelfBillingEInvoice;
use Illuminate\Support\Facades\Log;

class getEinvoiceSubmission extends Command
{
    private CachingService $cache;
    public function __construct(CachingService $cache)
    {
        parent::__construct();
        $this->cache = $cache;
    }
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'getEinvoiceSubmission:cron';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */

    private function getSelfBillingSubmission(){
        try {
            $selfBillingEinvoices = SelfBillingEInvoice::where('status', 0)->get();
            foreach($selfBillingEinvoices as $selfBillingEinvoice){
                if(!empty($selfBillingEinvoice->submission_uid)){
                    $selfBilling = DB::table('self_billing')->where('id', $selfBillingEinvoice->self_billing_id)->first();
                    if($selfBilling) {
                        $result = EInvoiceHelper::getSubmission($selfBilling->school_id, $selfBillingEinvoice->submission_uid);
                        if ($result['success']) {
                            $submissionData = $result['data'];
                            $status = '';
                            if ($submissionData['overallStatus'] == 'Invalid') {
                                $status = 2;
                            } else if ($submissionData['overallStatus'] == 'Valid') {
                                $status = 1;
                            }

                            $updateData = [
                                'status' => $status,
                                'document_summary' => json_encode($submissionData['documentSummary'])
                            ];
                            if($submissionData['documentSummary']){
                                $documentSummary = $submissionData['documentSummary'][0];
                                if($documentSummary['uuid']){
                                    $updateData['uuid'] = $documentSummary['uuid'];
                                }
                                
                                if($documentSummary['longId']){
                                    $updateData['long_id'] = $documentSummary['longId'];
                                }

                                if($documentSummary['internalId']){
                                    $updateData['internal_id'] = $documentSummary['internalId'];
                                }
                            }
                            
                            if ($status == 2) {
                                $updateData['deleted_at'] = now();
                            }
                            
                            SelfBillingEInvoice::where('id', $selfBillingEinvoice->id)->update($updateData);
                        } else {
                            Log::error('Error in self billing submission validation: ' . json_encode($result['errors']));
                        }
                    }
                }
            }
        } catch (\Throwable $th) {
            Log::error('Error in self billing submission: ' . $th->getMessage(), [
                'exception' => $th,
                'stack_trace' => $th->getTraceAsString(),
            ]);
        }
    }
    
    public function handle()
    {
        try { 
            // Process self billing submissions
            $this->getSelfBillingSubmission();
            
            //Get Submission that is not validated
            $studentFee=DB::table('student_fees_einvoice')->where('status',0)->whereNull('deleted_at')->get();

            $creditNote=DB::table('credit_note_einvoice')->where('status',0)->whereNull('deleted_at')->get();
            $debitNote=DB::table('debit_note_einvoice')->where('status',0)->whereNull('deleted_at')->get();
            $refundNote=DB::table('refund_note_einvoice')->where('status',0)->whereNull('deleted_at')->get();
         
          
            
            $documents = $studentFee->merge($creditNote)->merge($debitNote)->merge($refundNote);
            foreach($documents as $document){
                if(isset($document->student_fees_id)){
                    $studentFees = DB::table('student_fees')->where('id',$document->student_fees_id)->first();
                } else if(isset($document->credit_note_id)){
                    $studentFees = DB::table('credit_note')->where('id',$document->credit_note_id)->first();
                } else if(isset($document->debit_note_id)){
                    $studentFees = DB::table('debit_note')->where('id',$document->debit_note_id)->first();
                } else if(isset($document->refund_note_id)){
                    $studentFees = DB::table('refund_note')->where('id',$document->refund_note_id)->first();
                }
                if($studentFees){
                    $schoolSettings = $this->cache->getSchoolSettings(['*'],$studentFees->school_id);
                    if($schoolSettings) {
                        $accessToken = '';
                        $clientId = '';
                        $client_secret = array();
                        if($schoolSettings['client_id']) {
                            $clientId = $schoolSettings['client_id'];
                        }
                        if($schoolSettings['client_secret_1']){
                            $client_secret[] = $schoolSettings['client_secret_1'];
                        }
                        if($schoolSettings['client_secret_2']){
                            $client_secret[] = $schoolSettings['client_secret_2'];
                        }
                        if(!empty($clientId) && count($client_secret) > 0) {
                            $student = DB::table('students')->where('id',$studentFees->student_id)->first();
                            if($student !== null){
                                $e_invoice_guardian = DB::table('e_invoice_guardian')->where('guardian_id',$student->guardian_id)->first();
                                if($e_invoice_guardian){
                                    $accessTokenExist = DB::table('access_token_einvoice')->where('guardian_id',$e_invoice_guardian->guardian_id)->orderBy('id','desc')->first();
                                    if($accessTokenExist && $accessTokenExist->time_expired > now()){
                                        $accessToken = $accessTokenExist->access_token; 
                                    } else {
                                        foreach ($client_secret as $secret){
                                            $url = 'https://api.myinvois.hasil.gov.my/connect/token';
                                            // dd($e_invoice->tax_identification_number);
                                            $headers = ['onbehalfof:'.$e_invoice_guardian->tax_identification_number];
                                            $fields = [
                                                'client_id' => $clientId,
                                                'client_secret' => $secret,
                                                'grant_type'    => 'client_credentials',
                                                'scope'         => 'InvoicingAPI'
                                            ];
                                            $encodedFields = http_build_query($fields);
                                            $ch = curl_init();
                                            curl_setopt($ch, CURLOPT_URL, $url);
                                            curl_setopt($ch, CURLOPT_POST, true);
                                            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
                                            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                                            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
                                            curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
                                            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                                            curl_setopt($ch,CURLOPT_POSTFIELDS, $encodedFields);
                                            $result = curl_exec($ch);
                                            if($result == false){
                                                ResponseService::errorResponse("Unable to connect to submit e-invoice");
                                                die('Curl failed: ' . curl_error($ch));
                                            }else {
                                                $data = json_decode($result);
                                                if(!isset($data->error)){
                                                    if(!empty($data->access_token)){
                                                        $accessToken = $data->access_token;
                                                        DB::table('access_token_einvoice')->insert([
                                                            'access_token' => $accessToken,
                                                            'guardian_id' => $e_invoice_guardian->guardian_id,
                                                        ]);
                                                        break;
                                                    }
                                                }
                                            }
                                            curl_close($ch);
                                        }
                                    }
                                    if($accessToken){
                                        $url = 'https://api.myinvois.hasil.gov.my/api/v1.0/documentsubmissions/'.$document->submission_uid;
                                        $headers = [
                                            'authorization: Bearer '.$accessToken,
                                            'Content-Type:application/json',
                                        ];
                                        $ch = curl_init();
                                        curl_setopt($ch, CURLOPT_URL, $url);
                                        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
                                        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                                        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
                                        curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
                                        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                                        $result = curl_exec($ch);
                                        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                                        curl_close($ch);
                                        $resultData = json_decode($result);
                                        if($httpCode == 200){
                                            $status = '';
                                            if($resultData->overallStatus == 'Invalid'){
                                                $status = 2;
                                            } else if($resultData->overallStatus =='Valid') {
                                                $status = 1;
                                            }
                                            $data = [
                                                'status' => $status,
                                                'document_summary' => json_encode($resultData->documentSummary)
                                            ];

                                            if(isset($document->student_fees_id)){
                                                DB::table('student_fees_einvoice')->where('id',$document->id)->update($data);
                                            } else if(isset($document->credit_note_id)){
                                                DB::table('credit_note_einvoice')->where('id',$document->id)->update($data);

                                            } else if(isset($document->debit_note_id)){
                                                DB::table('debit_note_einvoice')->where('id',$document->id)->update($data);

                                            } else if(isset($document->refund_note_id)){
                                                DB::table('refund_note_einvoice')->where('id',$document->id)->update($data);

                                            }
 
                                        }
                                    }
                                }
                            }else{
                                        $accessTokenExist = DB::table('access_token_einvoice')->where('consolidate_id',$document->id)->orderBy('id','desc')->first();        
                                        if($accessTokenExist && $accessTokenExist->time_expired > now()){
                                        $accessToken = $accessTokenExist->access_token; 
                                    } else {
                                        foreach ($client_secret as $secret){
                                            $url = 'https://api.myinvois.hasil.gov.my/connect/token';
                                            // dd($e_invoice->tax_identification_number);
                                            $headers = ['onbehalfof:'.'EI00000000010'];
                                            $fields = [
                                                'client_id' => $clientId,
                                                'client_secret' => $secret,
                                                'grant_type'    => 'client_credentials',
                                                'scope'         => 'InvoicingAPI'
                                            ];
                                            $encodedFields = http_build_query($fields);
                                            $ch = curl_init();
                                            curl_setopt($ch, CURLOPT_URL, $url);
                                            curl_setopt($ch, CURLOPT_POST, true);
                                            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
                                            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                                            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
                                            curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
                                            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                                            curl_setopt($ch,CURLOPT_POSTFIELDS, $encodedFields);
                                            $result = curl_exec($ch);
                                            if($result == false){
                                                ResponseService::errorResponse("Unable to connect to submit e-invoice");
                                                die('Curl failed: ' . curl_error($ch));
                                            }else {
                                                $data = json_decode($result);
                                                if(!isset($data->error)){
                                                    if(!empty($data->access_token)){
                                                        $accessToken = $data->access_token;
                                                        DB::table('access_token_einvoice')->insert([
                                                            'access_token' => $accessToken,
                                                            'consolidate_id' => $document->id,
                                                        ]);
                                                        break;
                                                    }
                                                }
                                            }
                                            curl_close($ch);
                                        }
                                    }
                                    if($accessToken){
                                        $url = 'https://api.myinvois.hasil.gov.my/api/v1.0/documentsubmissions/'.$document->submission_uid;
                                        $headers = [
                                            'authorization: Bearer '.$accessToken,
                                            'Content-Type:application/json',
                                        ];
                                        $ch = curl_init();
                                        curl_setopt($ch, CURLOPT_URL, $url);
                                        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
                                        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                                        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
                                        curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
                                        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                                        $result = curl_exec($ch);
                                        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                                        curl_close($ch);
                                        $resultData = json_decode($result);
                                        if($httpCode == 200){
                                            $status = '';
                                            if($resultData->overallStatus == 'Invalid'){
                                                $status = 2;
                                            } else if($resultData->overallStatus =='Valid') {
                                                $status = 1;
                                            }
                                            $data = [
                                                'status' => $status,
                                                'document_summary' => json_encode($resultData->documentSummary)
                                            ];
                                            if($status == 2){
                                                $data['deleted_at'] = now();
                                            }
                                            if(isset($document->student_fees_id)){
                                                DB::table('student_fees_einvoice')->where('id',$document->id)->update($data);
                                            } else if(isset($document->credit_note_id)){
                                                DB::table('credit_note_einvoice')->where('id',$document->id)->update($data);

                                            } else if(isset($document->debit_note_id)){
                                                DB::table('debit_note_einvoice')->where('id',$document->id)->update($data);

                                            } else if(isset($document->refund_note_id)){
                                                DB::table('refund_note_einvoice')->where('id',$document->id)->update($data);

                                            }
 
                                        }
                                    }
                            }
                        }
                    }
                }
            }
            // Log::info('Get Submission Successfully');

        } catch (\Throwable $th) {
            Log::error('Error in submission einvoice cron job: ' . $th->getMessage(), [
                'exception' => $th,
                'stack_trace' => $th->getTraceAsString(),
            ]);
        }
    }
}
