<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;

class StudentFee extends Model {
    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'name',
        'due_date',
        'due_charges',
        'due_charges_amount',
        'early_date',
        'early_offer',
        'early_offer_amount',
        'recurring_invoice',
        'current_cycle',
        'total_cycles',
        'class_id',
        'school_id',
        'session_year_id',
        'student_id',
        'recurring_reference',
        'tax_type',
        'tax_percentage',
        'created_at',
        'created_at_draft',
        'updated_at',
        'uid',
        'status',
        'invoice_date',
        'auto_publish_date',
        'disable_recurring',
        'long_id',
        'internal_id'
    ];
    protected $appends = ['total_compulsory_fees', 'total_optional_fees', 'compulsory_fees', 'optional_fees'];

    //'compulsory_fees','optional_fees',



    public function student_fees_paid() {
        return $this->hasMany(StudentFeesPaid::class, 'student_fees_id')->withTrashed();
    }

    public function class() {
        return $this->belongsTo(ClassSchool::class)->withTrashed();
    }

    public function session_year() {
        return $this->belongsTo(SessionYear::class)->withTrashed();
    }

    public function student() {
        return $this->belongsTo(Students::class,'student_id')->withTrashed();
    }

    public function getTotalCompulsoryFeesAttribute() {
        if ($this->relationLoaded('student_fees_details')) {
            $compulsoryFees = $this->student_fees_details->filter(function ($data) {
                return $data->optional == 0;
            });
            return $compulsoryFees->sum('fees_type_amount');
        }
        return null;
    }

    public function getTotalOptionalFeesAttribute() {
        if ($this->relationLoaded('student_fees_details')) {
            $optionalFees = $this->student_fees_details->filter(function ($data) {
                return $data->optional == 1;
            });
            return $optionalFees->sum('fees_type_amount');
        }
        return null;
    }


    public function getCompulsoryFeesAttribute() {
        if ($this->relationLoaded('student_fees_details')) {
            $compulsoryFees = $this->student_fees_details->filter(function ($data) {
                return $data->optional == 0;
            });
            // Reset the keys
            $compulsoryFees = $compulsoryFees->values();

            return $compulsoryFees;
        }
        return null;
    }

    public function getOptionalFeesAttribute() {
        if ($this->relationLoaded('student_fees_details')) {
            $optionalFees = $this->student_fees_details->filter(function ($data) {
                return $data->optional == 1;
            });
            // Reset the keys
            $optionalFees = $optionalFees->values();

            return $optionalFees;
        }
        return null;
    }

    public function getDueDateAttribute($value) {
        //        $data = getSchoolSettings('date_format');
        return date('d-m-Y', strtotime($value));
    }

    protected function setDueDateAttribute($value) {
        $this->attributes['due_date'] = date('Y-m-d', strtotime($value));
    }

    public function scopeOwner($query) {
        if (Auth::check()) {
            if (Auth::user()->hasRole('Super Admin')) {
                return $query;
            }

            if (Auth::user()->hasRole('School Admin') || Auth::user()->hasRole('Teacher')) {
                return $query->where('school_id', Auth::user()->school_id);
            }

            if (Auth::user()->hasRole('Student')) {
                return $query->where('school_id', Auth::user()->school_id);
            }
        }

        return $query;
    }

    public function student_fees_details() {
        return $this->hasMany(StudentFeesDetail::class, 'student_fees_id');
    }

    protected static function boot() {
        parent::boot();
        
        static::deleting(function($studentFee) {
            // Set uid to null when soft deleting
            if (!$studentFee->isForceDeleting()) {
                $studentFee->uid = null;
                $studentFee->save();
            }
            
            // You can also handle related records here if needed
            // For example: $studentFee->student_fees_details()->update(['deleted_at' => now()]);
        });
    }
}
