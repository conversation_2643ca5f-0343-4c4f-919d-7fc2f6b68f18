<?php

namespace App\Console\Commands;

use DateTime;
use Carbon\Carbon;
use App\Models\StudentFee;
use App\Models\StudentFeesDetail;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use App\Repositories\StudentFeesDetail\StudentFeesDetailInterface;

class recurringInvoice extends Command
{
    public function __construct()
    {
        parent::__construct();
    }
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'recurringInvoice:cron';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        //New Iogic
        try {
            //If auto_publish_date and invoice_date is not null
            // $studentFees = StudentFee::whereNotNull('invoice_date')
            //     ->whereNull('deleted_at')
            //     ->whereRaw('current_cycle < total_cycles OR total_cycles IS NULL')
            //     ->get();
            $studentFees = StudentFee::whereNotNull('invoice_date')->whereNull('deleted_at')->where('disable_recurring', 0)->get();
            foreach ($studentFees as $fee) {
                // Check for termination condition
                if (is_numeric($fee->total_cycles) && is_numeric($fee->current_cycle) && $fee->current_cycle >= $fee->total_cycles) {
                    echo 'Recurring invoice terminated for Fee Id: '.$fee->id.' due to reaching total cycles.('.$fee->current_cycle .'/'. $fee->total_cycles.')';
                    echo "<br/>";
                    $fee->disable_recurring = 1;
                    $fee->save();
                    continue;
                }
                //To query for draft fee with a predefined publish date
                if($fee->status == 'draft' && $fee->auto_publish_date != null) {
                    echo 'Draft Fee Id: '.$fee->id;
                    echo "<br/>";
                    //Auto publish draft invoice
                    if (Carbon::now()->gte(Carbon::parse($fee->auto_publish_date))) {
                        $fee->status = 'published';
                        $fee->updated_at = Carbon::now();
                        $fee->uid = $fee->uid == null ? $this->generateNextUid($fee->school_id) : $fee->uid;
                        $fee->save();

                        $title = 'New Invoice Available';
                        $body = 'A new Invoice has been generated';
                        $type = 'Notification';
                        $parent = DB::select("SELECT guardian_id FROM students WHERE id = (SELECT student_id FROM student_fees WHERE id = ".$fee->id.")");
                        if(COUNT($parent)){
                            send_notification([$parent[0]->guardian_id], $title, $body, $type);
                        }

                        echo 'Update draft fee to publish and generate the fee invoice id: '.$fee->uid;
                        echo "<br/>";
                    }
                }
                else if($fee->recurring_invoice != null){
                    echo 'Recurring Fee Id: '.$fee->id;
                    echo "<br/>";
                    //Handle recurring invoice
                    $autoPublishDate = $fee->auto_publish_date;
                    if($autoPublishDate == null){
                        $autoPublishDate = $fee->invoice_date;
                    }
                    $nextInvoiceDate = $this->nextRecurringDate($fee->invoice_date, $fee->current_cycle, $fee->recurring_invoice);
                    $nextPublishDate = $this->nextRecurringDate($autoPublishDate, $fee->current_cycle, $fee->recurring_invoice);
                    echo 'Process published invoice auto_publish_date:'.$autoPublishDate.' invoice_date:'.$fee->invoice_date;
                    echo "<br/>";
                    echo 'nextInvoiceDate:'.$nextInvoiceDate.' nextPublishDate:'.$nextPublishDate;
                    echo "<br/>";
                    
                    //Make sure the logic only execute if the nextPublishDate for this fee is today or after today
                    if (Carbon::now()->gte(Carbon::parse($nextPublishDate))) {
                        echo 'nextInvoiceDate:'.$nextInvoiceDate.' Carbon::now():'.Carbon::now();
                        echo "<br/>";
                         // Check if there is existing student fee with the same next invoice date and recurring reference
                        $existingFee = StudentFee::where('invoice_date', $nextInvoiceDate)
                            ->where('recurring_reference', '=', $fee->id)
                            ->exists();
                        if ($existingFee) {
                            echo 'Invoice date already exist with date:'.$nextInvoiceDate;
                            echo "<br/>";
                            continue;
                        }


                        //YYYY-MM-DD
                        $sessionYear = DB::select("SELECT end_date FROM session_years WHERE school_id = ? AND `default` = 1", [$fee->school_id]);
                        if (COUNT($sessionYear) == 0) {
                            continue;
                        } else {
                            $sessionEndDate = $sessionYear[0]->end_date;
                            $sessionEndDate = Carbon::parse($sessionEndDate)->format('Y-m-d');
                            if (Carbon::parse($nextInvoiceDate)->gt(Carbon::parse($sessionEndDate))) {
                                continue;
                            }
                        }

                        $earlyDate = $this->calculateEarlyDate($fee->early_date, $fee->current_cycle, $fee->recurring_invoice);
                        $dueDate = $this->calculateDueDate($fee->due_date, $fee->current_cycle, $fee->recurring_invoice);
                        if (Carbon::parse($nextPublishDate)->lte(Carbon::now())) {
                            $fee->current_cycle += 1;
                            $fee->save();
                            $newFee = StudentFee::create([
                                'name'               => 'Auto-generated',
                                'due_date'           => $dueDate,
                                'due_charges'        => $fee->due_charges ?? 0,
                                'due_charges_amount' => $fee->due_charges_amount ?? 0,
                                'early_date'         => $earlyDate ? Carbon::parse($earlyDate)->format('Y-m-d') : null,
                                'early_offer'        => $fee->early_offer ?? 0,
                                'early_offer_amount' => $fee->early_offer_amount ?? 0,
                                'class_id'           => $fee->class_id,
                                'school_id'          => $fee->school_id,
                                'session_year_id'    => $fee->session_year_id,
                                'student_id'         => $fee->student_id,
                                'status'             => 'published',
                                'uid'                => $this->generateNextUid($fee->school_id),
                                'current_cycle'      => $fee->current_cycle += 1,
                                'total_cycles'        => $fee->total_cycles,
                                'recurring_reference' => $fee->id,
                                'created_at'         => now(),
                                'updated_at'         => now(),
                                'invoice_date'      => $nextInvoiceDate,
                            ]);
            
                            $feeTypes = DB::table('student_fees_details')->where('student_fees_id',$fee->id)->whereNotIn('fees_type_name', ['Overdue Fees', 'Early Discount'])->get();
                            // $this->studentFeeType->builder()->where("student_id", $fee->student_id)->get();

                            $feeDetails = [];
                            foreach ($feeTypes as $feeType) {
                                $feeDetails[] = [
                                    "student_fees_id"   => $newFee->id,
                                    "fees_type_name"    => $feeType->fees_type_name,
                                    "fees_type_amount"  => $feeType->fees_type_amount,
                                    "classification_code" => $feeType->classification_code,
                                    "quantity"          => $feeType->quantity ?? 1,
                                    "unit"              => $feeType->unit,
                                    "discount"          => $feeType->discount ?? null,
                                    "tax"               => $feeType->tax ?? null,
                                    "optional"          => 0,
                                    "school_id"         => $fee->school_id,
                                    "created_at"        => now(),   
                                    "updated_at"        => now(),
                                ];
                            }
                            if (count($feeDetails) > 0) {
                                // Log::info('Fee Details: ' . json_encode($feeDetails));
                                StudentFeesDetail::insert($feeDetails);
                                // $this->studentFeesDetail->upsert($feeDetails, ['student_fees_id','school_id'], ['fees_type_name', 'fees_type_amount', 'classification_code', 'unit', 'quantity', 'optional']);
                            }
                            
                            $title = 'New Invoice Available';
                            $body = 'A new Invoice has been generated';
                            $type = 'Notification';
                            $parent = DB::select("SELECT guardian_id FROM students WHERE id = (SELECT student_id FROM student_fees WHERE id = ".$newFee->id.")");
                            if(COUNT($parent)){
                                send_notification([$parent[0]->guardian_id], $title, $body, $type);
                            }
                        }
                    }
                    else{
                        echo 'Publish date is not today or after today';
                        echo "<br/>";
                    }
                }
            }

            
            //If auto_publish_date, invoice_date is null and recurring_invoice is not null, then we only query those published invoice for recurring processing (Past invoice record)
            $studentFees = StudentFee::whereNull('auto_publish_date')->whereNull('invoice_date')->whereNull('deleted_at')->where('status', 'published')->whereNotNull('recurring_invoice')->where('disable_recurring', 0)->get();
            // $studentFees = StudentFee::whereNull('auto_publish_date')
            //     ->whereNull('invoice_date')
            //     ->whereNull('deleted_at')
            //     ->where('status', 'published')
            //     ->whereNotNull('recurring_invoice')
            //     ->whereRaw('current_cycle < total_cycles OR total_cycles IS NULL')
            //     ->get();
            foreach ($studentFees as $fee) {
                // Check for termination condition
                if (is_numeric($fee->total_cycles) && is_numeric($fee->current_cycle) && $fee->current_cycle >= $fee->total_cycles) {
                    echo 'Recurring invoice terminated for Fee Id: '.$fee->id.' due to reaching total cycles.('.$fee->current_cycle .'/'. $fee->total_cycles.')';
                    echo "<br/>";
                    $fee->disable_recurring = 1;
                    $fee->save();
                    continue;
                }
                echo 'Past Recurring Fee Id: '.$fee->id;
                echo "<br/>";
                //Handle recurring invoice
                $nextInvoiceDate = $this->nextRecurringDate($fee->created_at, $fee->current_cycle, $fee->recurring_invoice);
                $nextPublishDate = $this->nextRecurringDate($fee->created_at, $fee->current_cycle, $fee->recurring_invoice);
                echo 'Process published invoice auto_publish_date:'.$fee->created_at.' invoice_date:'.$fee->created_at;
                echo "<br/>";
                echo 'nextInvoiceDate:'.$nextInvoiceDate.' nextPublishDate:'.$nextPublishDate;
                echo "<br/>";
                    
                //Make sure the logic only execute if the nextPublishDate for this fee is today or after today
                if (Carbon::now()->gte(Carbon::parse($nextPublishDate))) {
                    echo 'nextInvoiceDate:'.$nextInvoiceDate.' Carbon::now():'.Carbon::now();
                    echo '<br/>';
                     // Check if there is existing student fee with the same next invoice date and recurring reference
                    $existingFee = StudentFee::where('invoice_date', $nextInvoiceDate)
                        ->where('recurring_reference', '=', $fee->id)
                        ->exists();
                    if ($existingFee) {
                        echo 'Invoice date already exist with date:'.$nextInvoiceDate;
                        echo '<br/>';
                        continue;
                    }

                    //YYYY-MM-DD
                    $sessionYear = DB::select("SELECT end_date FROM session_years WHERE school_id = ? AND `default` = 1", [$fee->school_id]);
                    if (COUNT($sessionYear) == 0) {
                        continue;
                    } else {
                        $sessionEndDate = $sessionYear[0]->end_date;
                        $sessionEndDate = Carbon::parse($sessionEndDate)->format('Y-m-d');
                        if (Carbon::parse($nextInvoiceDate)->gt(Carbon::parse($sessionEndDate))) {
                            continue;
                        }
                    }
                    $earlyDate = $this->calculateEarlyDate($fee->early_date, $fee->current_cycle, $fee->recurring_invoice);
                    $dueDate = $this->calculateDueDate($fee->due_date, $fee->current_cycle, $fee->recurring_invoice);
                    if (Carbon::parse($nextPublishDate)->lte(Carbon::now())) {
                        $fee->current_cycle += 1;
                        $fee->save();
                        $newFee = StudentFee::create([
                            'name'               => 'Auto-generated',
                            'due_date'           => $dueDate,
                            'due_charges'        => $fee->due_charges ?? 0,
                            'due_charges_amount' => $fee->due_charges_amount ?? 0,
                            'early_date'         => $earlyDate ? Carbon::parse($earlyDate)->format('Y-m-d') : null,
                            'early_offer'        => $fee->early_offer ?? 0,
                            'early_offer_amount' => $fee->early_offer_amount ?? 0,
                            'class_id'           => $fee->class_id,
                            'school_id'          => $fee->school_id,
                            'session_year_id'    => $fee->session_year_id,
                            'student_id'         => $fee->student_id,
                            'status'             => 'published',
                            'uid'                => $this->generateNextUid($fee->school_id),
                            'current_cycle'      => $fee->current_cycle += 1,
                            'total_cycles'        => $fee->total_cycles,
                            'recurring_reference' => $fee->id,
                            'created_at'         => now(),
                            'updated_at'         => now(),
                            'invoice_date'      => $nextInvoiceDate,
                        ]);
        
                        $feeTypes = DB::table('student_fees_details')->where('student_fees_id',$fee->id)->whereNotIn('fees_type_name', ['Overdue Fees', 'Early Discount'])->get();
                        // $this->studentFeeType->builder()->where("student_id", $fee->student_id)->get();
                        $feeDetails = [];
                        foreach ($feeTypes as $feeType) {
                            $feeDetails[] = [
                                "student_fees_id"   => $newFee->id,
                                "fees_type_name"    => $feeType->fees_type_name,
                                "fees_type_amount"  => $feeType->fees_type_amount,
                                "classification_code" => $feeType->classification_code,
                                "quantity"          => $feeType->quantity ?? 1,
                                "unit"              => $feeType->unit,
                                "discount"          => $feeType->discount ?? null,
                                "tax"               => $feeType->tax ?? null,
                                "optional"          => 0,
                                "school_id"         => $fee->school_id,
                                "created_at"        => now(),   
                                "updated_at"        => now(),
                            ];
                        }
                        if (count($feeDetails) > 0) {
                            // Log::info('Fee Details: ' . json_encode($feeDetails));
                            StudentFeesDetail::insert($feeDetails);
                            // $this->studentFeesDetail->upsert($feeDetails, ['student_fees_id','school_id'], ['fees_type_name', 'fees_type_amount', 'classification_code', 'unit', 'quantity', 'optional']);
                        }
                        
                        $title = 'New Invoice Available';
                        $body = 'A new Invoice has been generated';
                        $type = 'Notification';
                        $parent = DB::select("SELECT guardian_id FROM students WHERE id = (SELECT student_id FROM student_fees WHERE id = ".$newFee->id.")");
                        if(COUNT($parent)){
                            send_notification([$parent[0]->guardian_id], $title, $body, $type);
                        }
                    }
                }
                else{
                    echo 'Publish date is not today or after today';
                    echo "<br/>";
                }
            }
        } catch (\Exception $e) {
            echo $e->getMessage();
            Log::error('Error in recurring cron job: ' . $e->getMessage(), [
                'exception' => $e,
                'stack_trace' => $e->getTraceAsString(),
            ]);
        }
    }

    public function addMonth($date, $count) {
        $start = new DateTime($date);
        $checkDate = new DateTime($date);
        $checkDate->modify('last day of +'.$count.' month');
        if ($start->format('Y-m-d') === $start->format('Y-m-t')) {
            $start->modify('last day of +'.$count.' month');
        } else {
            if (($start->format('d') > $checkDate->format('d'))) {
                $start = $checkDate;
            } else {
                $start->modify('+'.$count.' month');
            }   
        }
        return $start;
    }

    private function nextRecurringDate($targetDate, $currentCycle, $recurringMonths)
    {
        $createdAt = Carbon::parse($targetDate);

        $nextRecurringDate = $this->addMonth($createdAt,($currentCycle + 1) * $recurringMonths);
        return $nextRecurringDate->format('Y-m-d');
    }

    private function calculateDueDate($due_date, $current_cycle, $recurring_invoice)
    {
        $createdAt = Carbon::parse($due_date);
        $nextRecurringDate = $this->addMonth($createdAt,($current_cycle + 1) * $recurring_invoice);

        return $nextRecurringDate->format('Y-m-d');
    }

    private function calculateEarlyDate($early_date, $current_cycle, $recurring_invoice)
    {
        if (!$early_date) {
            return null;
        }
        $createdAt = Carbon::parse($early_date);

        $nextRecurringDate = $this->addMonth($createdAt,($current_cycle + 1) * $recurring_invoice);

        return $nextRecurringDate->format('Y-m-d');
    }

    public function generateNextUid($schoolId)
    {
        $latestUID = DB::table('student_fees')->where('school_id', $schoolId)->where('status', 'published')->whereNull('deleted_at')->select('uid')->orderBy('uid', 'desc')->value('uid');
        $uid = $latestUID ? $latestUID + 1 : 1;

        while (DB::table('student_fees')
            ->where('uid', $uid)
            ->where('school_id', $schoolId)
            ->where('status', 'published')
            ->exists()
        ) {
            $uid++;
        } 

        return $uid;
    }
}
