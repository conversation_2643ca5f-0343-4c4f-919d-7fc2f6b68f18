<html>
    <?php
        $no = 1;
        $total_fees = 0;



        $totalFees = 0.0;


        $wrappedAddress = wordwrap($studentDetail->current_address ?? 'N/A', 50, '<br>');        if($studentFeeEinvoice){
            $dateTimeValidated = $studentFeeEinvoice->created_at;
        }

    ?>


    <head>
        <meta charset="utf-8">
        <title>Consolidated Invoice</title>
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400&display=swap" rel="stylesheet">
        <style>
            @font-face {
                font-family: "WenQuanYi Micro Hei";
                font-weight: normal;
                font-style: normal;
                src: url('<?php echo e(storage_path('fonts/WenQuanYiMicroHeiRegular.ttf')); ?>') format('truetype');
            }

            body {
                font-family: 'WenQuanYi Micro Hei', system-ui, sans-serif;
                line-height: 1.0;
            }
        </style>
    </head>

    <body>
        <style>
            /* reset */
            * {
                border: 0;
                box-sizing: content-box;
                color: inherit;
                font-family: inherit;
                font-size: inherit;
                font-style: inherit;
                font-weight: inherit;
                line-height: inherit;
                list-style: none;
                margin: 0;
                padding: 0;
                text-decoration: none;
                vertical-align: top;
            }

            /* content editable */
            *[contenteditable] {
                border-radius: 0.25em;
                min-width: 1em;
                outline: 0;
            }

            *[contenteditable] {
                cursor: pointer;
            }

            *[contenteditable]:hover,
            *[contenteditable]:focus,
            td:hover *[contenteditable],
            td:focus *[contenteditable],
            img.hover {
                background: #DEF;
                box-shadow: 0 0 1em 0.5em #DEF;
            }

            span[contenteditable] {
                display: inline-block;
            }

            /* heading */
            h1 {
                font: bold 100% sans-serif;
                letter-spacing: 0.5em;
                text-align: center;
                text-transform: uppercase;
            }

            /* table */
            table {
                font-size: 75%;
                table-layout: fixed;
                width: 100%;
            }

            table {
                border-collapse: separate;
                border-spacing: 2px;
            }

            th,
            td {
                border-width: 1px;
                padding: 0.5em;
                position: relative;
                text-align: left;
            }

            th,
            td {
                border-radius: 0.25em;
                border-style: solid;
            }

            th {
                background: #EEE;
                border-color: #BBB;
            }

            td {
                border-color: #DDD;
            }

            /* page */
            html {
                font: 16px/1 'Open Sans', sans-serif;
                overflow: auto;
                padding: 0.5in;
            }

            html {
                background: #999;
                cursor: default;
            }

            body {
                box-sizing: border-box;
                margin: 40px;
            }

            body {
                background: #FFF;
                border-radius: 1px;
                box-shadow: 0 0 1in -0.25in rgba(0, 0, 0, 0.5);
            }

            /* header */
            header {
                margin: 0 0 3em;
            }

            header:after {
                clear: both;
                content: "";
                display: table;
            }

            header h1 {
                background: #C0C0C0;
                border-radius: 0.25em;
                color: #FFF;
                margin: 0 0 1em;
                padding: 0.5em 0;
            }

            header address {
                float: left;
                width: 50%;
                font-size: 70%;
                font-style: normal;
                /* font-weight: bold; */
                line-height: 1.25;
                margin: 0 1em 1em 0;
            }

            header address p {
                margin: 0 0 0.25em;
            }

            header address p strong {
                font-weight: bold;
            }

            header rightsection {
                float: right;
                width: 220px;
                text-align: right;
                font-size: 75%;
                font-style: normal;
                line-height: 1.25;
            }

            header span,
            header img {
                display: block;
                float: right;
            }

            header span {
                margin: 0 0 1em 1em;
                max-height: 25%;
                max-width: 60%;
                position: relative;
            }

            header img {
                max-height: 100%;
                max-width: 100%;
            }

            header input {
                cursor: pointer;
                -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
                height: 100%;
                left: 0;
                opacity: 0;
                position: absolute;
                top: 0;
                width: 100%;
            }

            /* article */
            article,
            article address,
            table.meta,
            table.inventory {
                margin: 0 0 3em;
            }

            table.inventory td:empty {
                background-color: white;
                border: none;
            }

            table.inventory td.amount {
                text-align: right;
            }

            article:after {
                clear: both;
                content: "";
                display: table;
            }

            article h1 {
                clip: rect(0 0 0 0);
                position: absolute;
            }

            article address {
                float: left;
                font-size: 72%;
                /* font-weight: bold; */
                line-height: 20px;
            }

            h2 {
                font-size: 1em;
                font-weight: bold;
                margin-bottom: 10px;
                color: #333;
                text-align: right;
            }

            /* table meta & balance */
            table.meta,
            table.balance {
                float: right;
                width: 51%;
            }

            table.meta:after,
            table.balance:after {
                clear: both;
                content: "";
                display: table;
            }

            /* table meta */
            table.meta th {
                width: 40%;
            }

            table.meta td {
                width: 60%;
            }

            /* table items */
            table.inventory {
                clear: both;
                width: 100%;
            }

            table.inventory th {
                font-weight: bold;
                text-align: center;
            }

            /* table balance */
            table.balance th,
            table.balance td {
                width: 50%;
            }

            table.balance td {
                text-align: right;
            }

            /* aside */
            aside h2 {
                border: none;
                border-width: 0 0 1px;
                margin: 0 0 1em;
            }

            aside h2 {
                border-color: #999;
                border-bottom-style: solid;
            }

            /* javascript */
            .add,
            .cut {
                border-width: 1px;
                display: block;
                font-size: .8rem;
                padding: 0.25em 0.5em;
                float: left;
                text-align: center;
                width: 0.6em;
            }

            .add,
            .cut {
                background: #9AF;
                box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
                background-image: -moz-linear-gradient(#00ADEE 5%, #0078A5 100%);
                background-image: -webkit-linear-gradient(#00ADEE 5%, #0078A5 100%);
                border-radius: 0.5em;
                border-color: #0076A3;
                color: #FFF;
                cursor: pointer;
                font-weight: bold;
                text-shadow: 0 -1px 2px rgba(0, 0, 0, 0.333);
            }

            .add {
                margin: -2.5em 0 0;
            }

            .add:hover {
                background: #00ADEE;
            }

            .cut {
                opacity: 0;
                position: absolute;
                top: 0;
                left: -1.5em;
            }

            .cut {
                -webkit-transition: opacity 100ms ease-in;
            }

            tr:hover .cut {
                opacity: 1;
            }

            @media print {
                * {
                    -webkit-print-color-adjust: exact;
                }

                html {
                    background: none;
                    padding: 0;
                }

                body {
                    box-shadow: none;
                    margin: 0;
                }

                span:empty {
                    display: none;
                }

                .add,
                .cut {
                    display: none;
                }
            }

            @page {
                margin: 0;
            }

            .footer {
                display: relative;
                padding: 10px;
                font-size: 72%;
            }

            .footer-text {
                margin-top: 3em;
                position: absolute;
                height: auto;
            }

            .footer-image img {
                max-width: 90px;
                height: auto;
                float: right;
            }

            p {
                line-height: 1.0;
            }
        </style>
        <header>
            <h1><?php echo e('Consolidated Invoice'); ?></h1>
            <address contenteditable>
                <p><strong><?php echo e($school['school_name'] ?? ''); ?></strong></p>
                <p style="font-size:90% !important;"><strong>Main Address:</strong><span style="display:inline-block;float:none; vertical-align:middle; margin: 2px 0 0 4px;"><?php echo e($school['school_address'] ?? '-'); ?></span></p>
                <p style="font-size:90% !important"><strong>Branch Address:</strong> <?php echo nl2br(e($school['branch_address'] ?? '-')); ?></p>
            </address>
            <rightsection>
                <i><img style="height: auto;width:220px;" src="<?php echo e(asset('/storage/' . $schoolLogo)); ?>"
                        alt="logo"></i><br>
            </rightsection>
        </header>
        <article>

            <address contenteditable>
                <p>Supplier Tin:
                    <?php echo e(!empty($e_invoice_school->tax_identification_number) ? $e_invoice_school->tax_identification_number : 'N/A'); ?>

                </p>
                <p>Supplier Registration Number:
                    <?php echo e(!empty($e_invoice_school->new_company_registration_number) ? $e_invoice_school->new_company_registration_number : 'N/A'); ?>

                </p>
                <p>Supplier SST ID:
                    <?php echo e(!empty($e_invoice_school->sst_registration_number) ? $e_invoice_school->sst_registration_number : 'N/A'); ?>

                </p>
                <p>Supplier MSIC code:
                    <?php echo e(!empty($e_invoice_school->company_msic_code) ? $e_invoice_school->company_msic_code : 'N/A'); ?>

                </p>
                <br>
                <p>Student Name: <?php echo e($student->user->full_name ?? 'N/A'); ?></p>
                <p>Student ID: <?php echo e($student->admission_no ?? 'N/A'); ?></p>
                <p>Student Class:
                    <?php echo e(isset($feesPaid) ? $feesPaid->student_fees->class->full_name : $student->class_section->class->full_name ?? ''); ?>

                </p>
                <p>Admission Date: <?php echo e($student->admission_date ?? 'N/A'); ?></p>
                <p>Buyer TIN:   EI00000000010</p>
                <p>Buyer Name: General Public</p>
             
                <p>Buyer BRN: <?php echo e($e_invoice_guardian->BRN ?? 'N/A'); ?></p>
                <p>Buyer SST No: <?php echo e($e_invoice_guardian->SST ?? 'N/A'); ?></p>
                <p>Address: <?php echo $wrappedAddress; ?></p>
                <p>Contact: <?php echo e($parentDetail->mobile ?? 'N/A'); ?></p>
                <p>Email: <?php echo e($parentDetail->email ?? 'N/A'); ?></p>
            </address>
            <h2><?php echo e('E-INVOICE'); ?></h2>
            <table class="meta">
                <tr>
                    <th><span contenteditable><?php echo e('Unique Identifier #'); ?></span></th>
                    <td><span contenteditable><?php echo e($studentFeeEinvoice->uuid ?? '-'); ?></span></td>
                </tr>
                <tr>
                    <th><span contenteditable>Invoice #</span></th>
                    <td><span
                            contenteditable><?php echo e('INV' . sprintf('%08d', $consolidateInvoices[0]->reference_uid)); ?></span>
                    </td>
                </tr>

                <tr>
                    <th><span contenteditable>Invoice Date</span></th>
                    <td><span contenteditable><?php echo e(explode(' ', $studentFee[0]->created_at)[0]); ?></span></td>
                </tr>
                <tr>
                    <th><span contenteditable>Due Date</span></th>
                    <td><span contenteditable><?php echo e($dueDate ?? '-'); ?></span></td>
                </tr>
                <tr>
                    <th><span contenteditable>Payment Date</span></th>
                    <td><span contenteditable><?php echo e(isset($isPaid) && $isPaid ? $feesPaid->date : '-'); ?></span></td>
                </tr>
                <th><span contenteditable>Payment Type</span></th>
                <td><span contenteditable><?php echo e(isset($isPaid) && $isPaid ? $feesPaid->getMode() : '-'); ?></span></td>
                </tr>
                <tr>
                    <th><span contenteditable>Total Payable</span></th>
                    <td><span id="prefix"
                            contenteditable><?php echo e($school['currency_symbol'] ?? ''); ?></span><span><?php echo e(number_format((float) $studentFeesDetail['totalAmount'], 2, '.', '')); ?></span>
                    </td>
                </tr>
            </table>

            <table class="inventory">
                <thead>
                    <tr>
                        <th style="width:7%;text-align: left;"><span contenteditable>Code</span></th>
                        <th style="width:37%;text-align: left;"><span contenteditable>Description</span></th>
                        <th style="width:7%;text-align: left;"><span contenteditable>UOM</span></th>
                        <th style="width:7%;text-align: left;"><span contenteditable>Qty</span></th>
                        <th style="width:9%;text-align: left;"><span contenteditable>Unit Price</span></th>
                        <th style="width:9%;text-align: left;"><span contenteditable>Amount</span></th>
                        <th style="width:8%;text-align: left;"><span contenteditable>Disc</span></th>
                        <th style="width:10%;text-align: left;"><span contenteditable>Tax Amount</span></th>
                        <th style="width:15%;text-align: left;"><span contenteditable>Total Amount</span></th>
                    </tr>
                </thead>
                <tbody>
                    
                        <?php
                            $classificationCodeOnly = '004';
                            $unitOnly = 1;
                            // dd($studentFeesDetail);
                        ?>
                        <tr>
                            <td>
                                <span
                                    contenteditable><?php echo e(!empty($classificationCodeOnly) ? $classificationCodeOnly : '-'); ?></span>
                            </td>
                            <td>
                                <span contenteditable><?php echo e($studentFeesDetail['name'] ?? '-'); ?></span>
                            </td>
                            <td>
                                <span contenteditable><?php echo e(!empty($unitOnly) ? $unitOnly : '-'); ?></span>
                            </td>
                            <td>
                                <span contenteditable>1</span>
                            </td>
                            <td class="amount">
                                <span><?php echo e(number_format((float) $studentFeesDetail['totalAmount'], 2, '.', '')); ?></span>
                            </td>
                            <td class="amount">
                                <span><?php echo e(number_format((float) $studentFeesDetail['totalAmount'], 2, '.', '')); ?></span>
                            </td>
                            <td class="amount">
                                <span contenteditable>-
                                </span>
                            </td>
                            <td class="amount">
                                <span contenteditable>-</span>
                            </td>
                            <td class="amount">
                                <span>RM</span>
                                <span><?php echo e(number_format((float) $studentFeesDetail['totalAmount'], 2, '.', '')); ?></span>
                            </td>
                        </tr>
                   
                    <tr>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td>Subtotal</td>
                        <td class="amount">
                            <span> <?php echo e(number_format((float) $studentFeesDetail['totalAmount'], 2, '.', '')); ?></span>
                        </td>
                        <td class="amount">
                            <span contenteditable>-</span>
                        </td>
                        <td class="amount">
                            <span contenteditable>-</span>
                        </td>
                        <td class="amount">
                            <span>RM</span>
                            <span><?php echo e(number_format((float) $studentFeesDetail['totalAmount'], 2, '.', '')); ?></span>
                        </td>
                    </tr>
                    <tr>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td colspan="4">Total excluding tax</td>
                        <td class="amount">
                            <span>RM</span>
                            <span><?php echo e(number_format((float) $studentFeesDetail['totalAmount'], 2, '.', '')); ?></span>
                        </td>
                    </tr>
                    <tr>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td colspan="4">Tax amount</td>
                        <td class="amount">
                            <span contenteditable>-</span>
                        </td>
                    </tr>
                    <tr>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td colspan="4">Total including tax</td>
                        <td class="amount">
                            <span>RM</span>
                            <span><?php echo e(number_format((float) $studentFeesDetail['totalAmount'], 2, '.', '')); ?></span>
                        </td>
                    </tr>
                </tbody>
            </table>

            <table class="balance">
                <tr>
                    <th><span contenteditable>Due Charges</span></th>
                    <td><span
                            data-prefix><?php echo e($school['currency_symbol'] ?? ''); ?></span><span><?php echo e(number_format($due_fees ?? 0, 2, '.', '-')); ?></span>
                    </td>
                </tr>
                <tr>
                    <th><span contenteditable>Early Payment Discount</span></th>
                    <td><span
                            data-prefix><?php echo e($school['currency_symbol'] ?? ''); ?></span><span><?php echo e(number_format($earlyy ?? 0, 2, '.', '-')); ?></span>
                    </td>
                </tr>
                <tr>
                    <th><span contenteditable>Total</span></th>
                    <td><span
                            data-prefix><?php echo e($school['currency_symbol'] ?? ''); ?></span><span><?php echo e(number_format((float) $studentFeesDetail['totalAmount'], 2, '.', '')); ?></span>
                    </td>
                </tr>
                <tr>
                    <th><span contenteditable>Amount Paid</span></th>
                    <td><span data-prefix><?php echo e($school['currency_symbol'] ?? ''); ?></span><span
                            contenteditable><?php echo e(number_format((float) $studentFeesDetail['totalAmount'], 2, '.', '')); ?></span>
                    </td>
                </tr>
                <tr>
                    <th><span contenteditable>Balance Due</span></th>
                    <td><span
                            data-prefix><?php echo e($school['currency_symbol'] ?? ''); ?></span><span>0.00</span>
                    </td>
                </tr>
            </table>
        </article>

   <aside>
            <div class="footer">
                <span>Remarks:</span>
                <p><?php echo nl2br(e($school['remark'])); ?></p>
                <br>
                <div class="footer-text-with-qrcode">
                    <div class="footer-text">
                        <span>Digital Signature: N/A</span><br>
                        <span>Date and Time of Validation: <?php echo e($dateTimeValidated ?? 'N/A'); ?></span><br>
                        <?php if(isset($studentFeeEinvoice)): ?>
                        <span>This invoice is an validated e-invoice by LHDN. Scan the QR to verify:</span>
                        <?php else: ?>
                        <span>This invoice is an unvalidated e-invoice by LHDN</span>
                        <?php endif; ?>
                    </div>
                    <div class="footer-image">
                        <?php if(isset($studentFeeEinvoice->e_invoice_url)): ?>
                            <img src="<?php echo e($studentFeeEinvoice->e_invoice_url); ?>" style="width: 100px; height: 100px;" alt="QR Code">
                        <?php else: ?>
                            <img src="assets/qrcode_demo.jpg" style="width: 100px; height: 100px;" alt="QR Code">
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </aside>
        <div contenteditable>
            <p style="font-size:smaller;line-height: 2;"></p>
        </div>
    </body>
    






</html><?php /**PATH D:\laragon\www\schola\resources\views/student-fees/consolidate_fees_receipt.blade.php ENDPATH**/ ?>