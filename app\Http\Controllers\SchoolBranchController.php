<?php

namespace App\Http\Controllers;

use App\Models\Language;
use App\Models\School;
use App\Models\SchoolSetting;
use App\Models\User;
use App\Repositories\Guidance\GuidanceInterface;
use App\Repositories\Package\PackageInterface;
use App\Repositories\School\SchoolInterface;
use App\Repositories\SchoolSetting\SchoolSettingInterface;
use App\Repositories\SessionYear\SessionYearInterface;
use App\Repositories\User\UserInterface;
use App\Services\BootstrapTableService;
use App\Services\CachingService;
use App\Services\ResponseService;
use App\Services\SchoolDataService;
use App\Services\SubscriptionService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Stripe\Review;
use Throwable;
use TypeError;
use App\Models\Mediums;
use App\Models\Section;

class SchoolBranchController extends Controller {

    // Initializing the schools Repository
    private SchoolInterface $schoolsRepository;
    private UserInterface $userRepository;
    private PackageInterface $package;
    private CachingService $cache;
    private SubscriptionService $subscriptionService;
    private SchoolSettingInterface $schoolSettings;
    private SessionYearInterface $sessionYear;
    private GuidanceInterface $guidance;

    public function __construct(SchoolInterface $school, UserInterface $user, PackageInterface $package, CachingService $cache, SubscriptionService $subscriptionService, SchoolSettingInterface $schoolSettings, SessionYearInterface $sessionYear, GuidanceInterface $guidance)
    {
        $this->schoolsRepository = $school;
        $this->userRepository = $user;
        $this->package = $package;
        $this->cache = $cache;
        $this->subscriptionService = $subscriptionService;
        $this->schoolSettings = $schoolSettings;
        $this->sessionYear = $sessionYear;
        $this->guidance = $guidance;
    }
    public function index()
    {

        $baseUrl = url('/');
        // Remove the scheme (http:// or https://)
        $baseUrlWithoutScheme = preg_replace("(^https?://)", "", $baseUrl);
        $baseUrlWithoutScheme = str_replace("www.", "", $baseUrlWithoutScheme);
        $session_year_all = $this->sessionYear->all(['id', 'name', 'default']);

        return response(view('school-branch', compact('baseUrlWithoutScheme', 'session_year_all')));
    }
    public function show()
    {
        $offset = request('offset', 0);
        $limit = request('limit', 10);
        $sort = request('sort', 'id');
        $order = request('order', 'ASC');
        $search = request('search');
        $showDeleted = request('show_deleted');
        $today_date = Carbon::now()->format('Y-m-d');

        $schoolId = Auth::user()->school_id;

        // Fetch school branches with statistics
        if ($schoolId == Auth::user()->school_id) {
            $rows = DB::table('school_branch')
                ->join('schools', 'school_branch.branch_id', '=', 'schools.id')
                ->select(
                    'schools.id',
                    'school_branch.branch_id',
                    'schools.name as school_name',
                    'schools.logo',
                    'schools.address',
                    'schools.support_email',
                    'schools.support_phone',
                    'schools.tagline',
                    'schools.status',
                    'schools.admin_id',
                    'school_branch.created_at',
                    'school_branch.updated_at'
                )
                ->where('school_branch.school_id', $schoolId)
                ->get();

            $results = [];
            $branchIds = [];
            foreach ($rows as $row) {
                $row = (array)$row;
                $branchIds[] = $row['id'];
            }

            foreach ($branchIds as $branchIdValue) {
                $maxFileSize = (float)DB::table('schools')
                    ->where('id', $branchIdValue)
                    ->value('capacity');

                $totalFileSize1 = (float)DB::table('files')
                    ->where('school_id', $branchIdValue)
                    ->sum('file_size');

                $totalFileSize2 = (float)DB::table('student_progress')
                    ->where('school_id', $branchIdValue)
                    ->sum('file_size');

                $totalFileSize3 = (float)DB::table('subjects')
                    ->where('school_id', $branchIdValue)
                    ->sum('file_size');

                $totalFileSize4 = (float) DB::table('users')
                    ->where('school_id', $branchIdValue)
                    ->sum('file_size');

                $totalFileSize5 = (float)DB::table('galleries')
                    ->where('school_id', $branchIdValue)
                    ->sum('file_size');

                $totalFileSize = $totalFileSize1 + $totalFileSize2 + $totalFileSize3 + $totalFileSize4 + $totalFileSize5;

                $totalFileSizeGB = number_format($totalFileSize / (1024 * 1024), 2);

                // $remainingCapacityGB =number_format($maxFileSize - $totalFileSizeGB,2);


                $results[$branchIdValue] = $totalFileSizeGB;
            }


            $schoolStats = DB::select(
                "SELECT 
                s.id AS school_id,
                s.name AS school_name,
                SUM(CASE WHEN u.status = 0 THEN 1 ELSE 0 END) AS inactive_count,
                SUM(CASE WHEN u.status = 1 THEN 1 ELSE 0 END) AS active_count,
                COUNT(*) AS total_students
            FROM 
                students stu
            JOIN 
                users u ON u.id = stu.user_id
            JOIN 
                schools s ON u.school_id = s.id
            GROUP BY 
                s.id, s.name"
            );
            $no = 1;

            $bulkData = [
                'total' => $rows->count(),
                'rows' => [],
            ];

            foreach ($rows as $row) {
                $row = (array)$row;
                $branchIdValue = $row['id'];
                $row['usagecapacity'] = $results[$branchIdValue] ?? 'N/A';

                // Add school statistics
                foreach ($schoolStats as $stat) {
                    if ($stat->school_id === $row['id']) {
                        $row['active_count'] = $stat->active_count;
                        $row['inactive_count'] = $stat->inactive_count;
                        $row['total_students'] = $stat->total_students;
                        break;
                    }
                }
                $row['no'] = $no++;

                $e_invoice_exist = DB::table('e_invoice')->where('school_id', $row['id'])->exists();
                if($e_invoice_exist){
                    $incompleteDataCount = DB::table('e_invoice')
                    ->where('school_id', $row['id'])
                    ->where(function ($query) {
                        $query->whereNull('new_company_registration_number')
                        ->orWhere('new_company_registration_number', '')
                        ->orWhereNull('old_company_registration_number')
                        ->orWhere('old_company_registration_number', '')
                        ->orWhereNull('company_business_activity')
                        ->orWhere('company_business_activity', '')
                        ->orWhereNull('tax_identification_number')
                        ->orWhere('tax_identification_number', '')
                        ->orWhereNull('sst_registration_number')
                        ->orWhere('sst_registration_number', '')
                        ->orWhereNull('tourism_tax_registration_number')
                        ->orWhere('tourism_tax_registration_number', '')
                        ->orWhereNull('company_msic_code')
                        ->orWhere('company_msic_code', '')
                        ->orWhereNull('address_line1')
                        ->orWhere('address_line1', '')
                        ->orWhereNull('city')
                        ->orWhere('city', '')
                        ->orWhereNull('postal_code')
                        ->orWhere('postal_code', '')
                        ->orWhereNull('country')
                        ->orWhere('country', '')
                        ->orWhereNull('state')
                        ->orWhere('state', '');
                    })
                    ->count();
                }

                $row['operate'] = BootstrapTableService::menuItem(
                    BootstrapTableService::menuButton('login_as_school', route('school.login_to', $row['id']))
                );

                $bulkData['rows'][] = $row;
            }



            return response()->json($bulkData);
        }
    }

    // public function loginAsSchool($schoolId)
    // {
    //     // Validate schoolId (e.g., ensure it exists and the current user has permission)
    //     $school = School::findOrFail($schoolId);

    //     // Find a user to log in as (or create logic to determine the appropriate user)
    //     $user = User::where('school_id', $schoolId)->first(); // Example logic; adjust as needed

    //     if ($user) {
    //         Auth::login($user);

    //         return redirect()->route('dashboard'); // Redirect to a dashboard or home page after login
    //     }

    //     // Handle case where no user is found
    //     return redirect()->back()->withErrors(['error' => 'No user found for the selected school.']);
    // }

    public function branchList(Request $request)
    {

        $schoolId = Auth::user()->school_id;
        // $requestSessionYearId = (int)request('sessionYearId');
        // $sessionYearId = $requestSessionYearId ?? $this->cache->getDefaultSessionYear()->id;
        $formattedStartDate = date('Y-m-d', strtotime($request->start_date));
        $formattedEndDate = date('Y-m-d', strtotime($request->end_date));

        $branches = DB::table('school_branch')
            ->where('school_id', $schoolId)
            ->pluck('branch_id')
            ->toArray();

        $allId = array_merge([$schoolId], $branches);

        //Daily Collection Report (Total Fees)
        $rowCollection = [];
        foreach ($allId as $schoolId) {
            $totalCollection = DB::table('student_fees_paids as sfp')
                ->join('schools as s', 'sfp.school_id', '=', 's.id')
                // ->join('student_fees as sf', 'sfp.student_fees_id', '=', 'sf.id')
                // ->join('school_branch as sb', 'sfp.school_id', '=', 'sb.branch_id')
                ->select(
                    'sfp.school_id',
                    's.name as school_name',
                    DB::raw('SUM(sfp.amount) AS total_amount')
                )
                ->where('sfp.school_id', $schoolId)
                // ->where('sf.session_year_id', $sessionYearId)
                ->where('sfp.is_fully_paid', 1)
                ->whereBetween('sfp.created_at', [$formattedStartDate, $formattedEndDate])
                ->groupBy('s.name')
                ->first();

            $tempRow = [];

            $tempRow['school_id'] = $totalCollection->school_id ?? '';
            $tempRow['school_name'] = $totalCollection->school_name ?? '';
            $tempRow['total_amount'] = $totalCollection->total_amount ?? '';

            $rowCollection[] = $tempRow;
        }


        //Payroll Report (Salary)
        $rowPayroll = [];
        foreach ($allId as $schoolId) {
            $totalPayroll = DB::table('expenses as e')
                ->join('schools as s', 'e.school_id', '=', 's.id')
                ->select(
                    'e.school_id',
                    's.name as school_name',
                    DB::raw('SUM(amount) AS total_amount')
                )
                ->whereNull('category_id')
                ->whereBetween('e.created_at', [$formattedStartDate, $formattedEndDate])
                ->where('school_id', $schoolId)
                ->first();

            $tempRow = [];

            $tempRow['school_id'] = $totalPayroll->school_id ?? '';
            $tempRow['school_name'] = $totalPayroll->school_name ?? '';
            $tempRow['total_amount'] = $totalPayroll->total_amount ?? '';

            $rowPayroll[] = $tempRow;
        }

        //Expenses Report (Expenses)
        $rowExpenses = [];
        foreach ($allId as $schoolId) {
            // $school = DB::table('schools')->where('id',$schoolId)->first();
            $totalExpenses = DB::table('expenses as e')
                ->join('schools as s', 'e.school_id', '=', 's.id')
                ->select(
                    'e.school_id',
                    's.name as school_name',
                    DB::raw('SUM(e.amount) AS total_amount')
                )
                ->where('e.school_id', $schoolId)
                ->where('category_id', '!=', 3)
                ->whereNotNull('category_id')
                ->whereBetween('e.created_at', [$formattedStartDate, $formattedEndDate])
                ->groupBy('s.name')
                ->first();

            $tempRow = [];

            $tempRow['school_id'] = $totalExpenses->school_id ?? '';
            $tempRow['school_name'] = $totalExpenses->school_name ?? '';
            $tempRow['total_amount'] = $totalExpenses->total_amount ?? '';

            $rowExpenses[] = $tempRow;
        }

        //Income Report

        $rowIncome = [];

        foreach ($allId as $allIds) {
            $totalFees =  DB::table('student_fees_paids as sfp')
            ->join('schools as s', 'sfp.school_id', '=', 's.id')
            ->select(
                'sfp.school_id',
                's.name as school_name',
                DB::raw('SUM(sfp.amount) AS total_amount')
                )
                ->where('sfp.school_id', $allIds)
                ->where('sfp.is_fully_paid', 1)
                ->whereBetween('sfp.created_at',[$formattedStartDate,$formattedEndDate])
                ->groupBy('s.name')
                ->first();
                
                $total_fees = $totalFees->total_amount ?? 0;
                
                $totalSalary = DB::table('expenses as e')
                ->select(DB::raw('SUM(amount) AS total_amount'))
                ->whereNull('category_id')
                ->whereBetween('e.created_at', [$formattedStartDate,$formattedEndDate])
                ->where('school_id', $allIds)
                ->first();
                
                $total_salary = $totalSalary->total_amount ?? 0;
                
                $totalExpenses = DB::table('expenses as e')
                ->join('schools as s', 'e.school_id', '=', 's.id')
                ->select(
                    'e.school_id',
                    's.name as school_name',
                    DB::raw('SUM(e.amount) AS total_amount')
                    )
                    ->where('e.school_id', $allIds)
                    ->where('category_id', '!=', 3)
                    ->whereNotNull('category_id')
                    ->whereBetween('e.created_at',[$formattedStartDate,$formattedEndDate])
                    ->groupBy('s.name')
                    ->first();
                    
                    $total_expenses = $totalExpenses->total_amount ?? 0;
                    
                    $totalCreditNote = DB::table('credit_note_details as cnd')
                    ->join('credit_note as cn', 'cn.id', '=', 'cnd.credit_note_id')
                    ->select(DB::raw('SUM(cnd.credit_note_amount * cnd.quantity) AS total_amount'))
                    ->where('cn.status', 'published')
                    ->where('cnd.school_id', $allIds)
                    ->whereBetween('cnd.created_at',[$formattedStartDate,$formattedEndDate])
                    ->first();
                    
                    $total_credit_note = $totalCreditNote->total_amount ?? 0;
                    
                    $grand_total = $total_fees - $total_salary - $total_expenses - $total_credit_note;

                    $rowIncome[] = [
                        'school_id' => $allIds,
                        'school_name' => $totalFees->school_name ?? 'Empty',
                        'total_fees' => $total_fees,
                        'total_salary' => $total_salary,
                        'total_expenses' => $total_expenses,
                        'total_credit_note' => $total_credit_note,
                        'grand_total' => $grand_total
                    ];
                }
                    
        // dd($total_fees, $total_expenses, $total_salary, $total_credit_note, $grand_total);

        return response()->json(['totalCollection' => $rowCollection, 'totalExpenses' => $rowExpenses, 'totalPayroll' => $rowPayroll, 'totalIncome' => $rowIncome]);
    }
}
