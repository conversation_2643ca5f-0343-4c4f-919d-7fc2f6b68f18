<?php

namespace App\Http\Controllers;

use App\Models\Gallery;
use App\Repositories\Files\FilesInterface;
use App\Repositories\Gallery\GalleryInterface;
use App\Repositories\SessionYear\SessionYearInterface;
use App\Services\BootstrapTableService;
use App\Services\CachingService;
use App\Services\ResponseService;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
use Throwable;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;
use App\Services\FileSizeLimitService;


class GalleryController extends Controller
{
    private SessionYearInterface $sessionYear;
    private CachingService $cache;
    private GalleryInterface $gallery;
    private FilesInterface $files;

    public function __construct(SessionYearInterface $sessionYear, CachingService $cache, GalleryInterface $gallery, FilesInterface $files) {
        $this->sessionYear = $sessionYear;
        $this->cache = $cache;
        $this->gallery = $gallery;
        $this->files = $files;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
        ResponseService::noFeatureThenRedirect("School Gallery Management");
        ResponseService::noAnyPermissionThenRedirect(['gallery-create','gallery-list']);

        $sessionYears = $this->sessionYear->all();

        $schoolId = Auth::getUser()->school_id;
        $classSubjectSections = DB::select("SELECT class_subjects.id, class_subjects.class_id, (SELECT name FROM classes WHERE id = class_subjects.class_id) AS class_name,subject_id,(SELECT name FROM subjects WHERE id = class_subjects.subject_id) AS subject_name,class_sections.id AS section_id,(SELECT name FROM sections WHERE id = class_sections.section_id AND school_id = ".$schoolId.") AS section_name FROM class_subjects, class_sections WHERE class_subjects.school_id = ".$schoolId." AND class_sections.class_id = class_subjects.class_id");

        return view('gallery.index', compact('sessionYears', 'classSubjectSections'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
        ResponseService::noFeatureThenRedirect("School Gallery Management");
        ResponseService::noPermissionThenSendJson('gallery-create');
        
        $validator = Validator::make($request->all(), [
            'title' => 'required',
            'thumbnail' => 'required|mimes:jpg,svg,jpeg,png',
            'images.*' => 'mimes:jpg,svg,jpeg,png',
            'video' => 'mimes:mp4,avi,mkv,flv,webm,mov',
        ]);
        if ($validator->fails()) {
            ResponseService::errorResponse($validator->errors()->first());
        }

        try {
            DB::beginTransaction();
            if ($request->youtube_links) {
                $links = explode(",", $request->youtube_links);
                $status = 1;
                foreach ($links as $key => $link) {
                    if (preg_match("/^(?:http(?:s)?:\/\/)?(?:www\.)?(?:m\.)?(?:youtu\.be\/|youtube\.com\/((?:watch)\?(?:.*&)?v(?:i)?=|(?:embed|v|vi|user)\/))([^\?&\"'>]{11})/", $link, $matches)) {
                        $status = 1;
                    } else {
                        $status = 0;
                        break;
                    }
                }

                if ($status == 0) {
                    ResponseService::errorResponse('Please Enter Valid Youtube Link');
                }

            }
            $classSubjectName = 'All';
            $subjectId = '';
            $sectionId = '';
            $schoolId = Auth::getUser()->school_id;

            if($request->class_subject_id != ''){
                $subjectSectionIdArray = explode(":",$request->class_subject_id);
                $subjectId = $subjectSectionIdArray[0];
                $sectionId = $subjectSectionIdArray[1];
                $classSubjectSections = DB::select("SELECT class_subjects.id, class_subjects.class_id, (SELECT name FROM classes WHERE id = class_subjects.class_id) AS class_name,subject_id,(SELECT name FROM subjects WHERE id = class_subjects.subject_id) AS subject_name,class_sections.id AS section_id,(SELECT name FROM sections WHERE id = class_sections.section_id AND school_id = ".$schoolId.") AS section_name FROM class_subjects, class_sections WHERE class_subjects.school_id = ".$schoolId." AND class_sections.class_id = class_subjects.class_id AND class_subjects.id = ".$subjectId);
                if(COUNT($classSubjectSections) > 0){
                    $classSubjectName = $classSubjectSections[0]->class_name.' - '.$classSubjectSections[0]->subject_name.' - '.$classSubjectSections[0]->section_name;
                }

                if (empty($request->student_tag)) {
                    return ResponseService::errorResponse('You must select at least one student tag or all.');
                }
                elseif(in_array('all',$request->student_tag) && count($request->student_tag) > 1   ){
                    return ResponseService::errorResponse('You cannot select "all" and specific students together');
                } else{
                    if(!in_array('all',$request->student_tag)){
                        $student_tagged = implode(',', $request->student_tag);
                    }
                }
                
            }
           
            if ($request->hasFile('thumbnail')) {
                if (FileSizeLimitService::checkFileSizeLimit($schoolId)) {
                    return ResponseService::errorResponse('storage capacity not enough');}
                $thumbnailFile = $request->file('thumbnail');
                $thumbnailSize = $thumbnailFile->getSize();
                $thumbnailSizeKB = round($thumbnailSize / 1024, 2); // Convert size to KB

                $thumbnailPath = $thumbnailFile->store('gallery', 'public');
                
            $data = [
                'title' => $request->title,
                'description' => $request->description,
                'thumbnail' => $request->file('thumbnail')->store('gallery','public'),
                'session_year_id' => $request->session_year_id,
                'class_subject_id' => $subjectId,
                'class_section_id' => $sectionId,
                'class_subject_section_name' => $classSubjectName,
                'session_year_id' => $request->session_year_id,
                'student_tag' => $student_tagged ?? null,
                'file_size'=> $thumbnailSizeKB,
            ];
            }

            $gallery = $this->gallery->create($data);

            // Initialize the Empty Array
            $galleryFileData = array();

            // Create A File Model Instance
            $galleryFile = $this->files->model();

            // Get the Association Values of File with gallery
            $galleryModelAssociate = $galleryFile->modal()->associate($gallery);

            if (!empty($request->images)) {
                foreach ($request->images as $key => $image) {
                    $fileSize = $image->getSize();
                    $fileSizeKB = round($fileSize / 1024, 2);
                    $totalFileSizeKB= $fileSizeKB;

                    $tempFileData = array(
                        'modal_type' => $galleryModelAssociate->modal_type,
                        'modal_id'   => $galleryModelAssociate->modal_id,
                        'file_name'  => basename($image->getClientOriginalName(), '.'.$image->getClientOriginalExtension()),
                        'file_size'  => $totalFileSizeKB,
                    );

                    $tempFileData['type'] = 1;
                    $tempFileData['file_thumbnail'] = null;
                    $tempFileData['file_url'] = $image;

                    $galleryFileData[] = $tempFileData;
                }
            }

            //Video
            $video = $request->video;
            if (!empty($request->video)) {
                    $fileSize = $video->getSize();
                    $fileSizeKB = round($fileSize / 1024, 2);
                    $totalFileSizeKB = $fileSizeKB;

                    // Store video first
                    $videoPath = $video->store('files', 'public');

                    // Handle thumbnail
                    $thumbnailPath = null;
                    if ($request->has('video_thumbnail')) {
                        // Extract base64 data
                        $thumbnailData = $request->input('video_thumbnail');
                        $base64Image = preg_replace('#^data:image/\w+;base64,#i', '', $thumbnailData);
                        $thumbnailImage = base64_decode($base64Image);
                        
                        // Generate thumbnail filename based on video filename
                        $videoFileName = pathinfo($videoPath, PATHINFO_FILENAME);
                        $thumbnailFileName = $videoFileName . '-thumb.jpg';
                        $thumbnailPath = 'files/' . $thumbnailFileName;
                        
                        // Store thumbnail
                        Storage::disk('public')->put($thumbnailPath, $thumbnailImage);
                    }

                    $tempFileData = array(
                        'modal_type' => $galleryModelAssociate->modal_type,
                        'modal_id'   => $galleryModelAssociate->modal_id,
                        'file_name'  => basename($video->getClientOriginalName(), '.'.$video->getClientOriginalExtension()),
                        'file_size'  => $totalFileSizeKB,
                        'type' => 3,
                        'file_url' => $videoPath,
                        'file_thumbnail' => $thumbnailPath  // Add thumbnail path
                    );

                    $galleryFileData[] = $tempFileData;
                    $this->files->createBulk($galleryFileData);
            }

            // YouTube links
            if ($request->youtube_links) {
                foreach ($links as $key => $link) {
                    $tempFileData = array(
                        'modal_type' => $galleryModelAssociate->modal_type,
                        'modal_id'   => $galleryModelAssociate->modal_id,
                        'file_name'  => 'YouTube Link',
                        'file_size'  => 0.0
                    );

                    $tempFileData['type'] = 2;
                    $tempFileData['file_thumbnail'] = null;
                    $tempFileData['file_url'] = $link;

                    $galleryFileData[] = $tempFileData;
                }
            }
            if (!empty($request->images) || $request->youtube_links) {
                $this->files->createBulk($galleryFileData);
            }

            if ($classSubjectName == 'All') {
                // Fetch all students
                $student_ids = DB::table('students')->where('school_id', Auth::user()->school_id)->pluck('user_id')->toArray();
            } else {
                if (!empty($student_tagged)) {
                    // Fetch galleries with the specified student tags
                    $gallery_ids = explode(',', $student_tagged);
  
                    if (!empty($gallery_ids)) {
                        // Fetch student IDs associated with these galleries
                        $student_ids = DB::table('students')
                            ->join('users', 'students.user_id', '=', 'users.id')
                            ->whereIn('students.id', $gallery_ids)
                            ->pluck('students.user_id')
                            ->toArray();
                    } else {
                        $student_ids = [];
                    }
                } else {
                    if ($sectionId) {
                        $student_ids = DB::table('students')
                            ->where('class_section_id', $sectionId)
                            ->where('school_id', Auth::user()->school_id)
                            ->pluck('user_id')
                            ->toArray();
                    } else {
                        $student_ids = [];
                    }
                }
                
            }

            //Notifications
            if($student_ids){   
                $student_ids = collect($student_ids);
                $students = DB::table('students')
                ->join('users', 'students.user_id', '=', 'users.id')
                ->whereIn('students.user_id', $student_ids)
                ->whereNull('users.deleted_at')
                ->get(['students.user_id', 'students.guardian_id', 'users.first_name', 'users.last_name']);
                foreach ($students as $student) {
                    $notifyUser = [];
                    $studentName = $student->first_name . ' ' . $student->last_name;
                    $notifyUser[] = $student->user_id;

                    if ($student->guardian_id) {
                        $notifyUser[] = $student->guardian_id;
                    }
                    $title = 'Gallery Update ';
                    $body = $studentName . ' has added a new learning memory!';
                    $type = "gallery";
                    send_notification($notifyUser, $title, $body, $type);
                }
            }
            DB::commit();
            ResponseService::successResponse('Data Stored Successfully');
        } catch (\Throwable $th) {
            DB::rollBack();
            ResponseService::logErrorResponse($th, "Gallery Controller -> Store Method");
            ResponseService::errorResponse();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show($id)
    {
        //
        ResponseService::noFeatureThenRedirect("School Gallery Management");
        ResponseService::noPermissionThenSendJson('gallery-list');

        $offset = request('offset', 0);
        $limit = request('limit', 10);
        $sort = request('sort', 'id');
        $order = request('order', 'DESC');
        $search = request('search');
        $session_year_id = request('session_year_id');

        $sql = $this->gallery->builder()->with('file')
            ->where(function ($query) use ($search) {
                $query->when($search, function ($query) use ($search) {
                    $query->where(function ($query) use ($search) {
                        $query->where('id', 'LIKE', "%$search%")
                        ->orwhere('title', 'LIKE', "%$search%")
                        ->orwhere('description', 'LIKE', "%$search%");
                    });
                });
            })
            ->where(function ($query) use ($session_year_id) {
                $query->when($session_year_id, function ($query) use ($session_year_id) {
                    $query->where(function ($query) use ($session_year_id) {
                        $query->where('session_year_id',$session_year_id);
                    });
                });
            });


        $total = $sql->count();

        $sql->orderBy($sort, $order)->skip($offset)->take($limit);
        $res = $sql->get();

        $bulkData = array();
        $bulkData['total'] = $total;
        $rows = array();
        $no = 1;
        foreach ($res as $row) {
            $operate = '';
            $operate = BootstrapTableService::button('fa fa-eye', route('gallery.edit',$row->id), ['btn-gradient-info'], ['title' => trans("view")]);

            $operate .= BootstrapTableService::editButton(route('gallery.update', $row->id));
            $operate .= BootstrapTableService::deleteButton(route('gallery.destroy', $row->id));

            $tempRow = $row->toArray();
            $tempRow['no'] = $no++;
            $tempRow['files'] = $row->file;

            $studentTags = explode(',',$row->student_tag);
            if (!empty($studentTags) && !in_array('', $studentTags, true)){
                $studentName = [];
                foreach ($studentTags as $studentTag){
                    $students = DB::select("SELECT users.first_name, users.last_name FROM students JOIN users ON users.id = students.user_id WHERE students.id = ?",[$studentTag]);
                    if(!empty($students)){
                        $student = $students[0];
                        $studentName[] = $student->first_name . " " . $student->last_name;
                    }
                }
                $tempRow['student_tag_name'] = implode(', ', $studentName);
            } else {
                $tempRow['student_tag_name'] = 'All';
            }
            $tempRow['operate'] = $operate;
            $rows[] = $tempRow;
        }
        $bulkData['rows'] = $rows;
        return response()->json($bulkData);

    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($id)
    {
        //
        ResponseService::noFeatureThenRedirect("School Gallery Management");
        ResponseService::noPermissionThenSendJson('gallery-edit');

        $gallery = $this->gallery->builder()->with('file')->where('id',$id)->first();
        $sessionYears = $this->sessionYear->builder()->pluck('name','id');
        $schoolId = Auth::getUser()->school_id;
        $classSubjects = DB::select("SELECT id, class_id, (SELECT name FROM classes WHERE id = class_id) AS class_name, subject_id, (SELECT name FROM subjects WHERE id = subject_id) AS subject_name FROM class_subjects WHERE school_id = ".$schoolId);
        return view('gallery.edit',compact('gallery','sessionYears', 'classSubjects'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        //
        ResponseService::noFeatureThenRedirect("School Gallery Management");
        ResponseService::noPermissionThenSendJson('gallery-edit');
        
        $validator = Validator::make($request->all(), [
            'title' => 'required',
            'thumbnail' => 'mimes:jpg,svg,jpeg,png',
            'video' => 'mimes:mp4,avi,mkv,flv,webm,mov',
        ]);
        if ($validator->fails()) {
            ResponseService::errorResponse($validator->errors()->first());
        }

        try {
            DB::beginTransaction();
            $schoolId = Auth::getUser()->school_id;

            if ($request->youtube_links) {
                $links = explode(",", $request->youtube_links);
                $status = 1;
                foreach ($links as $key => $link) {
                    if (preg_match("/^(?:http(?:s)?:\/\/)?(?:www\.)?(?:m\.)?(?:youtu\.be\/|youtube\.com\/((?:watch)\?(?:.*&)?v(?:i)?=|(?:embed|v|vi|user)\/))([^\?&\"'>]{11})/", $link, $matches)) {
                        $status = 1;
                    } else {
                        $status = 0;
                        break;
                    }
                }

                if ($status == 0) {
                    ResponseService::errorResponse('Please Enter Valid Youtube Link');
                }
            }

            $gallery = $this->gallery->findById($id);
            $classSubjectName = 'All';            
            $subjectId = '';
            $sectionId = '';
            if($request->class_subject_id != ''){
                $schoolId = Auth::getUser()->school_id;

                $subjectSectionIdArray = explode(":",$request->class_subject_id);
                $subjectId = $subjectSectionIdArray[0];
                $sectionId = $subjectSectionIdArray[1];
                $classSubjectSections = DB::select("SELECT class_subjects.id, class_subjects.class_id, (SELECT name FROM classes WHERE id = class_subjects.class_id) AS class_name,subject_id,(SELECT name FROM subjects WHERE id = class_subjects.subject_id) AS subject_name,class_sections.id AS section_id,(SELECT name FROM sections WHERE id = class_sections.section_id AND school_id = ".$schoolId.") AS section_name FROM class_subjects, class_sections WHERE class_subjects.school_id = ".$schoolId." AND class_sections.class_id = class_subjects.class_id AND class_subjects.id = ".$subjectId);
                if(COUNT($classSubjectSections) > 0){
                    $classSubjectName = $classSubjectSections[0]->class_name.' - '.$classSubjectSections[0]->subject_name.' - '.$classSubjectSections[0]->section_name;
                }

                // if(in_array('all',$request->student_tag) && count($request->student_tag) > 1   ){
                //     return ResponseService::errorResponse('You cannot select "all" and specific students together');
                // } else{
                //     if(!in_array('all',$request->student_tag)){
                //         $student_tagged = implode(',', $request->student_tag);
                //     }
                // }
            }
            // if(empty($request->student_tag)) {
            //     // If no student tags are selected in the update, keep the original tags
            //     $student_tagged = $gallery->student_tag;
            // } else if(in_array('all',$request->student_tag) && count($request->student_tag) > 1) {
            //     return ResponseService::errorResponse('You cannot select "all" and specific students together');
            // } else {
            //     if(!in_array('all',$request->student_tag)){
            //         $student_tagged = implode(',', $request->student_tag);
            //     }
            // }
            $data = [
                'title' => $request->title,
                'description' => $request->description,
                'class_subject_id' => $subjectId,
                'class_section_id' => $sectionId,
                'class_subject_section_name' => $classSubjectName,
                'session_year_id' => $request->session_year_id,
                'student_tag' => $gallery->student_tag
            ];

            if ($request->hasFile('thumbnail')) {
                if (FileSizeLimitService::checkFileSizeLimit($schoolId)) {
                    return ResponseService::errorResponse('storage capacity not enough');
                }
                if (Storage::disk('public')->exists($gallery->getRawOriginal('thumbnail'))) {
                    Storage::disk('public')->delete($gallery->getRawOriginal('thumbnail'));
                }
                $thumbnailFile = $request->file('thumbnail');
                $thumbnailSize = $thumbnailFile->getSize();
                $thumbnailSizeKB = round($thumbnailSize / 1024, 2);
                $data['thumbnail'] = $thumbnailFile->store('gallery','public');
                $data['file_size'] = $thumbnailSizeKB;
            }
    
            $this->gallery->update($id,$data);

            // Initialize the Empty Array
            $galleryFileData = array();

            // Create A File Model Instance
            $galleryFile = $this->files->model();

            // Get the Association Values of File with gallery
            $galleryModelAssociate = $galleryFile->modal()->associate($gallery);

            if (!empty($request->images)) {
                foreach ($request->images as $key => $image) {
                    if (FileSizeLimitService::checkFileSizeLimit($schoolId)) {
                        return ResponseService::errorResponse('storage capacity not enough');
                    }
                    $fileSize = $image->getSize();
                    $fileSizeKB = round($fileSize / 1024, 2);
                    
                    $tempFileData = array(
                        'modal_type' => $galleryModelAssociate->modal_type,
                        'modal_id'   => $galleryModelAssociate->modal_id,
                        'file_name'  => basename($image->getClientOriginalName(), '.'.$image->getClientOriginalExtension()),
                        'file_size'  => $fileSizeKB,
                        'type'       => 1,
                        'file_thumbnail' => null,
                        'file_url'   => $image->store('files', 'public')
                    );

                    $galleryFileData[] = $tempFileData;
                }
            }

            // Video upload handling - same as store method
            if ($request->hasFile('video')) {
                if (FileSizeLimitService::checkFileSizeLimit($schoolId)) {
                    return ResponseService::errorResponse('storage capacity not enough');
                }
                
                $video = $request->file('video');
                $fileSize = $video->getSize();
                $fileSizeKB = round($fileSize / 1024, 2);
                $videoPath = $video->store('files', 'public');

                // Handle thumbnail
                $thumbnailPath = null;
                if ($request->has('video_thumbnail')) {
                    // Extract base64 data
                    $thumbnailData = $request->input('video_thumbnail');
                    $base64Image = preg_replace('#^data:image/\w+;base64,#i', '', $thumbnailData);
                    $thumbnailImage = base64_decode($base64Image);
                    
                    // Generate thumbnail filename based on video filename
                    $videoFileName = pathinfo($videoPath, PATHINFO_FILENAME);
                    $thumbnailFileName = $videoFileName . '-thumb.jpg';
                    $thumbnailPath = 'files/' . $thumbnailFileName;
                    
                    // Store thumbnail
                    Storage::disk('public')->put($thumbnailPath, $thumbnailImage);
                }

                $tempFileData = array(
                    'modal_type' => $galleryModelAssociate->modal_type,
                    'modal_id'   => $galleryModelAssociate->modal_id,
                    'file_name'  => basename($video->getClientOriginalName(), '.'.$video->getClientOriginalExtension()),
                    'file_size'  => $fileSizeKB,
                    'type'       => 3,
                    'file_url'   => $videoPath,
                    'file_thumbnail' => $thumbnailPath
                );

                $galleryFileData[] = $tempFileData;
            }

            // YouTube links
            if ($request->youtube_links) {
                $links = explode(",", $request->youtube_links);
                foreach ($links as $key => $link) {
                    $tempFileData = array(
                        'modal_type' => $galleryModelAssociate->modal_type,
                        'modal_id'   => $galleryModelAssociate->modal_id,
                        'file_name'  => 'YouTube Link',
                        'type'       => 2,
                        'file_thumbnail' => null,
                        'file_url'   => $link
                    );

                    $galleryFileData[] = $tempFileData;
                }
            }

            if (!empty($galleryFileData)) {
                $this->files->createBulk($galleryFileData);
            }

            DB::commit();
            ResponseService::successResponse('Data Updated Successfully');
        } catch (\Throwable $th) {
            DB::rollBack();
            ResponseService::logErrorResponse($th, "Gallery Controller -> Update Method");
            ResponseService::errorResponse();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        //
        ResponseService::noFeatureThenRedirect('School Gallery Management');
        ResponseService::noPermissionThenSendJson('gallery-delete');
        try {
            DB::beginTransaction();

            // Find the Data by FindByID
            $gallery = $this->gallery->findById($id);
            if (Storage::disk('public')->exists($gallery->getRawOriginal('thumbnail'))) {
                Storage::disk('public')->delete($gallery->getRawOriginal('thumbnail'));
            }

            $gallery->delete();

            DB::commit();
            ResponseService::successResponse('Data Deleted Successfully');
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "Gallery Controller -> destroy Method");
            ResponseService::errorResponse();
        }
    }

    public function deleteFile($id) {
        ResponseService::noFeatureThenRedirect('School Gallery Management');
        ResponseService::noPermissionThenSendJson('gallery-delete');
        try {
            DB::beginTransaction();

            // Find the Data by FindByID
            $file = $this->files->findById($id);
            if (Storage::disk('public')->exists($file->getRawOriginal('file_url'))) {
                Storage::disk('public')->delete($file->getRawOriginal('file_url'));
            }

            // Delete the file data
            $file->delete();

            DB::commit();
            ResponseService::successResponse('Data Deleted Successfully');
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "Gallery Controller -> deleteFile Method");
            ResponseService::errorResponse();
        }
    }

    public function getStudentTags(Request $request){
        $class_section_id = $request->class_section_id;
        $school_id = Auth::user()->school_id;
        $students = DB::select("SELECT students.id, users.first_name, users.last_name, users.email FROM students JOIN users ON users.id = students.user_id WHERE students.school_id = ? AND students.class_section_id = ? AND users.deleted_at IS NULL",[$school_id,$class_section_id]);

        return response()->json($students);
    }
}
