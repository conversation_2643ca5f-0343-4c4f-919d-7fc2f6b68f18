{"__meta": {"id": "01JX1D4QYS91KFHZ3FKBVSY6EK", "datetime": "2025-06-06 09:19:32", "utime": **********.82633, "method": "GET", "uri": "/student-fees/paid/receipt-pdf/14505/invoice", "ip": "127.0.0.1"}, "messages": {"count": 1, "messages": [{"message": "[09:19:22] LOG.warning: Optional parameter $includeSignature declared before required parameter $invoiceTypeCode is implicitly treated as a required parameter in D:\\laragon\\www\\schola\\app\\Services\\EInvoiceFormatService.php on line 748", "message_html": null, "is_string": false, "label": "warning", "time": **********.820303, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.354673, "end": **********.826356, "duration": 10.471683025360107, "duration_str": "10.47s", "measures": [{"label": "Booting", "start": **********.354673, "relative_start": 0, "end": **********.749206, "relative_end": **********.749206, "duration": 0.****************, "duration_str": "395ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.749222, "relative_start": 0.****************, "end": **********.826359, "relative_end": 3.0994415283203125e-06, "duration": 10.***************, "duration_str": "10.08s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.777567, "relative_start": 0.****************, "end": **********.787852, "relative_end": **********.787852, "duration": 0.010285139083862305, "duration_str": "10.29ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: student-fees.consolidate_fees_receipt", "start": **********.622476, "relative_start": 4.***************, "end": **********.622476, "relative_end": **********.622476, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.822993, "relative_start": 10.**************, "end": **********.823228, "relative_end": **********.823228, "duration": 0.00023484230041503906, "duration_str": "235μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "91MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.1.10", "Environment": "local", "Debug Mode": "Enabled", "URL": "schola.one", "Timezone": "Asia/Kuala_Lumpur", "Locale": "en"}}, "views": {"count": 1, "nb_templates": 1, "templates": [{"name": "student-fees.consolidate_fees_receipt", "param_count": null, "params": [], "start": **********.622424, "type": "blade", "hash": "bladeD:\\laragon\\www\\schola\\resources\\views/student-fees/consolidate_fees_receipt.blade.phpstudent-fees.consolidate_fees_receipt", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fresources%2Fviews%2Fstudent-fees%2Fconsolidate_fees_receipt.blade.php&line=1", "ajax": false, "filename": "consolidate_fees_receipt.blade.php", "line": "?"}}]}, "queries": {"count": 30, "nb_statements": 30, "nb_visible_statements": 30, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.27482, "accumulated_duration_str": "275ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 2 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.837651, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "schola", "explain": null, "start_percent": 0, "width_percent": 0.266}, {"sql": "select * from `languages` where `code` = 'en' limit 1", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": "middleware", "name": "language", "file": "D:\\laragon\\www\\schola\\app\\Http\\Middleware\\LanguageManager.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 18, "namespace": null, "name": "app/Http/Middleware/DemoMiddleware.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Middleware\\DemoMiddleware.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}], "start": **********.843915, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "language:35", "source": {"index": 16, "namespace": "middleware", "name": "language", "file": "D:\\laragon\\www\\schola\\app\\Http\\Middleware\\LanguageManager.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FMiddleware%2FLanguageManager.php&line=35", "ajax": false, "filename": "LanguageManager.php", "line": "35"}, "connection": "schola", "explain": null, "start_percent": 0.266, "width_percent": 0.273}, {"sql": "select * from `schools` where `schools`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": "middleware", "name": "checkSchoolStatus", "file": "D:\\laragon\\www\\schola\\app\\Http\\Middleware\\CheckSchoolStatus.php", "line": 13}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 24, "namespace": "middleware", "name": "Role", "file": "D:\\laragon\\www\\schola\\app\\Http\\Middleware\\CheckRole.php", "line": 22}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": 1749172763.161436, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "checkSchoolStatus:13", "source": {"index": 22, "namespace": "middleware", "name": "checkSchoolStatus", "file": "D:\\laragon\\www\\schola\\app\\Http\\Middleware\\CheckSchoolStatus.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FMiddleware%2FCheckSchoolStatus.php&line=13", "ajax": false, "filename": "CheckSchoolStatus.php", "line": "13"}, "connection": "schola", "explain": null, "start_percent": 0.539, "width_percent": 0.226}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (2) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, {"index": 23, "namespace": "middleware", "name": "checkSchoolStatus", "file": "D:\\laragon\\www\\schola\\app\\Http\\Middleware\\CheckSchoolStatus.php", "line": 27}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "Role", "file": "D:\\laragon\\www\\schola\\app\\Http\\Middleware\\CheckRole.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": 1749172763.4452908, "duration": 0.00428, "duration_str": "4.28ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:188", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=188", "ajax": false, "filename": "HasRoles.php", "line": "188"}, "connection": "schola", "explain": null, "start_percent": 0.764, "width_percent": 1.557}, {"sql": "select * from `subscriptions` where `school_id` = 1 and date(`start_date`) <= '2025-06-06' and date(`end_date`) >= '2025-06-06' order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": [1, "2025-06-06", "2025-06-06"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/SubscriptionService.php", "file": "D:\\laragon\\www\\schola\\app\\Services\\SubscriptionService.php", "line": 288}, {"index": 17, "namespace": null, "name": "app/Services/FeaturesService.php", "file": "D:\\laragon\\www\\schola\\app\\Services\\FeaturesService.php", "line": 21}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 21, "namespace": null, "name": "app/Services/CachingService.php", "file": "D:\\laragon\\www\\schola\\app\\Services\\CachingService.php", "line": 119}], "start": 1749172763.900594, "duration": 0.00337, "duration_str": "3.37ms", "memory": 0, "memory_str": null, "filename": "SubscriptionService.php:288", "source": {"index": 16, "namespace": null, "name": "app/Services/SubscriptionService.php", "file": "D:\\laragon\\www\\schola\\app\\Services\\SubscriptionService.php", "line": 288}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FServices%2FSubscriptionService.php&line=288", "ajax": false, "filename": "SubscriptionService.php", "line": "288"}, "connection": "schola", "explain": null, "start_percent": 2.322, "width_percent": 1.226}, {"sql": "select * from `subscriptions` where `school_id` = 1 and `package_type` = 1 and date(`start_date`) <= '2025-06-06' and date(`end_date`) >= '2025-06-06' and not exists (select * from `subscription_bills` where `subscriptions`.`id` = `subscription_bills`.`subscription_id`) and exists (select * from `subscription_features` where `subscriptions`.`id` = `subscription_features`.`subscription_id`) order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": [1, 1, "2025-06-06", "2025-06-06"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/SubscriptionService.php", "file": "D:\\laragon\\www\\schola\\app\\Services\\SubscriptionService.php", "line": 293}, {"index": 17, "namespace": null, "name": "app/Services/FeaturesService.php", "file": "D:\\laragon\\www\\schola\\app\\Services\\FeaturesService.php", "line": 21}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 21, "namespace": null, "name": "app/Services/CachingService.php", "file": "D:\\laragon\\www\\schola\\app\\Services\\CachingService.php", "line": 119}], "start": 1749172764.1261609, "duration": 0.006030000000000001, "duration_str": "6.03ms", "memory": 0, "memory_str": null, "filename": "SubscriptionService.php:293", "source": {"index": 16, "namespace": null, "name": "app/Services/SubscriptionService.php", "file": "D:\\laragon\\www\\schola\\app\\Services\\SubscriptionService.php", "line": 293}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FServices%2FSubscriptionService.php&line=293", "ajax": false, "filename": "SubscriptionService.php", "line": "293"}, "connection": "schola", "explain": null, "start_percent": 3.548, "width_percent": 2.194}, {"sql": "select * from `subscription_features` where `subscription_features`.`subscription_id` in (86)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/SubscriptionService.php", "file": "D:\\laragon\\www\\schola\\app\\Services\\SubscriptionService.php", "line": 293}, {"index": 22, "namespace": null, "name": "app/Services/FeaturesService.php", "file": "D:\\laragon\\www\\schola\\app\\Services\\FeaturesService.php", "line": 21}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 26, "namespace": null, "name": "app/Services/CachingService.php", "file": "D:\\laragon\\www\\schola\\app\\Services\\CachingService.php", "line": 119}], "start": 1749172764.133631, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "SubscriptionService.php:293", "source": {"index": 21, "namespace": null, "name": "app/Services/SubscriptionService.php", "file": "D:\\laragon\\www\\schola\\app\\Services\\SubscriptionService.php", "line": 293}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FServices%2FSubscriptionService.php&line=293", "ajax": false, "filename": "SubscriptionService.php", "line": "293"}, "connection": "schola", "explain": null, "start_percent": 5.742, "width_percent": 0.302}, {"sql": "select * from `features` where `features`.`id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 22, 26, 27)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Services/SubscriptionService.php", "file": "D:\\laragon\\www\\schola\\app\\Services\\SubscriptionService.php", "line": 293}, {"index": 27, "namespace": null, "name": "app/Services/FeaturesService.php", "file": "D:\\laragon\\www\\schola\\app\\Services\\FeaturesService.php", "line": 21}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 31, "namespace": null, "name": "app/Services/CachingService.php", "file": "D:\\laragon\\www\\schola\\app\\Services\\CachingService.php", "line": 119}], "start": 1749172764.1623101, "duration": 0.00649, "duration_str": "6.49ms", "memory": 0, "memory_str": null, "filename": "SubscriptionService.php:293", "source": {"index": 26, "namespace": null, "name": "app/Services/SubscriptionService.php", "file": "D:\\laragon\\www\\schola\\app\\Services\\SubscriptionService.php", "line": 293}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FServices%2FSubscriptionService.php&line=293", "ajax": false, "filename": "SubscriptionService.php", "line": "293"}, "connection": "schola", "explain": null, "start_percent": 6.044, "width_percent": 2.362}, {"sql": "select * from `addon_subscriptions` where `addon_subscriptions`.`subscription_id` in (86) and `addon_subscriptions`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/SubscriptionService.php", "file": "D:\\laragon\\www\\schola\\app\\Services\\SubscriptionService.php", "line": 293}, {"index": 22, "namespace": null, "name": "app/Services/FeaturesService.php", "file": "D:\\laragon\\www\\schola\\app\\Services\\FeaturesService.php", "line": 21}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 26, "namespace": null, "name": "app/Services/CachingService.php", "file": "D:\\laragon\\www\\schola\\app\\Services\\CachingService.php", "line": 119}], "start": 1749172764.1712239, "duration": 0.00237, "duration_str": "2.37ms", "memory": 0, "memory_str": null, "filename": "SubscriptionService.php:293", "source": {"index": 21, "namespace": null, "name": "app/Services/SubscriptionService.php", "file": "D:\\laragon\\www\\schola\\app\\Services\\SubscriptionService.php", "line": 293}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FServices%2FSubscriptionService.php&line=293", "ajax": false, "filename": "SubscriptionService.php", "line": "293"}, "connection": "schola", "explain": null, "start_percent": 8.406, "width_percent": 0.862}, {"sql": "select * from `features` where `features`.`id` in (23, 24, 25, 28, 29, 32)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Services/SubscriptionService.php", "file": "D:\\laragon\\www\\schola\\app\\Services\\SubscriptionService.php", "line": 293}, {"index": 27, "namespace": null, "name": "app/Services/FeaturesService.php", "file": "D:\\laragon\\www\\schola\\app\\Services\\FeaturesService.php", "line": 21}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 31, "namespace": null, "name": "app/Services/CachingService.php", "file": "D:\\laragon\\www\\schola\\app\\Services\\CachingService.php", "line": 119}], "start": 1749172764.176081, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "SubscriptionService.php:293", "source": {"index": 26, "namespace": null, "name": "app/Services/SubscriptionService.php", "file": "D:\\laragon\\www\\schola\\app\\Services\\SubscriptionService.php", "line": 293}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FServices%2FSubscriptionService.php&line=293", "ajax": false, "filename": "SubscriptionService.php", "line": "293"}, "connection": "schola", "explain": null, "start_percent": 9.268, "width_percent": 0.269}, {"sql": "select * from `packages` where `packages`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/SubscriptionService.php", "file": "D:\\laragon\\www\\schola\\app\\Services\\SubscriptionService.php", "line": 293}, {"index": 22, "namespace": null, "name": "app/Services/FeaturesService.php", "file": "D:\\laragon\\www\\schola\\app\\Services\\FeaturesService.php", "line": 21}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 26, "namespace": null, "name": "app/Services/CachingService.php", "file": "D:\\laragon\\www\\schola\\app\\Services\\CachingService.php", "line": 119}], "start": 1749172764.178927, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "SubscriptionService.php:293", "source": {"index": 21, "namespace": null, "name": "app/Services/SubscriptionService.php", "file": "D:\\laragon\\www\\schola\\app\\Services\\SubscriptionService.php", "line": 293}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FServices%2FSubscriptionService.php&line=293", "ajax": false, "filename": "SubscriptionService.php", "line": "293"}, "connection": "schola", "explain": null, "start_percent": 9.537, "width_percent": 0.331}, {"sql": "select * from `package_features` where `package_features`.`package_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Services/SubscriptionService.php", "file": "D:\\laragon\\www\\schola\\app\\Services\\SubscriptionService.php", "line": 293}, {"index": 27, "namespace": null, "name": "app/Services/FeaturesService.php", "file": "D:\\laragon\\www\\schola\\app\\Services\\FeaturesService.php", "line": 21}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 31, "namespace": null, "name": "app/Services/CachingService.php", "file": "D:\\laragon\\www\\schola\\app\\Services\\CachingService.php", "line": 119}], "start": 1749172764.204463, "duration": 0.02429, "duration_str": "24.29ms", "memory": 0, "memory_str": null, "filename": "SubscriptionService.php:293", "source": {"index": 26, "namespace": null, "name": "app/Services/SubscriptionService.php", "file": "D:\\laragon\\www\\schola\\app\\Services\\SubscriptionService.php", "line": 293}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FServices%2FSubscriptionService.php&line=293", "ajax": false, "filename": "SubscriptionService.php", "line": "293"}, "connection": "schola", "explain": null, "start_percent": 9.868, "width_percent": 8.839}, {"sql": "select * from `features` where `features`.`id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 22, 26, 27)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 31, "namespace": null, "name": "app/Services/SubscriptionService.php", "file": "D:\\laragon\\www\\schola\\app\\Services\\SubscriptionService.php", "line": 293}, {"index": 32, "namespace": null, "name": "app/Services/FeaturesService.php", "file": "D:\\laragon\\www\\schola\\app\\Services\\FeaturesService.php", "line": 21}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 34, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 36, "namespace": null, "name": "app/Services/CachingService.php", "file": "D:\\laragon\\www\\schola\\app\\Services\\CachingService.php", "line": 119}], "start": 1749172764.230625, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "SubscriptionService.php:293", "source": {"index": 31, "namespace": null, "name": "app/Services/SubscriptionService.php", "file": "D:\\laragon\\www\\schola\\app\\Services\\SubscriptionService.php", "line": 293}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FServices%2FSubscriptionService.php&line=293", "ajax": false, "filename": "SubscriptionService.php", "line": "293"}, "connection": "schola", "explain": null, "start_percent": 18.707, "width_percent": 0.317}, {"sql": "select * from `subscription_bills` where `school_id` = 1 and exists (select * from `payment_transactions` where `subscription_bills`.`payment_transaction_id` = `payment_transactions`.`id` and not `payment_status` = 'succeed') and `due_date` < '2025-06-06' limit 1", "type": "query", "params": [], "bindings": [1, "succeed", "2025-06-06"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/FeaturesService.php", "file": "D:\\laragon\\www\\schola\\app\\Services\\FeaturesService.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "app/Services/CachingService.php", "file": "D:\\laragon\\www\\schola\\app\\Services\\CachingService.php", "line": 119}, {"index": 21, "namespace": null, "name": "app/Services/FeaturesService.php", "file": "D:\\laragon\\www\\schola\\app\\Services\\FeaturesService.php", "line": 20}], "start": 1749172764.233431, "duration": 0.039630000000000006, "duration_str": "39.63ms", "memory": 0, "memory_str": null, "filename": "FeaturesService.php:30", "source": {"index": 16, "namespace": null, "name": "app/Services/FeaturesService.php", "file": "D:\\laragon\\www\\schola\\app\\Services\\FeaturesService.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FServices%2FFeaturesService.php&line=30", "ajax": false, "filename": "FeaturesService.php", "line": "30"}, "connection": "schola", "explain": null, "start_percent": 19.023, "width_percent": 14.42}, {"sql": "select `name`, `id` from `features` where `id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 22, 26, 27, 23, 24, 25, 28, 29, 32)", "type": "query", "params": [], "bindings": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 22, 26, 27, 23, 24, 25, 28, 29, 32], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Services/FeaturesService.php", "file": "D:\\laragon\\www\\schola\\app\\Services\\FeaturesService.php", "line": 45}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 18, "namespace": null, "name": "app/Services/CachingService.php", "file": "D:\\laragon\\www\\schola\\app\\Services\\CachingService.php", "line": 119}, {"index": 19, "namespace": null, "name": "app/Services/FeaturesService.php", "file": "D:\\laragon\\www\\schola\\app\\Services\\FeaturesService.php", "line": 20}], "start": 1749172764.279298, "duration": 0.00282, "duration_str": "2.82ms", "memory": 0, "memory_str": null, "filename": "FeaturesService.php:45", "source": {"index": 14, "namespace": null, "name": "app/Services/FeaturesService.php", "file": "D:\\laragon\\www\\schola\\app\\Services\\FeaturesService.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FServices%2FFeaturesService.php&line=45", "ajax": false, "filename": "FeaturesService.php", "line": "45"}, "connection": "schola", "explain": null, "start_percent": 33.444, "width_percent": 1.026}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 266}, {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 304}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 174}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 175}], "start": 1749172764.467612, "duration": 0.00234, "duration_str": "2.34ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:266", "source": {"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 266}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=266", "ajax": false, "filename": "PermissionRegistrar.php", "line": "266"}, "connection": "schola", "explain": null, "start_percent": 34.47, "width_percent": 0.851}, {"sql": "select `roles`.*, `role_has_permissions`.`permission_id` as `pivot_permission_id`, `role_has_permissions`.`role_id` as `pivot_role_id` from `roles` inner join `role_has_permissions` on `roles`.`id` = `role_has_permissions`.`role_id` where `role_has_permissions`.`permission_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 600, 601, 602, 603, 604, 605, 606, 607, 608, 609, 610, 613, 1259, 1260, 1261, 1262, 1263, 1265, 1266, 1267, 1268, 1269, 1270, 1271, 1272, 1273)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 266}, {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 304}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 174}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 175}], "start": 1749172764.476872, "duration": 0.04327, "duration_str": "43.27ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:266", "source": {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 266}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=266", "ajax": false, "filename": "PermissionRegistrar.php", "line": "266"}, "connection": "schola", "explain": null, "start_percent": 35.321, "width_percent": 15.745}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 2 and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [2, "App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 193}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 244}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 136}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.214971, "duration": 0.0028399999999999996, "duration_str": "2.84ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:305", "source": {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\laragon\\www\\schola\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=305", "ajax": false, "filename": "HasPermissions.php", "line": "305"}, "connection": "schola", "explain": null, "start_percent": 51.066, "width_percent": 1.033}, {"sql": "select * from `student_fees_paids` where `school_id` = 1 and `student_fees_id` = '14505' and `student_fees_paids`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1, "14505"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 4145}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.304202, "duration": 0.01431, "duration_str": "14.31ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:4145", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 4145}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=4145", "ajax": false, "filename": "StudentFeesController.php", "line": "4145"}, "connection": "schola", "explain": null, "start_percent": 52.1, "width_percent": 5.207}, {"sql": "select * from `student_fees` where `school_id` = 1 and `student_fees`.`id` = 14505 and `student_fees`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1, 14505], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Repositories/Base/BaseRepository.php", "file": "D:\\laragon\\www\\schola\\app\\Repositories\\Base\\BaseRepository.php", "line": 62}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 4147}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.3201609, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:62", "source": {"index": 18, "namespace": null, "name": "app/Repositories/Base/BaseRepository.php", "file": "D:\\laragon\\www\\schola\\app\\Repositories\\Base\\BaseRepository.php", "line": 62}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FRepositories%2FBase%2FBaseRepository.php&line=62", "ajax": false, "filename": "BaseRepository.php", "line": "62"}, "connection": "schola", "explain": null, "start_percent": 57.307, "width_percent": 0.207}, {"sql": "select * from `student_fees_consolidate` where `reference_uid` = *********", "type": "query", "params": [], "bindings": [*********], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 4321}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.322019, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:4321", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 4321}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=4321", "ajax": false, "filename": "StudentFeesController.php", "line": "4321"}, "connection": "schola", "explain": null, "start_percent": 57.514, "width_percent": 0.196}, {"sql": "select * from `student_fees` where `id` = '14505'", "type": "query", "params": [], "bindings": ["14505"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 4327}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.323905, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:4327", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 4327}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=4327", "ajax": false, "filename": "StudentFeesController.php", "line": "4327"}, "connection": "schola", "explain": null, "start_percent": 57.711, "width_percent": 0.124}, {"sql": "select * from `student_fees` where `id` in (14504, 14502) and `school_id` = 1", "type": "query", "params": [], "bindings": [14504, 14502, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 4332}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.3251052, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:4332", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 4332}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=4332", "ajax": false, "filename": "StudentFeesController.php", "line": "4332"}, "connection": "schola", "explain": null, "start_percent": 57.834, "width_percent": 0.164}, {"sql": "select * from `school_settings` where `school_id` = 1 and `school_id` = 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/CachingService.php", "file": "D:\\laragon\\www\\schola\\app\\Services\\CachingService.php", "line": 131}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 19, "namespace": null, "name": "app/Services/CachingService.php", "file": "D:\\laragon\\www\\schola\\app\\Services\\CachingService.php", "line": 119}, {"index": 20, "namespace": null, "name": "app/Services/CachingService.php", "file": "D:\\laragon\\www\\schola\\app\\Services\\CachingService.php", "line": 130}], "start": **********.365914, "duration": 0.03631, "duration_str": "36.31ms", "memory": 0, "memory_str": null, "filename": "CachingService.php:131", "source": {"index": 15, "namespace": null, "name": "app/Services/CachingService.php", "file": "D:\\laragon\\www\\schola\\app\\Services\\CachingService.php", "line": 131}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FServices%2FCachingService.php&line=131", "ajax": false, "filename": "CachingService.php", "line": "131"}, "connection": "schola", "explain": null, "start_percent": 57.998, "width_percent": 13.212}, {"sql": "SELECT data FROM school_settings WHERE name = 'horizontal_logo' AND school_id = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 4338}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.4213722, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:4338", "source": {"index": 11, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 4338}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=4338", "ajax": false, "filename": "StudentFeesController.php", "line": "4338"}, "connection": "schola", "explain": null, "start_percent": 71.21, "width_percent": 0.16}, {"sql": "select * from `student_fees_einvoice` where `student_fees_id` = '14505' order by `id` desc limit 1", "type": "query", "params": [], "bindings": ["14505"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 4340}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.42346, "duration": 0.03587, "duration_str": "35.87ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:4340", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 4340}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=4340", "ajax": false, "filename": "StudentFeesController.php", "line": "4340"}, "connection": "schola", "explain": null, "start_percent": 71.37, "width_percent": 13.052}, {"sql": "select * from `e_invoice` where `school_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 4344}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.460197, "duration": 0.00296, "duration_str": "2.96ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:4344", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 4344}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=4344", "ajax": false, "filename": "StudentFeesController.php", "line": "4344"}, "connection": "schola", "explain": null, "start_percent": 84.423, "width_percent": 1.077}, {"sql": "select sum(`amount`) as aggregate from `student_fees_paids` where `student_fees_id` = 14502", "type": "query", "params": [], "bindings": [14502], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 4361}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.46398, "duration": 0.038689999999999995, "duration_str": "38.69ms", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:4361", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 4361}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=4361", "ajax": false, "filename": "StudentFeesController.php", "line": "4361"}, "connection": "schola", "explain": null, "start_percent": 85.5, "width_percent": 14.078}, {"sql": "select sum(`amount`) as aggregate from `student_fees_paids` where `student_fees_id` = 14504", "type": "query", "params": [], "bindings": [14504], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 4361}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.503448, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:4361", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 4361}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=4361", "ajax": false, "filename": "StudentFeesController.php", "line": "4361"}, "connection": "schola", "explain": null, "start_percent": 99.578, "width_percent": 0.153}, {"sql": "select * from `student_fees_einvoice` where `student_fees_id` = '14505' order by `id` desc limit 1", "type": "query", "params": [], "bindings": ["14505"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 4371}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.504545, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "StudentFeesController.php:4371", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StudentFeesController.php", "file": "D:\\laragon\\www\\schola\\app\\Http\\Controllers\\StudentFeesController.php", "line": 4371}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=4371", "ajax": false, "filename": "StudentFeesController.php", "line": "4371"}, "connection": "schola", "explain": null, "start_percent": 99.731, "width_percent": 0.269}]}, "models": {"data": {"Spatie\\Permission\\Models\\Role": {"value": 13675, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Spatie\\Permission\\Models\\Permission": {"value": 227, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\SchoolSetting": {"value": 118, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FSchoolSetting.php&line=1", "ajax": false, "filename": "SchoolSetting.php", "line": "?"}}, "App\\Models\\Feature": {"value": 52, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FFeature.php&line=1", "ajax": false, "filename": "Feature.php", "line": "?"}}, "App\\Models\\SubscriptionFeature": {"value": 23, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FSubscriptionFeature.php&line=1", "ajax": false, "filename": "SubscriptionFeature.php", "line": "?"}}, "App\\Models\\PackageFeature": {"value": 23, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FPackageFeature.php&line=1", "ajax": false, "filename": "PackageFeature.php", "line": "?"}}, "App\\Models\\AddonSubscription": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FAddonSubscription.php&line=1", "ajax": false, "filename": "AddonSubscription.php", "line": "?"}}, "App\\Models\\Subscription": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FSubscription.php&line=1", "ajax": false, "filename": "Subscription.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Language": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "App\\Models\\School": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FSchool.php&line=1", "ajax": false, "filename": "School.php", "line": "?"}}, "App\\Models\\Package": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FPackage.php&line=1", "ajax": false, "filename": "Package.php", "line": "?"}}, "App\\Models\\StudentFee": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FStudentFee.php&line=1", "ajax": false, "filename": "StudentFee.php", "line": "?"}}}, "count": 14131, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => fees-paid,\n  target => null,\n  result => true,\n  user => 2,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1792797938 data-indent-pad=\"  \"><span class=sf-dump-note>fees-paid </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">fees-paid</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1792797938\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.222412, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "https://schola.test/student-fees/paid/receipt-pdf/14505/invoice", "action_name": "student-fees.paid.receipt.pdf", "controller_action": "App\\Http\\Controllers\\StudentFeesController@feesPaidReceiptPDF", "uri": "GET student-fees/paid/receipt-pdf/{id}/{filename?}/{isPaid?}", "controller": "App\\Http\\Controllers\\StudentFeesController@feesPaidReceiptPDF<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=4136\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/student-fees", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FHttp%2FControllers%2FStudentFeesController.php&line=4136\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/StudentFeesController.php:4136-4398</a>", "middleware": "web, Role, auth, checkSchoolStatus, status, language", "duration": "10.47s", "peak_memory": "106MB", "response": "application/pdf", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1830434075 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1830434075\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1803707147 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1803707147\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-320429577 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">schola.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">https://schola.test/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"742 characters\">XSRF-TOKEN=eyJpdiI6IlBibEhCZlppOXJGMzBZSFFsWGYra1E9PSIsInZhbHVlIjoiTnhnWmUrUzhjS2FkQ3VoWGc3Vy9uVlJPanVqMFVMbmhjcnlFa2RURCtHWkhXUTBmRG5sS1U3d3ZoSHRnTGV1V1Zrdkl3ejBMdXJXNmRoWkVDTkwvcFR5V2JpaVJKQnB3UUkvdE1IY3NIcGFiS085OU9WRnhTeUg0UUM1Kys0Mk4iLCJtYWMiOiI5YmQzY2M1NWE1ZGZmYzkxZGY4MDBiOTc4YzliZWNkZDE3NWM1YjIzZTRmOTY3YjZkMzMzN2IyODZjZjQxOWFmIiwidGFnIjoiIn0%3D; schola_saas_school_management_system_session=eyJpdiI6ImxiTzZzTXZrTk91WGwvdmtUR1dMVlE9PSIsInZhbHVlIjoiZ3dzd2tvZEZuUm1YVUF0SnNkOFRqTWVWcTU2YU03NVdVZnc0TC9YbjRseS9VaDAxQXlhOVRhcWNiYnB0cVpiOXBZMkN4MjVTaEFSSlovT2xKZTNOS3hWeVJTKzVFU0hMTmdMQytOblRLMlQxaXNBQmhHNXAxNytKYVVGZmJDUjIiLCJtYWMiOiIxMjEzMmIyM2I3ZDk0NjE2ZDkzMGMwMTU3ZDgzZjdhNWRjYmQ1M2FlMjBjNTQwYjgzN2RhMDllZmVhZjVlNmZjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-320429577\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-5753983 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qzZAZCbyxouAflTIwxgtbSqzkAL1GsMdrfVvou3B</span>\"\n  \"<span class=sf-dump-key>schola_saas_school_management_system_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ngwskcSF7OPJhEVQnobXQuptusCBAMo2pYQsya27</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-5753983\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1864178818 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">application/pdf</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-disposition</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"43 characters\">inline; filename=&quot;student-fees-invoice.pdf&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 01:19:32 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1864178818\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1781925919 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qzZAZCbyxouAflTIwxgtbSqzkAL1GsMdrfVvou3B</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">https://schola.test/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>landing_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>language</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Language\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Language</span></span> {<a class=sf-dump-ref>#3442</a><samp data-depth=2 class=sf-dump-compact>\n    #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n    #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">languages</span>\"\n    #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n    #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n    +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n    #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n    +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n    +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n    +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">English</span>\"\n      \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n      \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"7 characters\">en.json</span>\"\n      \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>is_rtl</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2023-11-18 23:37:23</span>\"\n      \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2023-11-18 23:37:23</span>\"\n    </samp>]\n    #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">English</span>\"\n      \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n      \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"7 characters\">en.json</span>\"\n      \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>is_rtl</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2023-11-18 23:37:23</span>\"\n      \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2023-11-18 23:37:23</span>\"\n    </samp>]\n    #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n    #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n    +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n    +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"4 characters\">code</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"4 characters\">file</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"6 characters\">is_rtl</span>\"\n    </samp>]\n    #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n    </samp>]\n  </samp>}\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>01JX1D4DPSES6REYAPPPS3STYK</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1781925919\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://schola.test/student-fees/paid/receipt-pdf/14505/invoice", "action_name": "student-fees.paid.receipt.pdf", "controller_action": "App\\Http\\Controllers\\StudentFeesController@feesPaidReceiptPDF"}, "badge": null}}