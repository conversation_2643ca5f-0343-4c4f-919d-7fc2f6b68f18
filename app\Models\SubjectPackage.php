<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SubjectPackage extends Model
{
    use HasFactory;

    protected $table = 'subject_package';

    protected $fillable = [
        'name',
        'subject_id',
        'description',
        'thumbnail_id',
        'price',
        'expiry_days',
        'total_sessions',
        'school_id',
        'created_at',
        'updated_at',
    ];

    public function thumbnail()
{
    return $this->belongsTo(File::class, 'thumbnail_id');
}

}
