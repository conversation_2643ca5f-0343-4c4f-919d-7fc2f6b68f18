<?php

namespace App\Http\Controllers;

use App\Repositories\Attendance\AttendanceInterface;
use App\Repositories\ClassSection\ClassSectionInterface;
use App\Repositories\Student\StudentInterface;
use App\Services\CachingService;
use App\Services\ResponseService;
use App\Models\Subject;
use App\Models\User;
use App\Models\SubjectAttendance;
use App\Models\TeacherAttendance;
use App\Services\BootstrapTableService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Throwable;

class TeacherAttendanceController extends Controller
{

    private AttendanceInterface $attendance;
    private ClassSectionInterface $classSection;
    private StudentInterface $student;
    private CachingService $cache;

    public function __construct(AttendanceInterface $attendance, ClassSectionInterface $classSection, StudentInterface $student, CachingService $cachingService)
    {
        $this->attendance = $attendance;
        $this->classSection = $classSection;
        $this->student = $student;
        $this->cache = $cachingService;
    }

    public function scan()
    {
        ResponseService::noFeatureThenRedirect('Attendance Management');
        ResponseService::noAnyPermissionThenRedirect(['class-teacher', 'attendance-list']);
        $class_sections = $this->classSection->builder()->ClassTeacher()->with('class', 'class.stream', 'section', 'medium')->get();
        $schoolId = Auth::user()->school_id;
        
        $subjects = DB::table('subjects')
            ->where('school_id', $schoolId)
            ->get();; // Fetch all subjects
        $students = DB::table('students as s')
            ->join('users as u', 's.user_id', '=', 'u.id')
            ->where('s.school_id', $schoolId)
            ->whereNull('u.deleted_at')
            ->select('u.id', DB::raw("CONCAT(u.first_name, ' ', u.last_name) AS full_name"))
            ->get();

        $staffs = DB::table('staffs as s')
            ->join('users as u', 's.user_id', '=', 'u.id')
            ->where('u.school_id', $schoolId)
            ->whereNull('u.deleted_at')
            ->select('u.id', DB::raw("CONCAT(u.first_name, ' ', u.last_name) AS full_name"))
            ->get();

        return view('attendance.teacher_scan', compact('class_sections', 'subjects', 'students', 'staffs'));
    }

    public function getAttendanceData(Request $request)
    {
        ResponseService::noFeatureThenRedirect('Attendance Management');
        $response = $this->attendance->builder()->select('type')->where(['date' => date('Y-m-d', strtotime($request->date)), 'class_section_id' => $request->class_section_id])->pluck('type')->first();
        return response()->json($response);
    }

    public function getAttData(Request $request)
    {
        ResponseService::noFeatureThenRedirect('Attendance Management');

        // Retrieve the subject_id and date from the request
        $subject_id = $request->input('subject_id');
        $date = $request->input('date');

        // Retrieve attendance data from the subject_students table
        $attendanceData = DB::table('subject_students')
            ->where('subject_id', $subject_id)
            ->where('created_at', $date) // Assuming you want to filter by created_at
            ->get();

        // Return the response as needed
        return response()->json($attendanceData);
    }

    public function showAttendance(Request $request)
    {
        ResponseService::noFeatureThenRedirect('Attendance Management');
        ResponseService::noAnyPermissionThenRedirect(['class-teacher', 'attendance-list']);
        // Get request parameters
        $offset = $request->input('offset', 0);
        $limit = $request->input('limit', 9999);
        $sort = $request->input('sort', 'id');
        $order = $request->input('order', 'DESC');
        $search = $request->input('search');
        $subject_id = $request->input('subject_id');
        $teacher_id = $request->input('teacher_id');
        $schoolId = Auth::user()->school_id;
        $start_date = Carbon::createFromFormat('d-m-Y', $request->input('start_date'))->startOfDay();
        $end_date = Carbon::createFromFormat('d-m-Y', $request->input('end_date'))->endOfDay();

        $query = TeacherAttendance::query()
            ->join('users', 'teacher_attendance.user_id', '=', 'users.id')
            ->join('staffs as s', 's.user_id', '=', 'users.id') // Join with users table to get student names
            ->leftJoin('subjects', 'teacher_attendance.subject_id', '=', 'subjects.id')
            ->select(
                'teacher_attendance.*',
                'users.first_name as student_first_name',
                'users.last_name as student_last_name',
                DB::raw('COALESCE(subjects.name, "All Day") as subject_name'),
            )
            ->where('teacher_attendance.school_id', $schoolId)
            ->whereNull('users.deleted_at');

        // Apply search filters
        if ($search) {
            $searchTerms = explode(' ', $search);
        
            $query->where(function ($q) use ($searchTerms) {
                foreach ($searchTerms as $term) {
                    $q->where(function ($q) use ($term) {
                        $q->where('users.first_name', 'LIKE', "%$term%")
                          ->orWhere('users.last_name', 'LIKE', "%$term%");
                    });
                }
            });
        }

        if ($subject_id == '0') {
            $query->where(function ($q) {
                $q->whereNull('teacher_attendance.subject_id')
                  ->orWhere('teacher_attendance.subject_id', '=', 0);
            });
        } else if ($subject_id) {
            $query->where('teacher_attendance.subject_id', $subject_id);
        }
        
        // Apply date range filter if provided
        if ($start_date && $end_date) {
            $query->whereBetween('teacher_attendance.date', [$start_date, $end_date]);
        }

        // Get the total count of records (after applying filters)
        $total = $query->count();

        // Apply sorting, offset, and limit
        $query->orderBy($sort, $order)->skip($offset)->take($limit);

        // Get the records
        $res = $query->get();
        // Prepare the response data
        $bulkData = [];
        $bulkData['total'] = $total;
        $rows = [];
        $no = 1;

        foreach ($res as $row) {
            $operate = '';
            $operate .= BootstrapTableService::editButton(route('attendance.updateTeacherAttendance', $row->id));
            $operate .= BootstrapTableService::deleteButton(route('attendance.teacher.destroy', $row->id));
            $tempRow = $row->toArray();
            $tempRow['no'] = $no++;
            $tempRow['student_name'] = $row->student_first_name . ' ' . $row->student_last_name;
            $tempRow['subject_name'] = $row->subject_name;
            $tempRow['operate'] = $operate;
            $tempRow['clock_in'] = $row->clock_in ? Carbon::parse($row->clock_in)->format('H:i:s') : '-';
            $tempRow['clock_out'] = $row->clock_out ? Carbon::parse($row->clock_out)->format('H:i:s') : '-';
            $tempRow['teacher_name'] = $row->teacher_name;
            $tempRow['in_temperature'] = $tempRow['in_temperature'] ? number_format((float)$tempRow['in_temperature'], 1, '.', '') : '-';
            $tempRow['out_temperature'] = $tempRow['out_temperature'] ? number_format((float)$tempRow['out_temperature'], 1, '.', '') : '-';
            // Determine status based on type (example logic)
            if ($row->status == 1) {
                $tempRow['status'] = 'Present';
            } elseif ($row->status == 2) {
                $tempRow['status'] = 'Late';
            } elseif ($row->status == 3) {
                $tempRow['status'] = 'Replacement';
            } else {
                $tempRow['status'] = 'Absent';
            }
            $rows[] = $tempRow;
        }

        $bulkData['rows'] = $rows;

        return response()->json($bulkData);
    }


    public function updateStudentAttendance(Request $request)
    {
        ResponseService::noFeatureThenRedirect('Attendance Management');
        ResponseService::noAnyPermissionThenRedirect(['class-teacher', 'attendance-list']);

        $request->validate([
            'id'              => 'required|exists:subject_attendances,id',
            'date'            => 'required|date',
            'clock_in'        => 'nullable|date_format:H:i',
            'clock_out'       => 'nullable|date_format:H:i',
            'status'          => 'required',
            'remark_picture'  => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'in_temperature'  => 'nullable|numeric|between:35.0,42.0',
            'out_temperature' => 'nullable|numeric|between:35.0,42.0',
        ]);

        try {
            DB::beginTransaction();

            $subjectAttendance = TeacherAttendance::findOrFail($request->id);

            $subjectAttendance->date = $request->date;

            // Handle clock_in
            if (!empty($request->clock_in)) {
                $clockInDateTime = Carbon::createFromFormat('Y-m-d H:i', $request->date . ' ' . $request->clock_in);
                $subjectAttendance->clock_in = $clockInDateTime;
            } else {
                $subjectAttendance->clock_in = null; // or whatever default behavior you need
            }

            // Handle clock_out
            if (!empty($request->clock_out)) {
                $clockOutDateTime = Carbon::createFromFormat('Y-m-d H:i', $request->date . ' ' . $request->clock_out);
                $subjectAttendance->clock_out = $clockOutDateTime;
            } else {
                $subjectAttendance->clock_out = null; // or whatever default behavior you need
            }

            // Handle status
            $subjectAttendance->status = $request->status;

            // Handle student_remark
            $subjectAttendance->student_remark = $request->student_remark;

            // Handle remark_picture
            if ($request->hasFile('remark_picture')) {
                $file = $request->file('remark_picture');
                $path = $file->store('remark_pictures', 'public');
                $subjectAttendance->remark_picture = $path;
            }

            // Add temperature readings
            $subjectAttendance->in_temperature = $request->in_temperature;
            $subjectAttendance->out_temperature = $request->out_temperature;

            // Calculate total time if both clock_in and clock_out are provided
            if ($subjectAttendance->clock_in && $subjectAttendance->clock_out) {
                $interval = $subjectAttendance->clock_out->diff($subjectAttendance->clock_in);
                $subjectAttendance->total_time = $interval->format('%H:%I:%S');
            } else {
                $subjectAttendance->total_time = null; // or set to default value
            }

            $subjectAttendance->save();

            $oldAttendance = DB::select("SELECT * FROM attendances WHERE subject_attendance_id = ? ", [$request->id]);
            if ($oldAttendance) {
                $type = $request->status == 0 ? 0 : 1;
                $oldAttendanceData = [
                    'type' => $type,
                    'date' => $request->date,
                    'updated_at' => now(),
                ];
                $result = DB::table('attendances')
                    ->where('subject_attendance_id', $request->id)
                    ->update($oldAttendanceData);
            }
            DB::commit();
            ResponseService::successResponse('Data Updated Successfully');
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "Leave Controller -> Update Method");
            ResponseService::errorResponse();
        }
    }

    public function destroy($id)
    {
        ResponseService::noFeatureThenRedirect('Attendance Management');
        ResponseService::noAnyPermissionThenRedirect(['class-teacher', 'attendance-list']);
        try {
            DB::beginTransaction();
            DB::table('teacher_attendance')->where('id', $id)->delete();
            $oldData = DB::table('attendances')->where('subject_attendance_id', $id)->first();
            if ($oldData) {
                DB::table('attendances')->where('subject_attendance_id', $id)->delete();
            }
            DB::commit();
            ResponseService::successResponse('Data Deleted Successfully');
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "Attendance Controller -> Destroy Method");
            ResponseService::errorResponse();
        }
    }

    public function attendanceSummary(Request $request)
    {
        $schoolId = Auth::user()->school_id;
        $searchParams = [];

        $dateFilter = "";
        if ($request->start_date && !$request->end_date) {
            $dateFilter .= " AND created_at >= ? ";
            $searchParams[] = date('Y-m-d', strtotime($request->start_date)) . " 00:00:00";
        } else if (!$request->start_date && $request->end_date) {
            $dateFilter .= " AND created_at <= ? ";
            $searchParams[] = date('Y-m-d', strtotime($request->end_date)) . " 23:59:59";
        } else if ($request->start_date && $request->end_date) {
            $dateFilter .= " AND (created_at BETWEEN ? AND ?) ";
            $searchParams[] = date('Y-m-d', strtotime($request->start_date)) . " 00:00:00";
            $searchParams[] = date('Y-m-d', strtotime($request->end_date)) . " 23:59:59";
        }

        $subjectFilter = "";
        if (isset($request->subject_id)) {
            if ($request->subject_id == 0) {
                $subjectFilter .= " AND subjects_id IS NULL ";
            } elseif (!empty($request->subject_id)) {
                $subjectFilter .= " AND subjects_id = ? ";
                $searchParams[] = $request->subject_id;
            }
        }
        $studentFilter = "";
        if (isset($request->search)) {
            $studentName = '%' . $request->search . '%';
            $studentFilter .= " 
                            AND EXISTS (
                            SELECT 1
                            FROM users
                            WHERE subject_attendances.user_id = users.id
                            AND users.school_id = ?
                            AND (
                                users.first_name LIKE ? 
                                OR users.last_name LIKE ? 
                                OR CONCAT(users.first_name, ' ', users.last_name) LIKE ?
                            )
                        )";
            $searchParams[] = $schoolId;
            $searchParams[] = $studentName;
            $searchParams[] = $studentName;
            $searchParams[] = $studentName;
        }

        $schoolFilter = " 
            AND EXISTS (
            SELECT 1
            FROM users
            WHERE subject_attendances.user_id = users.id
            AND subject_attendances.school_id = ?
            AND users.deleted_at IS NULL
        ) ";
        $searchParams[] = $schoolId;

        $query = "
            SELECT
            COUNT(CASE WHEN status = 1 THEN 1 END) AS total_present,
            COUNT(CASE WHEN status = 0 THEN 1 END) AS total_absent,
            COUNT(CASE WHEN status = 2 THEN 1 END) AS total_late,
            COUNT(CASE WHEN status = 3 THEN 1 END) AS total_replacement
            FROM subject_attendances
            WHERE 1=1
            {$dateFilter}
            {$subjectFilter}
            {$studentFilter}
            {$schoolFilter}
        ";
        $totalResult = DB::select($query, $searchParams);

        $attendance_data = [
            'total_present' => $totalResult[0]->total_present,
            'total_absent' => $totalResult[0]->total_absent,
            'total_late' => $totalResult[0]->total_late,
            'total_replacement' => $totalResult[0]->total_replacement,
        ];

        return response()->json([
            'error'   => false,
            'message' => "Attendance Summary",
            'attendance_data' => $attendance_data
        ]);
    }

    public function storeAddStudent(Request $request)
    {
        // Automatically get the school_id from the logged-in user
        $schoolId = Auth::user()->school_id;

        // Validate the incoming request
        $request->validate([
            'subjects_id' => 'nullable',
            'user_id' => 'required|exists:users,id',
            'date' => 'required|date',
            'clock_in' => 'nullable|date_format:H:i',
            'clock_out' => 'nullable|date_format:H:i|after:clock_in',
            'teacher_id' => 'nullable|exists:staffs,id',
            'status' => 'required|in:0,1,2,3', // Assuming 0: Absent, 1: Present, 2: Late, 3: Replacement
            'picture_remark' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'in_temperature' => 'nullable|numeric|between:35.0,42.0',
            'out_temperature' => 'nullable|numeric|between:35.0,42.0',
        ]);

        DB::beginTransaction();
        $subjectId = $request->subjects_id !== '0' ? $request->subjects_id : null;
        try {
            // Create a new TeacherAttendance record
            $teacherAttendance = TeacherAttendance::create([
                'school_id' => $schoolId,
                'subjects_id' => $request->subjects_id ?: null,
                'teacher_id' => $request->teacher_id ?: null,
                'user_id' => $request->user_id,
                'status' => $request->status,
                'student_remark' => $request->student_remark,
                'date' => Carbon::parse($request->date),
                'clock_in' => $request->clock_in ? Carbon::parse($request->clock_in) : null,
                'clock_out' => $request->clock_out ? Carbon::parse($request->clock_out) : null,
                'subject_id'=>$request->subjects_id,
                'total_time' => $this->calculateTotalTime($request->clock_in, $request->clock_out),
                'in_temperature' => $request->in_temperature,
                'out_temperature' => $request->out_temperature,
                // Additional fields can be added here as needed
            ]);

            if ($request->hasFile('picture_remark')) {
                $file = $request->file('picture_remark');
                $path = $file->store('remark_pictures', 'public');
                $teacherAttendance->remark_picture = $path;
                $teacherAttendance->save(); 
            }

            // Optionally, handle attendance logic related to credits or fees here
            // Call a method to handle fees, notifications, etc.

            DB::commit();
            return redirect()->back()->with('success', 'Data Updated Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', 'An error occurred: ' . $e->getMessage());
        }
    }

    private function calculateTotalTime($clockIn, $clockOut)
    {
        if ($clockIn && $clockOut) {
            $clockInTime = Carbon::parse($clockIn);
            $clockOutTime = Carbon::parse($clockOut);
            return $clockOutTime->diff($clockInTime)->format('%H:%I:%S');
        }
        return null;
    }


    public function dailyAttendanceReportIndex(Request $request)
    {
        ResponseService::noFeatureThenRedirect('Attendance Management');
        ResponseService::noAnyPermissionThenRedirect(['class-teacher', 'attendance-list']);
        $schoolId = Auth::user()->school_id;
        $teacherId = Auth::user()->id;
        $searchParams = [];
        if (Auth::user()->hasRole('School Admin')) {
            $sql = 'SELECT DISTINCT 
                            class_sections.*,
                            classes.name AS class_name,
                            streams.name AS stream_name,
                            sections.name AS section_name,
                            mediums.name AS medium_name
                        FROM 
                            class_sections
                        JOIN 
                            classes ON class_sections.class_id = classes.id
                        LEFT  JOIN 
                            streams ON classes.stream_id = streams.id
                        JOIN 
                            sections ON class_sections.section_id = sections.id
                        JOIN 
                            mediums ON classes.medium_id = mediums.id
                        AND 
                            class_sections.school_id = ?';
            $searchParams[] = $schoolId;
        } else {
            $sql = "SELECT DISTINCT 
                            class_sections.*,
                            classes.name AS class_name,
                            streams.name AS stream_name,
                            sections.name AS section_name,
                            mediums.name AS medium_name
                        FROM 
                            class_sections
                        JOIN 
                            class_teachers ON class_sections.id = class_teachers.class_section_id
                        JOIN 
                            classes ON class_sections.class_id = classes.id
                        LEFT  JOIN 
                            streams ON classes.stream_id = streams.id
                        JOIN 
                            sections ON class_sections.section_id = sections.id
                        JOIN 
                            mediums ON classes.medium_id = mediums.id
                        WHERE 
                            class_teachers.teacher_id = ?
                        AND 
                            class_sections.school_id = ?
                        ";
            $searchParams[] = $teacherId;
            $searchParams[] = $schoolId;
        }
        $start_date = \Carbon\Carbon::now()->startOfMonth()->format("d-m-Y");
        $end_date = \Carbon\Carbon::now()->endOfMonth()->format("d-m-Y");
        $class_sections = DB::select($sql, $searchParams);
        foreach ($class_sections as $section) {
            $full_name = '';
            if ($section->class_name) {
                $full_name = $section->class_name;
            }
            if ($section->section_name) {
                $full_name .= " " . $section->section_name;
            }
            if ($section->class_name && $section->stream_name) {
                $full_name .= isset($section->stream_name) ? ' ( ' . $section->stream_name . ' ) ' : '';
            }
            if ($section->medium_name) {
                $full_name .= " - " . $section->medium_name;
            }
            $section->full_name = $full_name;
        }

        $subject_id = DB::table('subjects as s')
            ->distinct()
            ->select('s.id', 's.name')
            ->leftJoin('subject_students as ss', 'ss.subject_id', '=', 's.id')
            ->leftJoin('class_subjects as cs', 'cs.subject_id', '=', 's.id')
            ->where('s.school_id', Auth::user()->school_id)
            ->whereNull('s.deleted_at')
            ->get();

        return view('attendance.teacher_daily_attendance_report', compact('start_date', 'end_date', 'class_sections', 'subject_id'));
    }

    public function dailyAttendanceReportList(Request $request)
    {
        ResponseService::noFeatureThenRedirect('Attendance Management');
        ResponseService::noAnyPermissionThenRedirect(['class-teacher', 'attendance-list']);

        $offset = $request->input('offset', 0);
        $limit = $request->input('limit', 9999);
        $sort = $request->input('sort', 'id');
        $order = $request->input('order', 'ASC');
        $search = $request->input('search');
        $schoolId = Auth::user()->school_id;
        $start_date = Carbon::parse($request->input('start_date'))->format('Y-m-d');
        $end_date = Carbon::parse($request->input('end_date'))->format('Y-m-d');
        $classSectionId = $request->class_section_id;
        $type = $request->type;
        $userQuery = [];

        if ($type === "staff") {
            $userQuery = DB::table('staffs')
                ->select('users.id as user_id', 'users.first_name', 'users.last_name', DB::raw('CONCAT("users.first_name"," ","users.last_name") as full_name'))
                ->join('users', 'staffs.user_id', '=', 'users.id')
                ->where('users.school_id', $schoolId)
                ->whereNull('users.deleted_at')
                ->when($search, function ($query) use ($search) {
                    $query->where(function ($query) use ($search) {
                        $query->whereRaw("concat(users.first_name, ' ', users.last_name) LIKE '%" . $search . "%'")
                            ->orWhere('users.first_name', 'LIKE', "%$search%")
                            ->orWhere('users.last_name', 'LIKE', "%$search%");
                    });
                })
                ->get();

            $attendanceQuery = DB::table('users')
                ->select('users.id AS user_id', 'teacher_attendance.date', 'teacher_attendance.status')
                ->leftJoin('teacher_attendance', 'users.id', '=', 'teacher_attendance.user_id')
                ->join('staffs', 'users.id', '=', 'staffs.user_id')
                ->where('users.school_id', $schoolId)
                ->whereBetween('teacher_attendance.date', [$start_date, $end_date])
                ->get();
        
        }

        $dateHeaders = [];
        $days = $request->days ?? [];
        $currentDate = Carbon::parse($start_date);
        while ($currentDate->lte($end_date)) {
            if (empty($days) || in_array($currentDate->dayOfWeek, $days)) {
                // dd($currentDate->dayOfWeek);
                $dateHeaders[] = $currentDate->format('Y-m-d');
            }
            $currentDate->addDay();
        }

        $no = 1;
        $users = [];
        foreach ($userQuery as $user) {
            $userData = [
                'no' => $no++,
                'user_id' => $user->user_id,
                'user_name' => $user->first_name . ' ' . $user->last_name,
            ];

            foreach ($dateHeaders as $date) {
                $userData['attendance_' . $date] = null;
            }

            foreach ($attendanceQuery as $attendance) {
                if ($attendance->user_id == $user->user_id && in_array($attendance->date, $dateHeaders)) {
                    $userData['attendance_' . $attendance->date] = $attendance->status !== null ? $attendance->status : $attendance->type;
                }
            }
            $users[] = $userData;
        }

        $bulkData = [
            'total' => count($users),
            'rows' => $users,
            'date_headers' => $dateHeaders,
        ];

        return response()->json($bulkData);
    }
}
