<?php
namespace App\Http\Controllers;

use App\Models\Subject;
use App\Models\ClassReport;
use App\Models\StudentSubject;
use App\Repositories\ClassSection\ClassSectionInterface;
use App\Services\CachingService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class ClassReportController extends Controller
{
    private ClassSectionInterface $classSection;
    private CachingService $cache;

    public function __construct(ClassSectionInterface $classSection, CachingService $cache)
    {
        $this->classSection = $classSection;
        $this->cache = $cache;
    }


public function store(Request $request)
{
    $request->validate([
        'data' => 'required|array',
        'data.*.Student_Name' => 'required|string',
        'data.*.Class' => 'required|string',
        'data.*.Subjects' => 'required|string',
        'data.*.total_students' => 'sometimes|integer|min:0'
    ]);

    $data = $request->input('data');
    $savedCount = 0;

    try {
        DB::beginTransaction();
        
        foreach ($data as $row) {
            ClassReport::updateOrCreate(
                [
                    'student_name' => $row['Student_Name'],
                    'class' => $row['Class'],
                    'subjects' => $row['Subjects'],
                ],
                [
                    'total_students' => $row['total_students'] ?? 0,
                ]
            );
            $savedCount++;
        }
        
        DB::commit();
        
        return response()->json([
            'success' => true,
            'message' => "Successfully saved {$savedCount} records",
            'saved_count' => $savedCount
        ]);
    } catch (\Exception $e) {
        DB::rollBack();
        Log::error('Failed to save class report: ' . $e->getMessage());
        return response()->json([
            'error' => 'Failed to save class report',
            'details' => $e->getMessage()
        ], 500);
    }
}


    public function index()
    {
        $class_sections = $this->classSection->all(['*'], ['class', 'class.stream', 'section', 'medium']);

        $subjects = DB::table('class_sections')
            ->join('class_subjects', 'class_subjects.class_id', '=', 'class_sections.class_id')
            ->join('subjects', 'class_subjects.subject_id', '=', 'subjects.id')
            ->whereNull('class_sections.deleted_at')
            ->where('class_sections.school_id', Auth::user()->school_id)
            ->select('subjects.id', 'subjects.name')
            ->get();

        return view('class-report.index', compact('class_sections', 'subjects'));
    }


    public function getSubjects($classSectionId)
    {
        $subjects = DB::table('subjects')
            ->join('class_subjects', 'subjects.id', '=', 'class_subjects.subject_id')
            ->join('class_sections', 'class_sections.class_id', '=', 'class_subjects.class_id')
            ->where('class_sections.id', $classSectionId)
            ->whereNull('subjects.deleted_at')
            ->whereNull('class_subjects.deleted_at')
            ->select('subjects.id', 'subjects.name')
            ->distinct()
            ->get();

        // Also get the count of students for each subject
        $subjectCounts = [];
        foreach ($subjects as $subject) {
            $count = DB::table('students')
                ->join('class_sections', 'students.class_section_id', '=', 'class_sections.id')
                ->join('class_subjects', 'class_sections.class_id', '=', 'class_subjects.class_id')
                ->where('class_sections.id', $classSectionId)
                ->where('class_subjects.subject_id', $subject->id)
                ->whereNull('students.deleted_at')
                ->count();

            $subject->student_count = $count;
        }

        return response()->json([
            'subjects' => $subjects
        ]);
    }


    public function show(Request $request)
{
    $request->validate([
        'class_section_id' => 'required|exists:class_sections,id',
        'subject_id' => 'sometimes|exists:subjects,id',
        'search' => 'sometimes|string'
    ]);

    $classSectionId = $request->class_section_id;
    $subjectId = $request->subject_id;
    $sessionYear = $this->cache->getDefaultSessionYear();

    // Use eager loading for better performance
    $query = DB::table('students')
        ->join('class_sections', 'class_sections.id', '=', 'students.class_section_id')
        ->join('users', 'students.user_id', '=', 'users.id')
        ->leftJoin('class_subjects', 'class_subjects.class_id', '=', 'class_sections.class_id')
        ->leftJoin('subjects', 'class_subjects.subject_id', '=', 'subjects.id')
        ->leftJoin('student_subjects', function ($join) {
            $join->on('student_subjects.student_id', '=', 'users.id')
                 ->on('student_subjects.class_subject_id', '=', 'class_subjects.id');
        })
        ->select(
            'class_subjects.id AS class_subject_id',
            'class_sections.id AS class_section_id',
            'subjects.id AS subject_id',
            'subjects.name AS subject_name',
            'class_subjects.type AS class_subject_type',
            'students.id',
            'users.first_name',
            'users.last_name',
            'class_sections.class_id as class_id',
            'student_subjects.id AS student_subject_id'
        )
        ->where('students.session_year_id', $sessionYear->id)
        ->where('class_sections.school_id', Auth::user()->school_id)
        ->whereNull('users.deleted_at')
        ->where(function ($query) {
            $query->whereNull('class_subjects.deleted_at')
                  ->orWhereNull('class_subjects.id');
        })
        ->where(function ($query) {
            $query->where('class_subjects.type', 'Compulsory')
                  ->orWhere('class_subjects.type', 'Elective')
                  ->orWhereNull('class_subjects.id');
        });

    if ($request->has('search') && !empty($request->search)) {
        $query->where(function ($q) use ($request) {
            $q->where('users.first_name', 'LIKE', '%' . $request->search . '%')
              ->orWhere('users.last_name', 'LIKE', '%' . $request->search . '%');
        });
    }

    if ($classSectionId) {
        $query->where('class_sections.id', $classSectionId);
    }

    if ($subjectId) {
        $query->where('subjects.id', $subjectId);
    }

    // Order by subject name first, then by student name
    $res = $query->orderBy('subjects.name', 'asc')
                ->orderBy('users.first_name', 'asc')
                ->orderBy('users.last_name', 'asc')
                ->get();

    $students = [];
    $totalStudents = 0;
    $subjectWiseCount = [];
    $index = 1;

    foreach ($res as $row) {
        if ($row->class_subject_type === "Compulsory" || ($row->class_subject_type === "Elective" && $row->student_subject_id)) {
            $students[] = $row;
            $totalStudents++;

            if (!isset($subjectWiseCount[$row->subject_name])) {
                $subjectWiseCount[$row->subject_name] = 1;
            } else {
                $subjectWiseCount[$row->subject_name]++;
            }
        }
    }

    if (empty($students)) {
        // Log that no students were found
        Log::debug('No students found for class section ID: ' . $classSectionId);
        return response()->json([
            'rows' => [],
            'total_students' => 0,
            'subject_summary' => [],
        ]);
    }

    // Log the subject-wise count for debugging
    Log::debug('Subject-wise count for class section ID ' . $classSectionId . ':', $subjectWiseCount);

    // No need to fetch class name for the simplified table

    $studentData = [];
    $rowId = 1; // Unique identifier for each row
    $subjectGroups = [];

    // Group students by subject
    foreach ($students as $student) {
        $subjectGroups[$student->subject_name][] = $student;
    }

    // Process each subject group
    foreach ($subjectGroups as $subjectName => $subjectStudents) {
        $isFirstStudent = true;

        // Add each student in the group
        foreach ($subjectStudents as $student) {
            $studentData[] = [
                'id' => $rowId++,
                'Student_Name' => $student->first_name . ' ' . $student->last_name,
                'Subjects' => $isFirstStudent ? $student->subject_name : '',  // Only show subject for first student
                'total_students' => 1 // Each student row represents 1 student
            ];

            $isFirstStudent = false;
            $index++;
        }

        // Add total for this subject
        $subjectTotal = $subjectWiseCount[$subjectName];
        $studentData[] = [
            'id' => 'total_' . str_replace(' ', '_', strtolower($subjectName)),
            'Student_Name' => '',
            'Subjects' => "Total",  // Just 'total' instead of 'Total for'
            'total_students' => $subjectTotal
        ];
    }

    // Add grand total row
    $studentData[] = [
        'id' => 'grand_total',
        'Student_Name' => '',
        'Subjects' => 'Grand Total',
        'total_students' => $totalStudents
    ];

    // Log the final response data
    Log::debug('Sending response with subject_summary:', $subjectWiseCount);

    return response()->json([
        'rows' => $studentData,
        'total_students' => $totalStudents,
        'subject_summary' => $subjectWiseCount,
    ]);
}
    public function update(Request $request)
{
    $request->validate([
        'class_section_id' => 'required|exists:class_sections,id',
        'subject_name' => 'required|string',
    ]);

    $classSectionId = $request->class_section_id;
    $subjectName = $request->subject_name;

    // Fetch the number of students enrolled in the class for the given subject
    $totalStudents = StudentSubject::where('class_section_id', $classSectionId)
        ->where('subject_name', $subjectName)
        ->count();

    return response()->json([
        'success' => true,
        'message' => 'Class report updated successfully',
        'data' => [
            'subject_name' => $subjectName,
            'total_students' => $totalStudents,
        ],
    ]);
}

}

