<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Attendances extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'subject_attendances'; // Change this if your table name is different

    protected $fillable = [
        'user_id',
        'subjects_id',
        'teacher_id',
        'school_id',
        'date',
        'clock_in',
        'clock_out',
        'status',
        'commission_type',
        'commission_amount',
        'fees_per_section',
        'fees_per_month',
    ];

    /**
     * Get the user associated with the attendance.
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Get the subject associated with the attendance.
     */
    public function subject()
    {
        return $this->belongsTo(Subject::class, 'subjects_id');
    }

    /**
     * Get the teacher associated with the attendance.
     */
    public function teacher()
    {
        return $this->belongsTo(Staff::class, 'teacher_id');
    }

    /**
     * Get the school associated with the attendance.
     */
    public function school()
    {
        return $this->belongsTo(School::class, 'school_id');
    }
}