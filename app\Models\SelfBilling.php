<?php

namespace App\Models;

use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class SelfBilling extends Model
{
    use HasFactory,SoftDeletes;

    protected $table ='self_billing';

    protected $fillable = [
        'supplier_id',
        'issue_date_time',
        'type',
        'uid',
        'total_tax_amount',
        'total_net_amount',
        'invoice_discount_value',
        'invoice_discount_description',
        'invoice_charge_value',
        'invoice_charge_description',
        'total_excluding_tax',
        'total_including_tax',
        'total_rounding_amount',
        'total_payable_amount',
        'session_year_id',
        'einvoice_status',
        'self_billing_reference',
        'school_id',
        'created_by',
        'updated_by',
    ];

    public function details()
    {
        return $this->hasMany(SelfBillingDetails::class)->with('taxDetails');
    }

    public function importInfo()
    {
        return $this->hasOne(SelfBillingImportInfo::class);
    }

    public function billingInfo()
    {
        return $this->hasOne(SelfBillingBillingInfo::class);
    }

    public function shipping()
    {
        return $this->hasOne(SelfBillingShipping::class);
    }

    protected static function boot()
    {
        parent::boot();
        
        static::deleting(function($selfBilling) {
            if ($selfBilling->isForceDeleting()) {
                $selfBilling->details()->each(function($detail) {
                    $detail->taxDetails()->delete();
                    $detail->delete();
                });
                $selfBilling->importInfo()->delete();
                $selfBilling->billingInfo()->delete();
                $selfBilling->shipping()->delete();
            }
        });
    }

    public function supplier()
    {
        return $this->belongsTo(SelfBillingSupplier::class);
    }

    public function sessionYear()
    {
        return $this->belongsTo(SessionYear::class);
    }

    protected static function booted() {
        static::addGlobalScope('school', static function (Builder $builder) {
            if (Auth::check()) {
                if (!empty(Auth::user()->school_id) || Auth::user()->hasRole('School Admin')) {
                    $builder->where('school_id', Auth::user()->school_id);
                }
            }
        });

        // Add creating event
        static::creating(function ($model) {
            if (Auth::check() && Auth::user()->school_id) {
                $model->school_id = Auth::user()->school_id;
            }
        });
    }

    public function getFullNameTypeAttribute()
    {
        return match ($this->type) {
            'INV' => 'invoice',
            'CN' => 'credit_note',
            'DB' => 'debit_note',
            'RF' => 'return_note',
            default => 'unknown',
        };
    }

    
}
