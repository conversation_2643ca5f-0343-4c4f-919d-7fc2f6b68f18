<?php 

namespace App\Http\Controllers;

use Carbon\Carbon;
use App\Repositories\Reward\RewardInterface;
use App\Repositories\ClassSchool\ClassSchoolInterface;
use App\Repositories\ClassSection\ClassSectionInterface;
use App\Repositories\FeesType\FeesTypeInterface;
use App\Repositories\Student\StudentInterface;

use App\Services\BootstrapTableService;
use App\Services\CachingService;
use App\Services\ResponseService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class RewardCategoryController extends Controller {

    private ClassSchoolInterface $class;

    public function __construct(
        // CreditInterface $creditTable,
        CachingService $cache,
        ClassSchoolInterface $classSchool,
        StudentInterface $student,
        ClassSectionInterface $classSection
    ) {

        // $this->credit_system = $creditTable;
        $this->cache = $cache;
        $this->class = $classSchool;
        $this->student = $student;
        $this->classSection = $classSection;
    }

    public function index() {
    $classes = $this->class->all(['*'], ['stream', 'medium', 'stream']);
    $simpleClassDropdown = $this->class->builder()->pluck('name','id');

    
    return view('reward.RewardCategory', compact('classes', 'simpleClassDropdown'));
    }

    public function store(Request $request) {
        
        $request->validate([
            'points_amount' => 'required|numeric',
            'category_name' => 'required|string',
        ]);

        try{

            $school_id = Auth::user()->school_id; 
            $mode=$request->mode;

            if(!$mode){
                $mode=0;
            }
           
            $points_amount=(int)$request->points_amount;
            $category_name=$request->category_name;
            $remark=$request->remark;

            $type = $request->type;

            if($type==='debit'){
                $points_amount=$points_amount*-1;
            }

           
            DB::beginTransaction();

            $data=[
                'category_name'     =>$category_name,
                'points_amount'     =>$points_amount,
                'remark'            =>$remark,
                'school_id'         =>$school_id,
                'mode'              =>$mode,    
            ];

            DB::table('rewards_category')->insert($data);
            DB::commit();
            ResponseService::successResponse('Data Stored Successfully');
        }catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "RewardCategory Controller -> Store Method");
            ResponseService::errorResponse();
        }

    }

    public function category_show() {

        $offset = request('offset', 0);
        $limit = request('limit', 10);
        $sort = request('sort', 'category_name'); 
        $order = request('order', 'ASC');
        $search = request('search');
        $showDeleted = request('show_deleted');
        $school_id = Auth::user()->school_id; 
        $query= DB::table ('rewards_category')
        ->when($search, function ($query) use ($search) {
            $query->where(function ($query) use ($search) {
                $query->where('category_name', 'LIKE', "%$search%")
                    ->orWhere('points_amount', 'LIKE', "%$search%")
                    ->orWhere('remark', 'LIKE', "%$search%")
                    ->orWhere('school_id', '=', $school_id);

            });
        })
        ->when(!empty($showDeleted), function ($query) {
            $query->whereNotNull('deleted_at');
        }, function ($query) {
            $query->whereNull('deleted_at');
        });
        

        $total = $query->Where('school_id', '=', $school_id)->count();

        $data = $query->orderBy($sort, $order)
                    ->skip($offset)
                    ->take($limit)
                    ->get();

        $bulkData = array();
        $bulkData['total'] = $total;
        $rows = array();
        $no = 1;

        foreach ($data as $row) {
             $isDefault = $row->is_default;

            $operate = '';
            if (empty($showDeleted) &&  $isDefault == 0) {
                $operate .= BootstrapTableService::editButton(route('reward-category.update', $row->id));
                $operate .= BootstrapTableService::deleteButton(route('reward-category.destroy', $row->id));
            } else if(empty($showDeleted) &&  $isDefault == 1){
                $operate .= BootstrapTableService::editButton(route('reward-category.update', $row->id));
            }else {
                $operate .= BootstrapTableService::restoreButton(route('reward-category.restore', $row->id));
                $operate .= BootstrapTableService::trashButton(route('reward-category.trash', $row->id));
            }

       
                $tempRow = (array) $row; 
                $tempRow['no'] = $no++;
                $tempRow['operate'] = $operate;
                $rows[] = $tempRow;

        }

        $bulkData['rows'] = $rows;
        return response()->json($bulkData);


    }

    public function update(Request $request,$id){
      
        $request->validate([
            'points_amount' => 'required|numeric',
            'remark' => 'nullable|string',
            'type' => 'required|in:credit,debit', 
        ]);
        
        $points_amount=$request->points_amount;
        
        try {
            $mode=$request->mode;
            if($request->type =='debit' && $points_amount > 0){

                $points_amount=$points_amount*-1;
    
            }

            if(!$mode){
                $mode=0;
            }

            DB::beginTransaction();
            
            $data = [
                'category_name' => $request->category_name,
                'points_amount' => $points_amount,
                'remark'        => $request->remark,
                'mode'          => $mode,
            ];
            
            // Update the record in the leave_category table
            DB::table('rewards_category')->where('id', $id)->update($data);
            
            DB::commit();
            ResponseService::successResponse('Data Updated Successfully');
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "RewardCategory Controller -> Delete Method");
            ResponseService::errorResponse();
        }

    }
    public function destroy($id){

        try {
            DB::beginTransaction();
            DB::table('rewards_category')->where('id', $id)->update(['deleted_at' => now()]);            
            DB::commit();
            ResponseService::successResponse('Data Deleted Successfully');
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "RewardCategory Controller -> Delete Method");
            ResponseService::errorResponse();
        }

    }
    public function restore($id){

        try {
            DB::beginTransaction();
            DB::table('rewards_category')->where('id', $id)->update(['deleted_at' => null]);
            
            DB::commit();
            ResponseService::successResponse('Data Restored Successfully');
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "RewardCategory Controller -> Restore Method");
            ResponseService::errorResponse();
        }

    }

    public function trash($id)
    {
        try {
            DB::beginTransaction();
            DB::table('rewards_category')->where('id', $id)->delete();
            
            DB::commit();
            ResponseService::successResponse('Data Delete Successfully');
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "RewardCategory Controller -> Trash Method");
            ResponseService::errorResponse();
        }
    }

}

