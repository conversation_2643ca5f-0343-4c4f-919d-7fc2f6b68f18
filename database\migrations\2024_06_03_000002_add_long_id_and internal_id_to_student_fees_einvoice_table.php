<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('student_fees_einvoice', function (Blueprint $table) {
            $table->string('long_id', 255)->nullable()->after('uuid'); 

            $table->string('internal_id', 255)->nullable()->after('long_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('student_fees_einvoice', function (Blueprint $table) {
            $table->dropColumn('long_id');
            $table->dropColumn('internal_id');
        });
    }
};