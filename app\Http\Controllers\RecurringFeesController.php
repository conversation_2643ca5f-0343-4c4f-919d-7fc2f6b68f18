<?php

namespace App\Http\Controllers;

use App\Models\StudentFee;
use App\Models\Students;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class RecurringFeesController extends Controller
{
    public function index()
    {
        return view('student-fees.recurring-fees');
    }

    public function list(Request $request)
    {
        $offset = $request->get('offset', 0);
        $limit = $request->get('limit', 10);
        $sort = $request->get('sort', 'uid');
        $order = $request->get('order', 'asc');
        $search = $request->get('search');

        $sql = StudentFee::query()
            ->whereNull('deleted_at')
            ->where('school_id', Auth::user()->school_id)
            ->where(function($query) {
                $query->where(function($q) {
                    $q->whereNotNull('invoice_date')
                      ->whereNotNull('recurring_invoice')
                      ->where('disable_recurring', 0)
                      ->where('total_cycles', '>', 0)
                      ->where('total_cycles', '>', 0);
                });
            });
        if (!empty($search)) {
            $sql->where(function ($query) use ($search) {
                $query->where('uid', 'LIKE', '%' . $search . '%')
                    ->orWhereHas('student', function ($q) use ($search) {
                        $q->whereHas('user', function ($q) use ($search) {
                            $q->where(DB::raw("CONCAT(first_name, ' ', last_name)"), 'LIKE', '%' . $search . '%');
                        });
                    });
            });
        }

        $total = $sql->count();

        $sql->orderBy($sort, $order)
            ->skip($offset)
            ->take($limit);

        $res = $sql->get();

        $bulkData = array();
        $bulkData['total'] = $total;
        $rows = array();
        $tempRow = array();
        $no = 1;

        foreach ($res as $row) {
            $student = Students::with('user')->where('id', $row->student_id)->first();
            $studentName = $student ? $student->user->first_name . ' ' . $student->user->last_name : 'N/A';

            if ($row->status == 'draft' && $row->auto_publish_date != null) {
                $autoPublishDate = date('Y-m-d', strtotime($row->auto_publish_date));
                if (date('Y-m-d') < $autoPublishDate) {
                    $nextInvoiceDate = $autoPublishDate;
                    $status = 'Draft to Publish';
                } else {
                    //continue;
                }
            } else if($row->recurring_invoice != null){
                $autoPublishDate = $row->auto_publish_date ?? $row->invoice_date;
            }

            $nextInvoiceDate = $this->calculateNextInvoiceDate($row);
            $status = $row->disable_recurring ? 'Disabled' : 'Active';

            $operate = '<a href="javascript:void(0);" class="btn btn-danger btn-sm" onclick="disableRecurring(' . $row->id . ')" title="Disable Recurring"><i class="fa fa-stop-circle"></i></a>';

            $tempRow['id'] = $row->id;
            $tempRow['invoice_no'] = 'INV' . sprintf('%08d', $row->uid);
            $tempRow['student_name'] = $studentName;
            $tempRow['invoice_date'] = $row->invoice_date;
            $tempRow['next_invoice_date'] = $nextInvoiceDate;
            $tempRow['current_cycle'] = $row->current_cycle;
            $tempRow['total_cycles'] = $row->total_cycles ?? 0;
            $tempRow['status'] = $status;
            $tempRow['action'] = $row->disable_recurring ? '-' : $operate;
            $rows[] = $tempRow;
            $no++;
        }

        $bulkData['rows'] = $rows;
        return response()->json($bulkData);
    }

    public function disable($id)
    {
        try {
            $fee = StudentFee::where('id', $id)
                ->where('school_id', Auth::user()->school_id)
                ->first();

            if (!$fee) {
                return response()->json([
                    'error' => true,
                    'status' => false,
                    'message' => 'Fee not found'
                ]);
            }

            $fee->disable_recurring = 1;
            $fee->save();

            return response()->json([
                'error' => false,
                'status' => true,
                'message' => 'Recurring invoice disabled successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => true,
                'status' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    private function calculateNextInvoiceDate($fee)
    {
        $createdAt = Carbon::parse($fee->invoice_date);

        $nextRecurringDate = $this->addMonth($createdAt,($fee->currentCycle + 1) * $fee->recurring_invoice);
        return $nextRecurringDate->format('Y-m-d');
    }
}