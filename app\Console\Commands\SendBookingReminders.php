<?php

namespace App\Console\Commands;

use App\Http\Controllers\Api\ParentApiController;
use Illuminate\Console\Command;

class SendBookingReminders extends Command
{
    protected $signature = 'bookings:send-reminders';
    protected $description = 'Send reminders for tomorrow\'s bookings';

    protected $parentController;

    public function __construct(ParentApiController $parentController)
    {
        parent::__construct();
        $this->parentController = $parentController;
    }

    public function handle()
    {
        try {
            $result = $this->parentController->sendBookingReminders();
            $this->info('Reminders sent successfully');
            return 0;
        } catch (\Exception $e) {
            $this->error('Failed: ' . $e->getMessage());
            return 1;
        }
    }
}
