1749176352s:78763:"{
    "The :attribute must contain at least one letter.": "The :attribute must contain at least one letter.",
    "The :attribute must contain at least one number.": "The :attribute must contain at least one number.",
    "The :attribute must contain at least one symbol.": "The :attribute must contain at least one symbol.",
    "The :attribute must contain at least one uppercase and one lowercase letter.": "The :attribute must contain at least one uppercase and one lowercase letter.",
    "The given :attribute has appeared in a data leak. Please choose a different :attribute.": "The given :attribute has appeared in a data leak. Please choose a different :attribute.",
    "login": "Login",
    "username": "Username",
    "password": "Password",
    "forgot_password": "Forgot password?",
    "invalid_credentials": "Invalid username or password",
    "dashboard": "Dashboard",
    "general_settings": "General Settings",
    "system_settings": "System Settings",
    "Role & Permission": "Role & Permission",
    "copyright": "Copyright",
    "all_rights_reserved": "All rights reserved",
    "school_name": "School Name",
    "school_address": "Address",
    "business_registration_no": "Business Registration No",
    "remark": "Bank Name:\nCompany Name:\nBank Account Number:",
    "school_phone": "Phone",
    "school_email": "Email",
    "time_zone": "Time Zone",
    "date_format": "Date Format",
    "time_format": "Time Format",
    "color": "Color",
    "medium": "Medium",
    "manage_medium": "Manage Medium",
    "create_medium": "Create Medium",
    "list_medium": "List Medium",
    "edit_medium": "Edit Medium",
    "teacher": "Teacher",
    "manage_teacher": "Manage Teacher",
    "create_teacher": "Create Teacher",
    "list_teacher": "List Teacher",
    "edit_teacher": "Edit Teacher",
    "no": "No",
    "id": "Id",
    "name": "Name",
    "email": "Email",
    "mobile": "Mobile",
    "dob": "Date of Birth",
    "image": "Image",
    "qualification": "Qualification",
    "action": "Action",
    "role_management": "Role Management",
    "show": "Show",
    "show_role": "Show Role",
    "back": "Back",
    "permission": "Permissions",
    "submit": "Submit",
    "Cancel": "Cancel",
    "Are you sure": "Are you sure?",
    "You wont be able to revert this": "You won't be able to revert this!",
    "yes_delete": "Yes, delete it",
    "academics": "Academics",
    "manage": "Manage",
    "create": "Create",
    "edit": "Edit",
    "list": "List",
    "section": "Section",
    "Class": "Class",
    "subject": "Subject",
    "subject_group": "Subject Group",
    "subject_code": "Subject Code",
    "bg_color": "Background Color",
    "upload": "Upload",
    "type": "Type",
    "data_fetch_successfully": "Data Fetch Successfully",
    "Data Stored Successfully": "Data Saved Successfully",
    "Data Updated Successfully": "Data Updated Successfully",
    "Data Deleted Successfully": "Data Deleted Successfully",
    "error_occurred": "Oops! Error occurred. Please Try again Later.",
    "created_at": "Created At",
    "updated_at": "Updated At",
    "no_permission_message": "You Don't have enough permissions.",
    "assign_class_subject": "Assign Class Subject",
    "total_selectable_subjects": "Total Selectable Subjects",
    "assign_class_teacher": "Assign Class Teacher",
    "Guardian": "Guardian",
    "Session Years": "Session Years",
    "status": "Status",
    "admission_no": "Admission Number",
    "roll_no": "Roll Number",
    "caste": "Caste",
    "religion": "Religion",
    "admission_date": "Admission Date",
    "blood_group": "Blood Group",
    "height": "Height",
    "weight": "Weight",
    "occupation": "Occupation",
    "category": "Category",
    "user_id": "User Id",
    "Core Subjects": "Core Subjects",
    "students": "Students",
    "Student": "Student",
    "student_admission": "Students Admission",
    "student_category": "Students Category",
    "add_bulk_data": "Add Bulk Data",
    "bank_transfer": "Bank Transfer",
    "credit_card": "Credit Card",
    "debit_card": "Debit Card",
    "e_wallet": "E-Wallet",
    "digital_bank": "Digital Bank",
    "assign": "Assign",
    "timetable": "Timetable",
    "create_timetable": "Create Timetable",
    "class_timetable": "Class Timetable",
    "teacher_timetable": "Teacher Timetable",
    "subject_lesson": "Subject Lesson",
    "create_lesson": "Create Lesson",
    "create_topic": "Create Topic",
    "lesson_already_exists": "Lesson is already exists",
    "topic_already_exists": "Topic is already exists",
    "student_assignment": "Student Assignment",
    "create_assignment": "Create Assignment",
    "assignment_submission": "Assignment Submission",
    "attendance": "Attendance",
    "add_attendance": "Add Attendance",
    "view_attendance": "View Attendance",
    "announcement": "Announcement",
    "exam": "Exam",
    "create_exam": "Create Exam",
    "create_exam_timetable": "Create Exam Timetable",
    "upload_exam_result": "Upload Exam Result",
    "select": "Select",
    "select_class_section": "Select Class",
    "no_data_found": "No Data Found.",
    "first_name": "First Name",
    "last_name": "Last Name",
    "current_address": "Current Address",
    "permanent_address": "Permanent Address",
    "guardian": "Guardian",
    "gender": "Gender",
    "male": "Male",
    "female": "Female",
    "Date": "Date",
    "select_lesson": "Select Lesson",
    "topic_name": "Topic Name",
    "topic_description": "Topic Description",
    "Select Subject": "Select Subject",
    "Class Section": "Class Section",
    "description": "Description",
    "view": "View",
    "criteria": "Criteria",
    "lesson": "Lesson",
    "lesson_name": "Lesson Name",
    "lesson_description": "Lesson Description",
    "files": "Files",
    "file_name": "File Name",
    "file_upload": "File Upload",
    "video_upload": "Video Upload",
    "youtube_link": "Youtube Link",
    "other_link": "Other Link",
    "Student Id": "Student Id",
    "holiday_list": "Holiday List",
    "assignment": "Assignment",
    "assignment_name": "Assignment Name",
    "assignment_instructions": "Assignment Instructions",
    "last_submission_date": "Last Submission Date",
    "points": "Points",
    "resubmission_allowed": "Resubmission Allowed",
    "extra_days_for_resubmission": "Extra Days for Resubmission",
    "thumbnail": "Thumbnail",
    "holiday": "Holiday",
    "title": "Title",
    "note": "Note",
    "due_date": "Due Date",
    "resubmission": "Resubmission",
    "instructions": "Instructions",
    "student_name": "Student Name",
    "accept": "Accept",
    "reject": "Reject",
    "assign_to": "Assign To",
    "for_all": "For All",
    "sliders": "Sliders",
    "link": "Link",
    "start_date": "Start Date",
    "end_date": "End Date",
    "add_new_files": "Add New Files",
    "are_you_sure": "Are You Sure ?",
    "Yes": "Yes",
    "No": "No",
    "fcm_key": "FCM Key",
    "fcm_server_key": "FCM Server Key",
    "select_class": "Select Class",
    "has_elective_subject": "Has Elective Subject",
    "elective_subject": "Elective Subjects",
    "group": "Group",
    "close": "Close",
    "general": "General",
    "upload_new_files": "Upload New Files",
    "old_files": "Old Files",
    "Exam Name": "Exam Name",
    "Exam Description": "Exam Description",
    "total_marks": "Total Marks",
    "passing_marks": "Passing Marks",
    "obtained_marks": "Obtained Marks",
    "start_time": "Start Time",
    "end_time": "End Time",
    "exam_result": "Exam Result",
    "teacher_review": "Teacher Review",
    "or": "OR",
    "email_configuration": "Email Configuration",
    "mail_mailer": "Mailer",
    "mail_host": "Host",
    "mail_port": "Port",
    "mail_username": "Email",
    "mail_password": "Password",
    "mail_encryption": "Encryption",
    "mail_send_from": "Send From",
    "privacy_policy": "Privacy - Policy",
    "contact_us": "Contact Us",
    "student_details": "Student Details",
    "about_us": "About Us",
    "reset_password": "Reset Password",
    "terms_condition": "Terms & Conditions",
    "promote_student": "Promote Student",
    "Promote Student In Next Session": "Promote Student In Next Session",
    "Promote In": "Promote In",
    "Promote Class": "Promote Class",
    "change_password": "Change Password",
    "old_password": "Old Password",
    "new_password": "New Password",
    "confirm_password": "Confirm Password",
    "language_settings": "Language Settings",
    "language_name": "Language Name",
    "language_code": "Language Code",
    "upload_file": "Language File",
    "download_sample": "Download Sample File",
    "horizontal_logo": "Horizontal Logo",
    "vertical_logo": "Vertical Logo",
    "favicon": "Favicon",
    "role": "Roles",
    "app_settings": "App Settings",
    "Student/Guardian App Settings": "Student/Guardian App Settings",
    "teacher_app_settings": "Teacher App Settings",
    "app_link": "App Link",
    "ios_app_link": "iOS App Link",
    "app_version": "App Version (Android)",
    "ios_app_version": "App Version (iOS)",
    "force_app_update": "Force Update App",
    "app_maintenance": "App Maintenance",
    "school_tagline": "School Tagline",
    "noticeboard": "Noticeboard",
    "system_update": "System Update",
    "current_version": "Current Version",
    "System Updated Successfully": "System Updated Successfully",
    "something_wrong_try_again": "Something wrong!. Please Try again later",
    "System is already upto date": "System is already upto date",
    "Please update nearest version first": "Please update nearest version first",
    "Zip File is not Uploaded to Correct Path": "Zip File is not Uploaded to Correct Path",
    "all": "All",
    "select_teacher": "Select Teacher",
    "student_bulk_import": "Student Bulk import",
    "grade": "Grade",
    "starting_range": "Starting Range",
    "ending_range": "Ending Range",
    "Exam Timetable Does not Exists": "Exam Timetable Does not Exists",
    "exam_marks": "Exam Marks",
    "exam_published": "Exam have been published",
    "profile": "Profile",
    "default": "Default",
    "default_session_year_cannot_delete": "Default session year Cannot be deleted",
    "percentage": "Percentage",
    "update": "Update",
    "add_email_configuration": "Add Email Configuration",
    "Email Configuration Verification": "Email Configuration Verification",
    "email_sent_successfully": "Email Sent Successfully",
    "invalid_email": "Invalid Email",
    "exam_not_completed_yet": "Exam not completed yet",
    "marks_are_not_submitted": "Marks are not submitted",
    "download_dummy_file": "Download Dummy File",
    "grades_data_does_not_exists": "Grades data does not exists",
    "subject_already_exists": "Subject already exists",
    "click_here_to_remove_class_teacher": "Click here to remove class teacher",
    "Fees": "Fees",
    "total": "Total",
    "enter": "Enter",
    "Amount": "Amount",
    "paid": "Paid",
    "Classes": "Classes",
    "Mode": "Mode",
    "transaction_id": "Transaction ID",
    "cheque_no": "Cheque No.",
    "pay": "Pay",
    "cash": "Cash",
    "cheque": "Cheque",
    "optional": "Optional",
    "base": "Base",
    "Razorpay": "Razorpay",
    "Enable": "Enable",
    "Disable": "Disable",
    "configuration": "Configuration",
    "Currency Code": "Currency Code",
    "Secret Key": "Secret Key",
    "Api Key": "API Key",
    "Stripe": "Stripe",
    "Stripe Publishable Key": "Stripe Publishable Key",
    "Stripe Webhook Secret": "Stripe Webhook Secret",
    "Stripe Secret Key": "Stripe Secret Key",
    "Stripe Webhook URL": "Stripe Webhook URL",
    "due_charges": "Due Charges",
    "Other Fees Configuration": "Other Fees Configuration",
    "Currency Symbol": "Currency Symbol",
    "cannot_delete_because_data_is_associated_with_other_data": "Cannot Delete because data is associated with other Data",
    "delete_warning": "You will not be able to revert this!",
    "no.": "No.",
    "table": "Table",
    "grid": "Grid",
    "transactions": "Transactions",
    "logs": "Logs",
    "online": "Online",
    "order_id": "Order ID",
    "payment_intent_id": "Payment Intent ID",
    "payment_id": "Payment ID",
    "payment_signature": "Payment Signature",
    "Success": "Success",
    "failed": "Failed",
    "Pending": "Pending",
    "transaction_payment_id": "Transaction Payment ID",
    "fee_receipt": "Fee Receipt",
    "Razorpay Webhook URL": "Razorpay Webhook URL",
    "Razorpay Webhook Secret": "Razorpay Webhook Secret",
    "save": "Save",
    "total_teachers": "Total Teachers",
    "total_students": "Total Students",
    "Total Guardians": "Total Guardians",
    "sort_by": "Sort By",
    "please_fill_all_roll_numbers_data": "Please fill all the roll numbers data",
    "roll_number_already_exists_of_number": "Roll number already exists of number .",
    "in_minutes": "In minutes",
    "duration": "Duration",
    "exam_key": "Exam Key",
    "Add Online Exam Questions": "Add Online Exam Questions",
    "question_type": "Question Type",
    "simple_question": "Simple question",
    "equation_based": "Equation Based",
    "add_questions": "Add Questions",
    "question": "Question",
    "add_option": "Add Option",
    "option": "Option",
    "questions": "Questions",
    "answer": "Answer",
    "question_is_required": "Question is required",
    "All options are required": "All options are required",
    "add": "Add",
    "add_new_question": "Add new question",
    "marks_are_required": "Marks are required",
    "Invalid Exam Key": "Invalid Exam Key",
    "Student already attempted exam": "Student already attempted exam",
    "Invalid online exam id": "Invalid online exam id",
    "Invalid question id": "Invalid question id",
    "Invalid option id": "Invalid option id",
    "Exam not started yet": "Exam not started yet",
    "Online payment mode": "Online payment mode",
    "old_roll_no": "Old roll number",
    "new_roll_no": "New roll number",
    "Yes, Change it": "Yes, Change it",
    "yes_uncheck": "Yes! Uncheck",
    "marks": "Marks",
    "delete": "Delete",
    "clear": "Clear",
    "generate_pdf": "Generate PDF",
    "language": "Language",
    "code": "Code",
    "exams": "Exams",
    "boys": "Boys",
    "girls": "Girls",
    "change_password_title": "Change password title",
    "confirm_change_message": "Confirm change message",
    "online_exam_based_on": "Online exam based on",
    "class_and_class_section_exam_info": "Class - Online Exam will Appear to all section of Class && Class Section - Online Exam will appear to specific class section",
    "class_required_when_online_exam_is_class_based": "Class is required when online exam is based on class",
    "class_section_required_when_online_exam_is_class_section_based": "Class section is required when online exam is based on class section",
    "subject_required": "Subject required",
    "title_required": "Title required",
    "exam_key_required": "Exam key required",
    "duration_required": "Duration required",
    "start_date_required": "Start date required",
    "end_date_required": "End date required",
    "exam_key_already_taken": "Exam key already taken",
    "duration_should_be_greater_than_or_equal_to_1": "Duration should be greater than or equal to 1",
    "end_date_should_be_date_after_start_date": "End date should be date after start date",
    "passing_marks_should_less_than_or_equal_to_total_marks": "Passing marks should be less than or equal to total marks",
    "end_time_should_be_greater_than_start_time": "End time should be greater than Start time",
    "error_mail_sending": "Error in Mail Sending",
    "schools": "Schools",
    "logo": "Logo",
    "support": "Support",
    "phone": "Phone",
    "tagline": "Tagline",
    "address": "Address",
    "contact": "Contact",
    "admin": "Admin",
    "school": "School",
    "search_admin_email": "Search for Admin Email",
    "change": "Change",
    "email_is_required": "Email is required",
    "enter_valid_email": "Enter valid Email",
    "email_already_in_use": "Email already in use",
    "admission_form_fields": "Admission Form Fields",
    "form-fields": "Form Fields",
    "required": "Required",
    "text": "Text",
    "add_new_option": "Add New Option",
    "add_new_attribute": "Add New Attribute",
    "attribute": "Attribute",
    "value": "Value",
    "remove": "Remove",
    "is": "Is",
    "other": "Other",
    "rank": "Rank",
    "preview": "Preview",
    "class_section": "Class Section",
    "class_section_not_found": "Class Section not found",
    "exam_not_found": "Exam not found",
    "present": "Present",
    "absent": "Absent",
    "Set Default Session Year": "Set Default Session Year",
    "Default Session Year has been Changed SuccessFully": "Default Session Year has been Changed SuccessFully",
    "change_admin": "Change Admin",
    "activate_school": "Activate School",
    "deactivate_school": "Deactivate School",
    "change_school_status": "Change School Status!",
    "subjects": "Subjects",
    "teachers": "Teachers",
    "created_by_teacher": "Created By Teacher",
    "edited_by_teacher": "Edited By Teacher",
    "pass": "Pass",
    "fail": "Fail",
    "continue": "Continue",
    "leave": "Leave",
    "change_status": "Change Status",
    "yes_change_status": "Yes, Change Status",
    "Current": "Current",
    "Pass Out": "Pass Out",
    "preview_pass_out_students": "Preview passout students",
    "draggable_rows_notes": "Note :- you can change the rank of rows by dragging rows",
    "package": "Package",
    "days": "Days",
    "staff": "Staff",
    "per_active_student_charges": "Per Active Student Charges",
    "per_active_staff_charges": "Per Active Staff Charges",
    "charges": "Charges",
    "Charges": "Charges",
    "staffs": "Staffs",
    "users": "Users",
    "used_by": "Used By",
    "features": "Features",
    "change_package_status": "Change Package Status!",
    "publish_package": "Publish Package",
    "unpublished_package": "Unpublished Package",
    "the_no_of_student_field_is_required_when_fixed_student": "The No. of student field is required when fixed student.",
    "the_no_of_staff_field_is_required_when_fixed_staff": "The No. of staff field is required when fixed staff.",
    "the_no_of_archived_user_field_is_required_when_fixed_archived_user": "The No. of archived user field is required when fixed archived user.",
    "published": "Published",
    "Search Teacher Name": "Search Teacher Name",
    "Class Section & Teachers": "Class Section & Teachers",
    "Select Option": "Select Option",
    "Enter Option": "Enter Option",
    "Confirm": "Confirm",
    "You want to Change the Current Session Year": "You want to Change the Current Session Year ?",
    "online_exam_terms_condition": "Online Exam Terms & Conditions",
    "Create New Role": "Create New Role",
    "Salary": "Salary",
    "assign_class_fees": "Assign Class Fees",
    "add_fee": "Add fee",
    "change_teacher_status": "Change Teacher Status!",
    "your_account_has_been_deactivated_please_contact_admin": "Your account has been deactivated. Please contact the administrator for assistance.",
    "class_sections": "Class Sections",
    "manage_offline_exam": "Manage Offline Exam",
    "offline_exam_result": "Offline Exam Result",
    "online_exam_result": "Online Exam Result",
    "Add New Data": "Add New Data",
    "include_fees_installment": "Include Fees Installment",
    "fees_due_date": "Fees Due Date",
    "fees_due_charges": "Fees Due Charges",
    "in_percentage": "In Percentage %",
    "installment_name": "Installment Name",
    "due_date_should_be_after_or_equal_session_year_start_date_at_row": "Due date should be after or equal session year's start date at row :-",
    "due_date_should_be_before_or_equal_session_year_end_date_at_row": "Due date should be before or equal session year's end date at row :-",
    "Pay in installment": "Pay in installment",
    "Due date on": "Due date on",
    "paid_on": "Paid on",
    "Total Amount": "Total Amount",
    "pay_optional_fees": "Pay Optional Fees",
    "semester": "Semester",
    "include_semester": "Include Semester",
    "semester_name": "Semester Name",
    "semester_start_date": "Semester Start Date",
    "semester_end_date": "Semester End Date",
    "class_subject_data": "Class Subject Data",
    "total_subjects": "Total Subjects",
    "timetable_created": "Timetable Created",
    "subject_name": "Subject Name",
    "subject_teachers": "Subject Teachers",
    "highlight": "Highlight",
    "Manage Fee": "Manage Fee",
    "Manage Fees": "Manage Fees",
    "edit_fees": "Edit Fees",
    "please_select_at_least_one_feature": "Please select at least one feature",
    "the_class_section_field_id_required": "The class section field is required",
    "subscription_plans": "Subscription Plan",
    "plans": "Plans",
    "addons": "Addons",
    "subscription": "Subscription",
    "fee": "Fee",
    "manage_optional_fees": "Manage Optional Fees",
    "you_are_currently_enrolled_in_the": "You are currently enrolled in the ",
    "subscription_plan": " subscription plan",
    "price": "Price",
    "please_select_feature": "Please select feature",
    "daily": "Daily",
    "daily_price": "Daily Price",
    "feature": "Feature",
    "you_have_previously_created_an_addon_for_this_feature": "You have previously created an addon for this feature",
    "publish_addon": "Publish Addon",
    "unpublished_addon": "Unpublished Addon",
    "change_addon_status": "Change Addon Status!",
    "please_choose_a_plan_before_proceeding": "Please choose a plan before proceeding",
    "this_addon_has_already_been_included_by_you": "This addon has already been included by you",
    "you_presently_have_access_to_this_functionality_as_part_of_your_current_subscription": "You presently have access to this functionality as part of your current subscription",
    "cancel_subscription": "Cancel Subscription",
    "unsubscription_confirmation": "Unsubscription Confirmation",
    "Oops! It looks like you entered the wrong password": "Oops! It looks like you entered the wrong password",
    "Your Subscription Cancellation is Confirm": "Your Subscription Cancellation is Confirm",
    "total_staffs": "Total staffs",
    "upcoming_plan": "Upcoming Plan",
    "Stop Auto-Renewal for the Next Billing Cycle": "Stop Auto-Renewal for the Next Billing Cycle",
    "Cancel This Plan": "Cancel This Plan",
    "Active Subscription Billing Details": "Active Subscription Billing Details",
    "plan": "Plan",
    "total_user": "Total User",
    "total_amount": "Total Amount",
    "user": "User",
    "Total User Charges": "Total User Charges",
    "Agree to This Subscription": "Agree to This Subscription?",
    "Your upcoming plan has been canceled successfully": "Your upcoming plan has been canceled successfully.",
    "Accept Upcoming Billing Cycle Subscription": "Accept Upcoming Billing Cycle Subscription",
    "Your Upcoming Billing Cycle Plan Has Been Added Successfully": "Your Upcoming Billing Cycle Plan Has Been Added Successfully.",
    "You have already selected an upcoming billing cycle plan If you want to change your upcoming billing cycle plan please ensure to remove the previously selected plan before making any alterations": "You have already selected an upcoming billing cycle plan. If you want to change your upcoming billing cycle plan, please ensure to remove the previously selected plan before making any alterations.",
    "Package Subscription Successfully": "Package Subscription Successfully",
    "Compulsory Amount": "Compulsory Amount",
    "Pay Compulsory Fees": "Pay Compulsory Fees",
    "Are you sure you want to add this add-on": "Are you sure you want to add this add-on?",
    "Addon added successfully": "Addon added successfully.",
    "Additional Billing Days": "Additional Billing Days",
    "Student Fee": "Student Fee",
    "Fees Paid": "Fees Paid",
    "Fees Transaction Logs": "Fees Transaction Logs",
    "Fees Type": "Fees Type",
    "Manage Fees Type": "Manage Fees Type",
    "Create Fees Type": "Create Fees Type",
    "Select Fees Type": "Select Fees Type",
    "List Fees": "List Fees",
    "Edit Fees Type": "Edit Fees Type",
    "Create Fees": "Create Fees",
    "Fees Installment": "Fees Installment",
    "Edit Fees": "Edit Fees",
    "Partial Paid": "Partial Paid",
    "Note : Certain additional features will not be part of the next billing period as they have already been integrated into your upcoming subscription package": "Note : Certain additional features will not be part of the next billing period as they have already been integrated into your upcoming subscription package.",
    "discontinue_upcoming_plan": "Discontinue upcoming Plan.",
    "including_teachers_and_other_staffs": "Including teachers and other staffs",
    "addon_charges": "Addon Charges",
    "addon": "Addon",
    "total_addon_charges": "Total Addon Charges",
    "charges_per_day": "Charges per day",
    "total_bill_amount": "Total Bill Amount",
    "Fees Configuration": "Fees Configuration",
    "Total Fees": "Total Fees",
    "Optional Fee": "Optional Fee",
    "Installment Fee": "Installment Fee",
    "Compulsory Full Fee": "Compulsory Full Fee",
    "Compulsory Fees": "Compulsory Fees",
    "Optional Fees": "Optional Fees",
    "Student Name": "Student Name",
    "Total Fees Amount": "Total Fees Amount",
    "Fees Status": "Fees Status",
    "update_rank": "Update Rank",
    "currency_code": "Currency Code",
    "currency_symbol": "Currency Symbol",
    "Rank Updated Successfully": "Rank Updated Successfully",
    "To Reorder the Package, Drag the Table Row Up and Down and then Click on Update Rank": "To Reorder the Package, Drag the Table Row Up and Down and then Click on Update Rank",
    "auto_renewal_for_the_next_billing_cycle": "Auto Renewal for The Next Billing Cycle",
    "Kindly note that your license will expire on": "Kindly note that your license will expire on ",
    "If you want to modify your upcoming plan or remove any add-ons, please ensure that these changes are made before your current license expires": "If you want to modify your upcoming plan or remove any add-ons, please ensure that these changes are made before your current license expires",
    "Semester": "Semester",
    "Include Semesters": "Include Semesters",
    "manage_fees": "Manage Fees",
    "fee_structure": "Fee Structure",
    "add_new_structure": "Add new structure",
    "remove_structure": "Remove Structure",
    "purchase_date": "Purchase date",
    "add_new_data": "Add new Data",
    "expired_date": "Expired Date",
    "Start Month": "Start Month",
    "End Month": "End Month",
    "January": "January",
    "February": "February",
    "March": "March",
    "April": "April",
    "May": "May",
    "June": "June",
    "July": "July",
    "August": "August",
    "September": "September",
    "October": "October",
    "November": "November",
    "December": "December",
    "Month is already Occupied": "Month is already Occupied",
    "No Semester": "No Semester",
    "Group": "Group",
    "Yes Restore it": "Yes, Restore it",
    "You are about to Delete this data": "You are about to Delete this data",
    "Yes Delete Permanently": "Yes, Delete Permanently!",
    "Stream": "Stream",
    "Active": "Active",
    "Inactive": "Inactive",
    "Optional": "Optional",
    "Shift": "Shift",
    "Select Shift": "Select Shift",
    "No Stream": "No Stream",
    "month": "Month",
    "session_years": "Session Years",
    "the_payment_has_been_completed_successfully": "The payment has been completed successfully",
    "the_payment_has_been_cancelled": "The payment has been cancelled",
    "Manage Exams": "Manage Exams",
    "Create Exams": "Create Exams",
    "List Exams": "List Exams",
    "Manage Session Years": "Manage Session Years",
    "Create Session Years": "Create Session Years",
    "List Session Years": "List Session Years",
    "Edit Session Years": "Edit Session Years",
    "Select Class Section": "Select Class Section",
    "date": "Date",
    "registration": "Registration",
    "active": "Active",
    "deactivate": "Deactivate",
    "discontinue": "Discontinue",
    "unpaid": "Unpaid",
    "billing_cycle": "Billing Cycle",
    "continue_next_cycle": "Continue Next Cycle",
    "bill_date": "Bill Date",
    "total_billing_cycle": "Total Billing Cycle",
    "current_cycle": "Current Cycle",
    "over_due": "Over Due",
    "next_billing_cycle": "Next Billing Cycle",
    "system_name": "System Name",
    "The validity of add-on is determined by the expiration date of package": "The validity of add-on is determined by the expiration date of package",
    "Please make the necessary payment as your license has expired on": "Please make the necessary payment as your license has expired on",
    "We apologize for inconvenience but your payment was not successful Please try to process the payment again": "We apologize for inconvenience, but your payment was not successful. Please try to process the payment again",
    "No API key provided": "No API key provided",
    "bill_amount": "Bill Amount",
    "default_feature": "Default Feature",
    "ok": "OK",
    "no_permission": "Feature Not Included in Current Subscription.",
    "no_permission_upgrade_plan": "If you want to access this feature, please consider purchasing the addon or upgrading your plan before the next billing cycle to ensure uninterrupted service.",
    "click_here_to_buy_addon": "Click here to buy addon",
    "expense": "Expense",
    "manage_category": "Manage Category",
    "manage_expense": "Manage Expense",
    "expense_category": "Expense Category",
    "reference_no": "Reference No.",
    "Your License Has Expired": "Your License Has Expired",
    "payroll": "Payroll",
    "search": "Search",
    "no_records_found": "No Records Found",
    "salary": "Salary",
    "session_year": "Session Year",
    "reason": "Reason",
    "from_date": "From Date",
    "to_date": "To Date",
    "pending": "Pending",
    "approved": "Approved",
    "rejected": "Rejected",
    "billing_cycle_in_days": "Billing Cycle In Days",
    "Staff Management": "Staff Management",
    "Class Subject": "Class Subject",
    "Offline Exam": "Offline Exam",
    "Trashed": "Trashed",
    "First Name - Ascending": "First Name - Ascending",
    "First Name - Descending": "First Name - Descending",
    "Last Name - Ascending": "Last Name - Ascending",
    "Last Name - Descending": "Last Name - Descending",
    "Roll Number Sorting": "Roll Number Sorting",
    "Change Roll Number for All Classes": "Change Roll Number for All Classes",
    "Fees Settings": "Fees Settings",
    "Select": "Select",
    "Transfer & Promote Students": "Transfer & Promote Students",
    "Transfer Student In Next Section": "Transfer Student In Next Section",
    "Kindly settle any outstanding payments from before": "Kindly settle any outstanding payments from before.",
    "assign_schools": "Assign Schools",
    "tag_line": "Tag Line",
    "Change Status For Selected Users": "Change Status For Selected Users",
    "Status Updated Successfully": "Status Updated Successfully",
    "faqs": "Faqs",
    "Ascending": "Ascending",
    "Descending": "Descending",
    "Class Section is Required": "Class Section is Required",
    "Email ID is already taken for Other Role": "Email ID is already taken for Other Role",
    "Source Code Zip Extraction Failed": "Source Code Zip Extraction Failed",
    "Permission Error while crating Temp Directory": "Permission Error while crating Temp Directory",
    "Error Occurred while moving a Zip File": "Error Occurred while moving a Zip File",
    "System Settings": "System Settings",
    "Kindly configure the cron job on your server to execute the URL every day This will facilitate the regular examination of school subscription expirations and the creation of bills": "Kindly configure the cron job on your server to execute the URL every day. This will facilitate the regular examination of school subscription expirations and the creation of bills.",
    "Purchase": "Purchase",
    "to Continue using this functionality": "to Continue using this functionality",
    "Yes Activate": "Yes, Activate",
    "You want to Activate the Student": "You want to Activate the Student",
    "Yes inactive": "Yes, Inactive",
    "You want to inactive the Student": "You want to inactive the Student",
    "You want to Activate the Teacher": "You want to Activate the Teacher",
    "You want to inactive the Teacher": "You want to inactive the Teacher",
    "You want to inactive the Staff": "You want to inactive the Staff",
    "You want to Activate the Staff": "You want to Activate the Staff",
    "is not a valid Role name Because it's Reserved role": "is not a valid Role name Because it's Reserved role",
    "No Support staff is assigned to you Yet": "No Support staff is assigned to you Yet",
    "total_schools": "Total Schools",
    "active_schools": "Active Schools",
    "inactive_schools": "Inactive Schools",
    "inactive_school": "Inactive School",
    "Lessons": "Lessons",
    "Lesson & Topic": "Lesson & Topic",
    "Exam & Marks": "Exam & Marks",
    "By Changing this Semester setting, your existing data related to this class will be Auto Deleted": "By Changing this Semester setting, your existing data related to this class will be Auto Deleted",
    "Please note that only image or document files are allowed for upload": "Please note that only image or document files are allowed for upload",
    "Manage Online Exam Questions": "Manage Online Exam Questions",
    "Edit Online Exam Questions": "Edit Online Exam Questions",
    "File": "File",
    "Gr Number": "Student ID",
    "Can not Delete this because marks are already submitted": "Can not Delete this because marks are already submitted",
    "Note : Subject Name,Code & Type should be Unique for Medium": "Note : Subject Name,Code & Type should be Unique for Medium",
    "front_site_settings": "Front Site Settings",
    "Themes Settings": "Themes Settings",
    "Footer Settings": "Footer Settings",
    "First download dummy file and convert to .csv file then upload it": "First download dummy file and convert to .csv file then upload it",
    "theme_color": "Theme Color",
    "primary_color": "Primary Color",
    "secondary_color": "Secondary Color",
    "home_image": "Home Image",
    "social_media_links": "Social Media Links",
    "facebook": "Facebook",
    "instagram": "Instagram",
    "linkedin": "LinkedIn",
    "footer_text": "Footer Text",
    "restore_default": "Restore Default",
    "short_description": "Short Description",
    "enter_marks": "Enter Marks",
    "reset_student_password": "Reset Student Password",
    "select_medium": "Select Medium",
    "result": "Result",
    "leaves": "Leaves",
    "today": "Today",
    "no_holiday_found": "No holiday found",
    "feedback": "Feedback",
    "Publish Result": "Publish Result",
    "Sunday": "Sunday",
    "Monday": "Monday",
    "Tuesday": "Tuesday",
    "Wednesday": "Wednesday",
    "Thursday": "Thursday",
    "Friday": "Friday",
    "Saturday": "Saturday",
    "full": "Full",
    "first_half": "First Half",
    "second_half": "Second Half",
    "Prefix Name": "Prefix Name",
    "Add Fees Type": "Add Fees Type",
    "Fee Installments": "Fee Installment",
    "Payment Settings": "Payment Settings",
    "To modify an existing leave, kindly delete the old entry and submit a new request": "To modify an existing leave, kindly delete the old entry and submit a new request",
    "total_leaves_per_year": "Total Leaves Per Year",
    "staff_leave_settings": "Staff Leaves Settings",
    "holiday_days": "Holiday Days",
    "basic_salary": "Basic Salary",
    "Monthly Allowed Paid Leaves": "Monthly Allowed Paid Leaves",
    "taken_leaves": "Taken Leaves",
    "net_salary": "Net Salary",
    "salary_deduction": "Salary Deduction",
    "filter": "Filter",
    "unpaid_leaves": "Unpaid Leaves",
    "login_page_logo": "Login Page Logo",
    "subscription_settings": "Subscription Settings",
    "free_trial_subscription_settings": "Free Trial Subscription Settings",
    "trial_days": "Trial Days",
    "student_limit": "Student Limit",
    "staff_limit": "Staff Limit",
    "inactive": "Inactive",
    "You want to go ahead with this plan?": "You want to go ahead with this plan?",
    "Restricted in the free trial subscription": "Restricted in the free trial subscription",
    "active_plan": "Active Plan",
    "my": "My",
    "select_month": "Select Month",
    "settings": "Settings",
    "apply_leave": "Apply Leave",
    "leave_report": "Leave Report",
    "details": "Details",
    "allocated": "Allocated",
    "used": "Used",
    "remaining": "Remaining",
    "CL": "CL",
    "LWP": "LWP",
    "assign_package": "Assign Package",
    "select_package": "Select Package",
    "expiry": "Expiry",
    "change_bill_date": "Change Bill Date",
    "change_billing_cycle": "Change Bill Cycle",
    "discontinue_upcoming_billing_cycle": "Discontinue upcoming billing cycle.",
    "start_immediate_this_plan": "Start immediate this plan, Your current plan is expire, and a billing statement will be generated according to the number of days usage.",
    "update_current_plan": "Update Current Plan",
    "stop_auto_renewal_plan": "Stop auto renewal plan",
    "Auto-renewal successfully canceled": "Auto-renewal successfully canceled",
    "school_terms_condition": "School Terms & Condition",
    "I accept the provided": "I accept the provided",
    "please_confirm_terms_condition": "Please confirm terms & condition",
    "casual_leave": "Casual Leave",
    "leave_without_pay": "Leave Without Pay",
    "You have previously submitted a leave request for these dates": "You have previously submitted a leave request for these dates",
    "bill_generate_date": "Bill Generate Date",
    "bill_due_date": "Bill Due Date",
    "bill_not_generated": "Bill Not Generated",
    "extend_bill_date": "Extend Bill Date",
    "extend_bill_due_date": "Extend Bill Due Date",
    "generate_bill": "Generate Bill",
    "bill generated successfully": "Bill Generated Successfully",
    "Settings not found, Contact super admin for assistance": "Settings not found, Contact super admin for assistance",
    "If you want to activate or deactivate students, teachers, or staff members in your upcoming plan, Please": "If you want to activate or deactivate students, teachers, or staff members in your upcoming plan, Please",
    "click here": "click here",
    "manage_user": "Manage User",
    "list_user": "List User",
    "full_name": "Full Name",
    "teachers_staffs": "Teachers & Staffs",
    "enable": "Enable",
    "disable": "Disable",
    "today_schedule": "Today Schedule",
    "get_start": "Get Start",
    "current_active_plan": "Current Active Plan",
    "update_upcoming_plan": "Update Upcoming Plan",
    "total_paidable_amount": "If your bill does not reach the minimum charge, Kindly pay minimum amount",
    "bill_to": "Bill To",
    "invoice_date": "Invoice Date",
    "bill": "Bill",
    "This add-on will be added into your current subscribed package and will expire when your current subscription expires": "This add-on will be added into your current subscribed package and will expire when your current subscription expires.",
    "added": "Added",
    "update_plan": "Update Plan",
    "fees_payment_settings": "Fees Payment Settings",
    "guidance": "Guidance",
    "If you wish to activate or deactivate students, teachers, or staff members in your upcoming plan, Please": "If you wish to activate or deactivate students, teachers, or staff members in your upcoming plan, Please",
    "signout": "signout",
    "Current Plan Expiry Warning Days": "Current Plan Expiry Warning Days",
    "Theory": "Theory",
    "Practical": "Practical",
    "new": "New",
    "Current Class Section": "Current Class Section",
    "Transfer Class Section": "Transfer Class Section",
    "Select Class": "Select Class",
    "Select Session Years": "Select Session Years",
    "Order By": "Order By",
    "Default Values": "Default Values",
    "Note": "Note",
    "Activating this will consider in your current subscription cycle": "Activating this will consider in your current subscription cycle",
    "Starting Time": "Starting Time",
    "Ending Time": "Ending Time",
    "Timeslot Duration": "Timeslot Duration",
    "in Minutes": "in Minutes",
    "Tomorrow": "Tomorrow",
    "Upcoming": "Upcoming",
    "Manage Staff": "Manage Staff",
    "Staff List": "Staff List",
    "history": "History",
    "Currency": "Currency",
    "Payment Gateway": "Payment Gateway",
    "Payment Status": "Payment Status",
    "subscription_transaction": "Subscription Transaction",
    "The school hours dont match the current time slots Please select a valid time": "The school hours don't match the current time slots. Please select a valid time.",
    "home": "Home",
    "pricing": "Pricing",
    "register": "Register",
    "get_started": "Get Started",
    "our_features": "Our Features",
    "flexible_pricing_packages": "Flexible Pricing Packages",
    "frequently_asked_questions": "Frequently Asked Questions",
    "lets_get_in_touch": "Let's Get In Touch",
    "enter_email": "Enter Email",
    "message": "Message",
    "send_your_message": "Send Your Message",
    "support_contact": "Support Contact",
    "location": "Location",
    "start_learning_by": "Start Learning By",
    "downloading_apps": "Downloading Apps",
    "apple_store": "Apple Store",
    "play_store": "Play Store",
    "links": "Links",
    "follow_us": "Follow US",
    "loading": "Loading",
    "Your message has been sent. Thank you": "Your message has been sent. Thank you",
    "Email verification failed Please check your SMTP credentials": "Email verification failed. Please check your SMTP credentials.",
    "total_payable_amount": "Total Payable Amount",
    "server_not_responding": "Server not responding, please try again later",
    "Student Management": "Student Management",
    "Academics Management": "Academics Management",
    "Slider Management": "Slider Management",
    "Teacher Management": "Teacher Management",
    "Session Year Management": "Session Year Management",
    "Holiday Management": "Holiday Management",
    "Timetable Management": "Timetable Management",
    "Attendance Management": "Attendance Management",
    "Exam Management": "Exam Management",
    "Lesson Management": "Lesson Management",
    "Assignment Management": "Assignment Management",
    "Announcement Management": "Announcement Management",
    "Expense Management": "Expense Management",
    "Staff Leave Management": "Staff Leave Management",
    "Fees Management": "Fees Management",
    "School Gallery Management": "School Gallery Management",
    "ID Card - Certificate Generation": "ID Card - Certificate Generation",
    "Website Management": "Website Management",
    "Student Limit": "Student Limit",
    "Staff Limit": "Staff Limit",
    "Per Student Charges": "Per Student Charges",
    "Per Staff Charges": "Per Staff Charges",
    "Days": "Days",
    "generate_id_card": "Generate ID Card",
    "student_id_card": "Student ID Card",
    "instant_effects": "Instant Effects",
    "receive_payment": "Receive Payment",
    "payment_type": "Payment Type",
    "enter_cheque_number": "Enter Cheque Number",
    "Optional Fees does not support Due charges & Installment Facility": "Optional Fees does not support Due charges & Installment Facility",
    "Select All": "Select All",
    "gallery": "Gallery",
    "images": "Images",
    "note: Only features are effected": "Note: Only features are effected",
    "youtube_links": "Youtube Links",
    "photo_gallery": "Photo Gallery",
    "video_gallery": "Video Gallery",
    "note: Please use a separate line for each link": "Note: Please use a separate line for each link.",
    "Add More": "Add More",
    "notification": "Notification",
    "manage_notification": "Manage Notification",
    "create_notification": "Create Notification",
    "select_users": "-- Select users --",
    "send_to": "Send To",
    "list_notification": "List Notification",
    "upload_multiple_images": "Upload multiple images",
    "please_use_commas_or_press_enter_to_add_multiple_links": "Please use commas or press enter to add multiple links.",
    "order_id_cheque_number": "Order ID / Cheque No.",
    "Payment Type": "Payment Type",
    "web_settings": "Web Settings",
    "hero_title_1": "Hero Title 1",
    "hero_title_2": "Hero Title 2",
    "hero_image_2": "Hero Image 2",
    "please_use_commas_or_press_enter_to_add_multiple_points": "Please use commas or press enter to add multiple points.",
    "heading": "Heading",
    "custom_package_section": "Custom Package Section",
    "download_our_app_section": "Download Our App Section",
    "feature_sections": "Feature Sections",
    "hero_image": "Hero Image",
    "Current stripe payment not available": "Current stripe payment not available",
    "sections": "Sections",
    "all_users": "All users",
    "over_due_fees": "Over due fees",
    "specific_users": "Specific users",
    "hero_title_2_image": "Hero Image 2",
    "colour_settings": "Colour Settings",
    "theme_primary_color": "Primary Colour",
    "theme_secondary_color": "Secondary Colour",
    "theme_secondary_color_1": "Secondary Colour 1",
    "theme_primary_background_color": "Primary Background",
    "theme_text_secondary_color": "Text Secondary Colour",
    "if_the_school_does_not_currently_have_a_plan_please_assign_from_here_If_there_is_already_an_active_plan_proceed_to_the_subscription_page_to_make_any_necessary_changes": "If the school does not currently have a plan, please assign from here. If there is already an active plan, proceed to the subscription page to make any necessary changes.",
    "year": "Year",
    "start_trial": "Start Trial",
    "have_a_question_or_just_want_to_say_hi_Wed_love_to_hear_from_you": "Have a question or just want to say hi? We'd love to hear from you.",
    "register_your_school": "Register Your School",
    "explore_our_top_features": "Explore Our Top Features",
    "view_more_features": "View More Features",
    "view_less_features": "View Less Features",
    "per_student_charges": "Per Student Charges",
    "per_staff_charges": "Per Staff Charges",
    "custom_package": "Custom Package",
    "get_in_touch": "Get In Touch",
    "download_our_app_now": "Download Our App Now!",
    "enter_your_name": "Enter Your Name",
    "enter_your_email": "Enter Your Email",
    "send": "Send",
    "info": "Info",
    "follow": "Follow",
    "registration_form": "Registration Form",
    "create_school": "Create School",
    "enter_your_school_name": "Enter Your School Name",
    "enter_your_school_email": "Enter Your School Email",
    "enter_your_school_mobile_number": "Enter Your School Mobile Number",
    "enter_your_school_address": "Enter Your School Address",
    "add_admin": "Add Admin",
    "enter_your_first_name": "Enter Your First Name",
    "enter_your_last_name": "Enter Your Last Name",
    "enter_your_contact_number": "Enter Your Contact Number",
    "start_trial_package": "Start Trial Package",
    "upload_profile_images": "Upload Profile Images",
    "compulsory_fees": "Compulsory Fees",
    "optional_fees": "Optional Fees",
    "collected": "Collected",
    "total_fees": "Total Fees",
    "prepaid": "Prepaid",
    "postpaid": "Postpaid",
    "no_of_students": "No. Of Students",
    "no_of_staffs": "No. Of Staffs",
    "package_amount": "Package Amount",
    "We kindly request that you make the necessary payment for the upcoming prepaid plan to avoid any interruptions in service": "We kindly request that you make the necessary payment for the upcoming prepaid plan to avoid any interruptions in service.",
    "click_here_to_pay": "Click here to pay",
    "edit_payment": "Edit Payment",
    "package_price_information": "Package Price Information",
    "package_information": "Package Information",
    "Delete Class Timetable": "Delete Class Timetable",
    "Trial Package": "Trial Packages",
    "bills": "Bills",
    "succeed": "Succeed",
    "Cron Job URL": "Cron Job URL",
    "NOTE : An email will be sent to test if your email settings are correct": "NOTE : An email will be sent to test if your email settings are correct.",
    "Steps": "Steps",
    "Enter the email address in the input box.(Do not enter the same email address which you have used for Email Configuration)": "Enter the email address in the input box.(Do not enter the same email address which you have used for Email Configuration)",
    "Click on Verify": "Click on Verify",
    "Check your inbox if you have received a Testing Email then your Email Configuration are Correct Congratulations Email Setup is done": "Check your inbox, if you have received a Testing Email then your Email Configuration are Correct. Congratulations, Email Setup is done",
    "Verify": "Verify",
    "Currency Settings": "Currency Settings",
    "Public URL": "Public URL",
    "Purchase Code": "Purchase Code",
    "Only Zip File is allowed": "Only Zip File is allowed",
    "Your Current Version is": "Your Current Version is",
    "Please update nearest version here if available": "Please update nearest version here if available",
    "Semester Included": "Semester Included",
    "Core Subjects are the Compulsory Subject": "Core Subjects are the Compulsory Subject",
    "Elective Subjects are the subjects where student have the choice to select the subject from the given subjects": "Elective Subjects are the subjects where student have the choice to select the subject from the given subjects",
    "Class Teacher": "Class Teacher",
    "Subject Teacher": "Subject Teacher",
    "Text": "Text",
    "Numeric": "Numeric",
    "Dropdown": "Dropdown",
    "Radio Button": "Radio Button",
    "Checkbox": "Checkbox",
    "TextArea": "TextArea",
    "File Upload": "File Upload",
    "Note :- Activating this will consider in your current subscription cycle": "Note :- Activating this will consider in your current subscription cycle",
    "Generate": "Generate",
    "View Teacher Timetable": "View Teacher Timetable",
    "Certain Fees modification are prohibited because some Parents have already Paid the Fees": "Certain Fees modification are prohibited because some Parents have already Paid the Fees",
    "Due Charges Type": "Due Charges Type",
    "Fixed Amount": "Fixed Amount",
    "Percentage": "Percentage",
    "Due Charges will be in fixed amount once the due date is passed": "Due Charges will be in fixed amount once the due date is passed",
    "Due Charges will be calculated in % on minimum Installment Amount": "Due Charges will be calculated in % on minimum Installment Amount",
    "Roll Number Settings": "Roll Number Settings",
    "File Preview": "File Preview",
    "topic": "Topic",
    "All Subjects marks all compulsory": "All Subjects marks all compulsory",
    "slips": "Slips",
    "You wont to delete this element": "You won't to delete this element?",
    "header_color": "Header Colour",
    "footer_color": "Footer Colour",
    "header_footer_text_color": "Header Footer Text Colour",
    "layout_type": "Layout Type",
    "vertical": "Vertical",
    "horizontal": "Horizontal",
    "background_image": "Background Image",
    "profile_image_style": "Profile Image Style",
    "signature": "Signature",
    "select_fields": "Select Fields",
    "layout_spacing": "Layout Spacing",
    "page_width": "Page Width",
    "page_height": "Page Height",
    "mm": "MM",
    "round": "Round",
    "squre": "Squre",
    "settings_not_found": "Settings Not Found",
    "total_classes": "Total Classes",
    "total_streams": "Total Streams",
    "this_month": "This Month",
    "next_month": "Next Month",
    "filter_by": "Filter By",
    "tomorrow": "Tomorrow",
    "upcoming": "Upcoming",
    "fees_details": "Fees Details",
    "upload_profile": "Upload Profile",
    "student": "Student",
    "guardian_user_id": "Guardian User ID",
    "if_youve_already_made_payment_for_your_upcoming_plan_changes_or_updates_to_the_current_and_upcoming_plan_will_not_be_permitted": "If you've already made payment for your upcoming plan, changes or updates to the current and upcoming plan will not be permitted.",
    "Session year not found": "Session year not found",
    "total_packages": "Total Packages",
    "transaction": "Transaction",
    "Email Configuration is not verified": "Email Configuration is not verified",
    "Click here to redirect to email configuration": "Click here to redirect to email configuration",
    "birthday": "Birthday",
    "Fees names will be created based on the Classes Prefix will be appended before Class Name.eg. Prefix Name - Class Name": "Fees names will be created based on the Classes. Prefix will be appended before Class Name.eg. \"Prefix Name - Class Name\"",
    "display_school_logos": "Display Schools Logos",
    "display_counters": "Display Counters",
    "paid_amount": "Paid Amount",
    "Teacher / Staff App Settings": "Teacher / Staff App Settings",
    "select_roles": "Select Roles",
    "exam_timetable": "Exam Timetable",
    "send_a_notification_to_the_guardian_if_the_student_is_absent": "Send a notification to the guardian if the student is absent.",
    "cache_clear": "Cache Clear",
    "free": "Free",
    "Updated announcement in": "Updated announcement in",
    "Updated announcement": "Updated announcement",
    "New announcement in": "New announcement in",
    "New announcement": "New announcement",
    "staff_id_card": "Staff ID Card",
    "certificate": "Certificate",
    "template": "Template",
    "student_certificate": "Student Certificate",
    "staff_certificate": "Staff Certificate",
    "manage_certificate": "Manage Certificate",
    "create_certificate": "Create Certificate",
    "page_layout": "Page Layout",
    "width": "Width",
    "user_image_shape": "User Image Shape",
    "image_size": "Image Size",
    "edit_certificate": "Edit Certificate",
    "design": "Design",
    "school_logo": "School Logo",
    "issue_date": "Issue Date",
    "generate": "Generate",
    "user_image": "User Image",
    "px": "PX",
    "reset": "Reset",
    "school_mobile": "School Mobile",
    "layout": "Layout",
    "id_card_setting": "ID Card Settings",
    "note_required_medium_or_large_screen_only": "Note: Required medium or large screens only.",
    "notification_settings": "Notification Settings",
    "firebase_project_id": "Firebase Project Id",
    "firebase_service_file": "Firebase Service File",
    "Only Json File Allowed": "Only Json File Allowed",
    "Service File": "Service File",
    "exam_grade": "Exam Grade",
    "student_gender": "Student Gender",
    "online_exam": "Online Exam",
    "manage_online_exam": "Manage Online Exam",
    "manage_questions": "Manage Questions",
    "guardian_name": "Guardian Name",
    "guardian_email": "Guardian Email",
    "guardian_mobile": "Guardian Mobile",
    "experience": "Experience",
    "bulk upload": "Bulk Upload",
    "Staff Bulk Upload": "Staff Bulk Upload",
    "Teacher Bulk Upload": "Teacher Bulk Upload",
    "attachments": "Attachments",
    "upload_multiple_files": "Upload Multiple Files",
    "manage_payroll": "Manage Payroll",
    "manage_deductions": "Manage Deduction",
    "manage_allowances": "Manage Allowances",
    "allowances": "Allowances",
    "deductions": "Deductions",
    "create_allowances": "Create Allowance",
    "list_allowances": "List Allowances",
    "create_deduction": "Create Deduction",
    "list_deduction": "List deductions",
    "fixed_amount": "Fixed Amount",
    "Note:- Compulsory Choose one between fixed amount or percentage": "Note:- Compulsory Choose one between fixed amount or percentage",
    "Set Custom Values for Allowance and Deduction": "Set Custom Values for Allowance and Deduction",
    "add_new_allowances": "Add New Allowances",
    "allowance_type": "Allowance Type",
    "add_new_deduction": "Add New Deduction",
    "deduction_type": "Deduction Type",
    "amount": "Amount",
    "view_salary_structure": "View Salary Structure",
    "salary_structure": "Salary Structure",
    "content": "Content",
    "education_program": "Education Program",
    "app": "App",
    "web": "Web",
    "both": "Both",
    "web_page": "Web Page",
    "expert_teachers": "Expert Teachers",
    "our_mission": "Our Mision",
    "enter_amount": "Enter Amount",
    "fees_paid_records": "Fees Paid Records",
    "domain": "Domain",
    "id_card_settings": "ID Card Settings",
    "note_these_signature_image_are_also_used_in_certificates": "Note : This signature image is also used in certificates.",
    "google_map_link": "Google Map Link",
    "convert_into_embed_url": " (Convert embed url)",
    "footer_logo": "Footer Logo",
    "class_group": "Class Group",
    "Delete this item": "Delete this item",
    "Duplicate values are not allowed": "Duplicate values are not allowed",
    "primary_background_color": "Primary Background Colour",
    "text_secondary_color": "Text Secondary Colour",
    "primary_hover_color": "Primary Hover Colour",
    "email_template": "Email Template",
    "school_admin_name": "School Admin Name",
    "super_admin_name": "Super Admin Name",
    "support_email": "Support Email",
    "url": "URL",
    "child_password": "Child Password",
    "child_name": "Child Name",
    "parent_name": "Parent Name",
    "staff-email-template": "Staff email template",
    "parent-email-template": "Parent email template",
    "generate_exam_certificate_note": "Kindly note that for creating exam certificates, it's essential to select the correct session year and exam.",
    "payroll_setting": "Payroll Settings",
    "list_payroll_setting": "List Payroll Settings",
    "School Website Management Feature Settings": "School Website Management Feature Settings",
    "note_if_you_have_vps_server": "NOTE : If you have a VPS server or can create a wildcard domain, you will be able to access this website management feature by clicking on the 'Website Management' box.",
    "view_result": "View Result",
    "certificate_template": "Certificate Template",
    "certificate_id_card": "Certificate & ID Card",
    "generate_student_id_card": "Generate Student ID Cards",
    "generate_staff_id_card": "Generate Staff ID Cards",
    "Staff Leave": "Staff Leave",
    "restore": "Restore",
    "View Timetable": "View Timetable",
    "delete_payment": "Delete Payment",
    "Immediate Features Access": "Immediate Features Access",
    "Checked": "Checked:",
    "Unchecked": "Unchecked:",
    "Features changes will be available to existing subscribers in their current billing cycle": "Features changes will be available to existing subscribers in their current billing cycle.",
    "Features changes will only be available to subscribers starting in their next billing cycle": "Features changes will only be available to subscribers starting in their next billing cycle.",
    "android_app": "Android App",
    "ios_app": "IOS App",
    "Kindly clear the data from the search field": "Kindly clear the data from the search field.",
    "razorpay": "Razorpay",
    "Roles": "Roles",
    "Over Due Fees": "Over Due Fees",
    "Specific users": "Specific users",
    "All users": "All users",
    "manage_students_roll_no": "Manage Students Roll Number",
    "create_online_exam": "Create Online Exam",
    "assign_roll_no": "Assign Roll Number",
    "YouTube Link": "YouTube Link",
    "Video Upload": "Video Upload",
    "Other Link": "Other Link",
    "Accepted": "Accepted",
    "Rejected": "Rejected",
    "Resubmitted": "Resubmitted",
    "view_bill": "View Bill",
    "New user Sign up to manage your school activities seamlessly": "New user? Sign up to manage your school activities seamlessly!",
    "notification_settings": "Notification Settings",
    "total_days": "Total Days",
    "presents": "Presents",
    "absents": "Absents",
    "payment_remark": "Payment remark",
    "fees": "Fees",
    "report": "Report",
    "collection": "Collection",
    "outstanding": "Outstanding",
    "pending": "Pending",
    "ic_no": "IC Number",
    "epf_no": "EPF Number",
    "socso_no": "Socso Number",
    "tax_no": "Tax No",
    "bank_name": "Bank name",
    "bank_no": "Bank No",
    "allowance": "Allowance",
    "claims": "Claims",
    "others": "Others",
    "payroll_remark": "Remark",
    "leave_without_pay": "Leave Without Pay(Days)",
    "employee_epf": "Employee EPF",
    "employee_socso": "Employee Socso",
    "employee_eis": "Employee EIS",
    "employee_pcb": "Employee PCB",
    "employer_epf": "Employer EPF",
    "employer_socso": "Employer Socso",
    "employer_eis": "Employer EIS",
    "position": "Position",
    "school_code": "School Code",
    "generate": "Generate",
    "online_admission": "Online Admission",
    "admission_form": "Admission Form",
    "attendance_tracker": "Attendance Tracker",
    "scan": "Scan",
    "enter_date": "Enter Date",
    "sign_in": "Sign In",
    "sign_out": "Sign Out",
    "total_time": "Total Time",
    "scan_attendance": "Scan to take attendance",
    "clock_in": "Clock In",
    "clock_out": "Clock Out",
    "student_id": "Student Id",
    "rfid_id": "RFID ID",
    "Manage Reward system": "Manage Reward system",
    "Reports": "Report",
    "Manage Reward": "Manage Reward",
    "Add New Points": "Add New Points",
    "Students": "Students",
    "Type": "Type",
    "Credit +": "Credit +",
    "Debit -": "Debit -",
    "Points": "Points",
    "Remarks": "Remarks",
    "Remark": "Remark",
    "add score": "Add Score",
    "Manage Reward Points": "Manage Reward Points",
    "Student Class": "Student Class",
    "Start Date": "Start date",
    "End Date": "End date",
    "Balance": "Balance",
    "Last Update": "Last Update",
    "Credit": "Credit",
    "Debit": "Debit",
    "Update Score": "Update Score",
    "Update Points": "Update Points",
    "Student Fees Type": "Student Fees Type",
    "Student Fee Types": "Student Fees Type",
    "Manage Student Fees": "Manage Student Fees",
    "Student Fees Paid": "Student Fees Paid",
    "Fees Collection Report": "Fees Collection Report",
    "Pending Fees Report": "Pending Fees Report",
    "Outstanding Fees Report": "Outstanding Fees Report",
    "Student Fees Report": "Student Fees Report",
    "Class Fees Report": "Class Fees Report",
    "Financial Report": "Financial Report",
    "Manage Student Fee Types": "Manage Student Fee Types",
    "Create Student Fee Type": "Create Student Fee Type",
    "amount": "amount",
    "List Student Fee Type": "List Student Fee Type",
    "Import": "Import",
    "Create Student Fees": "Create Student Fees",
    "List Student Fees": "List Student Fees",
    "Invoice Date": "Invoice Date",
    "Status": "Status",
    "Due Date": "Due Date",
    "Paid Date": "Paid Date",
    "Action": "Action",
    "Pay Fees": "Pay Fees",
    "mode": "Payment Mode",
    "Invoice Number": "Invoice Number",
    "Description": "Description",
    "total_active_students": "Total Active Students",
    "total_inactive_students": "Total Inactive Students",
    "student_remark": "Remark",
    "remark_picture": "Remark Picture",
    "rfid_whitelist": "RFID Whitelist",
    "student_subject": "Student Subject",
    "created_on": "Created On",
    "picture": "Picture",
    "created_date": "Created date",
    "files_or_picture": "Files or Picture",
    "old_file": "Old File",
    "old_file_or_image": "Old File Or Image",
    "file_or_picture": "File Or Picture",
    "update_file_or_picture": "Edit Update File Or Picture",
    "progress": "Progress",
    "replacement": "Replacement",
    "employee_epf_11%": "Employee EPF (11%)",
    "employer_epf_13%": "Employer EPF (13%)",
    "admin_manual": "Admin Manual",
    "update notification": "Update Notification",
    "send_list": "Send List",
    "video_setting": "Video Settings",
    "feature_highlights": "Feature Highlights:",
    "leave_category": "Leave Category",
    "e-invoice": "E-Invoice",
    "new_company_registration_number": "New Company Registration Number",
    "old_company_registration_number": "Old Company Registration Number",
    "company_business_activity": "Company Business Activity",
    "tax_identification_number": "Tax Identification Number (TIN) ",
    "sst_registration_number": "SST Registration Number",
    "tourism_tax_registration_number": "Tourism Tax Registration Number",
    "company_msic_code": "Company MSIC Code",
    "address_line1": "Address Line 1",
    "address_line2": "Address Line 2",
    "address_line3": "Address Line 3",
    "city": "City",
    "postal_code": "Postal Code",
    "country": "Country",
    "state": "State",
    "timetable_configurations": "Timetable Configurations",
    "name_per_icno": "Name Per NRIC",
    "unit": "Unit",
    "classification_code": "Classification Code",
    "quantity": "Quantity",
    "fees_type_amount": "Fees Type Amount",
    "Manage Credit Note": "Manage Credit Note",
    "Create Credit Note": "Create Credit Note",
    "Credit Note Type": "Credit Note Type",
    "credit_note": "Credit Note",
    "credit_note_no": "Credit Note No",
    "invoice_number": "Invoice Number",
    "uom": "UOM",
    "invoice_number": "Invoice Number",
    "picture_setting": "Picture Setting",
    "my_partner": "Our Partners",
    "storage_capacity": "Storage Capacity",
    "branch_address": "Branch Address",
    "edit_branch_school": "Edit School Branch",
    "usage_storage_capacity": "Usage Storage Capacity",
    "login_as_school": "Login As School",
    "subject_fees_per_section": "Subject Fees Per Session",
    "user_group": "User Group",
    "commission_type": "Commission Type",
    "commission_amount": "Commission Amount",
    "group_name": "Group Name",
    "manage_commission": "Manage Commission",
    "Terms_and_Condition": "Terms and Condition",
    "click_here_to_view_file": "Click here to view file",
    "new_package": "New Package",
    "package_name": "Package Name",
    "package_id": "Package Id",
    "total_sessions": "Total Sessions",
    "expiry_days": "Expiry Days",
    "add_package": "Add Package",
    "last_update": "Last Update",
    "subject_package": "Subject Package",
    "sales": "Sales",
    "update_subject_package": "Update Subject Package",
    "add_new_package": "Add New Package",
    "edit_package": "Edit Package",
    "update_credit_points": "Update Credit Points",
    "update_credit": "Update Credit",
    "early_offer": "Early Payment Discount",
    "early_date": "Early Date",
    "package_purchase": "Package Purchase",
    "purchase_package": "Purchase New Package",
    "purchase": "Purchase",
    "view_all": "View All",
    "usage": "Usage",
    "remaining_days": "Remaining Days",
    "package_usage": "Package Usage",
    "remaining_sessions": "Remaining Sessions",
    "deduct": "Deduct",
    "early_date": "Early Payment Date",
    "refund_note_no": "Refund Note No",
    "debit_note_no": "Debit Note No",
    "expired": "Expired",
    "deduction_setting": "Deduction Setting",
    "package_sales": "Package Sales",
    "add_credit": "Add Credit",
    "assign_new_package": "Assign New Package",
    "assigned_package_log": "Assigned Package Log",
    "package_history": "Package History",
    "expiry_date": "Expiry Date",
    "expired_soon": "Expired Soon",
    "item_code": "Item Code",
    "fees_name": "Fees Name",
    "create_item_code": "Create Item Code",
    "manage_item_code": "Manage Item Code",
    "edit_item_code": "Edit Item Code",
    "select_code": "Select Code",
    "video": "Video",
    "leaderboard": "Leaderboard",
    "all Time": "All time",
    "ranking": "Ranking",
    "show full leaderboard": "Show Full Leaderboard",
    "academic_performance": "Academic Performance",
    "attendance_setting": "Attendance Setting",
    "statement": "Statement",
    "account_summary": "Account Summary",
    "clear_all": "Clear All",
    "total_score": "Total Score",
    "redeem_reward": "Redeem Reward",
    "manage_balances": "Manage Balances",
    "update_reward_points": "Update Reward Points",
    "current_reward_points": "Current Reward Points",
    "last_class": "Last Class",
    "current_class": "Current Class",
    "last_session_year": "Last Session Year",
    "current_session_year": "Current Session Year",
    "body_temperature": "Body Temperature",
    "in_temperature": "In Temperature",
    "out_temperature": "Out Temperature",
    "change_debit": "Change Debit",
    "change_remark": "Change Remark",
    "list_by_single": "List Data by Single Row",
    "reward_point": "Reward Points",
    "next": "Next",
    "reward_redeem": "Reward Redeem",
    "total_reward_points": "Total Reward Points",
    "enter_reward_point": "Enter Reward Point",
    "remarks": "Remarks",
    "previous": "Previous",
    "unit_price": "Unit Price",
    "paid_date": "Paid Date",
    "fees_status": "Fees Status",
    "discount": "Discount",
    "tax": "Tax",
    "sequence": "Sequence",
    "scan_reward": "Scan To Update Score And Redeem Gift",
    "update_reward": "Update Score And Redeem Gift",
    "select_performance": "Select An Academic Performance",
    "redeem_gift": "Redeem Gift",
    "update_score": "Update Score",
    "points_redemption": "Points Redemption",
    "no_enough_point": "You Don't Have Enough Point",
    "point_empty": "The Points Redemption is Required",
    "negative_point": "The Points Must Greater Than 0",
    "point_select_error": "Please Select An Academic Performance",
    "sql_customer_code": "SQL Customer Code",
    "setting": "Setting",
    "through_rfid": "Through RFID",
    "subject_next_note": "Please note: The information you have entered will not be saved if you clicking 'next'.",
    "subject_next_confirmation_note": "Make sure to review and save the form before navigating away.",
    "positive_note_reward": "Note: By default, adding or deducting points will affect only the Score. To include changes to Reward Points, please enable the option for the relevant Academic Performance in the Settings under the Reward System.",
    "negative_note_reward": " Deducting points for bad behavior will only decrease the Score and not the Reward Points, ensuring that the student's ranking remains unaffected when they redeem gifts.",
    "redeeming_limitation": "Note: Redeeming rewards only deducts Reward Points, not the Score, so the ranking stays the same.",
    "student_detail_note": "Note: Upon completion of the \"Online Application Form\" by the parent, please be aware that the student will remain in \"inactive\" status by default. You will need to manually activate the student through the Student Details menu in order to complete the enrollment process.",
    "show_leaderboard": "Show Full Leaderboard",
    "hide_leaderboard": "Hide Leaderboard",
    "all_time_ranking": "All Time Ranking",
    "update_reward_point": "Update Reward Point",
    "reward_system": "Reward System",
    "recurring_month": "Recurring Month",
    "allow_affect_reward": "Mark this to change reward points with the score.",
    "leave_school": "Leave",
    "item_code_notice": "Please Note: This is master data, so you only need to set it up once.",
    "item_code_notice_two": "You won’t have to recreate it each time you generate an invoice.",
    "rfid_no": "RFID No",
    "score": "Score",
    "eInvoice_settings": "E-invoice Settings",
    "Booking System": "Booking System",
    "start time": "Start Time",
    "end time": "end Time",
    "capacity": "Capacity",
    "time": "Time",
    "reject_remark": "Rejection Reason",
    "approve": "Approve",
    "manage_slot": "Manage Slot",
    "validate e-invoice": "Validate E-invoice",
    "invoice": "Invoice",
    "submit e-invoice": "Submit E-invoice",
    "in_picture": "In Picture",
    "out_picture": "Out Picture",
    "xiaohongshu": "Xiaohongshu",
    "tiktok": "Tik Tok",
    "photos": "Photos",
    "videos": "Videos",
    "recurring_from": "Recurring From",
    "valid": "Valid",
    "invalid": "Invalid",
    "cancel e-invoice": "Cancel E-Invoice",
    "cancelled": "Cancelled",
    "Auto Publish Date": "Auto Publish Date",
    "Auto publish date (⚠️ Draft invoices only. Must set future date.)": "Auto publish date (⚠️ Draft invoices only. Must set future date.)",
    "subject_fees_per_month":"Subject Fees Per Month",
    "fees_per_month":"Fees Per Month",
    "commission_per_month":"Commision Per Month",
    "commission_per_session":"Commision Per Session",
    "ic_no_2": "IC Number",
    "face_recognition_attendance_settings": "Face Recognition Attendance Settings",
    "tax_type": "Tax Type",
    "select_tax_type": "Select Tax Type",
    "tax_percentage": "Tax Percentage (%)",
    "extra_tax": "Extra Tax",
    "face_recognition_attendance_settings": "Face Recognition Attendance Settings",
    "self_billing": "Self-Billing",
    "supplier":"Supplier",
    "id_type":"ID Type",
    "phone_number":"Phone Number",
    "business_activity_description":"Business Activity Description",
    "validated":"Validated",
    "not_validated":"Not Validated",
    "back_to_list":"Back to List",
    "basic_information":"Basic Information",
    "line_item":"Line Item",
    "additional_information":"Additional Information",
    "debit_note":"Debit Note",
    "refund_note":"Refund Note",
    "issue_date_time":"Issue Date Time",
    "manage_self_billing":"Manage Self-Billing",
    "tariff_code":"Tariff Code",
    "measurement":"Measurement",
    "discount_rate":"Discount Rate",
    "discount_description":"Discount Description",
    "charge_rate":"Charge Rate",
    "charge_description":"Charge Description",
    "tax_details":"Tax Details",
    "tax_type":"Tax Type",
    "tax_amount":"Tax Amount",
    "tax_percentage":"Tax %",
    "tax_exempted":"Tax Exempted",
    "tax_exempted_details":"Details Of Tax Exempted ",
    "country_of_origin":"Country Of Origin",
    "add_line_item":"Add Line Item",
    "delete_line_item":"Delete Line Item",
    "description_of_product_service":"Description Of Product / Service",
    "total_discount_amount":"Total Discount Amount",
    "total_charge_rate":"Total Charge Rate",
    "total_exempted":"Total Exempted",
    "select_tax_type":"Select Tax Type",
    "total_excluding_tax":"Total Excl. Tax",
    "total_tax":"Total Tax",
    "add_tax":"Add Tax",
    "number_unit": "Number of Unit",
    "tax_per_unit": "Tax Per Unit",
    "frequency_of_billing_information":"Frequency of Billing Information",
    "frequency_of_billing":"Frequency of Billing",
    "billing_period_start_date":"Billing Period Start Date",
    "billing_period_end_date":"Billing Period End Date",
    "payment_and_prepayment_information":"Payment And Prepayment Information",
    "payment_mode":"Payment Mode",
    "select_payment_mode":"Select Payment Mode",
    "supplier_bank_account_number":"Supplier Bank Account Number",
    "payment_terms":"Payment Terms",
    "select_payment_terms":"Select Payment Terms",
    "prepayment_amount":"Prepayment Amount",
    "prepayment_date":"Prepayment Date",
    "prepayment_reference_number":"Prepayment Reference Number",
    "bill_reference_number":"Bill Reference Number",
    "shipping_information":"Shipping Information",
    "shipping_recipient_name":"Shipping Recipient Name",
    "select_country":"Select Country",
    "select_id_type":"Select ID Type",
    "passport_number":"Passport Number",
    "shipping_recipient_tin":"Shipping Recipient TIN",
    "import_and_export_information":"Import And Export Information",
    "reference_number_of_customs_form_no_1_9_etc":"Reference Number Of Customs Form No. 1 / 9 / Etc.",
    "incoterms":"Incoterms",
    "free_trade_agreement_fta_information":"Free Trade Agreement (FTA) Information",
    "authorization_number_for_certified_exporter":"Authorization Number For Certified Exporter",
    "reference_number_of_custom_form_no_2":"Reference Number Of Custom Form No. 2",
    "details_of_other_charges":"Details Of Other Charges",
    "details_of_other_charges_description":"Details Of Other Charges Description",
    "select_frequency":"Select Frequency",
    "weekly":"Weekly",
    "biweekly":"Biweekly",
    "monthly":"Monthly",
    "bimonthly":"Bimonthly",
    "quarterly":"Quarterly",
    "half_yearly":"Half Yearly",
    "yearly":"Yearly",
    "not_applicable":"Not Applicable",
    "enter_bank_account_number":"Enter Bank Account Number",
    "select_state":"Select State",
    "passport":"Passport",
    "total_including_tax":"Total Incl. Tax",
    "you_want_to_delete_selected_self_billing":"You Want To Delete Selected Self-Billing?",
    "trash":"Trash",
    "add_self_billing":"Add Self-Billing",
    "you_want_to_delete_line_item":"You Want To Delete Line Item?",
    "you_want_to_delete_tax":"You Want To Delete Tax?",
    "summary":"Summary",
    "total_tax_amount":"Total Tax Amount",
    "total_net_amount":"Total Net Amount",
    "invoice_discount":"Invoice Discount",
    "invoice_charge":"Invoice Charge",
    "total_rounding_amount":"Total Rounding Amount",
    "tin_type":"TIN Type",
    "e_invoice_email":"E-Invoice Email",
    "invoice_number_reference":"Invoice Number Reference",
    "check_status":"Check Status"
}";