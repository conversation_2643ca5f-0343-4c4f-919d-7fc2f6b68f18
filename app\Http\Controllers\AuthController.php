<?php

namespace App\Http\Controllers;

use App\Repositories\SystemSetting\SystemSettingInterface;
use App\Repositories\User\UserInterface;
use App\Services\CachingService;
use App\Services\ResponseService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Throwable;

class AuthController extends Controller {
    private UserInterface $user;
    private CachingService $cache;
    private SystemSettingInterface $systemSettings;

    public function __construct(UserInterface $user, CachingService $cachingService, SystemSettingInterface $systemSettings) {
        $this->middleware('auth');
        $this->user = $user;
        $this->cache = $cachingService;
        $this->systemSettings = $systemSettings;
    }

    public function login() {
        if (Auth::user()) {
            return redirect('/');
        }
        $systemSettings = $this->cache->getSystemSettings();
        return view('auth.login', compact('systemSettings'));
    }


    public function changePasswordIndex() {
        return view('auth.change-password');
    }

    public function changePasswordStore(request $request) {
        if (env('DEMO_MODE')) {
            return response()->json(array(
                'error'   => true,
                'message' => "This is not allowed in the Demo Version.",
                'code'    => 112
            ));
        }
        $id = Auth::id();
        $request->validate([
            'old_password'     => 'required',
            'new_password'     => 'required|min:8',
            'confirm_password' => 'required|same:new_password',
        ]);
        try {
            if (Hash::check($request->old_password, Auth::user()->password)) {
                $data['password'] = Hash::make($request->new_password);
                $this->user->builder()->where('id', $id)->update($data);
                $response = array(
                    'error'   => false,
                    'message' => trans('Data Updated Successfully')
                );
            } else {
                ResponseService::errorResponse('In valid old password');
            }


        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e, "HomeController --> Change Password Method");
            ResponseService::errorResponse();
        }
        return response()->json($response);
    }

    public function checkPassword(Request $request) {
        $old_password = $request->old_password;
        $password = $this->user->findById(Auth::id());
        if (Hash::check($old_password, $password->password)) {
            return response()->json(1);
        }

        return response()->json(0);
    }


    public function logout(Request $request) {
        Auth::logout();
        $request->session()->flush();
        $request->session()->regenerate();
        return redirect('/');
    }

    public function profileEdit() {
        $userData = Auth::user()->load('staff');
        return view('auth.profile', compact('userData'));
    }

    public function profileUpdate(Request $request) {
        if (env('DEMO_MODE')) {
            return response()->json(array(
                'error'   => true,
                'message' => "This is not allowed in the Demo Version.",
                'code'    => 112
            ));
        }
        $request->validate([
            'first_name' => 'required',
            'last_name'  => 'required',
            'mobile'     => 'nullable|numeric|digits_between:1,16',
            'gender'     => 'required',
            'dob'        => 'required',
            'email'      => 'required|email|unique:users,email,' . Auth::user()->id,

            'current_address'   => 'required',
            'permanent_address' => 'nullable',
            'qualification'     => 'nullable',
            'ic_no'             => 'nullable',
            'epf_no'            => 'nullable',
            'socso_no'          => 'nullable',
            'tax_no'            => 'nullable',
            'bank_name'         => 'nullable',
            'bank_no'           => 'nullable',
            'rfid_id'           => 'nullable',
        ]);
        try {
            $userData = [
                'first_name' => $request->first_name,
                'last_name' => $request->last_name,
                'gender' => $request->gender,
                'dob' => $request->dob,
                'email' => $request->email,
                'current_address' => $request->current_address,
                'permanent_address' => $request->permanent_address,
                'mobile' => $request->mobile,
                'rfid_id' => $request->rfid_id,
            ];
            
            if (!empty($request->image)) {
                $userData['image'] = $request->image;
            }
            $this->user->update(Auth::user()->id, $userData);
            $user = Auth::user();
            if ($user->hasRole('Teacher') || $user->hasRole('Staff')) {
                $staffRepository = app(\App\Repositories\Staff\StaffInterface::class);
                
                $staffData = [
                    'qualification' => $request->qualification,
                    'ic_no' => $request->ic_no,
                    'epf_no' => $request->epf_no,
                    'socso_no' => $request->socso_no,
                    'tax_no' => $request->tax_no,
                    'bank_name' => $request->bank_name,
                    'bank_no' => $request->bank_no,
                    'position' => $request->position,
                    'admission_date' => $request->admission_date,
                    
                ];
                
                $staffRecord = $staffRepository->builder()->where('user_id', $user->id)->first();
                
                if ($staffRecord) {
                    $staffRepository->update($staffRecord->id, $staffData);
                } else {
                    $staffData['user_id'] = $user->id;
                    $staffRepository->create($staffData);
                }
            }

            if (Auth::user()->hasRole('Super Admin')) {
                $data = [
                    'name' => 'super_admin_name',
                    'data' => $request->first_name .' '. $request->last_name,
                    'type' => 'text'
                ];
                $this->systemSettings->upsert($data,['name'],['data','type']);
                $this->cache->removeSystemCache(config('constants.CACHE.SYSTEM.SETTINGS'));
            }
            ResponseService::successResponse('Data Stored Successfully');
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e, "Home Controller -> updateProfile Method");
            ResponseService::errorResponse();
        }
    }
}
