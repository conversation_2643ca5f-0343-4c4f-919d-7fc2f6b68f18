<?php

namespace App\Http\Controllers;

use App\Repositories\LeaveMaster\LeaveMasterInterface;
use App\Repositories\SessionYear\SessionYearInterface;
use App\Services\BootstrapTableService;
use App\Services\CachingService;
use App\Services\ResponseService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Throwable;

class LeaveMasterController extends Controller
{

    private LeaveMasterInterface $leaveMaster;
    private SessionYearInterface $sessionYear;

    public function __construct(LeaveMasterInterface $leaveMaster, SessionYearInterface $sessionYear) {
        $this->leaveMaster = $leaveMaster;
        $this->sessionYear = $sessionYear;
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
        ResponseService::noPermissionThenRedirect('school-setting-manage');
        $sessionYear = $this->sessionYear->builder()->pluck('name','id');
        $staffs = DB::table('staffs')
        ->join('users', 'staffs.user_id', '=', 'users.id')
        ->where('users.school_id', Auth::user()->school_id)
        ->whereNull('users.deleted_at')
        ->pluck(DB::raw("CONCAT(users.first_name, ' ', users.last_name) AS name"), 'staffs.id');
        $leaveCategory = DB::table('leave_category')->where('school_id', Auth::user()->school_id)->whereNull('deleted_at')->pluck('name', 'id')->toArray();

        return view('leave.leave_master',compact('sessionYear','staffs','leaveCategory'));

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
        ResponseService::noPermissionThenRedirect('school-setting-manage');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
        ResponseService::noPermissionThenRedirect('school-setting-manage');
        $request->validate([
            'leaves' => 'required|numeric',
            'holiday_days' => 'required',
        //     'session_year_id' => 'required|unique:leave_masters'
        // ],[
        //     'session_year_id.unique' => 'This session year has already been taken.'
        // ]);
        ]);
        $staffIds = $request->staffs_id;
        if (in_array('all', $staffIds) && count($staffIds) > 1) {
            return ResponseService::errorResponse("Please choose either 'All' teachers or specific teachers");
        }

        if(in_array('all',$staffIds)){
            $staffIds = DB::table('staffs')
            ->join('users', 'staffs.user_id', '=', 'users.id')
            ->where('users.school_id',Auth::user()->school_id)
            ->whereNull('users.deleted_at')
            ->pluck('staffs.id');
        }
        try {
            DB::beginTransaction();
            foreach($staffIds as $staffId){
                $day = implode(',',$request->holiday_days);
                $data = [
                    'leaves' => $request->leaves,
                    'holiday' => $day,
                    'leave_category_id' => $request->leave_category_id,
                    'session_year_id' => $request->session_year_id,
                ];
                $existRecord = DB::table('leave_masters')->where('staff_id', $staffId)->where('leave_category_id', $request->leave_category_id)->first();
                if($existRecord){
                    $this->leaveMaster->update($existRecord->id,$data);
                } else {
                    $data['staff_id'] = $staffId;
                    $this->leaveMaster->create($data);
                }
                DB::commit();
            }
            ResponseService::successResponse('Data Stored Successfully');
        } catch (\Throwable $e) {
            ResponseService::logErrorResponse($e, "LeaveMaster Controller -> Store Method");
            ResponseService::errorResponse();
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id, Request $request)
    {
        //
        ResponseService::noPermissionThenRedirect('school-setting-manage');
        $offset = request('offset', 0);
        $limit = request('limit', 10);
        $sort = request('sort', 'id');
        $order = request('order', 'DESC');
        $search = request('search');
        $staffId = $request->staff_id;
        $leaveCategoryId = $request->leave_category_id;

        $sql = DB::table('leave_masters')
                ->join('staffs', 'leave_masters.staff_id', '=', 'staffs.id')
                ->join('users', 'staffs.user_id', '=', 'users.id')
                ->join('session_years','leave_masters.session_year_id','=','session_years.id')
                ->join('leave_category', 'leave_masters.leave_category_id', '=', 'leave_category.id')
                ->select([
                    'leave_masters.*',
                    DB::raw("CONCAT(users.first_name, ' ', users.last_name) as staff_name"),
                    'leave_category.name AS leave_category_name',
                    'session_years.name AS session_year_name'
                ])
                ->when($staffId, function ($query) use ($staffId) {
                    $query->where('leave_masters.staff_id', $staffId);
                })
                ->when($leaveCategoryId, function ($query) use ($leaveCategoryId) {
                    $query->where('leave_masters.leave_category_id', $leaveCategoryId);
                })
                ->when(request('session_year_id') != null, function ($query) use ($request) {
                    $query->where('leave_masters.session_year_id', $request->session_year_id);
                })
                ->where(function ($q) use ($search) {
                    $q->when($search, function ($query) use ($search) {
                        $query->where('leave_masters.leaves', 'LIKE', "%$search%")
                            ->orWhere('leave_masters.holiday', 'LIKE', "%$search%")
                            ->orWhere(DB::raw("CONCAT(users.first_name, ' ', users.last_name)"), 'LIKE', "%$search%")
                            ->orWhere('leave_category.name', 'LIKE', "%$search%");
                    });
                })
                ->where('leave_masters.school_id','=',Auth::user()->school_id)
                ->whereNull('leave_category.deleted_at');
            

        $total = $sql->count();

        $sql->orderBy($sort, $order)->skip($offset)->take($limit);
        $res = $sql->get();

        $bulkData = array();
        $bulkData['total'] = $total;
        $rows = array();
        $no = 1;
        foreach ($res as $row) {
            $operate = BootstrapTableService::editButton(route('leave-master.update', $row->id));
            $operate .= BootstrapTableService::deleteButton(route('leave-master.destroy', $row->id));
            $tempRow = (array)$row;
            $tempRow['no'] = $no++;
            $tempRow['operate'] = $operate;
            $rows[] = $tempRow;
        }
        $bulkData['rows'] = $rows;
        return response()->json($bulkData);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
        ResponseService::noPermissionThenRedirect('school-setting-manage');
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
        ResponseService::noPermissionThenRedirect('school-setting-manage');
        $request->validate([
            'leaves' => 'required|numeric',
            'holiday_days' => 'required',
        //     'session_year_id' => 'required|unique:leave_masters,session_year_id,'.$id
        // ],[
        //     'session_year_id.unique' => 'This session year has already been taken.'
        // ]);
        ]);
        try {
            DB::beginTransaction();
            $checkExist = DB::table('leave_masters')->where('staff_id',$request->staff_id)->where('leave_category_id',$request->leave_category_id)->where('id','!=',$id)->exists();
            if($checkExist){
                return ResponseService::errorResponse('Leave category already assigned to this staff');
            }
            $day = implode(',',$request->holiday_days);
            $data = [
                'leaves' => $request->leaves,
                'holiday' => $day,
                'session_year_id' => $request->session_year_id,
                'leave_category_id' => $request->leave_category_id
            ];

            $this->leaveMaster->update($id,$data);
            DB::commit();
            ResponseService::successResponse('Data Updated Successfully');
        } catch (\Throwable $e) {
            ResponseService::logErrorResponse($e, "LeaveMaster Controller -> Store Method");
            ResponseService::errorResponse();
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
        ResponseService::noPermissionThenRedirect('school-setting-manage');        
        try {
            $leaveMaster =$this->leaveMaster->findById($id);
            if(count($leaveMaster->leave)) {
                ResponseService::errorResponse('cannot_delete_because_data_is_associated_with_other_data');
            } else {
                $this->leaveMaster->deleteById($id);
            }
            ResponseService::successResponse('Data Deleted Successfully');
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e, "LeaveMaster Controller -> Delete Method");
            ResponseService::errorResponse();
        }
    }

    public function leaveCategoryIndex(){
        ResponseService::noPermissionThenRedirect('school-setting-manage');        
        return view('leave.leave_category');
    }

    public function leaveCategoryStore(Request $request){
        ResponseService::noPermissionThenRedirect('school-setting-manage');        
        try {
            DB::beginTransaction();
            $schoolId = Auth::user()->school_id;
            $data = [
                'name' => $request->name,
                'description' => $request->description,
                'school_id' => $schoolId
            ];
            DB::table('leave_category')->insert($data);
            DB::commit();
            ResponseService::successResponse('Data Stored Successfully');
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "LeaveMaster Controller -> Delete Method");
            ResponseService::errorResponse();
        }
    }

    public function leaveCategoryShow(){
        ResponseService::noPermissionThenRedirect('school-setting-manage');        

        $offset = request('offset', 0);
        $limit = request('limit', 10);
        $sort = request('sort', 'name'); 
        $order = request('order', 'ASC');
        $search = request('search');
        $showDeleted = request('show_deleted');
        $query = DB::table('leave_category')
        ->when($search, function ($query) use ($search) {
            $query->where(function ($query) use ($search) {
                $query->where('name', 'LIKE', "%$search%")
                    ->orWhere('description', 'LIKE', "%$search%");
            });
        })
        ->where('school_id','=',Auth::user()->school_id)
        ->when(!empty($showDeleted), function ($query) {
            $query->whereNotNull('deleted_at');
        }, function ($query) {
            $query->whereNull('deleted_at');
        });

        $total = $query->count();

        $data = $query->orderBy($sort, $order)
                    ->skip($offset)
                    ->take($limit)
                    ->get();

        $bulkData = array();
        $bulkData['total'] = $total;
        $rows = array();
        $no = 1;

        foreach ($data as $row) {
            $operate = '';
            if (empty($showDeleted)) {
                $operate .= BootstrapTableService::editButton(route('leave-master.category-update', $row->id));
                $operate .= BootstrapTableService::deleteButton(route('leave-master.category-delete', $row->id));
            } else {
                $operate .= BootstrapTableService::restoreButton(route('leave-master.category-restore', $row->id));
            }

            $tempRow = (array) $row; 
            $tempRow['no'] = $no++;
            $tempRow['operate'] = $operate;
            $rows[] = $tempRow;
        }

        $bulkData['rows'] = $rows;
        return response()->json($bulkData);
    }

    public function leaveCategoryUpdate(Request $request,$id){
        ResponseService::noPermissionThenRedirect('school-setting-manage');        

        try {
            DB::beginTransaction();
            
            $data = [
                'name' => $request->name,
                'description' => $request->description
            ];
            
            // Update the record in the leave_category table
            DB::table('leave_category')->where('id', $id)->update($data);
            
            DB::commit();
            ResponseService::successResponse('Data Updated Successfully');
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "LeaveMaster Controller -> Delete Method");
            ResponseService::errorResponse();
        }
    }

    public function leaveCategoryDelete($id){
        ResponseService::noPermissionThenRedirect('school-setting-manage');        
        try {
            DB::beginTransaction();
            DB::table('leave_category')->where('id', $id)->update(['deleted_at' => now()]);            
            DB::commit();
            ResponseService::successResponse('Data Deleted Successfully');
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "LeaveMaster Controller -> Delete Method");
            ResponseService::errorResponse();
        }
    }

    public function leaveCategoryRestore($id){
        ResponseService::noPermissionThenRedirect('school-setting-manage');       
        try {
            DB::beginTransaction();
            DB::table('leave_category')->where('id', $id)->update(['deleted_at' => null]);
            
            DB::commit();
            ResponseService::successResponse('Data Restored Successfully');
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "Expense Category Controller -> Restore Method");
            ResponseService::errorResponse();
        }
    }

}
