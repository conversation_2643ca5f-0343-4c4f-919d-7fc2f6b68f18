<?php

namespace App\Http\Controllers;

use App\Repositories\Staff\StaffInterface;
use App\Repositories\Subscription\SubscriptionInterface;
use App\Repositories\User\UserInterface;
use App\Services\BootstrapTableService;
use App\Services\CachingService;
use App\Services\ResponseService;
use App\Services\SubscriptionService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Throwable;
use TypeError;
use App\Exports\TeacherDataExport;
use Maatwebsite\Excel\Facades\Excel;
use App\Imports\TeacherImport;
use Illuminate\Validation\ValidationException;
use App\Repositories\PayrollSetting\PayrollSettingInterface;
use App\Repositories\StaffSalary\StaffSalaryInterface;
use App\Services\UserService;
use App\Services\FileSizeLimitService;

class TeacherController extends Controller {
    private UserInterface $user;
    private StaffInterface $staff;
    private SubscriptionInterface $subscription;
    private CachingService $cache;
    private SubscriptionService $subscriptionService;
    private PayrollSettingInterface $payrollSetting;
    private StaffSalaryInterface $staffSalary;

    public function __construct(StaffInterface $staff, UserInterface $user, SubscriptionInterface $subscription, CachingService $cache, SubscriptionService $subscriptionService, PayrollSettingInterface $payrollSetting, StaffSalaryInterface $staffSalary) {
        $this->user = $user;
        $this->staff = $staff;
        $this->subscription = $subscription;
        $this->cache = $cache;
        $this->subscriptionService = $subscriptionService;
        $this->payrollSetting = $payrollSetting;
        $this->staffSalary = $staffSalary;
    }

    public function index() {
        ResponseService::noPermissionThenRedirect('teacher-list');

        $allowances = $this->payrollSetting->builder()->where('type', 'allowance')->get();
        $deductions = $this->payrollSetting->builder()->where('type', 'deduction')->get();

        return view('teacher.index',compact('allowances', 'deductions'));
    }

    public function store(Request $request) {
        ResponseService::noAnyPermissionThenSendJson(['teacher-create', 'teacher-edit']);
        
        $rfidCheck = DB::select('SELECT COUNT(*) as count FROM users WHERE rfid_id = ?', [$request->rfid_id]);
        if ($rfidCheck[0]->count > 0) {
            return ResponseService::errorResponse('Invalid, RFID ID has been taken.');
        }
        $request->validate([
            'first_name'        => 'required',
            'last_name'         => 'required',
            'ic_no'             => 'required',
            'admission_date'    => 'nullable',
            'gender'            => 'required',
            'email'             => 'required|email|unique:users,email',
            'mobile'            => 'required|numeric|digits_between:1,16',
            'dob'               => 'required|date|unique:users,email',
            'qualification'     => 'required',
            'current_address'   => 'required',
            'epf_no'            => 'nullable',
            'socso_no'          => 'nullable',
            'tax_no'            => 'nullable',
            'bank_name'         => 'nullable',
            'bank_no'           => 'nullable',
            'rfid_id'           => 'nullable',
            'status'            => 'nullable|in:0,1',
        ]);
        try {
            DB::beginTransaction();

            // Check free trial package
            $today_date = Carbon::now()->format('Y-m-d');
            $subscription = $this->subscription->builder()->doesntHave('subscription_bill')->whereDate('start_date','<=',$today_date)->where('end_date','>=',$today_date)->whereHas('package',function($q){
                $q->where('is_trial',1);
            })->first();
            
            if ($subscription) {
                $systemSettings = $this->cache->getSystemSettings();
                $staff = $this->user->builder()->role('Teacher')->withTrashed()->orWhereHas('roles', function ($q) {
                    $q->where('custom_role', 1)->whereNotIn('name', ['Teacher','Guardian']);
                })->whereNotNull('school_id')->Owner()->count();
                if ($staff >= $systemSettings['staff_limit']) {
                    $message = "The free trial allows only ".$systemSettings['staff_limit']." staff.";
                    ResponseService::errorResponse($message);
                }
            }

            // If prepaid plan check student limit
            $subscription = $this->subscriptionService->active_subscription(Auth::user()->school_id);
            if ($subscription && $subscription->package_type == 0) {
                $status = $this->subscriptionService->check_user_limit($subscription, "Staffs");
                
                if (!$status) {
                    ResponseService::errorResponse('You reach out limits');
                }
            }


            $teacher_plain_text_password = str_replace('-', '', date('d-m-Y', strtotime($request->dob)));
            $schoolId = Auth::getUser()->school_id;
            if($image = $request->file('image')){
                if (FileSizeLimitService::checkFileSizeLimit($schoolId)) {
                    return ResponseService::errorResponse('storage capacity not enough');}
                }
            $user_data = array(
                ...$request->all(),
                'password'          => Hash::make($teacher_plain_text_password),
                'image'             => $request->file('image'),
                'file_size'         => $request->file('image')? round($request->file('image')->getSize() / 1024, 2) : null,
                'status'            => $request->status ?? 0,
                'dob'               => $request->dob ?? null,
                'deleted_at'        => $request->status == 1 ? null : '1970-01-01 01:00:00'
            );

            //Call store function of User Repository and get the User Data
            $user = $this->user->create($user_data);

            $user->assignRole('Teacher');

            $staff = $this->staff->create([
                'user_id'          => $user->id,
                'qualification'    => $request->qualification,
                'salary'           => $request->salary ?? 0,
                'position'         => $request->position,
                'ic_no'            => $request->ic_no,
                'epf_no'           => $request->epf_no,
                'socso_no'         => $request->socso_no,
                'tax_no'           => $request->tax_no,
                'bank_name'        => $request->bank_name,
                'bank_no'          => $request->bank_no,
                'rfid_id'          => $request->rfid_id,
                'admission_date'   => !empty($request->admission_date) ? date('Y-m-d', strtotime($request->admission_date)) : null,
            ]);

            DB::commit();

            if ($user->school_id) {
                $sendEmail = app(UserService::class);
                $sendEmail->sendStaffRegistrationEmail($user, $teacher_plain_text_password);
            }

            // $school_name = Auth::user()->school->name;
            // $data = [
            //     'subject'     => 'Welcome to ' . $school_name,
            //     'name'        => $request->first_name,
            //     'email'       => $request->email,
            //     'password'    => $teacher_plain_text_password,
            //     'school_name' => $school_name
            // ];

            // Mail::send('teacher.email', $data, static function ($message) use ($data) {
            //     $message->to($data['email'])->subject($data['subject']);
            // });
            ResponseService::successResponse('Data Stored Successfully');
        } catch (Throwable $e) {
            if (Str::contains($e->getMessage(), ['Failed', 'Mail', 'Mailer', 'MailManager'])) {
                ResponseService::warningResponse("Teacher Registered successfully. But Email not sent.");
            } else {
                DB::rollback();
                ResponseService::logErrorResponse($e, "Teacher Controller -> Store method");
                ResponseService::errorResponse();
            }
        }
    }

    public function show() {
        ResponseService::noPermissionThenRedirect('teacher-list');
        $offset = request('offset', 0);
        $limit = request('limit', 10);
        $sort = request('sort', 'id');
        $order = request('order', 'DESC');
        $search = request('search');
        $showDeleted = request('show_deactive');
        $sql = $this->user->builder()->role('Teacher')->with('staff','staff.staffSalary')
            ->where(function ($query) use ($search) {
                $query->when($search, function ($query) use ($search) {
                $query->where('id', 'LIKE', "%$search%")
                    ->orwhere('first_name', 'LIKE', "%$search%")
                    ->orwhere('last_name', 'LIKE', "%$search%")
                    ->orwhere('gender', 'LIKE', "%$search%")
                    ->orwhere('email', 'LIKE', "%$search%")
                    ->orwhere('current_address', 'LIKE', "%$search%")
                    ->orwhere('permanent_address', 'LIKE', "%$search%")
                    ->orwhere('rfid_id','LIKE',"%$search%")
                    ->whereHas('staff', function ($q) use ($search) {
                        $q->orwhere('staffs.qualification', 'LIKE', "%$search%")
                        ->orWhere('position','LIKE',"%$search%")
                        ->orWhere('ic_no', 'LIKE', "%$search%")
                        ->orWhere('epf_no', 'LIKE', "%$search%")
                        ->orWhere('socso_no', 'LIKE', "%$search%")
                        ->orWhere('tax_no', 'LIKE', "%$search%")
                        ->orWhere('bank_name', 'LIKE', "%$search%")
                        ->orWhere('bank_no', 'LIKE', "%$search%");
                    });
                });
            })
            ->when(!empty($showDeleted), function ($query) {
                $query->where('status',0)->onlyTrashed();
            });
        $total = $sql->count();
        $sql->orderBy($sort, $order)->skip($offset)->take($limit);
        $res = $sql->get();
        $bulkData = array();
        $bulkData['total'] = $total;
        $rows = array();
        $no = 1;
        foreach ($res as $row) {
            if (isset($row->staff) && !empty($row->staff->admission_date)) {
                $row->staff->admission_date = date('d-m-Y', strtotime($row->staff->admission_date));
            }
            $operate = '';
           if ($showDeleted) {
                //Show Restore and Hard Delete Buttons
                // $operate = BootstrapTableService::button('fa fa-calendar', route('timetable.teacher.show', $row->id), ['btn-gradient-success'], ['title' => "View Timetable"]);
                if(Auth::user()->can('teacher-edit')){
                    $operate = BootstrapTableService::button('fa fa-check', route('teachers.change-status', $row->id), ['activate-teacher', 'btn-gradient-success'], ['title' => __('active')]);
                }
                if(Auth::user()->can('teacher-delete')){
                    $operate .= BootstrapTableService::trashButton(route('teachers.trash', $row->id));
                }
            } else if ($showDeleted) {
                //Show Restore and Hard Delete Buttons
                // $operate = BootstrapTableService::button('fa fa-calendar', route('timetable.teacher.show', $row->id), ['btn-gradient-success'], ['title' => "View Timetable"]);
                if(Auth::user()->can('teacher-edit')){
                    $operate = BootstrapTableService::button('fa fa-check', route('teachers.change-status', $row->id), ['activate-teacher', 'btn-gradient-success'], ['title' => __('active')]);
                }
                if(Auth::user()->can('teacher-delete')){
                    $operate .= BootstrapTableService::trashButton(route('teachers.trash', $row->id));
                }
            } else {
                if(Auth::user()->can('teacher-edit')){
                    $operate = BootstrapTableService::editButton(route('teachers.update', $row->id, ['data-id' => $row->id]));
                }
                $operate .= BootstrapTableService::button('fa fa-calendar', route('timetable.teacher.show', $row->id), ['btn-gradient-success'], ['title' => "View Timetable"]);
                if(Auth::user()->can('teacher-edit')){
                    $operate .= BootstrapTableService::button('fa fa-exclamation-triangle', route('teachers.change-status', $row->id), ['deactivate-teacher', 'btn-gradient-info'], ['title' => __('inactive')]);
                }
                $operate .= BootstrapTableService::button('fa fa-envelope', route('teachers.email', $row->id), ['btn-gradient-warning'], ['title' => "Resend Welcome Email"]);
                if(Auth::user()->can('teacher-delete')){
                    $operate .= BootstrapTableService::trashButton(route('teachers.trash', $row->id));
                }
            }

            $tempRow = $row->toArray();
            if(!Auth::user()->hasRole('School Admin') && !(Auth::user()->can('teacher-create') && Auth::user()->can('teacher-delete') && Auth::user()->can('teacher-edit') && Auth::user()->can('teacher-list'))) {
                $tempRow['mobile'] = '**********';
            }
            $tempRow['no'] = $no++;
            // $tempRow['dob'] = format_date($row->dob);
            $tempRow['operate'] = BootstrapTableService::menuItem($operate);
            $rows[] = $tempRow;
        }

        $bulkData['rows'] = $rows;
        return response()->json($bulkData);
    }


    public function edit($id) {
        $teacher = $this->staff->findById($id);
        return response($teacher);
    }

    public function update(Request $request, $id) {
        // ResponseService::noFeatureThenSendJson('Teacher Management');
        $rfidCheck = DB::select('SELECT COUNT(*) as count FROM users WHERE rfid_id = ? AND id != ? ', [$request->rfid_id,$id]);
        if ($rfidCheck[0]->count > 0) {
            return ResponseService::errorResponse('Invalid, RFID ID has been taken.');
        }
        ResponseService::noPermissionThenSendJson('teacher-edit');
        $validator = Validator::make($request->all(), [
            'first_name'        => 'required',
            'last_name'         => 'required',
            'ic_no'             => 'required',
            'rfid_id'           => 'nullable',
            'admission_date'    => 'nullable',
            'gender'            => 'required',
            'email'             => 'required|email|unique:users,email,' . $id,
            'mobile'            => 'required|numeric|digits_between:1,16',
            'dob'               => 'required|date',
            'qualification'     => 'required',
            'current_address'   => 'required',
            'permanent_address' => 'nullable',
            'epf_no'            => 'nullable',
            'socso_no'          => 'nullable',
            'tax_no'            => 'nullable',
            'bank_name'         => 'nullable',
            'bank_no'           => 'nullable',
            'status'            => 'nullable|in:0,1',
        ]);
        if ($validator->fails()) {
            ResponseService::errorResponse($validator->errors()->first());
        }
        try {
            DB::beginTransaction();
            $schoolId = Auth::getUser()->school_id;
            $user_data = array(
                ...$request->all(),
            );
            if ($request->file('image')) {
                if (FileSizeLimitService::checkFileSizeLimit($schoolId)) {
                    return ResponseService::errorResponse('storage capacity not enough');}
                $user_data['image'] = $request->file('image');
                $fileSizeInBytes = $request->file('image')->getSize();
                $fileSizeInKB = $fileSizeInBytes / 1024;
                $fileSizeInKBB = round($fileSizeInKB, 2);
            }
            if(isset($fileSizeInKBB)){
                $user_data['file_size'] = $fileSizeInKBB;
            }

            if ($request->reset_password) {
                $password = str_replace('-', '', date('d-m-Y', strtotime($request->dob)));
                $user_data['password'] = Hash::make($password);
            }

            //Call store function of User Repository and get the User Data
            $user = $this->user->update($id, $user_data);

            //Call store function of User Repository and get the User Data
            $this->staff->updateOrCreate(
                ['user_id' => $user->id], // Attributes to search for
                [
                    'qualification' => $request->qualification,
                    //'salary' => $request->salary,
                    'position' => $request->position,
                    'ic_no' => $request->ic_no,
                    'epf_no' => $request->epf_no,
                    'socso_no' => $request->socso_no,
                    'tax_no' => $request->tax_no,
                    'bank_name' => $request->bank_name,
                    'bank_no' => $request->bank_no,
                    'admission_date' => !empty($request->admission_date) ? date('Y-m-d', strtotime($request->admission_date)) : null,
                ]
            );
            if($request->salary ?? null != null){
                $this->staff->updateOrCreate(
                ['user_id' => $user->id], // Attributes to search for
                [
                    'salary' => $request->salary,
                ]
            );
            }

            DB::commit();
            ResponseService::successResponse('Data Updated Successfully');
        } catch (Throwable $e) {
            if ($e instanceof TypeError && Str::contains($e->getMessage(), ['Mail', 'Mailer', 'MailManager'])) {
                ResponseService::warningResponse("Teacher Registered successfully. But Email not sent.");
            } else {
                DB::rollBack();
                ResponseService::logErrorResponse($e, "Teacher Controller -> Update method");
                ResponseService::errorResponse();
            }
        }
    }

    public function trash($id) {
        ResponseService::noPermissionThenSendJson('teacher-delete');
        try {
            DB::beginTransaction();
    
            // Find the teacher record to delete
            $teacher = $this->user->findTrashedById($id);
            
             // Get the staff record
            $staff = $teacher->staff;
            if ($staff) {
                // Delete related expenses first
                DB::table('expenses')->where('staff_id', $staff->id)->delete();
                
                // Now delete the staff record
                $staff->delete();
            }

            // Delete any other related records
            $this->deleteRelatedRecords($teacher);

            // Finally delete the teacher user record
            $teacher->forceDelete();
            DB::commit();
            ResponseService::successResponse("Data Deleted Permanently");
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "Teacher Controller ->trash Method", 'cannot_delete_because_data_is_associated_with_other_data');
            ResponseService::errorResponse();
        }
    }

    private function deleteRelatedRecords($teacher) {
        // Example: Delete related staff records (assuming one-to-one relationship)
        if ($teacher->staff) {
            $teacher->staff()->delete();
        }
    }

    public function changeStatus($id) {
        // ResponseService::noFeatureThenSendJson('Teacher Management');
        ResponseService::noPermissionThenRedirect('teacher-delete');
        try {
            DB::beginTransaction();
            $teacher = $this->user->findTrashedById($id);

            if ($teacher->status == 0) {
                // If prepaid plan check student limit
                $subscription = $this->subscriptionService->active_subscription(Auth::user()->school_id);
                if ($subscription && $subscription->package_type == 0) {
                    $status = $this->subscriptionService->check_user_limit($subscription, "Staffs");
                    
                    if (!$status) {
                        ResponseService::errorResponse('You reach out limits');
                    }
                }
            }

            $this->user->builder()->where('id',$id)->withTrashed()->update(['status' => $teacher->status == 0 ? 1 : 0,'deleted_at' => $teacher->status == 1 ? now() : null]);
            DB::commit();
            ResponseService::successResponse('Data Updated Successfully');
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e, 'Status methods -> Teacher controller');
            ResponseService::errorResponse();
        }
    }

    public function changeStatusBulk(Request $request){
        // ResponseService::noFeatureThenSendJson('Teacher Management');
        ResponseService::noPermissionThenRedirect('teacher-delete');
        try {
            DB::beginTransaction();
            $userIds = json_decode($request->ids);
            foreach ($userIds as $userId) {
                $teacher = $this->user->findTrashedById($userId);
                if ($teacher->status == 0) {
                    // If prepaid plan check student limit
                    $subscription = $this->subscriptionService->active_subscription(Auth::user()->school_id);
                    if ($subscription && $subscription->package_type == 0) {
                        $status = $this->subscriptionService->check_user_limit($subscription, "Staffs");
                        
                        if (!$status) {
                            ResponseService::errorResponse('You reach out limits');
                        }
                    }
                }
                $this->user->builder()->where('id',$userId)->withTrashed()->update(['status' => $teacher->status == 0 ? 1 : 0,'deleted_at' => $teacher->status == 1 ? now() : null]);
            }
            DB::commit();
            ResponseService::successResponse("Status Updated Successfully");
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function bulkUploadIndex()
    {
        ResponseService::noAnyPermissionThenSendJson(['teacher-create', 'teacher-edit']);
        return view('teacher.bulk_upload');
       
    }
    public function storeBulkUpload(Request $request)
    {
        ResponseService::noAnyPermissionThenSendJson(['teacher-create', 'teacher-edit']);
        $validator = Validator::make($request->all(), [
            'file'             => 'required|mimes:csv,txt'
        ]);
        if ($validator->fails()) {
            ResponseService::errorResponse($validator->errors()->first());
        }
        try {
            Excel::import(new TeacherImport, $request->file('file'));
            ResponseService::successResponse('Data Stored Successfully');
        } catch (ValidationException $e) {
            ResponseService::errorResponse($e->getMessage());
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e, "Teacher Controller -> Store Bulk method");
            ResponseService::errorResponse($e->getMessage());
        }                                                                                                                               
    }

    public function downloadSampleFile() {
        try {
            return Excel::download(new TeacherDataExport(), 'teachers.xlsx');
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e, 'Teacher Controller ---> Download Sample File');
            ResponseService::errorResponse();
        }
    }

    
    public function sendWelcomeEmail(Request $request, $id) {
        $cache = app(CachingService::class);
        $schoolSettings = $cache->getSchoolSettings();
        
        $teacher = DB::select('SELECT email, dob, last_name, first_name FROM users WHERE id = ?', [$id]);
        if(COUNT($teacher)){
            $teacher = $teacher[0];
        }
        $teacher_plain_text_password = str_replace('-', '', date('d-m-Y', strtotime($teacher->dob)));

        $email_body = $this->replaceStaffPlaceholders($teacher, $teacher_plain_text_password, $schoolSettings);
        $data = [
            'subject'     => 'Welcome to ' . $schoolSettings['school_name'],
            'email'       => $teacher->email,
            'email_body'  => $email_body
        ];

        Mail::send('teacher.email', $data, static function ($message) use ($data) {
            $message->to($data['email'])->subject($data['subject']);
        });
        //ResponseService::successResponse('Email Sent Successfully');
        return redirect()->back()->with('success', 'Email Sent Successfully');   
    }
    
    private function replaceStaffPlaceholders($user, $password, $schoolSettings)
    {

        $cache = app(CachingService::class);
        $systemSettings = $cache->getSystemSettings();

        $templateContent = $schoolSettings['email-template-staff'] ?? '';
        // Define the placeholders and their replacements
        $placeholders = [
            '{full_name}' => $user->first_name.' '.$user->last_name,
            '{email}' => $user->email,
            '{password}' => $password,
            '{school_name}' => $schoolSettings['school_name'],
            
            '{support_email}' => $schoolSettings['school_email'] ?? '',
            '{support_contact}' => $schoolSettings['school_phone'] ?? '',

            '{url}' => url('/'),

            '{android_app}' => $systemSettings['app_link'] ?? '',
            '{ios_app}' => $systemSettings['ios_app_link'] ?? '',

            // Add more placeholders as needed
        ];

        // Replace the placeholders in the template content
        foreach ($placeholders as $placeholder => $replacement) {
            $templateContent = str_replace($placeholder, $replacement, $templateContent);
        }

        return $templateContent;
    }
}
