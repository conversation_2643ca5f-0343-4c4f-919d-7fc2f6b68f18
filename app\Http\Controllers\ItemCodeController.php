<?php

namespace App\Http\Controllers;

use App\Exports\StudentFeeTypesExport;
use App\Imports\StudentFeeTypesImport;
use App\Models\StudentFeeType;
use App\Services\ResponseService;
use App\Models\ItemCode;

use App\Repositories\StudentFeeType\StudentFeeTypeInterface;
use App\Repositories\ClassSchool\ClassSchoolInterface;
use App\Repositories\FeesType\FeesTypeInterface;
use App\Repositories\Student\StudentInterface;
use App\Services\CachingService;
use App\Services\BootstrapTableService;
use Carbon\Carbon;


use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use Throwable;
use Exception;

class ItemCodeController extends Controller
{

    private StudentFeeTypeInterface $studentFeeTypes;
    private StudentInterface $student;
    private FeesTypeInterface $feesType;
    private ClassSchoolInterface $class;
    private CachingService $cache;
    private ItemCode $itemCode;

    public function __construct(
        StudentFeeTypeInterface $studentFeeTypes,
        CachingService $cache,
        ClassSchoolInterface $classSchool,
        FeesTypeInterface $feesType,
        StudentInterface $student,
        ItemCode $itemCode

    ) {

        $this->studentFeeTypes = $studentFeeTypes;
        $this->cache = $cache;
        $this->class = $classSchool;
        $this->feesType = $feesType;
        $this->student = $student;
        $this->itemCode = $itemCode;
    }

    public function itemCodeIndex()
    {
        $classes = $this->class->all(['*'], ['stream', 'medium', 'stream']);
        $simpleClassDropdown = $this->class->builder()->pluck('name', 'id');
        $feesTypeData = $this->feesType->all();

        return view('item-code.index', compact('classes', 'simpleClassDropdown', 'feesTypeData'));
    }

    public function itemCodeStore(Request $request)
    {
        $request->validate([
            'student_fee_types.*.fees_type'    => 'required',
            'classification_code'                   => 'nullable',
            'unit'                                  => 'nullable',
            // 'item_code'                             => 'required'
        ]);

        try {
            DB::beginTransaction();

            $feeTypes = [];
            $type=DB::table('fees_type_master')->where('school_id',Auth::user()->school_id)->get();
            // foreach ($request->student_id as $student_id) {
            foreach ($request->student_fee_types as $data) {

                foreach($type as $validate){
                    if($validate->item_code==$data['item_code']){
                        return ResponseService::errorResponse('The Item Code Already Exist');
                    }
                }

                $feeTypes[] = array(
                    'item_code'             => $data['item_code'],
                    'classification_code'   => $data['classification_code'],
                    'unit'                  => $data['unit'],
                    "name"                  => $data['fees_type'],
                    "discount"              => $data['discount_percentage'] ?? 0,
                    "tax"                   => $data['tax_percentage'] ?? 0,
                    "price"                 => $data['unit_price'] ?? 0,
                    "school_id"             => Auth::user()->school_id,
                    "created_at"            => Carbon::now(),
                    "updated_at"            => Carbon::now(),
                );
            }

            if (count($feeTypes) > 0) {
                $this->itemCode->insert($feeTypes, ['name', 'classification_code', 'unit', 'price', 'school_id','created_at', 'updated_at']);
            }

            DB::commit();
            ResponseService::successResponse('Data Stored Successfully');
        } catch (Throwable $e) {
            DB::rollback();
            ResponseService::logErrorResponse($e, "StudentFeeTypesController -> Store Method");
            ResponseService::errorResponse();
        }
    }

    public function itemCodeShow()
    {

        $offset = request('offset', 0);
        $limit = request('limit', 10);
        $sort = request('sort', 'id');
        $order = request('order', 'DESC');
        $search = request('search');
        $startDate = request('start_date');
        $endDate = request('end_date');

        $query = DB::table('fees_type_master as ftm')
                ->where('ftm.school_id',Auth::user()->school_id)
                ->where(function ($q) use ($search) {
                    $q->when($search, function ($query) use ($search) {
                        $query->where('ftm.item_code','LIKE', "%$search%")
                            ->orwhere('ftm.name','LIKE', "%$search%")
                            ->orwhere('ftm.classification_code','LIKE', "%$search%")
                            ->orwhere('ftm.unit','LIKE', "%$search%")
                            ->orwhere('ftm.price','LIKE', "%$search%")
                            ->orwhere('ftm.discount','LIKE', "%$search%")
                            ->orwhere('ftm.tax','LIKE', "%$search%");
                    });
        });

        $total = $query->count();

        $query->orderBy('ftm.created_at');

        $records = $query->offset($offset)
            ->limit($limit)
            ->get();


        $bulkData = [];
        $bulkData['total'] = $total;
        $rows = [];
        $no = $offset + 1;


        foreach ($records as $row) {
            $operate = '';
            $operate .= BootstrapTableService::editButton(route('student-fee-types.item-code-edit', $row->id), false);
            $operate .= BootstrapTableService::deleteButton(route('student-fee-types.item-code-destroy', $row->id), false);
            $tempRow = (array) $row; // Convert object to array
            $tempRow['operate'] = $operate;
            $tempRow['operate'] = $operate;
            $tempRow['no'] = $no++; // Add row number
            $rows[] = $tempRow; // Add the row to the array
        }

        $bulkData['rows'] = $rows;

        return response()->json($bulkData);
    }


    public function itemCodeEdit($id)
    {

        $itemsData = DB::table('fees_type_master')->find($id);

        return view('item-code.edit', compact('itemsData'));
    }

    public function itemCodeUpdate(Request $request)
    {
        $request->validate([
            'name'                      => 'string|required',
            'classification_code'       => 'nullable',
            'unit'                      => 'nullable',
            'item_code'                 => 'string|required',
            'price'                     => 'numeric|required',
            'discount_percentage'                  => 'numeric',
            'tax_percentage'                  => 'numeric'
        ]);

        try {
            DB::beginTransaction();

            DB::table('fees_type_master')
                ->where('id', $request->id)
                ->update([
                    'item_code'             => $request->item_code,
                    'classification_code'   => $request->classification_code,
                    "name"                  => $request->name,
                    'unit'                  => $request->unit,
                    "price"                 => $request->price ?? 0,
                    "discount"              => $request->discount_percentage ?? 0,
                    "tax"                   => $request->tax_percentage ?? 0,
                    "updated_at"            => Carbon::now(),
                ]);
            
            $studentFeeTypes = $this->studentFeeTypes->builder()->where('item_code',$request->item_code)->get();
            if(count($studentFeeTypes) > 0){
                foreach($studentFeeTypes as $fee){
                    $totalPrice = $fee->quantity * $request->price;
                    $data = [
                        'classification_code'   => $request->classification_code,
                        "fees_type_name"        => $request->name,
                        'unit'                  => $request->unit,
                        "unit_price"            => $request->price,
                        "discount"              => $request->discount_percentage ?? 0,
                        "tax"                   => $request->tax_percentage ?? 0,
                        "total_amount"          => $totalPrice ?? 0
                    ];
                    $fee->update($data);
                }
            }

            DB::commit();
            ResponseService::successRedirectResponse(route('student-fee-types.item-code'), 'Data Update Successfully');
        } catch (Throwable $e) {
            DB::rollback();
            ResponseService::logErrorResponse($e);
            ResponseService::errorRedirectResponse();
        }
    }

    public function itemCodeDestroy($id)
    {

        try {
            DB::beginTransaction();

            $itemsData = DB::table('fees_type_master')->where('id', $id)->first();

            if (!$itemsData) {
                return ResponseService::errorResponse('Record not found');
            }

            DB::table('fees_type_master')->where('id', $id)->delete();

            DB::commit();
            ResponseService::successResponse("Data Deleted Successfully");
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "StudentFeeTypesController -> Destroy Method");
            ResponseService::errorResponse();
        }
    }
}
