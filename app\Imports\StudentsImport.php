<?php

namespace App\Imports;

use App\Repositories\FormField\FormFieldsInterface;
use App\Repositories\SessionYear\SessionYearInterface;
use App\Repositories\Student\StudentInterface;
use App\Repositories\Subscription\SubscriptionInterface;
use App\Repositories\User\UserInterface;
use App\Services\CachingService;
use App\Services\ResponseService;
use App\Services\UserService;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use JsonException;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;
use Str;
use Throwable;
use TypeError;

class StudentsImport implements WithMultipleSheets
{
    private mixed $classSectionID;
    private mixed $sessionYearID;

    public function __construct($classSectionID, $sessionYearID)
    {
        $this->classSectionID = $classSectionID;
        $this->sessionYearID = $sessionYearID;
    }

    /**
     * @throws Throwable
     */
    public function sheets(): array
    {
        return [
            new FirstSheetImport($this->classSectionID, $this->sessionYearID)
        ];
    }
}

class FirstSheetImport implements ToCollection, WithHeadingRow
{
    private mixed $classSectionID;
    private mixed $sessionYearID;

    /**
     * @param $classSectionID
     * @param $sessionYearID
     */

    // Import the Class Section and Repositories
    public function __construct($classSectionID, $sessionYearID)
    {
        $this->classSectionID = $classSectionID;
        $this->sessionYearID = $sessionYearID;
    }

    private function standardizeFieldName($fieldName) {
        return preg_replace('/[\s\(\)]+/', '_', trim($fieldName));
    }

    /**
     * @throws JsonException
     * @throws Throwable
     */
    public function collection(Collection $collection)
    {
        $student = app(StudentInterface::class);
        $formFields = app(FormFieldsInterface::class);
        $sessionYear = app(SessionYearInterface::class);

        $subscription = app(SubscriptionInterface::class);
        $user = app(UserInterface::class);
        $cache = app(CachingService::class);

        $validator = Validator::make($collection->toArray(), [
            '*.first_name'     => 'required',
            '*.last_name'      => 'required',
            '*.mobile'         => 'nullable|regex:/^([0-9\s\-\+\(\)]*)$/',
            '*.gender'         => 'required|in:male,female,Male,Female',
            '*.dob'            => 'required|date',
            '*.admission_date' => 'required|date',
            '*.current_address'      => 'required',
            '*.permanent_address'      => 'required',
            '*.guardian_email'      => 'required|email',
            '*.guardian_first_name' => 'required',
            '*.guardian_last_name'  => 'required',
            '*.guardian_mobile'     => 'required|regex:/^([0-9\s\-\+\(\)]*)$/',
            '*.ic_no_2'          => 'nullable',        
        ],[
            '*.dob.date' => 'Please ensure that the dob date format you use is either DD-MM-YYYY or MM/DD/YYYY.',
            '*.admission_date.date' => 'Please ensure that the admission date format you use is either DD-MM-YYYY or MM/DD/YYYY.',
        ]);

        //             If Validation fails then this will throw the ValidationFail Exception
        $validator->validate();

        // Check free trial package
        $today_date = Carbon::now()->format('Y-m-d');
        $get_subscription = $subscription->builder()->doesntHave('subscription_bill')->whereDate('start_date','<=',$today_date)->where('end_date','>=',$today_date)->whereHas('package',function($q){
            $q->where('is_trial',1);
        })->first();

        $userService = app(UserService::class);
        $sessionYear = $sessionYear->findById($this->sessionYearID);
        DB::beginTransaction();
        $studentList = DB::select('SELECT COUNT(id) AS total FROM students WHERE school_id = '.Auth::user()->school_id);
        $totalStudent = $studentList[0]->total + 1;
        foreach ($collection as $row) {

            // Check free trial package
            
            if ($get_subscription) {
                $systemSettings = $cache->getSystemSettings();
                $count_student = $user->builder()->role('Student')->withTrashed()->count();
                if ($count_student >= $systemSettings['student_limit']) {
                    $message = "The free trial allows only ".$systemSettings['student_limit']." students.";
                    ResponseService::errorResponse($message);
                    break;
                }
            }
            $row = $row->toArray();
            // Find the index of the key after which to split the array
            $splitIndex = array_search('guardian_mobile', array_keys($row)) + 1;

            // Get The Extra Details of it
            $extraDetailsFields = array_slice($row, $splitIndex);
            // Get the Session year ID
            // $sessionYear = $sessionYear->findById($this->sessionYearID);

            //Check if parent user exist as teacher and block it
            $isStaff = DB::select('SELECT * FROM staffs WHERE user_id = (SELECT id FROM users WHERE email = ?)', [$row['guardian_email']]);
            if (COUNT($isStaff)) {
                ResponseService::errorResponse('Unable to import guardian who holds a staff role in the system. First name: '.$row['guardian_first_name'].' Last name: '. $row['guardian_last_name'].' Email: '. $row['guardian_email']);
                break;
            }

            $guardian = $userService->createOrUpdateParent($row['guardian_first_name'], $row['guardian_last_name'], $row['guardian_email'], $row['guardian_mobile'], $row['guardian_gender']);
            
            //$get_student = $student->builder()->where('session_year_id', $sessionYear->id)->select('id')->latest('id')->pluck('id')->first();
            //$admission_no = $sessionYear->name . Auth::user()->school_id . ($get_student + 1);
            $admission_no = $sessionYear->name.Auth::user()->school_id.str_pad(($totalStudent), 4, '0', STR_PAD_LEFT);
            while(true){
                $studentList = DB::select('SELECT id FROM users WHERE email = ?',[$admission_no]);
                if(COUNT($studentList)){
                    $totalStudent = $totalStudent + 1;
                    $admission_no = $sessionYear->name.Auth::user()->school_id.str_pad(($totalStudent + 1), 4, '0', STR_PAD_LEFT);
                }
                else{
                    break;
                }
            }

            $extraDetails = array();
            // Check that Extra Details Exists
            if (!empty($extraDetailsFields)) {
                // Debugging: Print the contents of $extraDetailsFields
                $extraFieldName = array_map(static function ($d) {
                    return str_replace("_", " ", $d);
                }, array_keys($extraDetailsFields));
                //$formFieldsCollection = $formFields->builder()->whereIn('name', $extraFieldName)->get();

                $formFieldsCollection = $formFields->builder()->get()->filter(function($field) use ($extraFieldName) {
                    // Normalize the field name the same way as $extraFieldName
                    $normalizedFieldName = strtolower($field->name);
                    $normalizedFieldName = str_replace('.', '', $normalizedFieldName); // Replace spaces with underscore
                    $normalizedFieldName = str_replace(['(', ')'], '', $normalizedFieldName); // Remove parentheses
                    
                    // Check if the normalized name exists in $extraFieldName array
                    return in_array($normalizedFieldName, $extraFieldName);
                });

                $extraFieldValidationRules = [];
                foreach ($formFieldsCollection as $field) {
                    if ($field->is_required) {
                        $name = strtolower(str_replace(' ', '_', $field->name));
                        $extraFieldValidationRules[$name] = 'required';
                    }
                }
                $extraFieldValidator = Validator::make($row, $extraFieldValidationRules);
                $extraFieldValidator->validate();

                // Create Extra Details Array for Student's Extra Form Details
                foreach ($extraDetailsFields as $key => $value) {

                    $formField = $formFieldsCollection->first(function ($data) use ($key) {
                        return strtolower(str_replace(['(', ')'], '', str_replace('.', '', $data->name))) === str_replace("_", " ", $key);
                    });



                    if (!empty($formField)) {
                        // if Form Field is checkbox then make data in json format
                        $data = $formField->type == 'checkbox' ? explode(',', $value) : $value;
                        $extraDetails[] = array(
                            'input_type'    => $formField->type,
                            'form_field_id' => $formField->id,
                            'data'          => (is_array($data)) ? json_encode($data, JSON_THROW_ON_ERROR) : $data
                        );
                    }
                }
                //                     Make File Input Array to Store the Null Values
                $getFileExtraField = $formFields->builder()->where('type', 'file')->get();
                foreach ($getFileExtraField as $value) {
                    $extraDetails[] = array(
                        'input_type'    => 'file',
                        'form_field_id' => $value->id,
                        'data'          => NULL,
                    );
                }
            }
            //                $userService->createOrUpdateStudentUser($row['first_name'], $row['last_name'], $admission_no, $row['mobile'], $row['dob'], $row['gender'], null, $this->class_section_id, now(), $extraDetails, null, $guardian->id);

            $studentUser = DB::select('SELECT id FROM users WHERE first_name = "'.$row['first_name'].'" AND last_name = "'.$row['last_name'].'" AND dob = "'.date('Y-m-d', strtotime($row['dob'])).'" AND gender = "'.$row['gender'].'"');
            if(COUNT($studentUser) == 0){
                try {
                    $userService->createStudentUser($row['first_name'], $row['last_name'], $admission_no, $row['mobile'], $row['dob'], $row['gender'], null, $this->classSectionID, $row['admission_date'],$row['current_address'],$row['permanent_address'], $sessionYear->id, $guardian->id, $extraDetails, 0, $row['ic_no_2'] ?? '');
                } catch (Throwable $e) {
                    // IF Exception is TypeError and message contains Mail keywords then email is not sent successfully
                    if ($e instanceof TypeError && Str::contains($e->getMessage(), [
                            'Mail',
                            'Mailer',
                            'MailManager'
                        ])) {
                        continue;
                    }
                    DB::rollBack();
                    throw $e;
                }
            }
            else{
                $studentId = $studentUser[0]->id;
                $userService->updateStudentUser($studentId, $row['first_name'], $row['last_name'], $row['mobile'], $row['dob'], $row['gender'], $row['image'] ?? null, $sessionYear->id, $extraDetails, $guardian->id, $row['current_address'], $row['permanent_address'], null, $this->classSectionID, $row['rfid_id'] ?? null, $row['admission_date'], $row['ic_no_2']);
            }
            
            $totalStudent = $totalStudent + 1;
        }
        DB::commit();
        return true;
    }
}
