<?php

namespace App\Http\Controllers;

use App\Models\Students;
use App\Services\ResponseService;
use App\Repositories\SchoolSetting\SchoolSettingInterface;
use App\Repositories\User\UserInterface;
use App\Services\CachingService;
use App\Repositories\ClassSchool\ClassSchoolInterface;
use App\Repositories\SessionYear\SessionYearInterface;
use App\Repositories\SystemSetting\SystemSettingInterface;
use App\Repositories\Student\StudentInterface;
use App\Services\BootstrapTableService;
use Carbon\Carbon;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Throwable;

class SubjectStudentsController extends Controller
{

    private SessionYearInterface $sessionYear;
    private SchoolSettingInterface $schoolSettings;
    // private MediumInterface $medium;
    private ClassSchoolInterface $classes;
    private ClassSchoolInterface $class;
    private UserInterface $user;
    private CachingService $cache;
    private StudentInterface $student;
    private SystemSettingInterface $systemSetting;

    public function __construct(SessionYearInterface $sessionYear, SchoolSettingInterface $schoolSettings, ClassSchoolInterface $classes, UserInterface $user, CachingService $cache, ClassSchoolInterface $classSchool, StudentInterface $student, SystemSettingInterface $systemSetting)
    {

        $this->sessionYear = $sessionYear;
        $this->schoolSettings = $schoolSettings;
        $this->classes = $classes;
        $this->user = $user;
        $this->cache = $cache;
        $this->class = $classSchool;
        $this->student = $student;
        $this->systemSetting = $systemSetting;
    }

    public function index()
    {
        // ResponseService::noFeatureThenRedirect('Fees Management');
        // ResponseService::noPermissionThenRedirect('fees-list');

        $classes = $this->class->all(['*'], ['stream', 'medium']);
        $simpleClassDropdown = $this->class->builder()->pluck('name', 'id');
        $sessionYear = $this->sessionYear->builder()->pluck('name', 'id');
        $defaultSessionYear = $this->cache->getDefaultSessionYear();

        $user = Auth::user();
        $schoolId = $user->school_id;
        $subjects = DB::table('subjects')
            ->select(
                'id',
                'name'
            )
            ->where('school_id', $schoolId)
            ->get();

        $students = DB::table('students as s')
            ->join('users as u', 's.user_id', '=', 'u.id')
            ->select(
                's.id',
                DB::raw("CONCAT(u.first_name,' ', u.last_name) AS fullname"),
            )
            ->where('s.session_year_id', $this->cache->getDefaultSessionYear()->id)
            ->where('s.school_id', $schoolId)
            ->where('u.status', 1)
            ->get();

        return view('subject-students.index', compact('simpleClassDropdown', 'classes', 'sessionYear', 'defaultSessionYear', 'subjects', 'students'));
    }

    public function store(Request $request)
    {
        // dd($request->all());
        $request->validate([
            'student_id' => 'required|array',
            'student_id.*' => 'required|numeric',
            'subject_id' => 'required|array',
            'subject_id.*' => 'required|numeric'
        ]);

        $rows = [];

        foreach ($request->student_id as $studentId) {
            foreach ($request->subject_id as $subjectId) {
                $rows[] = [
                    'student_id' => $studentId,
                    'subject_id' => $subjectId,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }
        }


        DB::table('subject_students')
            ->insert($rows);

        return ResponseService::successResponse('Data Saved Successfully');
    }

    public function show(Request $request)
    {
        $offset = request('offset', 0);
        $limit = request('limit', 10);
        $sort = request('sort', 'id');
        $order = request('order', 'DESC');
        // $search = request('search');
        $showDeleted = request('show_deleted');
        $session_year_id = request('session_year_id');
        // $class_id = request('class_id');
        $student_id = request('student_id');
        $subject_id = $request->subject_id;



        $query = DB::table('subject_students as ss')
            ->join('subjects as sub', 'ss.subject_id', '=', 'sub.id')
            ->join('students as s', 'ss.student_id', '=', 's.id')
            ->join('users as u', 's.user_id', '=', 'u.id')
            ->select(
                'sub.id AS subject_id',
                'sub.name AS subject_name',
                DB::raw('GROUP_CONCAT(DISTINCT CONCAT(u.first_name, " ", u.last_name) ORDER BY u.last_name SEPARATOR ", ") AS students')
            )
            ->where('s.school_id',Auth::user()->school_id)
            ->groupBy('sub.id', 'sub.name');

        if ($subject_id) {
            $query->where('ss.subject_id', $subject_id);
        }

        if ($student_id) {
            $query->where('ss.student_id', $student_id);
        }

        $total = $query->count(); // Get total records based on current filters

        $records = $query->offset($offset)
            ->limit($limit)
            ->get();


        $bulkData = [];
        $bulkData['total'] = $total;
        $rows = [];
        $no = $offset + 1;


        foreach ($records as $row) {
            $operate = '';
            $operate = BootstrapTableService::editButton(route('subject-students.edit', $row->subject_id), false);
            $operate .=BootstrapTableService::deleteButton(route('subject-students.delete', $row->subject_id),false);
            // dd($row);
            $tempRow = (array) $row;
            $tempRow['operate'] = $operate;
            $tempRow['no'] = $no++;
            $rows[] = $tempRow;
        }

        $bulkData['rows'] = $rows;

        return response()->json($bulkData);
    }

    public function edit($id)
    {
        $subject = DB::table('subject_students as ss')
            ->join('subjects as s', 'ss.subject_id', '=', 's.id')
            ->select('ss.subject_id', 's.name as subject_name')
            ->where('ss.subject_id', $id)
            ->first();

        // dd($subject);

        if (!$subject) {
            return redirect()->route('subject-students.index')->withErrors($subject, 'Subject entry not found.');
        }


        $students = DB::table('subject_students as ss')
            ->distinct()
            ->join('students as s', 'ss.student_id', '=', 's.id')
            ->join('users as u', 's.user_id', '=', 'u.id') // Join to get user info
            // ->join('class_sections as cs', 's.class_section_id', '=', 'cs.id')
            // ->join('classes as c', 'cs.class_id', '=', 'c.id')
            ->select(
                's.*',
                DB::raw("CONCAT(u.first_name, ' ', u.last_name) AS full_name"),
                's.id'
            ) // Select fields you need
            ->where('ss.subject_id', $id)
            ->get();

        // $selectedClass = DB::table('subject_students')->where('id', $subject->class_id)->first();

        return view('subject-students.edit', compact('subject', 'students',));
    }

    public function remove(Request $request, $id)
    {
        $studentIds = $request->input('students_remove', []);

        if(empty($studentIds)) {
            return redirect()->route('subject-students.index')->with('error', 'No students selected');
        }

        $remove = DB::table('subject_students')
            ->where('subject_id', $id)
            ->whereIn('student_id', $studentIds)
            ->delete();

        return redirect()->route('subject-students.index')->with('success', 'Students removed successfully.');
    }

    public function delete(Request $request, $id)
    {

        $delete = DB::table('subject_students as ss')
            ->where('ss.subject_id', $id)
            ->delete();

        // dd($subject);

        if ($delete) {
            return response()->json(['success' => true, 'message' => 'Record deleted successfully.']);
        } else {
            return response()->json(['success' => false, 'message' => 'Record not found or could not be deleted.']);
        }
    }

    public function getStudent(Request $request, $class_id)
    {
        $user = Auth::user();
        $studentId = $request->input('student_id');



        $query = DB::table('subject_students as ss')
            ->join('subjects as sub', 'ss.subject_id', '=', 'sub.id')
            ->join('students as s', 'ss.student_id', '=', 's.id')
            ->join('users as u', 's.user_id', '=', 'u.id')
            ->select(
                'sub.id AS subject_id',
                'sub.name AS subject_name',
                DB::raw('GROUP_CONCAT(DISTINCT CONCAT(u.first_name, " ", u.last_name) ORDER BY u.last_name SEPARATOR ", ") AS students'),
                'ss.student_id'
            );

        if ($studentId) {
            $query->where('student_id', $studentId);
        }

        $students = $query->groupBy('sub.id', 'sub.name')->get();

        return response()->json($query);
    }
}
