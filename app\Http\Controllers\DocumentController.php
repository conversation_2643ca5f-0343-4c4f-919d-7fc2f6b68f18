<?php

namespace App\Http\Controllers;

use App\Repositories\Announcement\AnnouncementInterface;
use App\Models\Document;
use App\Models\DocumentClass;
use App\Models\File;
use App\Repositories\AnnouncementClass\AnnouncementClassInterface;
use App\Repositories\ClassSection\ClassSectionInterface;
use App\Repositories\ClassSubject\ClassSubjectInterface;
use App\Repositories\Files\FilesInterface;
use App\Repositories\Student\StudentInterface;
use App\Repositories\StudentSubject\StudentSubjectInterface;
use App\Repositories\SubjectTeacher\SubjectTeacherInterface;
use App\Services\BootstrapTableService;
use App\Services\CachingService;
use App\Services\ResponseService;
use App\Services\GeneralFunctionService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use App\Services\FileSizeLimitService;
use Throwable;
use TypeError;
use Exception;

class DocumentController extends Controller {

    private AnnouncementInterface $announcement;
    private ClassSectionInterface $classSection;
    private SubjectTeacherInterface $subjectTeacher;
    private StudentInterface $student;
    private FilesInterface $files;
    private StudentSubjectInterface $studentSubject;
    private ClassSubjectInterface $classSubject;
    private CachingService $cache;
    private AnnouncementClassInterface $announcementClass;
    private Document $document;
    // Add this line


    public function __construct(Document $document,AnnouncementInterface $announcement, ClassSectionInterface $classSection, SubjectTeacherInterface $subjectTeacher, StudentInterface $student, FilesInterface $files, StudentSubjectInterface $studentSubject, ClassSubjectInterface $classSubject, CachingService $cachingService, AnnouncementClassInterface $announcementClass) {
        $this->announcement = $announcement;
        $this->classSection = $classSection;
        $this->subjectTeacher = $subjectTeacher;
        $this->student = $student;
        $this->files = $files;
        $this->studentSubject = $studentSubject;
        $this->classSubject = $classSubject;
        $this->cache = $cachingService;
        $this->announcementClass = $announcementClass;
        $this->document = $document;

    }


    public function index() {
        $class_section = $this->classSection->builder()->with('class', 'class.stream', 'section', 'medium')->get();
         // Get the Class Section of Teacher
        $subjectTeachers = $this->subjectTeacher->builder()->with(['subject:id,name,type'])->get();
        return view('document', compact('class_section', 'subjectTeachers'));
    }

    public function store(Request $request)
{
    // Validate request
    $request->validate([
        'title' => 'required|string|max:128',
        'class_section_id' => 'required|array',
        'student_id' => 'nullable|array', 
        'file.*' => 'nullable|mimes:jpeg,png,jpg,gif,svg,webp,pdf,doc,docx,xml,xlsx|max:5120',
    ], [
        'class_section_id.required' => trans('the_class_section_field_id_required'),
    ]);

    try {
        DB::beginTransaction();
        $sessionYear = $this->cache->getDefaultSessionYear(); // Get Current Session Year

        // Prepare Document Data
        $documentData = [
            'title' => $request->title,
            'description' => $request->description,
            'session_year_id' => $sessionYear->id,
            'school_id' => Auth::user()->school_id,
        ];

        // Store Document
        $document = $this->document->create($documentData);

        // Prepare Document Class Data
        $documentClassData = [];
        $studentList = [];
        foreach ($request->class_section_id as $classSectionId) {
            foreach ($request->student_id as $studentId) {
                if (!in_array($studentId, $studentList)){            
                    $documentClassData[] = [
                        'document_id' => $document->id,
                        'class_section_id' => $classSectionId,
                        'school_id' => Auth::user()->school_id,
                        'class_subject_id' => $request->class_subject_id ?? null,
                        'student_id' => $studentId, // Use the current student ID
                    ];
                    $studentList[] = $studentId;
                }
            }
        }

        // Create DocumentClass records
        foreach ($documentClassData as $data) {
            DocumentClass::create($data);
        }

        // File Upload Logic
        if ($request->hasFile('file')) {
            $fileData = [];
            foreach ($request->file('file') as $file_upload) {
                if ($file_upload) {
                    if (FileSizeLimitService::checkFileSizeLimit(Auth::user()->school_id)) {
                        return ResponseService::errorResponse('storage capacity not enough');
                    }

                    $fileSize = $file_upload->getSize();
                    $fileSizeKB = round($fileSize / 1024, 2);
                    $tempFileData = [
                        'modal_type' => 'App\Models\Document', // Assuming the type is "Document"
                        'modal_id' => $document->id,
                        'file_name' => $file_upload->getClientOriginalName(),
                        'type' => 1,
                        'file_url' => $file_upload->store('documents', 'public'), // Store the file and get the path
                        'file_size' => $fileSizeKB,
                    ];
                    $fileData[] = $tempFileData;
                }
            }
            $this->files->createBulk($fileData);
        }



    
        // if ($notifyUser->isNotEmpty() ) {
        //     $guardianIds = collect();
        //     foreach ($notifyUser as $user) {
        //         $guardianId = DB::table('students')->where('user_id', $user)->pluck('guardian_id');
        //         $guardianIds = $guardianIds->merge($guardianId);
        //     }

            $notifyUser = DB::table('students')->where('id', $request->student_id)->select('user_id','guardian_id')->get();
            foreach($notifyUser as $user){ 
                    $userArray = [$user->user_id , $user->guardian_id];
                    $title = 'Document Created Successful';
                    $type = 'Class Section';
                    $body = $request->title; 
                   
                    send_notification($userArray, $title, $body, $type);     
            }
        DB::commit();
        return ResponseService::successResponse('Data Stored Successfully');
    } catch (Throwable $e) {
        DB::rollBack();
        ResponseService::logErrorResponse($e, "Document Controller -> Store Method");
        return ResponseService::errorResponse();
    }
}
    

public function update(Request $request)
{
    // Validate request
    $request->validate([
        'id' => 'required|exists:documents,id',
        'title' => 'required|string|max:128',
        'class_section_id' => 'required|array',
        'student_id' => 'nullable|array', 
        'file.*' => 'nullable|mimes:jpeg,png,jpg,gif,svg,webp,pdf,doc,docx,xml,xlsx|max:5120',
    ], [
        'class_section_id.required' => trans('the_class_section_field_id_required'),
    ]);

    try {
        DB::beginTransaction();
        
        // Fetch the existing document
        $document = $this->document->find($request->id);

        // Prepare updated document data
        $documentData = [
            'title' => $request->title,
            'description' => $request->description,
            'school_id' => Auth::user()->school_id,
        ];

        // Update the document
        $document->update($documentData);

        // Update Document Class Data
        DocumentClass::where('document_id', $document->id)->delete(); // Remove old records

        $documentClassData = [];
        foreach ($request->class_section_id as $classSectionId) {
            foreach ($request->student_id as $studentId) { // Iterate over each student ID
                $documentClassData[] = [
                    'document_id' => $document->id,
                    'class_section_id' => $classSectionId,
                    'school_id' => Auth::user()->school_id,
                    'class_subject_id' => $request->class_subject_id ?? null,
                    'student_id' => $studentId, // Use the current student ID
                ];
            }
        }

        // Create DocumentClass records
        DocumentClass::insert($documentClassData);

        // File Upload Logic
        if ($request->hasFile('file')) {
            foreach ($request->file('file') as $file_upload) {
                if ($file_upload) {
                    if (FileSizeLimitService::checkFileSizeLimit(Auth::user()->school_id)) {
                        return ResponseService::errorResponse('Storage capacity not enough');
                    }
        
                    $fileSize = $file_upload->getSize();
                    $fileSizeKB = round($fileSize / 1024, 2);
                    $fileName = $file_upload->getClientOriginalName();
                    $fileUrl = $file_upload->store('documents', 'public'); // Store the file
        
                    // Check if a file already exists for the current document
                    $existingFile = File::where('modal_id', $document->id)
                        ->where('modal_type', 'App\Models\Document')
                        ->first();
        
                    if ($existingFile) {
                        // Update existing file record
                        $existingFile->update([
                            'file_name' => $fileName,
                            'file_url' => $fileUrl,
                            'file_size' => $fileSizeKB,
                        ]);
                    } else {
                        // Create new file record
                        $this->files->create([
                            'modal_type' => 'App\Models\Document',
                            'modal_id' => $document->id,
                            'file_name' => $fileName,
                            'type' => 1,
                            'file_url' => $fileUrl,
                            'file_size' => $fileSizeKB,
                        ]);
                    }
                }
            }
        }

        DB::commit();
        return ResponseService::successResponse('Data Updated Successfully');
    } catch (Throwable $e) {
        DB::rollBack();
        ResponseService::logErrorResponse($e, "Document Controller -> Update Method");
        return ResponseService::errorResponse();
    }
}

public function show()
{
    $offset = request('offset', 0);
    $limit = request('limit', 10);
    $sort = request('sort', 'id');
    $order = request('order', 'ASC');
    $search = request('search');

    // Build the query
    $sql = DB::table('documents')
        ->join('document_classes', 'documents.id', '=', 'document_classes.document_id')
        ->join('students', 'document_classes.student_id', '=', 'students.id')
        ->join('users', 'students.user_id', '=', 'users.id')
        ->join('class_sections', 'document_classes.class_section_id', '=', 'class_sections.id')
        ->join('classes', 'class_sections.class_id', '=', 'classes.id')
        ->join('mediums', 'class_sections.medium_id', '=', 'mediums.id')
        ->leftjoin('files', function ($join) {
            $join->on('documents.id', '=', 'files.modal_id')
                 ->where('files.modal_type', '=', 'App\Models\Document');
        })
        ->select(
            'documents.id',
            'documents.title',
            'documents.description',
            'document_classes.class_section_id',
            'document_classes.class_subject_id',
            'document_classes.student_id',
            DB::raw("CONCAT(users.first_name, ' ', users.last_name) AS student_name"),
            DB::raw("CONCAT(classes.name, ' ', mediums.name) AS class_name"), // Combined class and medium name
            'files.file_name',
            'files.file_url'
        )
        ->where('documents.school_id', Auth::user()->school_id)
        ->where(function ($query) use ($search) {
            $query->when($search, function ($q) use ($search) {
                $q->where('documents.id', 'LIKE', "%$search%")
                  ->orWhere('documents.title', 'LIKE', "%$search%")
                  ->orWhere('documents.description', 'LIKE', "%$search%");
            });
        })
        ->groupBy('documents.id', 'document_classes.class_section_id', 'document_classes.student_id', 'users.first_name', 'users.last_name', 'classes.name', 'mediums.name', 'files.file_name', 'files.file_url');
    $total = $sql->count();// Get total count of documents


    $results = $sql->get(); 
    $bulkData = [
        'total' => $total,
        'rows' => []
    ];
    $no=1;
    $documents = [];

    foreach ($results as $row) {
        $docId = $row->id;

        if (!isset($documents[$docId])) {
            $documents[$docId] = [
                'no' =>$no++,
                'id' => $row->id,
                'title' => $row->title,
                'description' => $row->description,
                'assign_to' => [], // Store class names here
                'students' => [],
                'class_section_ids' => [],
                'students_ids' => [],
                'class_section_id'=>$row->class_section_id,
                'student_id'=>$row->student_id,
                'file_name' => $row->file_name,
                'file_url' => $row->file_url ===null ? '':asset('storage/' .$row->file_url),
                'operate' => BootstrapTableService::editButton(route('document.update', $row->id)) .
                             BootstrapTableService::deleteButton(route('document.destroy', $row->id))
            ];
        }


        if (!in_array($row->class_name, $documents[$docId]['assign_to'])) {
            $documents[$docId]['assign_to'][] = $row->class_name; 
        }

        if (!in_array($row->student_name, $documents[$docId]['students'])) {
            $documents[$docId]['students'][] = $row->student_name;
        }

        if (!in_array($row->student_id, $documents[$docId]['students_ids'])) {
            $documents[$docId]['students_ids'][] = $row->student_id; 
        }

        if (!in_array($row->class_section_id, $documents[$docId]['class_section_ids'])) {
            $documents[$docId]['class_section_ids'][] = $row->class_section_id; 
        }
    }

    foreach ($documents as $doc) {
        $bulkData['rows'][] = [
            'no' => $doc['no'],
            'id' => $doc['id'],
            'title' => $doc['title'],
            'description' => $doc['description'],
            'assign_to' => implode(', ', $doc['assign_to']), 
            'student_name' => implode(', ', $doc['students']), 
            'class_section_id'=> $doc['class_section_id'],
            'class_section_ids' => $doc['class_section_ids'], 
            'student_ids' => $doc['students_ids'], 
            'student_id'=>$doc['student_id'],
            'file_name' => $doc['file_name'],
            'file_url' => $doc['file_url'],
            'operate' => $doc['operate']
        ];
    }

    return response()->json($bulkData);
    
}


public function destroy($id)
{

    try {
        DB::beginTransaction();

        // Fetch the document
        $document = $this->document->findOrFail($id);

        // Delete associated DocumentClass records
        DocumentClass::where('document_id', $document->id)->delete();

        // Delete associated files if any
        File::where('modal_id', $document->id)
            ->where('modal_type', 'App\Models\Document')
            ->delete();

        // Delete the document
        $document->delete();

        DB::commit();
        return ResponseService::successResponse('Document Deleted Successfully');
    } catch (ModelNotFoundException $e) {
        DB::rollBack();
        return ResponseService::errorResponse('Document not found');
    } catch (Throwable $e) {
        DB::rollBack();
        ResponseService::logErrorResponse($e, "Document Controller -> Destroy Method");
        return ResponseService::errorResponse();
    }
}

    public function fileDelete($id) {
        // ResponseService::noFeatureThenRedirect('Announcement Management');
        // ResponseService::noPermissionThenRedirect('announcement-delete');
        try {
            DB::beginTransaction();

            // Find the Data by FindByID
            DB::table('files')
            ->where('modal_id',$id)
            ->where('modal_type','App\Models\Document')
            ->delete();


            DB::commit();
            ResponseService::successResponse('Data Deleted Successfully');
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "Document Controller -> fileDelete Method");
            ResponseService::errorResponse();
        }
    }

   
}
