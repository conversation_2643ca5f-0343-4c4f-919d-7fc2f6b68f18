<?php

namespace App\Http\Controllers;

use App\Repositories\Medium\MediumInterface;
use App\Repositories\Subject\SubjectInterface;
use App\Rules\uniqueForSchool;
use App\Services\BootstrapTableService;
use App\Services\ResponseService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Throwable;
use App\Services\FileSizeLimitService;


class CommissionController extends Controller {
    private MediumInterface $medium;
    private SubjectInterface $subject;

    public function __construct(MediumInterface $medium, SubjectInterface $subject) {
        $this->medium = $medium;
        $this->subject = $subject;
    }

    public function index() {
        //ResponseService::noFeatureThenRedirect('Commission Management');
        $mediums = $this->medium->builder()->orderBy('id', 'DESC')->get();
        return view('commission.commision_fees_per_section', compact('mediums'));
    }

    public function show(Request $request) {
        $offset = request('offset', 0);
        $limit = request('limit', 10);
        $sort = request('sort', 'id');
        $order = request('order', 'DESC');
        $search = $_GET['search'];
        $showDeleted = $request->show_deleted;

        $sql = $this->subject->builder()->with('medium')
            ->where(function ($query) use ($search) {
                $query->when($search, function ($q) use ($search) {
                    $q->where('id', 'LIKE', "%$search%")
                        ->orwhere('name', 'LIKE', "%$search%")
                        ->orwhere('code', 'LIKE', "%$search%")
                        ->orwhere('type', 'LIKE', "%$search%")->Owner();
                });
            })
            ->when(!empty($showDeleted), function ($q) {
                $q->onlyTrashed()->Owner();
            });
        if (!empty($_GET['medium_id'])) {
            $sql = $sql->where('medium_id', $_GET['medium_id']);
        }

        $total = $sql->count();

        $sql = $sql->orderBy($sort, $order)->skip($offset)->take($limit);
        $res = $sql->get();

        $bulkData = array();
        $bulkData['total'] = $total;
        $rows = array();
        $no = 1;

        foreach ($res as $row) {
            if ($request->show_deleted) {
                //Show Restore and Hard Delete Buttons
                $operate = BootstrapTableService::restoreButton(route('commission.restore', $row->id));
                $operate .= BootstrapTableService::trashButton(route('commission.trash', $row->id));
            } else {
                //Show Edit and Soft Delete Buttons
                $operate = BootstrapTableService::editButton(route('commission.update', $row->id));
                $operate .= BootstrapTableService::deleteButton(route('commission.destroy', $row->id));
            }
            $tempRow = $row->toArray();
            $tempRow['no'] = $no++;
            $tempRow['type'] = trans($row->type);
            $tempRow['eng_type'] = $row->type;
            $tempRow['operate'] = $operate;
            $rows[] = $tempRow;
        }

        $bulkData['rows'] = $rows;
        return response()->json($bulkData);
    }


    public function store(Request $request) {
        //ResponseService::noFeatureThenRedirect('Comission Management');
        $schoolId = auth()->user()->school_id;
        $validator = Validator::make($request->all(), [
            /*'medium_id' => 'required|numeric',*/
            'type'      => 'required|in:Practical,Theory',
            'name'      => [
                'required',
                new uniqueForSchool('subjects', ['name' => $request->name, 'medium_id' => $request->medium_id, 'type' => $request->type])
            ],
            'bg_color'  => 'required|not_in:transparent',
            //            'code'      => 'nullable|unique:subjects,code',
            'code'      => [
                'nullable',
                new uniqueForSchool('subjects', ['code' => $request->code, 'medium_id' => $request->medium_id, 'type' => $request->type])
            ],
            'image'     => 'required|max:2048|mimes:jpg,jpeg,png,svg',
            'file_size' =>'nullable|numeric',
            'commission' => 'nullable|min:0',
            'commission_month' => 'nullable|min:0',
        ])->setAttributeNames(['bg_color' => 'Background Color']);
        $request->medium_id = '';

        if ($validator->fails()) {
            ResponseService::errorResponse($validator->errors()->first());
        }
        try {
            $file = $request->file('image');
            if (FileSizeLimitService::checkFileSizeLimit($schoolId)) {
                return ResponseService::errorResponse('storage capacity not enough');}

            // Check if file is uploaded
            if ($file) {
                // Get the size in bytes
                $fileSizeInBytes = $file->getSize();
    
                // Convert size to kilobytes
                $fileSizeInKB = $fileSizeInBytes / 1024;
    
                // Optionally, round the size to 2 decimal places
                $fileSizeInKBB = round($fileSizeInKB, 2);

            }
            // dd($fileSizeInKB);

            $data = $request->all();

        
            $data['file_size'] = $fileSizeInKBB;
            if (isset($data['commission'])) {
                $data['commission'] = number_format((float)$data['commission'], 2, '.', '');
            }

            if (isset($data['commission_month'])) {
                $data['commission_month'] = number_format((float)$data['commission_month'], 2, '.', '');
            }

            $this->subject->create($data);
            ResponseService::successResponse('Data Stored Successfully');
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function updatecommission(Request $request, $id) {
        //ResponseService::noFeatureThenRedirect('Commission Management');
    
        $schoolId = auth()->user()->school_id;
    
        // Validation rules
        $validator = Validator::make($request->all(), [
            'name' => [
                'required',
                new uniqueForSchool('subjects', ['name' => $request->name, 'medium_id' => $request->medium_id, 'type' => $request->type], $id)
            ],
            'code' => [
                'nullable',
                new uniqueForSchool('subjects', ['code' => $request->code, 'medium_id' => $request->medium_id, 'type' => $request->type], $id)
            ],
            'type' => 'required|in:Practical,Theory',
            'bg_color' => 'required|not_in:transparent',
            'image' => 'mimes:jpg,jpeg,png,svg|max:2048|nullable',
            'file_size' => 'nullable|numeric',
            'commission' => 'nullable|min:0',
            'commission_month' => 'nullable|min:0',
        ])->setAttributeNames(['bg_color' => 'Background Color']);
    
        // Check for validation failures
        if ($validator->fails()) {
            return ResponseService::errorResponse($validator->errors()->first());
        }
    
        try {
            $data = $request->all();
            $fileSizeInKBB = null;
    
            // Handle file upload if present
            if ($request->file('image')) {
                $file = $request->file('image');
    
                // Check if file is uploaded and storage capacity
                if ($file && FileSizeLimitService::checkFileSizeLimit($schoolId)) {
                    return ResponseService::errorResponse('Storage capacity not enough');
                }
    
                $fileSizeInBytes = $file->getSize();
                $fileSizeInKBB = round($fileSizeInBytes / 1024, 2); // Convert size to KB
            }
    
            // Assign file size to data if available
            if ($fileSizeInKBB !== null) {
                $data['file_size'] = $fileSizeInKBB;
            }
    
            // Format commission if set
            if (isset($data['commission'])) {
                $data['commission'] = number_format((float)$data['commission'], 2, '.', '');
            }

            if (isset($data['commission_month'])) {
                $data['commission_month'] = number_format((float)$data['commission_month'], 2, '.', '');
            }
            
    
            // Update the subject with the prepared data
            $this->subject->update($id, $data);
            
            return ResponseService::successResponse('Data Updated Successfully');
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            return ResponseService::errorResponse('An error occurred while updating the data.');
        }
    }

    public function destroy($id) {
        try {
            $this->subject->deleteById($id);
            ResponseService::successResponse('Data Deleted Successfully');
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function restore(int $id) {
        try {
            $this->subject->findOnlyTrashedById($id)->restore();
            ResponseService::successResponse("Data Restored Successfully");
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function trash($id) {
        try {
            $this->subject->findOnlyTrashedById($id)->forceDelete();
            ResponseService::successResponse("Data Deleted Permanently");
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e, "Subject Controller -> Trash Method", 'cannot_delete_because_data_is_associated_with_other_data');
            ResponseService::errorResponse();
        }
    }
}
