<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Validator;
use Exception;
use Throwable;
use DOMDocument;
use DOMElement;
use DOMXPath;
use Carbon\Carbon;
use App\Models\ItemCode;
use Illuminate\Http\Request;

use chillerlan\QRCode\QRCode;
use App\Models\StudentFeeType;
use Barryvdh\DomPDF\Facade\Pdf;
use App\Services\CachingService;
use App\Services\ResponseService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\StudentFeeTypesExport;
use App\Imports\StudentFeeTypesImport;

use Klsheng\Myinvois\Helper\MyInvoisHelper;
use App\Helpers\EInvoiceHelper;
use App\Services\BootstrapTableService;
use App\Services\EInvoiceFormatService;
use App\Repositories\Student\StudentInterface;
use App\Repositories\FeesType\FeesTypeInterface;
use App\Repositories\ClassSchool\ClassSchoolInterface;
use App\Repositories\StudentFees\StudentFeesInterface;
use App\Repositories\ClassSection\ClassSectionInterface;
use App\Repositories\StudentFeeType\StudentFeeTypeInterface;

class StudentFeeTypesController extends Controller
{
    private StudentFeeTypeInterface $studentFeeTypes;
    private StudentFeesInterface $studentFees;
    private StudentInterface $student;
    private FeesTypeInterface $feesType;
    private ClassSchoolInterface $class;
    private CachingService $cache;
    private ClassSectionInterface $classSection;
    private EInvoiceFormatService $eInvoiceFormatService;

    public function __construct(
        StudentFeesInterface $studentFees,
        StudentFeeTypeInterface $studentFeeTypes,
        CachingService $cache,
        ClassSchoolInterface $classSchool,
        FeesTypeInterface $feesType,
        StudentInterface $student,
        ClassSectionInterface $classSection,
        EInvoiceFormatService $eInvoiceFormatService
    ) {

        $this->studentFees = $studentFees;
        $this->studentFeeTypes = $studentFeeTypes;
        $this->cache = $cache;
        $this->class = $classSchool;
        $this->feesType = $feesType;
        $this->student = $student;
        $this->classSection = $classSection;
        $this->eInvoiceFormatService= $eInvoiceFormatService;
    }

    public function index(){
        ResponseService::noFeatureThenRedirect('Fees Management');
        ResponseService::noPermissionThenRedirect('fees-type-list');

        $classes = $this->class->all(['*'], ['stream', 'medium', 'stream']);
        $simpleClassDropdown = $this->class->builder()->pluck('name','id');
        $currentSessionYear = $this->cache->getDefaultSessionYear();
        $currentClasses = DB::table('classes as c')
        ->join('class_sections as cs', 'cs.class_id', '=', 'c.id')
                
                ->join('students as s', function ($join) use ($currentSessionYear) {
                    $join->on('s.class_section_id', '=', 'cs.id')
                    ->where('s.session_year_id','=',$currentSessionYear->id);
                })
                ->wherenull('c.deleted_at')
        ->where('c.school_id', Auth::user()->school_id)
        ->pluck('c.name', 'c.id');

   
        $allSessionYears = DB::table('session_years')->where('school_id',Auth::user()->school_id)->get();
        $feesTypeData = $this->feesType->all();
        $itemCode = DB::table('fees_type_master')->where('school_id',Auth::user()->school_id)->get();
        return view('student-fee-types.index', compact('simpleClassDropdown','classes', 'feesTypeData', 'itemCode','currentClasses','currentSessionYear','allSessionYears',));
    }

    public function store(Request $request) {
        ResponseService::noFeatureThenRedirect('Fees Management');
        ResponseService::noPermissionThenRedirect('fees-create');
        $request->validate([
            'class_id'                                    => 'required|numeric',
            'student_id'                                  => 'required|array',
            'student_id.*'                               => 'required|numeric',
            'student_fee_types'                          => 'required|array',
            'student_fee_types.*'                        => 'required|array',
            'student_fee_types.*.fees_type_name'         => 'required',
            'student_fee_types.*.unit_price'             => 'required|numeric',
            'student_fee_types.*.classification_code'     => 'required',
            'student_fee_types.*.unit'                   => 'required',
            'student_fee_types.*.quantity'               => 'required|numeric|min:1',
            'student_fee_types.*.discount_percentage'     => 'nullable|numeric',
            'student_fee_types.*.tax_percentage'         => 'nullable|numeric',
            'student_fee_types.*.remarks'                => 'nullable',
            'student_fee_types.*.item_code'              => 'required'
      ]);

      
        try {

            $classData = $this->class->builder()->find($request->class_id);
            

            DB::beginTransaction();

            $feeTypes = [];
            $process=false;
            foreach ($request->student_id as $student_id) {

                foreach ($request->student_fee_types as $data) { 
                    
                    $itemCode = DB::table("fees_type_master")
                    ->where("id", $data['item_code'])
                    ->value('item_code'); 
                    $quantity = $data['quantity'] ?? 1; // Default to 1 if quantity is not provided

                    foreach($feeTypes as &$fees){
                        if(isset($itemCode) && isset($fees['item_code'])){
                            if($fees['item_code']==$itemCode && $fees['student_id']==$student_id && $fees!=null){
                                $fees['quantity'] += $quantity;
                                $process=true;
    
                            } else{
                                $process=false;
                            }
                        }
                    }

                    if($process){
                        continue;
                    }

                    $feeTypes[] = array(
                        'item_code'             => $itemCode,
                        'school_id'             => $classData->school_id,
                        'class_id'              => $request->class_id,
                        'student_id'            => $student_id,
                        'classification_code'   => $data['classification_code'],
                        'unit'                  => $data['unit'],
                        'quantity'              => $quantity,
                        "fees_type_name"        => $data['fees_type_name'],
                        "unit_price"            => $data['unit_price'],
                        "discount"              => $data['discount_percentage'],
                        "tax"                   => $data['tax_percentage'],
                        "remarks"               => $data['remarks'],
                    );
                }
            }
            if (count($feeTypes) > 0) {
                $this->studentFeeTypes->upsert($feeTypes, ['class_id','student_id','school_id','fees_type_name','classification_code','unit','item_code','remarks'], ['unit_price','quantity','tax', 'discount']);
            }

            DB::commit();
            ResponseService::successResponse('Data Stored Successfully');
        } catch (Throwable $e) {
            DB::rollback();
            ResponseService::logErrorResponse($e,"StudentFeeTypesController -> Store Method");
            ResponseService::errorResponse();
        }
    }

 
    public function show() {
        ResponseService::noFeatureThenRedirect('Fees Management');
        ResponseService::noPermissionThenRedirect('fees-list');
        $offset = request('offset', 0);
        $limit = request('limit', 99999);
        $sort = request('sort', 'id');
        $order = request('order', 'DESC');
        $search = request('search');
        $showDeleted = request('show_deleted');
        $class_id = request('class_id');

    
        $sessionYear = $this->cache->getDefaultSessionYear();

        // Query to fetch sorted student_fee_types with related students and users
        $query = DB::table('student_fee_types')
            ->join('students', 'student_fee_types.student_id', '=', 'students.id')
            ->join('users', 'students.user_id', '=', 'users.id')
            ->leftJoin('student_fees', 'student_fee_types.student_id', '=', 'student_fees.student_id')
            ->leftJoin('student_fees_paids', 'student_fees.id', '=', 'student_fees_paids.student_fees_id')
            
            ->select(
                'student_fee_types.*', 
                'students.id as student_id', 
                'users.first_name',
                'users.last_name',
                'users.rfid_id'
                 )
                 ->groupBy(
                    'student_fee_types.id',
                    // 'students.id',
                    // 'users.first_name',
                    // 'users.last_name',
                    // 'users.rfid_id',
                )

            ->where('students.session_year_id', $sessionYear->id)
            ->where('student_fee_types.class_id',$class_id)
            ->where('users.status', 1)
            ->where('users.school_id', Auth::user()->school_id)
            ->when($search, function ($query) use ($search,$class_id) {
                $query->where(function($query) use ($search,$class_id) {
                    $query->where(function($query) use ($search) {
                    $query->where(DB::raw("CONCAT_WS(' ', users.first_name, users.last_name)"), 'LIKE', "%$search%")
                          ->orWhere('users.rfid_id', 'LIKE', "%$search%")
                          ->orWhere('student_fee_types.fees_type_name', 'LIKE', "%$search%");
                    });
                        
                        });
            })

            ->when(!empty($showDeleted), function ($query) {
                $query->onlyTrashed();
            })
            ->orderBy('student_fee_types.id', 'DESC'); // Sort by student_fee_types.id
    
        // Calculate total before pagination
        $total = $query->get()->count();

        // Then apply pagination
        $res = $query->skip($offset)->take($limit)->get();

        // Prepare the response data
        $bulkData = array();
        $bulkData['total'] = $total;
        $rows = array();
        $no = 1;
    
        foreach ($res as $row) {
            
        
            $operate = '';
            if ($showDeleted) {
                $operate .= BootstrapTableService::restoreButton(route('student-fee-types.restore', $row->id));
                $operate .= BootstrapTableService::trashButton(route('student-fees-types.trash', $row->id));
            } else {
                $operate .= BootstrapTableService::editButton(route('student-fee-types.edit', $row->student_id), false);
                $operate .= BootstrapTableService::deleteButton(route('student-fee-types.destroy', $row->id));
            }

            $tempRow = [];
            $tempRow['no'] = $no++ ?? '';
            $tempRow['id'] = $row->id ?? '';
            $tempRow['item_code'] = $row->item_code ?? '';
            $tempRow['full_name'] = $row->first_name . ' ' . $row->last_name ?? '';
            $tempRow['rfid_id'] = $row->rfid_id ?? '';
            $tempRow['fees_type_name'] = $row->fees_type_name ?? '';
            $tempRow['classification_code'] = $row->classification_code ?? '';
            $tempRow['unit'] = $row->unit ?? '';
            $tempRow['unit_price'] = 'RM ' . number_format($row->unit_price, 2) ?? '';
            $tempRow['quantity'] = $row->quantity;
            $tempRow['discount'] = $row->discount ?? '';
            $tempRow['tax'] = $row->tax ?? '';
            $tempRow['remarks'] = $row->remarks ?? '';

            $tempRow['total_price'] = 'RM ' . number_format((($row->unit_price - ($row->unit_price * $row->discount / 100)) 
            + (($row->unit_price - ($row->unit_price * $row->discount / 100)) * $row->tax / 100))*$row->quantity,2);
           
            // $tempRow['total_fees'] = 'RM ' . number_format((float) $row->unit_price * (float) $row->quantity, 2) ?? '';
            $tempRow['operate'] = $operate;
            $rows[] = $tempRow;
        }
    
        $bulkData['rows'] = $rows;
        return response()->json($bulkData);
    }


    
    public function edit($id) {
        ResponseService::noFeatureThenRedirect('Fees Management');
        ResponseService::noPermissionThenRedirect('fees-edit');

        $student = $this->student->builder()->with(["class_section.class"])->find($id);
        $itemCode = DB::table('fees_type_master')->where('school_id',Auth::user()->school_id)->get();

        $feesTypeData = $this->feesType->all();
        $studentFeeTypes = $this->studentFeeTypes->builder()->where("student_fee_types.student_id",$id)->get();
        foreach($studentFeeTypes as $type){
            if($type){
                $checkItemCode = DB::table('fees_type_master')->where('item_code',$type->item_code)->first();
                if($checkItemCode){
                    $type->item_code = $checkItemCode->id;
                }else {
                    $type->item_code = "";
                }
            }
        }
        return view('student-fee-types.edit', compact('student','feesTypeData', 'studentFeeTypes', 'itemCode'));
    }

    public function update(Request $request, $id) {
        ResponseService::noFeatureThenRedirect('Fees Management');
        ResponseService::noPermissionThenRedirect('fees-edit');

    

        $request->validate([
            'student_id' => 'required',
            'class_id' => 'required',
            'school_id' => 'required',
            'student_fee_types'                => 'required|array',
            'student_fee_types.*'              => 'required|array',
            'student_fee_types.*.fees_type_name' => 'required',
            'student_fee_types.*.unit_price'       => 'required|numeric',
            'student_fee_types.*.classification_code' => 'required|string',
            'student_fee_types.*.unit' => 'required|string',
            'student_fee_types.*.quantity' => 'required|numeric|min:1',
            'student_fee_types.*.discount' => 'nullable|numeric',
            'student_fee_types.*.tax' => 'nullable|numeric',
            // 'student_fee_types.*.quantity' => 'required|numeric|min:1',
        ]);
        try {
            DB::beginTransaction();
            $feeTypes = [];
            foreach ($request->student_fee_types as $data) {
                
            
                $itemCode = DB::table("fees_type_master")
                ->where("id", $data['item_code'])
                ->value('item_code'); 
                // $total_amount = $data['quantity'] * $data['unit_price'];
                $feeTypes[] = array(
                    "id"           => $data['id'],
                    "class_id"=> $request->class_id,
                    "student_id"=> $request->student_id,
                    "school_id"=> $request->school_id,
                    "fees_type_name"    => $data['fees_type_name'],
                    "unit_price"  => $data['unit_price'],
                    "classification_code" => $data['classification_code'],
                    "unit" => $data['unit'],
                    "quantity" => $data['quantity']??1,
                    "discount"=>$data['discount']??0,
                    "tax"=>$data['tax']??0,
                    "item_code"=>$itemCode,
                    "remarks" => $data['remarks'],
                //total_amount" => $total_amount,
                );
            }

            if (isset($feeTypes)) {
                $this->studentFeeTypes->upsert($feeTypes, ['id', 'class_id', 'student_id', 'school_id', 'item_code'], ['fees_type_name', 'unit_price', 'classification_code', 'unit', 'quantity', 'tax', 'discount', 'remarks']);
            }

            DB::commit();
            ResponseService::successRedirectResponse(route('student-fee-types.index'), 'Data Update Successfully');
        } catch (Throwable) {
            DB::rollback();
            ResponseService::errorRedirectResponse();
        }
    }

    public function destroy($id) {
        ResponseService::noFeatureThenRedirect('Fees Management');
        ResponseService::noPermissionThenSendJson('fees-delete');
    
        try {
            DB::beginTransaction();
            
            $feeType = $this->studentFeeTypes->builder()->find($id);
            
            if (!$feeType) {
                return ResponseService::errorResponse('Record not found');
            }
            
            $feeType->delete(); 
    
            DB::commit();
            ResponseService::successResponse("Data Deleted Successfully");
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "StudentFeeTypesController -> Destroy Method");
            ResponseService::errorResponse();
        }
    }
    

    public function deleteFeeType($id) {
        ResponseService::noFeatureThenRedirect('Fees Management');
        ResponseService::noPermissionThenSendJson('fees-delete');

        try {
            DB::beginTransaction();
            $this->studentFeeTypes->builder()->where("id",$id)->delete();
            DB::commit();
            ResponseService::successResponse("Data Deleted Successfully");
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "StudentFeeTypesController -> Store Method");
            ResponseService::errorResponse();
        }
    }


    public function search(Request $request) {
        ResponseService::noFeatureThenRedirect('Fees Management');
        try {
            $data = $this->studentFeeTypes->builder()->where('session_year_id', $request->session_year_id)->get();
            ResponseService::successResponse("Data Restored Successfully", $data);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }


    public function getClassStudent(Request $request){

        $classStudents = DB::select("
            SELECT  s.id
                    , concat(u.first_name,' ', u.last_name) AS fullname
                    , s.school_id
            FROM    students s
            JOIN    class_sections cs
            ON      s.class_section_id = cs.id
            JOIN    users u
            ON      s.user_id = u.id
            WHERE   cs.class_id = ?
            AND     s.session_year_id = ?
            AND     u.status = 1
        ",[$request->class_id,$this->cache->getDefaultSessionYear()->id]);

        return response()->json($classStudents);
    }

    public function getSessionYearClass(Request $request){
      
            $currentSessionYear = $this->cache->getDefaultSessionYear();
            $session_year = DB::table('session_years')->where('id',$request->session_year)->first();

            if ($session_year == $currentSessionYear) {
                $sessionYearClass = DB::table('classes as c')
                ->join('class_sections as cs', 'cs.class_id', '=', 'c.id')
                
                ->join('students as s', function ($join) use ($session_year) {
                    $join->on('s.class_section_id', '=', 'cs.id')
                    ->where('s.session_year_id','=',$session_year->id);
                })
               
               ->where('c.school_id',Auth::user()->school_id)
               ->wherenull('c.deleted_at')
                ->distinct()
                ->get(['c.name', 'c.id']);

              
            }else{
                $sessionYearClass = DB::table('classes as c')
                ->join('class_sections as cs', 'cs.class_id', '=', 'c.id')
                
                ->join('students as s', function ($join) use ($session_year) {
                    $join->on('s.class_section_id', '=', 'cs.id')
                    ->where('s.session_year_id','=',$session_year->id);
                })
               
               ->where('c.school_id',Auth::user()->school_id)
                ->distinct()
                ->get(['c.name', 'c.id']);

            }



        return response()->json($sessionYearClass);
    }

    // public function downloadStudentFeeTypeTemplate($class_id){
    //     return Excel::download(new StudentFeeTypesExport($class_id,$this->cache->getDefaultSessionYear()->id), 'students-fee-types.xlsx');
    // }

    public function importStudentFeeType(Request $request, $class_id) {
        $validator = Validator::make($request->all(), [
            'import-file' => [
                'required',
                'file',
                function ($attribute, $value, $fail) {
                    $extension = strtolower($value->getClientOriginalExtension());
                    if (!in_array($extension, ['xls', 'xlsx'])) {
                        $fail('The import file must be an Excel file (xls or xlsx).');
                    }
                }
            ],
            'session_year_id' => 'required|integer'
        ]);

        if ($validator->fails()) {
            return response()->json([
                "status" => false,
                "message" => $validator->errors()->first()
            ]);
        }

        try {
            $classData = $this->class->builder()->find($class_id);
            if (!$classData) {
                return response()->json([
                    "status" => false,
                    "message" => "Class not found"
                ]);
            }

            $file = $request->file('import-file');
            $extension = $file->getClientOriginalExtension();
            
            $import = new StudentFeeTypesImport(
                $class_id, 
                $classData->school_id, 
                $request->session_year_id
            );

            if ($extension === 'xls') {
                Excel::import($import, $file, null, \Maatwebsite\Excel\Excel::XLS);
            } else {
                Excel::import($import, $file, null, \Maatwebsite\Excel\Excel::XLSX);
            }

            return response()->json([
                "status" => true,
                "message" => "Import Successful"
            ]);

        } catch (Exception $e) {
            \Log::error('Import Error: ' . $e->getMessage());
            return response()->json([
                "status" => false,
                "message" => "Error during import: " . $e->getMessage()
            ]);
        }
    }

    public function creditNoteIndex(Request $request)
    {
        ResponseService::noFeatureThenRedirect('Fees Management');
        ResponseService::noPermissionThenRedirect('fees-type-list');

        // Fetch all classes
        $classes = $this->class->all(['*'], ['stream', 'medium', 'stream']);
        $simpleClassDropdown = $this->class->builder()->pluck('name', 'id');
        
        // Fetch all item codes
        $itemCode = DB::table('fees_type_master')->where('school_id', Auth::user()->school_id)->get();
        
        // Fetch distinct session years (format as YYYY-MM) from the created_at field
        $availableSessions = DB::table('credit_note')
            ->selectRaw('CONCAT(YEAR(created_at), "-", LPAD(MONTH(created_at), 2, "0")) as session_year')
            ->distinct()
            ->where('school_id', Auth::user()->school_id)
            ->get();

        // Get the current session year from the session (stored in session data)
        $currentSession = session('session_year_id');
        
        // Start building the query to fetch credit notes
        $query = DB::table('credit_note')->where('school_id', Auth::user()->school_id);
        
        // If a session year is selected, filter by that session year
        if ($currentSession) {
            $query->whereRaw("CONCAT(YEAR(created_at), '-', LPAD(MONTH(created_at), 2, '0')) = ?", [$currentSession]);  // Using formatted created_at to filter
        }
        
        // Fetch the credit notes based on the query
        $creditNotes = $query->get();

        // Pass the session data, credit notes, and other variables to the view
        return view('student-fee-types.credit_note', compact('simpleClassDropdown', 'classes', 'itemCode', 'availableSessions', 'creditNotes', 'currentSession'));
    }

    public function creditNoteGetInvoice(Request $request){ 
        $studentId = $request->student_id;
        $invoices = DB::table('student_fees_paids as sfp')
                    ->join('student_fees as sf','sf.id','=','sfp.student_fees_id')
                    ->where('sf.student_id',$studentId)
                    ->get(['sf.id','sf.uid','sf.name']);

        return response()->json(['invoices' => $invoices]);
        
    }

    public function generateUniqueUid(){
        $uid = 1;
        while (DB::table('credit_note')->where('uid', $uid)->where('school_id',Auth::user()->school_id)->exists()) {
            $uid++; 
        }

        return $uid;
    }

    public function creditNoteStore(Request $request) {
        ResponseService::noFeatureThenRedirect('Fees Management');
        ResponseService::noPermissionThenRedirect('fees-create');
        $request->validate([
            'class_id'                                => 'required|numeric',
            'student_id'                              => 'required',
            'invoice_no'                              => 'required',
            'credit_note_types'                       => 'required|array',
            'credit_note_types.*'                     => 'required|array',
            'credit_note_types.*.credit_note_name'    => 'required',
            'credit_note_types.*.credit_note_amount'  => 'required|numeric',
            'credit_note_types.*.classification_code'                     => 'required',
            'credit_note_types.*.unit'                                    => 'required',
            'credit_note_types.*.quantity'                                => 'required'
        ]);

        if(isset($request->tax_type) && $request->tax_percentage == null) {
            $request->validate([
                'tax_percentage' => 'required|numeric',
            ]);
        }



    

        try {
            $classData = $this->class->builder()->find($request->class_id);
            DB::beginTransaction();

            $creditDetails = [];
            $sessionYear = $this->cache->getDefaultSessionYear();
            if ($request->student_id) {
                $uid = $this->generateUniqueUid();
                $data = [
                    'class_id'          => $request->class_id,
                    'student_id'        => $request->student_id,
                    'student_fee_id'    => $request->invoice_no,
                    'school_id'         => $classData->school_id,
                    'session_year_id'   => $sessionYear->id,
                    'status'            => 'draft',
                    'uid'               => $uid,
                    'tax_type'          => $request->tax_type,
                    'tax_percentage'    => $request->tax_percentage?? '0.00',
                ];
                $credit_note_id = DB::table('credit_note')->insertGetId($data);
                foreach ($request->credit_note_types as $data) {
                    $creditDetails[] = array(
                        'school_id'             => $classData->school_id,
                        'credit_note_id'        => $credit_note_id,
                        'classification_code'   => $data['classification_code'],
                        'unit'                  => $data['unit'],
                        'quantity'              => $data['quantity'],
                        "credit_note_name"        => $data['credit_note_name'],
                        "credit_note_amount"      => $data['credit_note_amount'],
                        "tax"                => $data['tax_percentage']
                    );
                }
            }
            if (count($creditDetails) > 0) {
                DB::table('credit_note_details')->insert($creditDetails);
            }

            DB::commit();
            ResponseService::successResponse('Data Stored Successfully');
        } catch (Throwable $e) {
            DB::rollback();
            ResponseService::logErrorResponse($e,"StudentFeeTypesController -> Store Method");
            ResponseService::errorResponse();
        }
    }

    public function creditNoteShow(){
        ResponseService::noFeatureThenRedirect('Fees Management');
        ResponseService::noPermissionThenRedirect('fees-list');
        $offset = request('offset', 0);
        $limit = request('limit', 99999);
        $sort = request('sort', 'id');
        $order = request('order', 'DESC');
        $search = request('search');
        $showDeleted = request('show_deleted');
        $start_date = request('start_date');
        $start_date = $start_date ? Carbon::createFromFormat('d-m-Y', $start_date)->startOfDay()->format('Y-m-d H:i:s') : null;
        $end_date = request('end_date');
        $end_date = $end_date ? Carbon::createFromFormat('d-m-Y', $end_date)->endOfDay()->format('Y-m-d H:i:s') : null;
        // $classId = request('class_id');

        // $sessionYear = $this->cache->getDefaultSessionYear();


        $sql = DB::table('credit_note as cn')
        ->join('students as s', 's.id', '=', 'cn.student_id')
        ->join('users as u', 'u.id', '=', 's.user_id')
        ->leftJoin('credit_note_details as cnd','cnd.credit_note_id','=','cn.id')
        ->select(
            DB::raw("CONCAT_WS(' ', u.first_name, u.last_name) AS full_name"),
            'cn.*',
            DB::raw("
            FORMAT(
                SUM(
                    (cnd.credit_note_amount + (cnd.credit_note_amount * IFNULL(cnd.tax, 0) / 100)) * cnd.quantity
                ), 
                2
            ) AS total_amount,            
            FORMAT(cnd.tax, 2) AS tax
        ")
        )
        // ->where('cn.session_year_id', $sessionYear->id)
        ->where('u.school_id', Auth::user()->school_id)
        ->whereNull('u.deleted_at')
        ->when($start_date && $end_date, function ($query) use ($start_date, $end_date) {
            $query->whereBetween('cn.created_at', [$start_date, $end_date]);
        })
        ->when($start_date && !$end_date, function ($query) use ($start_date) {
            $query->where('cn.created_at', '>=', $start_date);
        })
        ->when(!$start_date && $end_date, function ($query) use ($end_date) {
            $query->where('cn.created_at', '<=', $end_date);
        })
        ->when(!$showDeleted, function ($query) {
            $query->whereNull('cn.deleted_at');
        }, function ($query) {
            $query->whereNotNull('cn.deleted_at');
        })->groupBy('cn.id');

        // if($classId){
        //     $sql->where('cn.class_id',$classId);
        // }

        if($search) {
            $sql->where(function ($query) use ($search) {
                $query->where(DB::raw("CONCAT_WS(' ', u.first_name, u.last_name)"), 'LIKE', "%$search%")
                ->orWhere('cn.status', 'LIKE', "%$search%")
                ->orWhere(DB::raw("CONCAT('CN', LPAD(cn.uid, 6, '0'))"), 'LIKE', "%$search%");
            });
        }
        
        $total = $sql->count();
        $sql->orderBy($sort, $order)->skip($offset)->take($limit);
        $res = $sql->get();
        $bulkData = array();
        $bulkData['total'] = $total;
        $rows = array();
        $no = 1;
        foreach ($res as $row) {
            $e_invoice_status = false;
            $operate = '';
            $creditNoteEinvoice=DB::table('credit_note_einvoice')->where('credit_note_id',$row->id)->orderBy('id','DESC')->first();

            if ($showDeleted) {
                $operate .= BootstrapTableService::restoreButton(route('student-fee-types.credit-note-restore', $row->id));
            } else {
                $operate .= BootstrapTableService::menuEditButton('edit',route('student-fee-types.credit-note-edit', $row->id),false);
                $operate .= BootstrapTableService::menuButton('invoice', route('student-fee-types.credit-note-pdf', ['id' => $row->id]), ['generate-credit-note-pdf'], ['target' => "_blank", 'data-id' => $row->id]);
                $operate .= BootstrapTableService::menuButton('Update Status','javascript:doSetCreditNoteStatus('. $row->id.');');                    
                // $operate .= BootstrapTableService::menuButton('Cancel e-invoice',route('student-fee-types.credit-note-delete', $row->id));
                if (!isset($creditNoteEinvoice) || $creditNoteEinvoice->status != 1) {
                    $operate .= BootstrapTableService::menuDeleteButton('delete',route('student-fee-types.credit-note-delete', $row->id));
                }
                
                if(isset($creditNoteEinvoice) && $creditNoteEinvoice->status == 1){
                    $operate .= BootstrapTableService::menuButton('cancel e-invoice',route('student-fee-types.cancelCreditEinvoice',['id' => $row->id]),['cancel-credit-einvoice']);
                } else if($row->status == 'published' && (!isset($creditNoteEinvoice) || $creditNoteEinvoice->status != 0)){ 
                    $operate .= BootstrapTableService::menuButton('submit e-invoice',route('student-fee-types.submitCreditEinvoice',['id' => $row->id]),['submit-credit-einvoice']);
                }

                if(isset($creditNoteEinvoice)){
                    $operate .= BootstrapTableService::menuButton('check_status',route('student-fee-types.check-credit-note-e-invoice-status',['id' => $row->id]),['check-credit-note-e-invoice-status']);
                }
            }
            $e_invoice_exist = DB::table('e_invoice_guardian')->join('students', 'students.guardian_id', '=', 'e_invoice_guardian.guardian_id')->where('e_invoice_guardian.school_id',Auth::user()->school_id)->where('students.id', $row->student_id)->select('e_invoice_guardian.guardian_id','e_invoice_guardian.sql_code')->first();
            if ($e_invoice_exist) {
                $incompleteDataCount = DB::table('e_invoice_guardian')
                    ->where('guardian_id', $e_invoice_exist->guardian_id)
                    ->where(function ($query) {
                        $query->whereNull('name')
                            ->orWhere('name', '')
                            ->orWhereNull('ic_no')
                            ->orWhere('ic_no', '')
                            ->orWhereNull('tax_identification_number')
                            ->orWhere('tax_identification_number', '')
                            ->orWhereNull('address')
                            ->orWhere('address', '')
                            ->orWhereNull('city')
                            ->orWhere('city', '')
                            ->orWhereNull('postal_code')
                            ->orWhere('postal_code', '')
                            ->orWhereNull('country')
                            ->orWhere('country', '')
                            ->orWhereNull('state')
                            ->orWhere('state', '');
                    })
                    ->count();
                if ($incompleteDataCount === 0) {
                    $e_invoice_status = true;
                }
            }
            $tempRow = [];
            $totalAmount = $row->total_amount;
            $tempRow['submitted_e_invoice'] = isset($creditNoteEinvoice->status) ? $creditNoteEinvoice->status: null;
            $tempRow ['e_invoice_status'] = $e_invoice_status;
            $tempRow['no'] = $no++ ?? '';
            $tempRow['id'] = $row->id ?? '';
            $tempRow['full_name'] = $row->full_name ?? '';
            $tempRow['student_id'] = $row->student_id ?? '';
            $tempRow['credit_note_no'] =  ('CN'.sprintf('%06d', $row->uid));
            // $amount = DB::table('credit_note_details')->where('credit_note_id', $row->id)->selectRaw('SUM(credit_note_amount * quantity) as total')->value('total');
            $tempRow['tax'] = $row->tax;
            if(isset($row->tax_percentage) && $row->tax_percentage > 0){
                $extraTax = ($totalAmount * ($row->tax_percentage / 100));
                $totalAmount += $extraTax;
                $tempRow['extra_tax'] = number_format($extraTax,2);
            }
            $tempRow['total_amount'] = number_format($totalAmount,2);
            $tempRow['status'] = $row->status ?? '';
            $tempRow['created_at'] = date('d-m-Y', strtotime($row->created_at));
            $tempRow['operate'] = BootstrapTableService::menuItem($operate);
            $rows[] = $tempRow;
        }
        $bulkData['rows'] = $rows;
        return response()->json($bulkData);
    }

    public function creditNoteEdit($id) {
        ResponseService::noFeatureThenRedirect('Fees Management');
        ResponseService::noPermissionThenRedirect('fees-edit');

        $student = DB::table('students as s')
                   ->join('credit_note as cn','cn.student_id','=','s.id')
                   ->join('users as u','u.id','=','s.user_id')
                   ->join('class_sections as cs','cs.id','=','s.class_section_id')
                   ->where('cn.id',$id)
                   ->select(DB::raw("CONCAT_WS(' ', u.first_name, u.last_name) AS full_name"),'s.id','cs.class_id','s.school_id')
                   ->first();
        $creditNoteDetails = DB::table('credit_note_details')
                    ->join('credit_note','credit_note.id','=','credit_note_details.credit_note_id')
                    ->where('credit_note_id',$id)
                    ->select('credit_note.uid','credit_note_details.*')
                    ->get();
        
        $creditNote = DB::table('credit_note')->where('id',$id)->first();

        $itemCode = DB::table('fees_type_master')->where('school_id',Auth::user()->school_id)->get();
        return view('student-fee-types.credit_note_edit', compact('student','creditNoteDetails','itemCode','creditNote'));
    }

    public function creditNoteDetailDelete($id){
        if($id){
            DB::table('credit_note_details')->where('id',$id)->delete();
            DB::commit();
            ResponseService::successResponse('Data Deleted Successfully');
        }
    }

    public function creditNoteUpdate(Request $request, $id) {
        ResponseService::noFeatureThenRedirect('Fees Management');
        ResponseService::noPermissionThenRedirect('fees-edit');
        $request->validate([
            'credit_note_details'                       => 'required|array',
            'credit_note_details.*'                     => 'required|array',
            'credit_note_details.*.credit_note_name'    => 'required',
            'credit_note_details.*.credit_note_amount'  => 'required|numeric',
            'credit_note_details.*.classification_code'                     => 'required',
            'credit_note_details.*.unit'                                    => 'required',
            'credit_note_details.*.quantity'                                => 'required'
        ]);

        
        if(isset($request->tax_type) && $request->tax_percentage == null) {
            $request->validate([
                'tax_percentage' =>'required|numeric',
            ]);
        }

        try {
            DB::beginTransaction();
            $creditNote = [];
            foreach ($request->credit_note_details as $data) {
                if(isset($data['id'])){
                    $updateData = [
                        "credit_note_name"        => $data['credit_note_name'],
                        "credit_note_amount"      => $data['credit_note_amount'],
                        'classification_code'   => $data['classification_code'],
                        'unit'                  => $data['unit'],
                        'quantity'              => $data['quantity'],
                        'tax'                   => $data['tax'],
                    ];
                    DB::table('credit_note_details')->where('id',$data['id'])->update($updateData);
                } else {
                    $creditNote[] = array(
                        'school_id'             => $request->school_id,
                        'credit_note_id'        => $id,
                        "credit_note_name"        => $data['credit_note_name'],
                        "credit_note_amount"      => $data['credit_note_amount'],
                        'classification_code'   => $data['classification_code'],
                        'unit'                  => $data['unit'],
                        'quantity'              => $data['quantity'],
                        'tax'                   => $data['tax'],
                    );
                }
            }
            if (isset($creditNote)) {
                DB::table('credit_note_details')->insert($creditNote);
            }

            $taxUpdate = [
                'tax_type'          => $request->tax_type,
                'tax_percentage'    => $request->tax_percentage,
            ];
            DB::table('credit_note')->where('id',$id)->update($taxUpdate);

            DB::commit();
            ResponseService::successRedirectResponse(route('student-fee-types.credit-note'), 'Data Update Successfully');
        } catch (Throwable $e) {
            DB::rollback();
            ResponseService::errorRedirectResponse();
        }
    }

    public function creditNoteDelete($id){
        ResponseService::noFeatureThenRedirect('Fees Management');
        ResponseService::noPermissionThenSendJson('fees-delete');

        try {
            DB::beginTransaction();
            DB::table('credit_note')->where('id',$id)->update(["deleted_at" => now()]);
            DB::commit();
            ResponseService::successResponse("Data Deleted Successfully");
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function creditNoteRestore($id){
        ResponseService::noFeatureThenRedirect('Fees Management');

        try {
            DB::beginTransaction();
            DB::table('credit_note')->where('id',$id)->update(["deleted_at" => null]);
            DB::commit();
            ResponseService::successResponse("Data Restored Successfully");
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function creditNotePdf($id) {
        ResponseService::noFeatureThenRedirect('Fees Management');
        ResponseService::noPermissionThenRedirect('fees-list');
        try {
            $credit_note = DB::table('credit_note')->where('credit_note.id',$id)
            ->join('student_fees','student_fees.id','=','credit_note.student_fee_id')
            ->select('credit_note.*','student_fees.uid as sf_uid')
            ->first();
            $credit_note_details = DB::table('credit_note_details')->where('credit_note_id',$id)->get();
            $student = DB::table('students as s')
                    ->join('users as u','u.id','=','s.user_id')
                    ->join('class_sections as cs','cs.id','=','s.class_section_id')
                    ->join('classes as c','c.id','=','cs.class_id')
                    ->where('s.id',$credit_note->student_id)
                    ->select(DB::raw("CONCAT_WS(' ', u.first_name, u.last_name) AS full_name"),'s.*','c.name as class_name','u.current_address')
                    ->first();
            $credit_note_einvoice=DB::table('credit_note_einvoice')->where('credit_note_id',$id)->orderBy('id','DESC')->first();
         
            $parent = DB::table('users')->where('id',$student->guardian_id)->first();
            
            $e_invoice_guardian = DB::table('e_invoice_guardian')->where('guardian_id',$student->guardian_id)->where('school_id',Auth::user()->school_id)->where('status',1)->first();

            $school = $this->cache->getSchoolSettings();
            $schoolSettings = DB::select("SELECT data FROM school_settings WHERE name = 'horizontal_logo' AND school_id = ".$student->school_id);
            $schoolLogo = '';
            if(COUNT($schoolSettings)){
                $schoolLogo = $schoolSettings[0]->data;
            }

            if(isset($credit_note_einvoice) && $credit_note_einvoice->status != 0){
                $documentSummary = json_decode($credit_note_einvoice->document_summary);
                // dd($documentSummary);
                $url = env('E_INVOICE_URL').'/'.$credit_note_einvoice->uuid.'/share/'.$documentSummary[0]->longId;
                // dd($url);
                $credit_note_einvoice->e_invoice_url = (new QRCode)->render($url);
            }

            if(isset($credit_note->tax_percentage) && $credit_note->tax_percentage > 0){
                $taxTypeJSON = json_decode(file_get_contents('assets/JSON/eInvoice/TaxTypes.json'),true);
                foreach ($taxTypeJSON as $tax) {
                    if (isset($tax['Code']) && $tax['Code'] === $credit_note->tax_type) {
                        $credit_note->tax_type = $tax['Description'];
                        break;
                    }
                }
            }
    
            $pdf = Pdf::loadView('student-fee-types.credit_note_invoice', compact('credit_note', 'credit_note_details', 'student', 'parent', 'school', 'schoolSettings', 'schoolLogo','credit_note_einvoice','e_invoice_guardian'));
           

            return $pdf->stream('credit-note-invoice.pdf');
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
            return false;
        }
    }

    public function creditNoteStatus(Request $request){
        ResponseService::noFeatureThenRedirect('Fees Management');
        ResponseService::noPermissionThenRedirect('fees-list');

        $credit_note_id = $request->id;
        if($credit_note_id && $request->status){
            if($request->status == 'draft'){
                DB::table('credit_note')->where('id',$credit_note_id)->update(['status' => $request->status,'date' => null]);
            } else {
                DB::table('credit_note')->where('id',$credit_note_id)->update(['status' => $request->status,'date' => date('Y-m-d')]);
                $credit_note = DB::table('credit_note as cn')
                            ->join('credit_note_details as cnd', 'cn.id', '=', 'cnd.credit_note_id')
                            ->select('cn.*', DB::raw('SUM(cnd.credit_note_amount) as total_amount'))
                            ->where('cn.id', $credit_note_id)
                            ->groupBy('cn.id')
                            ->first();
                $guardian_id = DB::table('students as s')
                                ->join('credit_note as cn','cn.student_id','=','s.id')
                                ->where('cn.id',$credit_note_id)
                                ->pluck('s.guardian_id')
                                ->first();
                if($guardian_id){
                    $title = 'Credit Note Issued';
                    $body = 'A credit note has been issued for your account.';
                    $type = "Credit Note";
                    send_notification([$guardian_id], $title, $body, $type);
                }
            }
            return response()->json([
                'error'   => false,
                'message' => "Status Set Successful:"
            ]);
            
        }
    }
    public function submitCreditEInvoicing($id) {
        // Check if e-invoice already submitted
        $creditNoteSubmitted = DB::table('credit_note_einvoice')->where('credit_note_id',$id)->where('status',1)->first();
        if($creditNoteSubmitted) {
            $cre_number = DB::table('credit_note')->where('id',$id)->select('uid')->first();
            $cre_number = str_pad($cre_number->uid, 8, '0', STR_PAD_LEFT);
            ResponseService::errorResponse("E-Invoice already submitted and validated for INV".$cre_number);
        }
    
        // Get credit note data
        $creditNote = DB::table('credit_note')->where('id',$id)->first();
        $creditNoteDetails = DB::table('credit_note_details')->where('credit_note_id',$id)->get();
    
        $invoiceId = $creditNote->invoice_number ?? 'CN' . sprintf('%09d', $creditNote->uid ?? '');
       
        // Calculate monetary totals with rounding
        $lineExtensionAmount = 0;
        $taxExclusiveAmount = 0;
        $taxInclusiveAmount = 0;
        $allowanceTotalAmount = round($creditNote->early_offer_amount ?? 0, 2);
        $chargeTotalAmount = round($creditNote->due_charges_amount ?? 0, 2);
        $totalTaxAmount = 0;
    
        foreach ($creditNoteDetails as $detail) {
            // if (in_array($detail->credit_note_name, ['Early Discount', 'Overdue Fees'])) {
            //     $detail->credit_note_amount = 0;
            // }
            
            $priceExtensionAmount = round(
                ($detail->credit_note_amount * $detail->quantity),
                2
            );
            
            $taxAmount = round($priceExtensionAmount * ($detail->tax / 100), 2);
    
            $lineExtensionAmount += $priceExtensionAmount;
            $taxExclusiveAmount += $priceExtensionAmount;
            $taxInclusiveAmount += ($priceExtensionAmount + $taxAmount);
            $totalTaxAmount += $taxAmount;
        }
    
        // Apply final rounding to all amounts
        $creditNote->line_extension_amount = round($lineExtensionAmount, 2);
        $creditNote->tax_exclusive_amount = round($taxExclusiveAmount, 2);
        $creditNote->tax_inclusive_amount = round($taxInclusiveAmount, 2);
        $creditNote->allowance_total_amount = $allowanceTotalAmount;
        $creditNote->charge_total_amount = $chargeTotalAmount;
        $creditNote->payable_rounding_amount = 0;
        $creditNote->payable_amount = round(
            $creditNote->total_amount ?? ($taxInclusiveAmount - $allowanceTotalAmount + $chargeTotalAmount),
            2
        );
        $creditNote->total_tax_amount = round($totalTaxAmount, 2);
        $creditNote->total_taxable_amount = round($taxExclusiveAmount, 2);
        $creditNote->total_tax_percent = ($taxExclusiveAmount > 0)
            ? round(($totalTaxAmount / $taxExclusiveAmount) * 100, 2)
            : 0;
    
        $student = $this->student->builder()->where('id',$creditNote->student_id)->first();
        $e_invoice = DB::table('e_invoice')->where('school_id',Auth::user()->school_id)->first();
        $school = DB::table('schools')->where('id',Auth::user()->school_id)->first();
    
        if(!$e_invoice || $e_invoice->status != 1) {
            ResponseService::errorResponse("School's TIN number not verified");
        }
    
        // Get guardian e-invoice data
        $e_invoice_guardian = DB::table('e_invoice_guardian')->where('guardian_id',$student->guardian_id)
            ->where('school_id',Auth::user()->school_id)->first();
        
        if($e_invoice_guardian && $e_invoice_guardian->status != 1) {
            ResponseService::errorResponse("TIN number not verified");
        }
    
        $supplierAddressLines = array_filter([
            trim(preg_replace('/\s+/', ' ', $school->address ?? '')),
            // $school->address2 ?? null,
            // $school->address3 ?? null
        ]);
    
        $customerAddressLines = array_filter([
            trim(preg_replace('/\s+/', ' ', $e_invoice_guardian->address ?? '')),
            // $e_invoice_guardian->address2 ?? null,
            // $e_invoice_guardian->address3 ?? null
        ]);
    
        $studentFee = \App\Models\StudentFee::with('student_fees_details')->find($creditNote->student_fee_id);

        $eInvoiceUuid = DB::table('student_fees_einvoice')
            ->where('student_fees_id', $creditNote->student_fee_id)
            ->value('uuid');

        $eInvoiceInternalId = DB::table('student_fees_einvoice')
            ->where('student_fees_id', $creditNote->student_fee_id)
            ->value('invoice_code_number');

        // Prepare invoice data structure
        $invoiceData = [
            'invoice_type' => 'credit_note',
            'id' => $invoiceId,
            'billing_references' => [
                'invoice_document' => [
                    'id' => $eInvoiceInternalId ?? '',
                    'uuid' => $eInvoiceUuid ?? '',
                ]
            ],
            'supplier' => [
                'address' => [
                    'city' => $e_invoice->city ?? '',
                    'postcode' => $e_invoice->postal_code ?? '',
                    'country_subentity_code' => $e_invoice->state ?? ''
                ],
                'address_line' => $supplierAddressLines,
                'country_code' => 'MYS',
                'legal_entity' => $school->name ?? '',
                'contact_phone' => $school->support_phone ?? '',
                'contact_email' => $school->support_email ?? '',
                'msic_code' => $e_invoice->company_msic_code ?? '',
                'party_identification' => [
                    'TIN' => $e_invoice->tax_identification_number ?? '',
                    ($e_invoice->id_type == 'NRIC' ? 'NRIC' :
                     ($e_invoice->id_type == 'BRN' ? 'BRN' : 'ID'))
                        => $e_invoice->registration_id_passport_number ?? ''
                ],
            ],
            'customer' => [
                'address' => [
                    'city' => $e_invoice_guardian->city ?? ($student->guardian->city ?? ''),
                    'postcode' => $e_invoice_guardian->postal_code ?? ($student->guardian->postcode ?? ''),
                    'country_subentity_code' => $e_invoice_guardian->state ?? ($student->guardian->state_code ?? '')
                ],
                'address_line' => $customerAddressLines,
                'country_code' => $e_invoice_guardian->country ?? ($student->guardian->country_code ?? 'MYS'),
                'legal_entity' => $e_invoice_guardian->name ?? ($student->guardian->name ?? ''),
                'contact_phone' => $e_invoice_guardian->contact_no ?? ($student->guardian->phone ?? ''),
                'contact_email' => $e_invoice_guardian->email ?? ($student->guardian->email ?? ''),
                'party_identification' => [
                    'TIN' => $e_invoice_guardian->tax_identification_number ?? '',
                    'NRIC' => $e_invoice_guardian->ic_no ?? ''
                ],
            ],
            'document_line' => $creditNoteDetails->map(function ($creditNoteDetail) use ($school) {
                $itemCode = DB::table('fees_type_master')
                    ->where('school_id', $school->id)
                    ->where('name', $creditNoteDetail->credit_note_name)
                    ->value('item_code');
    
                if (empty($itemCode)) {
                    $itemCode = DB::table('fees_type_master')
                        ->where('school_id', $school->id)
                        ->where('classification_code', $creditNoteDetail->classification_code)
                        ->value('item_code');
                }
    
                if (empty($itemCode)) {
                    return null;
                }
    
                $priceExtensionAmount = round(
                    ($creditNoteDetail->credit_note_amount * $creditNoteDetail->quantity),
                    2
                );
                
                $priceExtensionAmountTrue = round($creditNoteDetail->quantity * $creditNoteDetail->credit_note_amount, 2);
                $taxTotalAmount = round($priceExtensionAmount * ($creditNoteDetail->tax / 100), 2);
    
                return [
                    'id' => $itemCode ?? '',
                    'quantity' => $creditNoteDetail->quantity ?? 1,
                    'unit' => !empty($creditNoteDetail->unit) ? explode(' - ', $creditNoteDetail->unit)[0] : '',
                    'line_amount' => $priceExtensionAmount ?? 0,
                    'item' => [
                        'description' => $creditNoteDetail->credit_note_name ?? '',
                        'classifications' => [
                            [
                                'code' => '010',
                                'type' => 'CLASS',
                            ]
                        ]
                    ],
                    'price' => [
                        'amount' => round($creditNoteDetail->credit_note_amount ?? 0, 2)
                    ],
                    'price_extension' => [
                        'amount' => $priceExtensionAmountTrue
                    ],
                    'tax_total' => [
                        'amount' => $taxTotalAmount
                    ],
                    'tax_sub_totals' => [
                        [
                            'taxable_amount' => $priceExtensionAmount,
                            'tax_amount' => $taxTotalAmount,
                            'percent' => (float) $creditNoteDetail->tax ?? 0,
                            'tax_scheme' => [
                                'id' => 'OTH'
                            ],
                            'tax_category' => [
                                'id' => '01',
                                'percent' => (float) $creditNoteDetail->tax ?? 0,
                                'tax_exemption_reason' => 'None'
                            ]
                        ]
                    ],
                ];
            })->filter()->values()->toArray(),
    
            "legal_monetary_total"=> [
                'line_extension_amount' => $creditNote->line_extension_amount,
                'tax_exclusive_amount' => $creditNote->tax_exclusive_amount,
                'tax_inclusive_amount' => $creditNote->tax_inclusive_amount,
                'allowance_total_amount' => $creditNote->allowance_total_amount,
                'charge_total_amount' => $creditNote->charge_total_amount,
                'payable_rounding_amount' => $creditNote->payable_rounding_amount,
                'payable_amount' => $creditNote->payable_amount
            ],
    
            'allowance_charges' => [
                [
                    'charge_indicator' => true,
                    'reason' => '',
                    'amount' => 0
                ],
            ],

            $totalTaxAmount = $creditNote->tax_inclusive_amount * ($creditNote->tax_percentage / 100),
    
            'tax_total' => [
                'tax_amount' => round($totalTaxAmount, 2),
                'tax_sub_totals' => [
                    [
                        'taxable_amount' => round($creditNote->tax_inclusive_amount, 2),
                        'tax_amount' => round($totalTaxAmount, 2),
                        'percent' => round($creditNote->tax_percentage, 2),
                        'tax_category' => [
                            'id' => $creditNote->tax_type ?? '06',
                            'tax_exemption_reason' => 'None'
                        ],
                        'tax_scheme' => [
                            'id' => 'OTH'
                        ]
                    ]
                ]
            ]
        ];
    
        // Get school settings for API credentials
        $schoolSettings = $this->cache->getSchoolSettings();
        if(!$schoolSettings || !$schoolSettings['client_id'] || empty($schoolSettings['client_secret_1'])) {
            ResponseService::errorResponse("School API credentials not configured");
        }
    
        $schoolId = auth()->user()->school_id;
    
        // New submission logic
        $submitDocument = null;
        $invoice = null;
    
        try {
            $data = [
                'invoice_type' => 'credit_note',
                'id' => $invoiceId,
                'billing_references' => $invoiceData['billing_references'],
                'supplier' => $invoiceData['supplier'],
                'customer' => $invoiceData['customer'],
                'document_line' => $invoiceData['document_line'],
                'legal_monetary_total' => $invoiceData['legal_monetary_total'],
                'allowance_charges' => $invoiceData['allowance_charges'],
                'tax_total' => $invoiceData['tax_total'],
            ];
    
            $invoiceType = EInvoiceHelper::mapInvoiceTypeCode($data['invoice_type']);
            //\Log::info('Invoice Type:', [$invoiceType]);

            $invoice = EInvoiceHelper::createXmlDocument($invoiceType, $data);
            //\Log::info('Invoice:', [$invoice]);
    
            $documents = [];
            $document = MyInvoisHelper::getSubmitDocument($id, $invoice);
            $documents[] = $document;

            $signatureValue = '';
            $dom = new DOMDocument();
            $dom->loadXML($invoice);
            $xpath = new DOMXPath($dom);
            $xpath->registerNamespace('ds', 'http://www.w3.org/2000/09/xmldsig#');
            $xpath->registerNamespace('cac', 'urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2');
            $xpath->registerNamespace('ext', 'urn:oasis:names:specification:ubl:schema:xsd:CommonExtensionComponents-2');
            $signatureValueNodes = $xpath->query('//ds:SignatureValue');
            if ($signatureValueNodes->length > 0) {
                $signatureValue = $signatureValueNodes->item(0)->nodeValue;
            }
            
            $submissionResult = EInvoiceHelper::submitDocument($schoolId, $documents);
            


            if ($submissionResult['success'] == true) {
                \Log::info('E-Invoice Submission Data: '. json_encode($data));
                //\Log::info('E-Invoice Submission Result: '. json_encode($submissionResult));
            }
    
        } catch (\Exception $exception) {
            $errorMessage = $exception->getMessage();
            $submitDocument = json_decode($errorMessage);
    
            if (json_last_error() !== JSON_ERROR_NONE) {
                $submitDocument = $errorMessage;
            }
    
            \Log::error('E-Invoice Submission Exception: ' . $errorMessage, ['exception' => $exception]);
            return ResponseService::errorResponse("An error occurred during e-invoice submission: " . $errorMessage);
        }
    
        if(isset($submissionResult['success']) && $submissionResult['success'] == true) {
            $resultData = $submissionResult['data'] ?? null;
    
            if ($resultData) {
                $dataToInsert = [
                    'credit_note_id' => $id,
                    'submission_uid' => null,
                    'rejected_documents' => null,
                    'uuid' => null,
                    'credit_note_number' => null,
                ];
    
                if (is_object($resultData)) {
                    $dataToInsert['submission_uid'] = $resultData->submissionUid ?? null;
                    $dataToInsert['rejected_documents'] = !empty($resultData->rejectedDocuments) ? json_encode($resultData->rejectedDocuments) : null;
    
                    if(isset($resultData->acceptedDocuments) && is_array($resultData->acceptedDocuments) && count($resultData->acceptedDocuments) > 0) {
                        foreach($resultData->acceptedDocuments as $document) {
                            if(isset($document->uuid)) $dataToInsert['uuid'] = $document->uuid;
                            if(isset($document->invoiceCodeNumber)) $dataToInsert['credit_note_number'] = $invoiceId;
                        }
                    }
                } elseif (is_array($resultData)) {
                    $dataToInsert['submission_uid'] = $resultData['submissionUid'] ?? null;
                    $dataToInsert['rejected_documents'] = !empty($resultData['rejectedDocuments']) ? json_encode($resultData['rejectedDocuments']) : null;
    
                    if(isset($resultData['acceptedDocuments']) && is_array($resultData['acceptedDocuments']) && count($resultData['acceptedDocuments']) > 0) {
                        foreach($resultData['acceptedDocuments'] as $document) {
                            if(isset($document['uuid'])) $dataToInsert['uuid'] = $document['uuid'];
                            if(isset($document['invoiceCodeNumber'])) $dataToInsert['credit_note_number'] = $invoiceId;
                        }
                    }
                }
    
                try {
                    DB::table('credit_note_einvoice')->insert($dataToInsert);

                    DB::table('credit_note')->where('id', $id)->update(['sign_value' => $signatureValue]);

                    // Call getEInvoiceStatus to immediately check and update the status
                    return $this->getCreditNoteEInvoiceStatus($id);
                    
                } catch (\Illuminate\Database\QueryException $ex) {
                    \Log::error('Database Insert Error: '. $ex->getMessage(), ['query' => $ex->getSql(), 'bindings' => $ex->getBindings()]);
                    return ResponseService::errorResponse("E-Invoice submitted, but failed to save e-invoice data to database. " . $ex->getMessage());
                }
    
            } else {
                \Log::warning('E-Invoice submission successful, but resultData is null or empty.');
                return ResponseService::errorResponse($submissionResult['message'] ?? "E-Invoice submission successful, but response data is missing or invalid.");
            }
        } else {
            $errorMessage = "Failed to submit e-invoice.";
            if (isset($submissionResult['message']) && !empty($submissionResult['message'])) {
                $errorMessage = $submissionResult['message'];
            }
            \Log::error('E-Invoice Submission Failed: ' . $errorMessage, $submissionResult);
            return ResponseService::errorResponse($errorMessage);
        } 
    }

    public function getCreditNoteEInvoiceStatus($id){
        try {
            $creditNoteEinvoice = DB::table('credit_note_einvoice')->where('credit_note_id', $id)->orderByDesc('id')->first();
            
            if (!$creditNoteEinvoice) {
                return ResponseService::errorResponse("No e-invoice submission found for this credit note");
            }
            
            // Check if submission_uid exists
            if (empty($creditNoteEinvoice->submission_uid)) {
                return ResponseService::errorResponse("No submission UID found for this e-invoice");
            }
            
            // Log the submission UID being used
            //\Log::info('Checking e-invoice status with submission UID: ' . $creditNoteEinvoice->submission_uid);
            
            // Make sure to pass the exact submission_uid from the database
            $result = EInvoiceHelper::getSubmission(Auth::user()->school_id, $creditNoteEinvoice->submission_uid);
            
            if ($result['success']) {
                $submissionData = $result['data'];
                $status = 0; // Default: pending
                
                // Log the submission data received
                //\Log::info('Submission data received: ' . json_encode($submissionData));
                
                if ($submissionData['overallStatus'] == 'Invalid') {
                    $status = 2; // Invalid
                } else if ($submissionData['overallStatus'] == 'Valid') {
                    $status = 1; // Valid
                }
                
                $updateData = [
                    'status' => $status,
                    'document_summary' => json_encode($submissionData['documentSummary'])
                ];
                
                if (!empty($submissionData['documentSummary'])) {
                    $documentSummary = $submissionData['documentSummary'][0];
                    if (isset($documentSummary['uuid'])) {
                        $updateData['uuid'] = $documentSummary['uuid'];
                    }
                    
                    // if (isset($documentSummary['longId'])) {
                    //     $updateData['long_id'] = $documentSummary['longId'];
                    // }
                    
                    // if (isset($documentSummary['internalId'])) {
                    //     $updateData['internal_id'] = $documentSummary['internalId'];
                    // }
                }
                
                if ($status == 2) {
                    $updateData['deleted_at'] = now();
                }
                
                DB::table('credit_note_einvoice')
                    ->where('id', $creditNoteEinvoice->id)
                    ->update($updateData);
                    
                return ResponseService::successResponse("E-Invoice status updated successfully", [
                    'status' => $status,
                    'status_text' => $submissionData['overallStatus']
                ]);
            } else {
                \Log::error('Error in e-invoice submission validation: ' . json_encode($result));
                \Log::error('Full result from EInvoiceHelper::getSubmission: ' . json_encode($result));
                return ResponseService::errorResponse("Failed to retrieve e-invoice status: " . ($result['message'] ?? "Unknown error"));
            }
        } catch (\Throwable $th) {
            \Log::error('Error in getting e-invoice status: ' . $th->getMessage(), [
                'exception' => $th,
                'stack_trace' => $th->getTraceAsString(),
            ]);
            return ResponseService::errorResponse("An error occurred while retrieving e-invoice status");
        }
    }
  
    public function cancelCreditEInvoicing($id, Request $request) {
        // Validate request
        if (empty($request->reason)) {
            return ResponseService::errorResponse("Cancellation reason is required");
        }
        
        // Get e-invoice record
        $creditNoteEinvoice = DB::table('credit_note_einvoice')
            ->where('credit_note_id', $id)
            ->where('status', 1)
            ->first();
        
        if (!$creditNoteEinvoice) {
            return ResponseService::errorResponse("No valid e-invoice found for this credit note");
        }
        
        if (empty($creditNoteEinvoice->uuid)) {
            return ResponseService::errorResponse("E-invoice UUID not found");
        }
        
        // Call the helper method to cancel the document
        $result = EInvoiceHelper::cancelDocument(
            Auth::user()->school_id, 
            $creditNoteEinvoice->uuid, 
            $request->reason
        );
        
        if ($result['success']) {
            // Update the database record
            DB::table('credit_note_einvoice')
                ->where('id', $creditNoteEinvoice->id)
                ->update([
                    'status' => 3,
                    'cancel_reason' => $request->reason,
                    'deleted_at' => now()
                ]);
            
            return ResponseService::successResponse("E-Invoice cancelled successfully");
        } else {
            // Handle error
            $errorMessage = $result['message'] ?? "Failed to cancel e-invoice";
            if (isset($result['errors']) && is_object($result['errors'])) {
                if (isset($result['errors']->error) && isset($result['errors']->error->details[0]->message)) {
                    $errorMessage = $result['errors']->error->details[0]->message;
                } else if (isset($result['errors']->message)) {
                    $errorMessage = $result['errors']->message;
                }
            }
            \Log::error('Full result from EInvoiceHelper::cancelDocument: ' . json_encode($result));
            return ResponseService::errorResponse($errorMessage);
        }
    }

}



