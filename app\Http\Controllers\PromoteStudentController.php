<?php

namespace App\Http\Controllers;

use App\Models\PromoteStudent;
use App\Repositories\ClassSection\ClassSectionInterface;
use App\Repositories\PromoteStudent\PromoteStudentInterface;
use App\Repositories\SessionYear\SessionYearInterface;
use App\Repositories\Student\StudentInterface;
use App\Repositories\User\UserInterface;
use App\Services\CachingService;
use App\Services\ResponseService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Throwable;

class PromoteStudentController extends Controller {

    private ClassSectionInterface $classSection;
    private SessionYearInterface $sessionYear;
    private StudentInterface $student;
    private UserInterface $user;
    private PromoteStudentInterface $promoteStudent;
    private CachingService $cache;

    public function __construct(ClassSectionInterface $classSection, SessionYearInterface $sessionYear, StudentInterface $student, UserInterface $user, PromoteStudentInterface $promoteStudent, CachingService $cachingService) {
        $this->classSection = $classSection;
        $this->sessionYear = $sessionYear;
        $this->student = $student;
        $this->user = $user;
        $this->promoteStudent = $promoteStudent;
        $this->cache = $cachingService;
    }

    public function index() {
        ResponseService::noAnyPermissionThenRedirect(['promote-student-list','transfer-student-list']);
        $classSections = $this->classSection->all(['*'], ['class', 'section', 'medium']);
        $sessionYears = $this->sessionYear->builder()->select(['id', 'name'])->get();
        return view('promote_student.index', compact('classSections', 'sessionYears'));
    }

    public function store(Request $request) {
        ResponseService::noAnyPermissionThenSendJson(['promote-student-create', 'promote-student-edit']);
        $request->validate([
            'class_section_id' => 'required',
            'promote_data' => 'required'
        ], ['promote_data.required' => "No Student Data Found"]);
        try {
            DB::beginTransaction();
            $promoteStudentData = array();
            $selectedStudentId = is_array($request->promote_student_ids) ? $request->promote_student_ids : explode(',', $request->promote_student_ids);
            $userIds = [];
            foreach ($selectedStudentId as $studentId) {
                $query = DB::select('SELECT users.id as id FROM users JOIN students ON students.user_id = users.id WHERE students.id = ?',[$studentId]);
                $userIds[] = $query[0]->id;
            }
            foreach ($request->promote_data as $key => $data) {
                if (in_array($data['student_id'], $userIds)) {
                    $student = DB::table('students as s')
                                ->join('users as u','u.id','=','s.user_id')
                                ->where('u.id',$data['student_id'])
                                ->first();
                    $classSections = $this->classSection->all(['*'], ['class', 'section', 'medium'])
                                     ->where('id',$student->class_section_id)
                                     ->value('full_name');


                    $promoteStudentData[$key] = array(
                        'student_id'      => $data['student_id'],
                        'session_year_id' => $request->session_year_id,
                        'result'          => $data['result'],
                        'status'          => $data['status'],
                        'prev_class_section_id' => $student->class_section_id,
                        'prev_class_name' => $classSections,
                        'prev_session_year_id' => $student->session_year_id
                    );

                    if ($data['result'] == 1) {
                        // IF Student Then Store New Class Section in Promote Data
                        $promoteStudentData[$key]['class_section_id'] = $request->new_class_section_id;

                        if ($data['status'] == 1) {
                            // IF Student Continues then get students IDs
                            $passStudentsIds[] = $data['student_id'];
                        }
                    } else {
                        // IF Students Fails then store Current Class Section in Promote Data
                        $promoteStudentData[$key]['class_section_id'] = $request->class_section_id;

                        if ($data['status'] == 1) {
                            // IF Student Fails then get students IDs
                            $failStudentsIds[] = $data['student_id'];
                        }
                    }

                    // IF Student Leaves then get Student IDs
                    if ($data['status'] == 0) {
                        $leftStudentSIds[] = $data['student_id'];
                    }
                }
            }
            if (!empty($passStudentsIds)) {
                // Get Sort Value and Order Value from Settings
                $sortBy = !empty($this->cache->getSchoolSettings('roll_number_sort_column')) ? $this->cache->getSchoolSettings('roll_number_sort_column') : 'first_name';
                $orderBy = !empty($this->cache->getSchoolSettings('roll_number_sort_order')) ? $this->cache->getSchoolSettings('roll_number_sort_order') : 'asc';

                // Get The Data of Users who is passed with Student Relation and make Array to Update Student Details
                $studentUsers = $this->user->builder()->role('Student')->whereIn('id',$passStudentsIds)->with('student')->orderBy('users.'.$sortBy, $orderBy)->get();
                $existingStudent = DB::select("SELECT COUNT(*) as student_count FROM students JOIN class_sections ON students.class_section_id =class_sections.id JOIN users ON users.id = students.user_id WHERE class_sections.id = ? AND users.deleted_at IS NULL",[$request->new_class_section_id]);
                $rollNumberBegin = $existingStudent[0]->student_count + 1;
                
                $studentsData = array();
                foreach ($studentUsers as $key => $user) {
                    $notifyUser = array();
                    $studentsData[] = array(
                        'id' => $user->student->id,
                        'roll_number' => (int)$key + $rollNumberBegin,
                        'class_section_id' => $request->new_class_section_id,
                        'session_year_id'  => $request->session_year_id,
                    );
                    $notifyUser[] = $user->id;
                    $notifyUser[] = $user->student->guardian_id;
                    if(isset($notifyUser)){
                        $className = DB::table('classes as c')
                                    ->join('class_sections as cs','cs.class_id','=','c.id')
                                    ->join('sections as s','s.id','=','cs.section_id')
                                    ->join('mediums as m','m.id','=','cs.medium_id')
                                    ->where('cs.id',$request->new_class_section_id)
                                    ->select(DB::raw("
                                      CONCAT(
                                        c.name,
                                        CASE 
                                            WHEN s.name IS NOT NULL THEN CONCAT(' - ', s.name) 
                                            ELSE '' 
                                        END,
                                        CASE 
                                            WHEN m.name IS NOT NULL THEN CONCAT(' - ', m.name) 
                                            ELSE '' 
                                        END
                                    ) as className
                                    "))->value('className');
                        $title = 'Promotion Alert!';
                        $type = 'Transfer Student';
                        $body = "Congrats ".$user->first_name ." ".$user->last_name."! You've been promoted to ".$className." for 2025"; 
                        send_notification($notifyUser, $title, $body, $type); 
                    }
                }

                if(!empty($request->new_class_section_id)){
                    $newStudents = DB::table('students')
                    ->join('users', 'students.user_id', '=', 'users.id')
                    ->where('students.class_section_id', $request->new_class_section_id)
                    ->whereNull('users.deleted_at')
                    ->orderBy('students.roll_number')
                    ->select('students.*') 
                    ->get();
                    $no = 1;
                    foreach ($newStudents as $student){
                        DB::table('students')
                            ->where('id', $student->id)
                            ->update(['roll_number' => $no++]);
                    }
                    $class_id = DB::table('class_sections')->where('id',$request->new_class_section_id)->value('class_id');
                }

               
            
                // Upsert Student Data
                $this->student->upsert($studentsData,['id'],['roll_number','class_section_id','session_year_id']);

                if(!empty($class_id)){
                DB::table('student_fee_types')
                ->where('student_id', $studentUsers[0]->student->id)
                ->where('school_id', Auth::user()->school_id)
                ->update(['class_id' => $class_id]);
                }

                $oldClassSectionIds = $request->class_section_id;
                if(!empty($oldClassSectionIds)){
                    $oldStudents = DB::table('students')
                    ->join('users', 'students.user_id', '=', 'users.id')
                    ->where('students.class_section_id', 1)
                    ->whereNull('users.deleted_at')
                    ->orderBy('students.roll_number')
                    ->select('students.*') 
                    ->get();
                    $no = 1;
                    foreach ($oldStudents as $student){
                        DB::table('students')
                            ->where('id', $student->id)
                            ->update(['roll_number' => $no++]);
                    }

                }
            }

            if (!empty($failStudentsIds)) {
                $this->student->builder()->whereIn('user_id', $failStudentsIds)->update(array(
                    'session_year_id' => $request->session_year_id,
                ));
            }

            if (!empty($leftStudentSIds)) {
                $this->user->builder()->whereIn('id', $leftStudentSIds)->update(['status' => 2,'deleted_at' => now()]);
            }
            $this->promoteStudent->upsert($promoteStudentData, ['class_section', 'student_id', 'session_year_id'], ['status', 'result','prev_class_section_id','prev_class_name','prev_session_year_id']);
            DB::commit();
            ResponseService::successResponse("Data Updated Successfully");

        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function getPromoteData(Request $request) {
        $response = PromoteStudent::where(['class_section_id' => $request->class_section_id])->get();
        return response()->json($response);
    }

    public function show(Request $request) {
        ResponseService::noPermissionThenRedirect('promote-student-list');
        $offset = request('offset', 0);
        $limit = request('limit', 10);
        $sort = request('sort', 'id');
        $order = request('order', 'ASC');
        $search = request('search');

        $class_section_id = $request->class_section_id;
        $sessionYear = $this->cache->getDefaultSessionYear(); // Get Current Session Year
        $sql = $this->student->builder()->where(['class_section_id' => $class_section_id, 'session_year_id' => $sessionYear->id])->whereHas('user', function ($query) {
            $query->where('status', 1);
        })->with('user')
            ->where(function ($query) use ($search) {
                $query->when($search, function ($query) use ($search) {
                $query->where('id', 'LIKE', "%$search%")
                ->orWhereHas('user',function($q) use($search){
                    $q->whereRaw("concat(users.first_name,' ',users.last_name) LIKE '%" . $search . "%'");
                });
            });
            });
        $total = $sql->count();
        // $sql->orderBy($sort, $order)->skip($offset)->take($limit);
        $sql->orderBy($sort, $order);
        $res = $sql->get();
        $bulkData = array();
        $bulkData['total'] = $total;
        $rows = array();
        $no = 1;
        foreach ($res as $row) {
            $tempRow = $row->toArray();
            $tempRow['no'] = $offset + $no++;
            $rows[] = $tempRow;
        }
        $bulkData['rows'] = $rows;
        return response()->json($bulkData);
    }

    public function showTransferStudent(Request $request) {
        // ResponseService::noFeatureThenRedirect('Academics Management');
        ResponseService::noPermissionThenRedirect('transfer-student-list');
        $offset = request('offset', 0);
        $limit = request('limit', 10);
        $sort = request('sort', 'id');
        $order = request('order', 'ASC');
        $search = request('search');

        $class_section_id = $request->current_class_section;
        $sessionYear = $this->cache->getDefaultSessionYear(); // Get Current Session Year
        $sql = $this->student->builder()->where(['class_section_id' => $class_section_id, 'session_year_id' => $sessionYear->id])->whereHas('user', function ($query) {
            $query->where('status', 1);
        })->with('user')
        ->where(function($q) use($search) {
            $q->when($search, function ($query) use ($search) {
                $query->where('id', 'LIKE', "%$search%")
                ->orWhereHas('user',function($q) use($search){
                    $q->whereRaw("concat(users.first_name,' ',users.last_name) LIKE '%" . $search . "%'");
                });
            });
        });
            
        $total = $sql->count();
        $sql->orderBy($sort, $order)->skip($offset)->take($limit);
        $res = $sql->get();
        $bulkData = array();
        $bulkData['total'] = $total;
        $rows = array();
        $no = 1;
        foreach ($res as $row) {
            $tempRow['no'] = $offset + $no++;
            $tempRow['student_id'] = $row->id;
            $tempRow['user_id'] = $row->user_id;
            $tempRow['name'] = $row->full_name;
            $rows[] = $tempRow;
        }
        $bulkData['rows'] = $rows;
        return response()->json($bulkData);
    }

    public function storeTransferStudent(Request $request){
        // ResponseService::noFeatureThenRedirect('Academics Management');
        ResponseService::noAnyPermissionThenSendJson(['transfer-student-list', 'transfer-student-edit']);
        $request->validate([
            'current_class_section_id' => 'required',
            'new_class_section_id' => 'required',
            'student_ids' => 'required'
        ]);
        try {
            DB::beginTransaction();
            // $studentIds = json_decode($request->student_ids);
            $studentIds = explode(",",$request->student_ids);
            $roll_number_db = $this->student->builder()->select(DB::raw('max(roll_number)'))->where('class_section_id', $request->new_class_section_id)->first();
            $roll_number_db = $roll_number_db['max(roll_number)'];

            $updateStudent = array();
            foreach ($studentIds as $id) {
                $updateStudent[] = array(
                    'id' => $id,
                    'class_section_id' => $request->new_class_section_id,
                    'roll_number' => (int)$roll_number_db + 1,
                );
            }

            $this->student->upsert($updateStudent,['id'],['class_section_id','roll_number']);
            DB::commit();
            ResponseService::successResponse("Data Updated Successfully");
        } catch (Throwable $e) {
            DB::rollback();
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function showPromoteStudentHistory(Request $request){
        ResponseService::noPermissionThenRedirect('promote-student-list');
        $offset = request('offset', 0);
        $limit = request('limit', 10);
        $sort = request('sort', 'id');
        $order = request('order', 'ASC');
        $search = request('search');
        $sql = DB::table('promote_students as ps')
                ->join('users as u','u.id','=','ps.student_id')
                ->select(
                    'u.id',
                    DB::raw("CONCAT(u.first_name ,' ',u.last_name) as full_name"),
                    'ps.class_section_id as current_class_section_id',
                    'ps.prev_class_section_id as last_class_section_id',
                    'ps.prev_class_name as last_class_name',
                    'ps.session_year_id as current_session_year_id',
                    'ps.prev_session_year_id as last_session_year_id',
                    'ps.status'
                    )
                ->where('ps.school_id',Auth::user()->school_id)
                ->where(function ($query) {
                    $query->whereNotNull('ps.prev_class_section_id')
                        ->whereNotNull('ps.prev_class_name')
                        ->whereNotNull('ps.prev_session_year_id');
                })->when($request->last_class_section,function($query) use ($request){
                    $query->where('ps.prev_class_section_id',$request->last_class_section);
                })
                ->when($request->last_session_year,function($query) use ($request){
                    $query->where('ps.prev_session_year_id',$request->last_session_year);
                });
        
        $total = $sql->count();
        $sql->orderBy($sort, $order)->skip($offset)->take($limit);
        $res = $sql->get();
        $bulkData = [];
        $bulkData['total'] = $total;
        $rows = [];
        $no = 1;

        foreach($res as $row){
            $tempRow = (array)$row;

            if($tempRow['current_class_section_id']){
                $classSections = $this->classSection->all(['*'], ['class', 'section', 'medium'])
                ->where('id',$tempRow['current_class_section_id'])
                ->value('full_name');
                $tempRow['current_class_name'] = $classSections;
            }
            if($tempRow['current_session_year_id']){
                $sessionYear = DB::table('session_years')->where('id',$tempRow['current_session_year_id'])->first();
                $tempRow['current_session_year'] = $sessionYear->name;
            }
            if($tempRow['last_session_year_id']){
                $sessionYear = DB::table('session_years')->where('id',$tempRow['last_session_year_id'])->first();
                $tempRow['last_session_year'] = $sessionYear->name;
            }
            $tempRow['no'] = $no++;
            $rows[] = $tempRow;
        }
        $bulkData['rows'] = $rows;
        return response()->json($bulkData);
    }
}
