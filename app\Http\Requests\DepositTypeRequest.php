<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class DepositTypeRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'name' => [
                'required',
                'string',
                'max:255',
                'unique:deposit_types,name,' . ($this->deposit_type->id ?? '')
            ],
            'description' => 'nullable|string'
        ];
    }

    public function messages()
    {
        return [
            'name.required' => 'The deposit type name is required.',
            'name.unique' => 'This deposit type name already exists.',
            'name.max' => 'The deposit type name cannot exceed 255 characters.'
        ];
    }
}