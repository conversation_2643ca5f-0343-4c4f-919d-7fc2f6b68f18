{"__meta": {"id": "01JX1D4DPSES6REYAPPPS3STYK", "datetime": "2025-06-06 09:19:22", "utime": **********.330118, "method": "POST", "uri": "/login", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.11197, "end": **********.330131, "duration": 1.2181611061096191, "duration_str": "1.22s", "measures": [{"label": "Booting", "start": **********.11197, "relative_start": 0, "end": **********.383419, "relative_end": **********.383419, "duration": 0.*****************, "duration_str": "271ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.383432, "relative_start": 0.*****************, "end": **********.330132, "relative_end": 9.5367431640625e-07, "duration": 0.****************, "duration_str": "947ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.405912, "relative_start": 0.****************, "end": **********.4118, "relative_end": **********.4118, "duration": 0.0058879852294921875, "duration_str": "5.89ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.326693, "relative_start": 1.****************, "end": **********.327243, "relative_end": **********.327243, "duration": 0.0005500316619873047, "duration_str": "550μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "28MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.1.10", "Environment": "local", "Debug Mode": "Enabled", "URL": "schola.one", "Timezone": "Asia/Kuala_Lumpur", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 1, "nb_statements": 1, "nb_visible_statements": 1, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00855, "accumulated_duration_str": "8.55ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `email` = '<EMAIL>' and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 139}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 381}, {"index": 18, "namespace": null, "name": "vendor/laravel/ui/auth-backend/AuthenticatesUsers.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php", "line": 87}, {"index": 19, "namespace": null, "name": "vendor/laravel/ui/auth-backend/AuthenticatesUsers.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.0630531, "duration": 0.00855, "duration_str": "8.55ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:139", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\schola\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=139", "ajax": false, "filename": "EloquentUserProvider.php", "line": "139"}, "connection": "schola", "explain": null, "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "302 Found", "full_url": "https://schola.test/login", "action_name": null, "controller_action": "App\\Http\\Controllers\\Auth\\LoginController@login", "uri": "POST login", "controller": "App\\Http\\Controllers\\Auth\\LoginController@login<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Flaravel%2Fui%2Fauth-backend%2FAuthenticatesUsers.php&line=32\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fschola%2Fvendor%2Flaravel%2Fui%2Fauth-backend%2FAuthenticatesUsers.php&line=32\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/laravel/ui/auth-backend/AuthenticatesUsers.php:32-60</a>", "middleware": "web", "duration": "1.22s", "peak_memory": "30MB", "response": "Redirect to https://schola.test/student-fees/paid/receipt-pdf/14505/invoice", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1680984305 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1680984305\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2145543903 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">C748oYk2oZjQoDB2Cy0tK82hW9zZFFSxcb4rF97P</span>\"\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"27 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>btnlogin</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Login</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2145543903\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1057961958 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">schola.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">117</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">https://schola.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">https://schola.test/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"742 characters\">XSRF-TOKEN=eyJpdiI6IlNtMGg0MDBtNU1OamtaYXZUN0tsY0E9PSIsInZhbHVlIjoiQjFLVnRmWFc1c3FLU2ZXeTVrSWtaN2FSVWFJYTl0SWZNQ0hQdmdVeURsbG9EMnM0RS9TWnpUSFYxU3MyWEU4ZzJMV3grOERhdGRYOHBMUXBza0NvVTNocjE0M2c2UGcrd2xQZXdSMVphN1U0a1p1TUNtTXNNczI5eUFMM3FPamgiLCJtYWMiOiJiOWY1MGIzYWNlNjk2NDE2MWViYTkwOGYxYTg0NTZhMzBhNmYzZDVmNGFlZTAyZTc2NDg2NzA2YjIwNTYwMGQwIiwidGFnIjoiIn0%3D; schola_saas_school_management_system_session=eyJpdiI6IjhheENEbUk4NGlqN2V1VUxHUUJzWmc9PSIsInZhbHVlIjoiWUVvZ2d1c0RZTmxPTGMwTHBDN2xZUzhndWpSN3Vvd2d2alNCNnlSQUxBaXBYVXpDSEVNR2JKV284cFhnK29VVlYyWjl4d1RxTGNwOGc0aU1IcUJhTk04RkFyazFvT0l2bWhxK3pQQTdZcXdMbVRWbWlNS0JZSFY2S3FOdXZHNzIiLCJtYWMiOiJjMjVkZGQ1ZjA0NDY0MmZhNDQzYWFhMmY2NjI5Nzc3NzU3YmViMmY2YTU3MjRmY2Y1N2Q0NTI1MmVlYmY2ZDkzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1057961958\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2008969766 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">C748oYk2oZjQoDB2Cy0tK82hW9zZFFSxcb4rF97P</span>\"\n  \"<span class=sf-dump-key>schola_saas_school_management_system_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YJIEhgM4Vi4O042eVOLSAaUivQUmuQw52BKiCOmF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2008969766\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1782677768 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 01:19:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"63 characters\">https://schola.test/student-fees/paid/receipt-pdf/14505/invoice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1782677768\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1504671322 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qzZAZCbyxouAflTIwxgtbSqzkAL1GsMdrfVvou3B</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">https://schola.test/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>landing_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>language</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Language\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Language</span></span> {<a class=sf-dump-ref>#2941</a><samp data-depth=2 class=sf-dump-compact>\n    #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n    #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">languages</span>\"\n    #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n    #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n    +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n    #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n    +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n    +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n    +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">English</span>\"\n      \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n      \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"7 characters\">en.json</span>\"\n      \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>is_rtl</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2023-11-18 23:37:23</span>\"\n      \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2023-11-18 23:37:23</span>\"\n    </samp>]\n    #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">English</span>\"\n      \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n      \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"7 characters\">en.json</span>\"\n      \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>is_rtl</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2023-11-18 23:37:23</span>\"\n      \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2023-11-18 23:37:23</span>\"\n    </samp>]\n    #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n    #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n    +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n    +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"4 characters\">code</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"4 characters\">file</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"6 characters\">is_rtl</span>\"\n    </samp>]\n    #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n    </samp>]\n  </samp>}\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1504671322\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "https://schola.test/login", "controller_action": "App\\Http\\Controllers\\Auth\\LoginController@login"}, "badge": "302 Found"}}