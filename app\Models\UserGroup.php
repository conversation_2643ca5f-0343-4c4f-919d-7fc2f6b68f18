<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserGroup extends Model
{
    use HasFactory;

    protected $fillable = [
        'group_name', 'commission_type', 'commission_amount', 
        'school_id', 'exclude_absent_students'
    ];
    

    public function details()
    {
        return $this->hasMany(UserGroupDetail::class,'group_id');
    }
}