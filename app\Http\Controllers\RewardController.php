<?php

namespace App\Http\Controllers;

use App\Exports\StudentFeeTypesExport;
use App\Imports\StudentFeeTypesImport;
use App\Models\Reward;
use App\Models\StudentFeeType;
use App\Services\ResponseService;


use App\Repositories\Reward\RewardInterface;
use App\Repositories\ClassSchool\ClassSchoolInterface;
use App\Repositories\ClassSection\ClassSectionInterface;
use App\Repositories\FeesType\FeesTypeInterface;
use App\Services\CachingService;
use App\Repositories\Student\StudentInterface;
use App\Services\BootstrapTableService;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Auth;
use Throwable;
use Exception;

class RewardController extends Controller
{
    private RewardInterface $rewardTable;
    private StudentInterface $student;
    private FeesTypeInterface $feesType;
    private ClassSchoolInterface $class;
    private CachingService $cache;
    private ClassSectionInterface $classSection;

    public function __construct(
        RewardInterface $rewardTable,
        CachingService $cache,
        ClassSchoolInterface $classSchool,
        FeesTypeInterface $feesType,
        StudentInterface $student,
        ClassSectionInterface $classSection
    ) {

        $this->rewardTable = $rewardTable;
        $this->cache = $cache;
        $this->class = $classSchool;
        $this->feesType = $feesType;
        $this->student = $student;
        $this->classSection = $classSection;
    }

    public function index(){

        ResponseService::noFeatureThenRedirect('Reward Management');
        //ResponseService::noPermissionThenRedirect('reward-list');

        $classes = $this->class->all(['*'], ['stream', 'medium', 'stream']);
        $simpleClassDropdown = $this->class->builder()->pluck('name','id');
        $rewardCategory = DB::table('rewards_category')->where('school_id','=',Auth::user()->school_id)->whereNull('deleted_at')->pluck('category_name', 'id')->toArray();
        $rewardCategoryPoints = DB::table('rewards_category')->where('school_id','=',Auth::user()->school_id)->whereNull('deleted_at')->pluck('points_amount', 'id')->toArray();
         // Merging reward category names with points
        $rewardCategoryWithPoints = [];
        foreach ($rewardCategory as $id => $categoryName) {
            // Default empty if no points are available
            $points = isset($rewardCategoryPoints[$id]) ? "({$rewardCategoryPoints[$id]}pts)" : '';
            $rewardCategoryWithPoints[$id] = "{$categoryName} {$points}";
        }
        $ranking = 1;
        $last_score = null;

        // get score from database
        $scores = DB::select("
            SELECT r.student_id, 
                s.user_id, 
                r.score_total, 
                u.image,
                CONCAT(u.first_name, ' ', u.last_name) AS studentName
            FROM rewards r
            JOIN students s ON r.student_id = s.id 
            JOIN users u ON s.user_id = u.id 
            WHERE s.school_id = ?
            AND r.deleted_at IS NULL
            AND r.id = (
                SELECT id FROM rewards r2 
                WHERE r2.student_id = r.student_id 
                AND r2.deleted_at IS NULL
                ORDER BY r2.id DESC 
                LIMIT 1
            )
            ORDER BY r.score_total DESC
        ", [Auth::user()->school_id]);

        $rewardPoints = DB::table("rewards")
        ->join("students","rewards.student_id","=","students.id")
        ->join("users","students.user_id","=","users.id")
        ->select("rewards.reward_point_amount as rewardPointAmount",
           "rewards.reward_point_total as rewardPointTotal",
            DB::raw("CONCAT(users.first_name, ' ', users.last_name) AS studentName")
        )
        ->whereNull('rewards.deleted_at')
        ->groupBy("users.first_name", "users.last_name")
        ->get();

        foreach ($scores as $score) {
            if ($last_score !== $score->score_total) {
                $score->rank = $ranking;
                $last_score = $score->score_total; 
                $ranking++; 
            } else {
                $score->rank = $ranking - 1; 
            }
        }
        
        
        return view('reward.index', compact('simpleClassDropdown','classes','rewardCategory','scores','rewardPoints','rewardCategoryPoints','rewardCategoryWithPoints'));
    }

    public function store(Request $request) {

        //ResponseService::noFeatureThenRedirect('Reward Management');
        //ResponseService::noPermissionThenRedirect('reward-create');

        $request->validate([
            'class_id'                              => 'required|numeric',
            'student_id'                            => 'required|array',
            'student_id.*'                          => 'required|numeric',
            'remark' => 'nullable|string',
        ]);

        try {

            $classData = $this->class->builder()->find($request->class_id);

            // Get the data from the request
            $class_id = $request->class_id;
            $school_id = $classData->school_id;
            $category_id = $request->input('reward_category_id');
            $points_amount = DB::table('rewards_category')->where('id','=',$category_id)->whereNull('deleted_at')->value('points_amount');
            $category = DB::table('rewards_category')->where('id',$category_id)->whereNull('deleted_at') ->select('remark','mode')->first();
            // dd($category);
            DB::beginTransaction();

            foreach ($request->student_id as $student_id) {

                $reward = Reward::where('student_id', $student_id)
                ->orderBy('updated_at', 'desc')
                ->whereNull('deleted_at')
                ->first();

                $currentRewardPoints = $reward ? $reward->reward_point_total : 0;
                $currentScore = $reward ? $reward->score_total : 0;
                $scoreAmount = $reward ? $reward->score_amount : 0;
                
                if ($category->mode==1) {
                    $rewardPointsAmount = $points_amount;
                    $totalRewardPoints = $currentRewardPoints + $points_amount;
                } else {
                    $rewardPointsAmount = 0;
                    $totalRewardPoints = $currentRewardPoints;
                }              
                //Update score
                
                $new_points=$points_amount;
                
                $updatedScore =  $new_points + $currentScore;
                
                // if($category->mode==1){
                //     $rewardPointsAmount=$points_amount;
                //     $totalRewardPoints=$currentRewardPoints + $rewardPointsAmount;
                //     $updatedScore=$currentScore+$points_amount;
                // } else{
                //     $rewardPointsAmount=0;
                //     $totalRewardPoints=$currentRewardPoints;
                //     $updatedScore=$currentScore+$points_amount;
                // }
                
                $reward = [
                    'school_id'          => $school_id,
                    'class_id'           => $class_id,
                    'student_id'         => $student_id,
                    'score_amount' => $points_amount,
                    'score_total' => $updatedScore,
                    'remark' => $category->remark,
                    'reward_point_amount'=> $rewardPointsAmount,
                    'reward_point_total' => $totalRewardPoints,
                    'category_id' => $category_id
                ];
          
                
                
                $this->rewardTable->create($reward);

                //notify
                $notifyUser = DB::table('students')
                ->where('id', $student_id)
                ->select('user_id')
                ->get()
                ->pluck('user_id');

                if ($notifyUser !== null){
                    $guardianIds = collect();
                    foreach ($notifyUser as $user){
                        $guardianId = DB::table('students')
                        ->where('user_id', $user)
                        ->pluck('guardian_id');
                        $guardianIds = $guardianIds->merge($guardianId);
                    }

                    $notifyUser = $notifyUser->merge($guardianIds);
                    if($updatedScore > $currentScore){
                    $title = 'Points Added!';
                    if($category->remark){
                    $body ="Great job! You've earned ". $points_amount . " points for " . $category->remark . ". Keep up the good work!";
                    }else{
                        $body ="Great job! You've earned ". $points_amount . " points. Keep up the good work!";
                    }
                    $type = "Notification";
                    send_notification($notifyUser, $title, $body, $type);
                    }else{
                    $title = 'Points Deducted';
                    if($category->remark){
                    $body = "You've lost ". $points_amount. " points due to ". $category->remark . ". Let's work on improving this!";
                    }else{
                        $body = "You've lost ". $points_amount. " points. Let's work on improving this!";
                    }
                    $type = "Notification";
                    send_notification($notifyUser, $title, $body, $type);
                    }
                  
                }

                
            }

            DB::commit();
            ResponseService::successResponse('Data Stored Successfully');
        } catch (Throwable $e) {
            DB::rollback();
            ResponseService::logErrorResponse($e,"RewardController -> Store Method");
            ResponseService::errorResponse();
        }
    }

    public function show() {

        //ResponseService::noFeatureThenRedirect('Reward Management');
        // ResponseService::noPermissionThenRedirect('reward-list');

        $offset = request('offset', 0);
        $limit = request('limit', 10);
        $sort = request('sort', 'id');
        $order = request('order', 'DESC');
        $search = request('search');
        $showDeleted = request('show_deleted');
        $classId = (int)request('class_id');
        $studentId = (int)request('student_id');
        $categoryId = (int)request('category_id');
        $startDate = request('start_date');
        $endDate = request('end_date');

        $query = Reward::query()
        ->where('school_id', Auth::user()->school_id);


        if ($startDate && $endDate) {
            // Filter records based on the datetime range
            $query->whereBetween(DB::raw('DATE(updated_at)'), [date('Y-m-d', strtotime($startDate)), date('Y-m-d', strtotime($endDate))]);
        }

        if ($showDeleted) {
            // If "Trashed" is selected, show records where deleted_at is NOT NULL
            $query->whereNotNull('deleted_at');
        } else {
            // Otherwise, only show records where deleted_at is NULL
            $query->whereNull('deleted_at');
        }

        $query->orderBy('updated_at', 'ASC');


        $records = $query->get();

        $total = DB::table('rewards')
        ->where('reward_point_amount', '>=', 0)
        ->whereNull('deleted_at')
        ->where('school_id', Auth::user()->school_id)
        ->count();

        $bulkData = array();
        $rows = array();
        $no = 1; 
       

        foreach ($records as $row) {
            if($row->category_id !== 0){
                $operate = '';
                $operate .= BootstrapTableService::editButton(route('reward.edit', $row->id), false);
                $operate .= BootstrapTableService::deleteButton(route('reward.delete', $row->id));

                $tempRow=$row;
                
                $tempRow['operate'] = $operate;
                //$tempRow['balance']=$current_balance;
                $tempRow['credit']=0;
                $tempRow['debit']=0;

                $student_id=$tempRow['student_id'];

                $tempRow['reward_point'] = $row->reward_point_total;
                $tempRow['scores'] = $row->score_total;
                

                $student_name=DB::select("
                SELECT  concat(u.first_name,' ', u.last_name) as full_name
                FROM    students s
                JOIN    users u
                ON      s.user_id = u.id
                WHERE   s.id IN ($student_id)");

                $category_id=$tempRow['category_id'];
                
                if($row->category_id > 0) {
                    $category_name = DB::select("
                    SELECT  rc.category_name
                    FROM    rewards_category rc
                    WHERE   rc.id = ?", [$category_id]);
                    
                    $tempRow['category_name'] = $category_name[0]->category_name ?? '';
                    $tempRow['points'] = $row->score_amount;
                } else {
                    $tempRow['category_name'] = "Redeem Gift";
                    $tempRow['points'] = $row->reward_point_amount;
                }

            
                $tempRow['full_name']=$student_name[0]->full_name ?? '';

                // filter based on class and student 
                if(($tempRow['class_id']==$classId || !$classId) && ($tempRow['student_id']==$studentId|| !$studentId)){
                            $rows[] = $tempRow;
                }
 
        }
    }

            usort($rows, function ($a, $b) {
                return $b['updated_at'] <=> $a['updated_at'];  
            });


            foreach ($rows as $index => $tempRow) {
                $rows[$index]['no'] = $index + 1;  
            }

            $bulkData['total'] = $total;

            $bulkData['rows'] = $rows;
            return response()->json($bulkData);
    }   

        public function edit($id) {
            // ResponseService::noFeatureThenRedirect('Reward Management');
            //ResponseService::noPermissionThenRedirect('reward-edit');

            $rewardCategory = DB::table('rewards_category')->where('school_id','=',Auth::user()->school_id)->whereNull('deleted_at')->pluck('category_name', 'id')->toArray();
            $selectedCategory=DB::table('rewards')->where('id','=',$id)->whereNull('deleted_at')->value('category_id');
            
            $rewardCategoryWithPoints = DB::table('rewards_category')
            ->where('school_id', '=', Auth::user()->school_id)
            ->whereNull('deleted_at')
            ->selectRaw("CONCAT(category_name, ' (', points_amount, 'pts)') as category_with_points, id")
            ->pluck('category_with_points', 'id')
            ->toArray();

            $reward = Reward::findOrFail($id);
            $rewardPoints = $reward->reward_point_total;
            $remark = $reward->remark;
            $classes = $this->class->all(['*'], ['stream', 'medium', 'stream']);
            $student = $this->student->builder()->with(["class_section.class"])->find($reward->student_id);
            //$selectedStudent = $this->student->find($reward->student_id);
            $selectedClass = $this->class->find($reward->class_id);
        
            
            if($selectedCategory !== 0){
                return view('reward.edit', compact('reward', 'classes', 'student', 'selectedClass','rewardCategory','selectedCategory','rewardCategoryWithPoints'));
            }else{
                return view('reward.RewardPointEdit', compact('reward', 'classes', 'student', 'selectedClass','rewardPoints','remark'));
            }
        }

    public function update(Request $request, $id) {

        $categoryId = $request->input('reward_category_id');
        $points_amount = DB::table('rewards_category')->where('id','=',$categoryId)->whereNull('deleted_at')->value('points_amount');
        $category = DB::table('rewards_category')->where('id',$categoryId)->whereNull('deleted_at') ->select('remark','mode')->first();

        

        $request->validate([
            'remark' => 'nullable|string',

        ]);

        $updatedScoreTotal = 0;

    
        try {
            DB::beginTransaction();
    
            //            // Find reward record by ID
            $reward = Reward::findOrFail($id);

            $sumRecentScoreAmount =  DB::table('rewards')->where('updated_at','>',$reward->updated_at)
            ->where('student_id','=',$reward->student_id)
            ->whereNull('deleted_at')
            ->sum('score_amount');

            $sumRecentRewardAmount =  DB::table('rewards')->where('updated_at','>',$reward->updated_at)
            ->where('student_id','=',$reward->student_id)
            ->whereNull('deleted_at')
            ->sum('reward_point_amount');


            //update score
            $initialScoreAmount = $reward->score_amount * -1;

            $new_points=$points_amount;

            $revertedScore = $reward->score_total + $initialScoreAmount;

            $updatedScore = $revertedScore + $new_points;

            $updatedScoreTotal = $updatedScore + $sumRecentScoreAmount;

            // update reward

            if($category->mode == 1){
                $initialRewardAmount = $reward->reward_point_amount * -1;

                $rewardPointAmount = $points_amount;

                $revertedReward = $reward->reward_point_total + $initialRewardAmount;

                $updatedReward = $revertedReward + $new_points;

                $updatedRewardTotal = $updatedReward + $sumRecentRewardAmount;
            }else{

                $initialRewardAmount = $reward->reward_point_amount * -1;

                $rewardPointAmount = 0; 

                $revertedReward = $reward->reward_point_total + $initialRewardAmount;

                $updatedRewardTotal =  $revertedReward + $sumRecentRewardAmount;
            }

            
    
            $reward->update([
                'remark' => $category->remark,
                'reward_point_amount' => $rewardPointAmount,
                'reward_point_total' => $updatedRewardTotal,
                'score_amount'=> $new_points,
                'score_total' => $updatedScoreTotal,
                'category_id'=>$categoryId
            ]);

            DB::commit();
            ResponseService::successRedirectResponse(route('reward.index'), 'Data Update Successfully');
        } catch (Throwable) {
            DB::rollback();
            ResponseService::errorRedirectResponse();
        }
    }
    


    public function destroy($id) {
        //ResponseService::noFeatureThenRedirect('Reward Management');
        //ResponseService::noPermissionThenSendJson('reward-delete');

        try {
            DB::beginTransaction();
            $this->rewardTable->builder()->where("student_id",$id)->delete();
            DB::commit();
            ResponseService::successResponse("Data Deleted Successfully");
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "RewardController -> Store Method");
            ResponseService::errorResponse();
        }
    }

    public function deleteReward($id) {
        //ResponseService::noFeatureThenRedirect('Reward Management');
        //ResponseService::noPermissionThenSendJson('fees-delete');

        try {
            DB::beginTransaction();
            $this->rewardTable->builder()->where("id",$id)->delete();
            DB::commit();
            ResponseService::successResponse("Data Deleted Successfully");
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "RewardController -> Store Method");
            ResponseService::errorResponse();
        }
    }


    public function search(Request $request) {

        //ResponseService::noFeatureThenRedirect('Reward Management');

        try {
            $data = $this->rewardTable->builder()->where('session_year_id', $request->session_year_id)->get();
            ResponseService::successResponse("Data Restored Successfully", $data);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }


    public function getClassStudent(Request $request){

        $classStudents = DB::select("
            SELECT  s.id
                    , concat(u.first_name,' ', u.last_name) AS fullname
                    , s.school_id
            FROM    students s
            JOIN    class_sections cs
            ON      s.class_section_id = cs.id
            JOIN    users u
            ON      s.user_id = u.id
            WHERE   cs.class_id = ?
            AND     s.session_year_id = ?
            AND     u.status = 1
        ",[$request->class_id,$this->cache->getDefaultSessionYear()->id]);

        return response()->json($classStudents);
    }

    public function downloadStudentFeeTypeTemplate($class_id){
        return Excel::download(new StudentFeeTypesExport($class_id,$this->cache->getDefaultSessionYear()->id), 'students-fee-types.xlsx');
    }

    public function importStudentFeeType(Request $request,$class_id){

        $isSuccess = true;

        try{
            $classData = $this->class->builder()->find($class_id);
            $aa = Excel::import(new StudentFeeTypesImport($class_id,$classData->school_id), $request->file("import-file"));
        }catch(Exception $e){
            $isSuccess = false;
        }


        return response()->json([
            "status"=>$isSuccess
        ]);
    }

    public function getPointsAmount(Request $request) {
        $rewardPoints=DB::table('rewards_category')->where('id','=',$request->category_id)->whereNull('deleted_at')->value('points_amount');

        return response()->json($rewardPoints);
    }

    public function clearRanking()
    {
        try {
            // Set the deleted_at column to today's date for all rows
            DB::table('rewards')->where('school_id',Auth::user()->school_id)->whereNull('deleted_at')->update([
                'deleted_at' => Carbon::now()->format('Y-m-d H:i:s')
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Ranking data has been cleared by setting deleted_at to today.'
            ]);
        } catch (\Exception $e) {
                return response()->json([
                    'success' => false,
                    'message' => 'Error clearing ranking data: ' . $e->getMessage()
                ], 500);
            }
        }

    }

