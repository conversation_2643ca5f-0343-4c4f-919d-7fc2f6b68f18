<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SelfBillingShipping extends Model
{
    use HasFactory;
    protected $table = 'self_billing_shipping';
    protected $fillable = [
        'self_billing_id',
        'recipient_name',
        'address_line1',
       'address_line2',
       'address_line3',
       'postal_code',
       'city',
       'state',
      'country',
      'id_type',
      'id_type_no',
      'tin_number',
      'status',
      'created_at',
      'updated_at',
    ];

}
