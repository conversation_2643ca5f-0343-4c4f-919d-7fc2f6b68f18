<?php

namespace App\Repositories\ExamTimetable;

use App\Models\ExamTimetable;
use App\Repositories\Saas\SaaSRepository;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class ExamTimetableRepository extends SaaSRepository implements ExamTimetableInterface {

    public function __construct(ExamTimetable $model) {
        parent::__construct($model);
    }

    public function create(array $payload): Model {
        $timetable = parent::create($payload);

        // If Timetable is added then identify
        $exam = $timetable->exam;
        $date = $exam->timetable->select(DB::raw('min(date) as start_date,max(date) as end_date'));

        $update = [];
        if ($date['start_date'] != $exam->start_date) {
            $update['start_date'] = $date['start_date'];
        }

        if ($date['end_date'] != $exam->end_date) {
            $update['end_date'] = $date['end_date'];
        }

        if (!empty($update)) {
            $exam->update($update);
        }

        return $timetable;
    }

    public function createBulk(array $payload): bool {
        return parent::createBulk($payload); // TODO: Change the autogenerated stub
    }

    public function updateOrCreate(array $uniqueColumns, array $updatingColumn): Model {
        return parent::updateOrCreate($uniqueColumns, $updatingColumn); // TODO: Change the autogenerated stub
    }
}
