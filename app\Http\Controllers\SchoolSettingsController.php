<?php

namespace App\Http\Controllers;
use App\Models\Attendance; 
use App\Repositories\ClassSection\ClassSectionInterface;
use App\Repositories\PaymentConfiguration\PaymentConfigurationInterface;
use App\Repositories\School\SchoolInterface;
use App\Repositories\SchoolSetting\SchoolSettingInterface;
use App\Repositories\Student\StudentInterface;
use App\Services\CachingService;
use App\Services\ResponseService;
use App\Helpers\EInvoiceHelper;
use Auth;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Validator;
use Storage;
use Throwable;

class SchoolSettingsController extends Controller {
    // Initializing the Settings Repository
    private SchoolSettingInterface $schoolSettings;
    private CachingService $cache;
    private ClassSectionInterface $classSection;
    private StudentInterface $student;
    private SchoolInterface $school;

    public function __construct(SchoolSettingInterface $schoolSettings, CachingService $cachingService, ClassSectionInterface $classSection, StudentInterface $student, PaymentConfigurationInterface $paymentConfiguration, SchoolInterface $school) {
        $this->schoolSettings = $schoolSettings;
        $this->cache = $cachingService;
        $this->classSection = $classSection;
        $this->student = $student;
        $this->paymentConfiguration = $paymentConfiguration;
        $this->school = $school;
    }

    public function index() {
        ResponseService::noPermissionThenRedirect('school-setting-manage');
        $settings = $this->cache->getSchoolSettings();
        $getDateFormat = getDateFormat();
        $getTimeFormat = getTimeFormat();
        $baseUrl = url('/');
        // Remove the scheme (http:// or https://)
        $baseUrlWithoutScheme = preg_replace("(^https?://)", "", $baseUrl);
        $baseUrlWithoutScheme = str_replace("www.", "", $baseUrlWithoutScheme);
        $systemSettings = $this->cache->getSystemSettings();
        $eInvoice = DB::table('e_invoice')->where('school_id',Auth::user()->school_id)->first();
        $status = DB::table('e_invoice')->select('status')->where('school_id', Auth::user()->school_id)->first()->status ?? false;
        return view('school-settings.general-settings', compact('settings','getDateFormat','getTimeFormat','eInvoice','baseUrlWithoutScheme','systemSettings','status'));
    }


    // database/migrations/xxxx_xx_xx_create_attendances_table.php

public function up()
{
    Schema::create('attendances', function (Blueprint $table) {
        $table->id();
        $table->foreignId('subject_id')->constrained();  // Assuming you have a subjects table
        $table->foreignId('user_id')->constrained();    // Assuming you have a users table (students)
        $table->timestamp('attendance_date')->useCurrent();  // This column records the time of attendance
        $table->timestamps();
        $table->unique(['subject_id', 'user_id', 'attendance_date']);  // Ensures only one attendance per subject/user per day
    });
}



    public function store(Request $request) {
        ResponseService::noPermissionThenRedirect('school-setting-manage');

        Validator::extend('unique_school_code', function ($attribute, $value, $parameters, $validator) {
            $schoolId = Auth::user()->school_id;
            $count = DB::table('schools')->where('school_code', $value)->where('id', '!=', $schoolId)->count();
            return $count === 0;    
        }, 'School code has been taken, Please generate a new one.');
        $settings = [
            'school_name'             => 'required|max:255',
            'school_email'            => 'required|email',
            'school_phone'            => 'required',
            'school_address'          => 'required',
            'branch_address'          => 'nullable',
            'business_registration_no'=> 'nullable',
            'remark'                  => 'nullable',
            'capacity'                => 'nullable|numeric',
            'favicon'                 => 'nullable|image|max:2048',
            'horizontal_logo'         => 'nullable|image|max:2048',
            'vertical_logo'           => 'nullable|image|max:2048',
            'school_code'             => 'required | unique_school_code',
            'roll_number_sort_column' => 'nullable|in:first_name,last_name',
            'roll_number_sort_order'  => 'nullable|in:asc,desc',
            'change_roll_number'      => 'nullable',
            'deduction_method'        => 'required|in: 0,1,2,3',
            //'school_tagline'          => 'required',
            'date_format'             => 'required',
            'time_format'             => 'required',
            'domain'                  => 'nullable|unique:schools,domain,'.Auth::user()->school_id,
            'google_map_link'         => 'nullable',
            'student_progress_approval' => 'required|in:0,1',
            'face_attendance_type' => 'required|in:0,1',
        ];
        $validator = Validator::make($request->all(), $settings);
        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        //$responseData = json_decode($request->getContent(), true);
        $tinValidation = $request->input('tinValidation', 0);

        try {
            $data = array();
            $data[] = [
                "name" => 'school_tagline',
                "data" =>  !isset($request->school_tagline) ? '' : $request->school_tagline,
                "type" => "string"
            ];
            $data [] = [
                "name" => 'deduction_method',
                "data" => (int)$request->deduction_method,
                "type" => "integer"
            ];
            if($request->attendance_setting){
                $attendanceSettings = implode(',',$request->attendance_setting);
            }
            $data [] = [
                "name" => 'attendance_setting',
                "data" => $attendanceSettings ?? '',
                "type" => "array"
            ];
            $data[] = [
                "name" => 'student_progress_approval',
                "data" => $request->student_progress_approval,
                "type" => "integer"
            ];
            $data[] = [
                "name" => 'face_attendance_type',
                "data" => $request->face_attendance_type,
                "type" => "integer"
            ];

            $this ->schoolSettings->upsert($data, ["name"], ["data"]);
            foreach ($settings as $key => $rule) {
                if ($key == 'horizontal_logo' || $key == 'vertical_logo' || $key == 'favicon') {
                    if ($request->hasFile($key)) {
                        // TODO : Remove the old files from server
                        $data[] = [
                            "name" => $key,
                            "data" => $request->file($key),
                            "type" => "file"
                        ];
                    }
                } else {
                    $data[] = [
                        "name" => $key,
                        "data" => $request->$key,
                        "type" => "string"
                    ];
                }
            }

            $this->schoolSettings->upsert($data, ["name"], ["data"]);
            // Update school master table
            $school_data = [
                'name' => $request->school_name,
                'address' => $request->school_address,
                'branch_address' => $request->branch_address,
                'support_phone' => $request->school_phone,
                'support_email' => $request->school_email,
                'tagline' => $request->school_tagline ?? '',
                'business_registration_no'=> $request->business_registration_no ?? '',
                'remark'        => $request->remark ?? '',
                'capacity'      =>$request->capacity,
                'school_code'  => $request->school_code,
                'domain' => $request->domain,
                'deduction_method' => (int)$request->deduction_method,
            ];
            
            $e_invoice_update = [
                'new_company_registration_number'   => $request->new_company_registration_number ?? '',
                'old_company_registration_number'   => $request->old_company_registration_number ?? '',
                'company_business_activity'         => $request->company_business_activity ?? '',
                'id_type'                           => $request->id_type?? '',
                'tax_payer_name'                    => $request->tax_payer_name?? '',
                'registration_id_passport_number'   => $request->registration_id_passport_number?? '',
                'tax_identification_number'         => $request->tax_identification_number ?? '',
                'sst_registration_number'           => $request->sst_registration_number ?? '',
                'tourism_tax_registration_number'   => $request->tourism_tax_registration_number ?? '',
                'company_msic_code'                 => $request->company_msic_code ?? '',
                'address_line1'                     => $request->address_line1 ?? '',
                'address_line2'                     => $request->address_line2 ?? '',
                'address_line3'                     => $request->address_line3 ?? '',
                'city'                              => $request->city ?? '',
                'postal_code'                       => $request->postal_code ?? '',
                'country'                           => $request->country ?? '',
                'state'                             => $request->state ?? '',
                'status'                            => (!empty($request->id_type) && !empty($request->registration_id_passport_number) && !empty($request->tax_identification_number)) ? '1' : '0',
            ];
            
            if (Auth::user()->school_id) {
                $e_invoice_exists = DB::table('e_invoice')->where('school_id',Auth::user()->school_id)->exists();
                if($e_invoice_exists){
                    DB::table('e_invoice')
                    ->where('school_id', Auth::user()->school_id)
                    ->update($e_invoice_update);
                } else {
                    DB::table('e_invoice')->insert(array_merge($e_invoice_update, ['school_id' => Auth::user()->school_id]));
                }
            }

            if ($request->hasFile('vertical_logo') && Auth::user()->school_id) {
                $school = $this->school->findById(Auth::user()->school_id);

                if (Storage::disk('public')->exists($school->getRawOriginal('logo'))) {
                    Storage::disk('public')->delete($school->getRawOriginal('logo'));
                }
                $school_data['logo'] = $request->file('vertical_logo')->store('school','public');
            }
            if (Auth::user()->school_id) {
                $this->school->update(Auth::user()->school_id,$school_data);
            }
            $this->cache->removeSchoolCache(config('constants.CACHE.SCHOOL.SETTINGS'));

            if ($request->change_roll_number) {
                // Get Sort And Order
                $sort = $request->roll_number_sort_column;
                $order = $request->roll_number_sort_order;

                //Get Class Section's Data With Student Sorted By There Names
                $classSections = $this->classSection->builder()
                    ->with(['students' => function ($query) use ($sort, $order) {
                        $query->join('users', 'students.user_id', '=', 'users.id')
                            ->select('students.*', 'users.first_name', 'users.last_name')
                            ->orderBy('users.' . $sort, $order);
                    }])
                    ->get();

                // Loop towards Class Section Data And make Array To get Student's id and Count Roll Number
                $studentArray = array();
                foreach ($classSections as $classSection) {
                    if (isset($classSection->students) && $classSection->students->isNotEmpty()) {
                        foreach ($classSection->students as $key => $student) {
                            $studentArray[] = array(
                                'id'               => $student->id,
                                'class_section_id' => $student->class_section_id,
                                'roll_number'      => (int)$key + 1
                            );
                        }
                    }
                }

                // Update Roll Number Of Students
                $this->student->upsert($studentArray, ['id'], ['roll_number']);

            }
            DB::commit();
            ResponseService::successResponse('Data Updated Successfully');
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "SchoolSettings Controller -> Store method");
            ResponseService::errorResponse();
        }
    }

    public function onlineExamIndex() {
        ResponseService::noPermissionThenRedirect('school-setting-manage');
        $onlineExamTermsConditions = $this->schoolSettings->getSpecificData('online_exam_terms_condition');
        $name = 'online_exam_terms_condition';
        return response(view('online_exam.terms_conditions', compact('onlineExamTermsConditions', 'name')));
    }

    public function onlineExamStore(Request $request) {
        ResponseService::noPermissionThenRedirect('school-setting-manage');
        try {
            DB::beginTransaction();
            $this->schoolSettings->updateOrCreate(["name" => $request->name], ["data" => $request->data, "type" => "string"]);
            DB::commit();
            $this->cache->removeSchoolCache(config('constants.CACHE.SCHOOL.SETTINGS'));
            ResponseService::successResponse('Data Updated Successfully');
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "SchoolSettings Controller -> storeOnlineExamTermsCondition method");
            ResponseService::errorResponse();
        }
    }

    public function id_card_index()
    {
        ResponseService::noFeatureThenRedirect('ID Card - Certificate Generation');
        ResponseService::noPermissionThenRedirect('id-card-settings');
        $settings = $this->cache->getSchoolSettings();
        $settings['student_id_card_fields'] = explode(",",$settings['student_id_card_fields'] ?? '');
        $settings['staff_id_card_fields'] = explode(",",$settings['staff_id_card_fields'] ?? '');

        return view('school-settings.id_card_settings',compact('settings'));
    }

    public function id_card_store(Request $request)
    {
        ResponseService::noFeatureThenSendJson('ID Card - Certificate Generation');
        ResponseService::noAnyPermissionThenSendJson(['id-card-settings']);

        if ($request->type == 'Student') {
            $settings = [
                'header_color'              => 'required',
                'footer_color'              => 'required',
                'header_footer_text_color'  => 'required',
                'layout_type'               => 'required',
                'background_image'          => 'nullable|image|max:2048',
                'profile_image_style'       => 'required',
                'page_width'                => 'required',
                'page_height'               => 'required',
                'student_id_card_fields'    => 'nullable',
    
                'signature'               => 'nullable|image|max:2048',
            ];
        } else {
            // Staff
            $settings = [
                'staff_header_color'              => 'required',
                'staff_footer_color'              => 'required',
                'staff_header_footer_text_color'  => 'required',
                'staff_layout_type'               => 'required',
                'staff_background_image'          => 'nullable|image|max:2048',
                'staff_profile_image_style'       => 'required',
                'staff_page_width'                => 'required',
                'staff_page_height'               => 'required',
                'staff_id_card_fields'    => 'nullable',
                'signature'               => 'nullable|image|max:2048',
            ];
        }
    

        $validator = Validator::make($request->all(), $settings);
        
        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }

        $request->validate([
            'student_id_card_fields' => 'required',
            'staff_id_card_fields' => 'required_if:type,Staff'
        ],[
            'student_id_card_fields.required' => 'Please select at least one field.',
            'staff_id_card_fields.required' => 'Please select at least one field.'
        ]);

        try {
            DB::beginTransaction();
            $data = array();
            foreach ($settings as $key => $rule) {
                if ($key == 'background_image' || $key == 'staff_background_image' || $key == 'signature') {
                    if ($request->hasFile($key)) {
                        // TODO : Remove the old files from server
                        $data[] = [
                            "name" => $key,
                            "data" => $request->file($key),
                            "type" => "file"
                        ];
                    }
                } else if($key == 'student_id_card_fields') {
                    $key_value = implode(",",$request->student_id_card_fields ?? []);
                    $data[] = [
                        "name" => $key,
                        "data" => $key_value,
                        "type" => "string"
                    ];

                } else if($key == 'staff_id_card_fields') {
                    $key_value = implode(",",$request->staff_id_card_fields ?? []);
                    $data[] = [
                        "name" => $key,
                        "data" => $key_value,
                        "type" => "string"
                    ];

                } else {
                    if ($request->$key) {
                        $data[] = [
                            "name" => $key,
                            "data" => $request->$key,
                            "type" => "string"
                        ];    
                    }
                    
                }
            }
            $this->schoolSettings->upsert($data, ["name"], ["data"]);
            $this->cache->removeSchoolCache(config('constants.CACHE.SCHOOL.SETTINGS'));

            DB::commit();
            ResponseService::successResponse('Data Updated Successfully');
        } catch (\Throwable $th) {
            ResponseService::logErrorResponse($th);
            ResponseService::errorResponse();
        }
    }

    public function remove_image_from_id_card($type)
    {
        ResponseService::noFeatureThenSendJson('ID Card - Certificate Generation');
        ResponseService::noAnyPermissionThenRedirect(['id-card-settings']);
        try {
            DB::beginTransaction();
            $settings = $this->cache->getSchoolSettings();
            if ($type == 'background') {
                $data = explode("storage/", $settings['background_image'] ?? '');
                if (Storage::disk('public')->exists(end($data))) {
                    Storage::disk('public')->delete(end($data));
                }
                $this->schoolSettings->builder()->where('name','background_image')->delete();
            } else if($type == 'staff_background'){
                $data = explode("storage/", $settings['staff_background_image'] ?? '');
                if (Storage::disk('public')->exists(end($data))) {
                    Storage::disk('public')->delete(end($data));
                }
                $this->schoolSettings->builder()->where('name','staff_background_image')->delete();
            } else if($type == 'signature'){
                $data = explode("storage/", $settings['signature'] ?? '');
                if (Storage::disk('public')->exists(end($data))) {
                    Storage::disk('public')->delete(end($data));
                }
                $this->schoolSettings->builder()->where('name','signature')->delete();
            }
            DB::commit();
            $this->cache->removeSchoolCache(config('constants.CACHE.SCHOOL.SETTINGS'));
            ResponseService::successResponse('Data Deleted Successfully');
        } catch (\Throwable $th) {
            ResponseService::logErrorResponse($th);
            ResponseService::errorResponse();
        }
    }

    public function terms_condition()
    {
        $name = 'terms_condition';
        $data = htmlspecialchars_decode($this->cache->getSchoolSettings($name));
        return view('school-settings.terms_condition', compact('name', 'data'));
    }

    public function public_terms_condition($id)
    {
        $schoolSettings = $this->cache->getSchoolSettings('*',$id);
        return htmlspecialchars_decode($schoolSettings['terms_condition'] ?? '');
    }

    public function public_privacy_policy($id)
    {
        $schoolSettings = $this->cache->getSchoolSettings('*',$id);
        return htmlspecialchars_decode($schoolSettings['privacy_policy'] ?? '');
    }
    
    public function public_refund_cancellation($id)
    {
        $schoolSettings = $this->cache->getSchoolSettings('*',$id);
        return htmlspecialchars_decode($schoolSettings['refund_cancellation'] ?? '');
    }

    public function privacy_policy()
    {
        $name = 'privacy_policy';
        $data = htmlspecialchars_decode($this->cache->getSchoolSettings($name));
        return view('school-settings.terms_condition', compact('name', 'data'));
    }

    public function update(Request $request) {
        $request->validate([
            'name' => 'required',
            'data' => 'required'
        ]);
        try {
            $OtherSettingsData[] = array(
                'name' => $request->name,
                'data' => htmlspecialchars($request->data),
                'type' => 'string'
            );
            $this->schoolSettings->upsert($OtherSettingsData, ["name"], ["data"]);
            $this->cache->removeSchoolCache(config('constants.CACHE.SCHOOL.SETTINGS'));
            ResponseService::successResponse("Data Stored Successfully");
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e, "School Settings Controller -> otherSystemSettings method");
            ResponseService::errorResponse();
        }
    }

    public function emailTemplate()
    {
        ResponseService::noAnyPermissionThenRedirect(['email-template']);
        $data = htmlspecialchars_decode($this->cache->getSchoolSettings());
        $settings = $this->cache->getSchoolSettings();
        return view('school-settings.email_template', compact('settings'));
    }

    public function emailTemplateUpdate(Request $request)
    {
        ResponseService::noAnyPermissionThenRedirect(['email-template']);
        $validator = Validator::make($request->all(), [
            'staff_data' => 'required',
            'parent_data' => 'required'
        ]);
        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }

        try {
            $OtherSettingsData = array([
                'name' => 'email-template-staff',
                'data' => htmlspecialchars($request->staff_data),
                'type' => 'string',
            ],
            [
                'name' => 'email-template-parent',
                'data' => htmlspecialchars($request->parent_data),
                'type' => 'string'
            ]);
            $this->schoolSettings->upsert($OtherSettingsData, ["name"], ["data"]);
            $this->cache->removeSchoolCache(config('constants.CACHE.SCHOOL.SETTINGS'));
            ResponseService::successResponse("Data Stored Successfully");
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e, "School Settings Controller -> otherSystemSettings method");
            ResponseService::errorResponse();
        }
    }

    public function refund_cancellation()
    {
        $name = 'refund_cancellation';
        $data = htmlspecialchars_decode($this->cache->getSchoolSettings($name));
        return view('school-settings.terms_condition', compact('name', 'data'));
    }

    
    public function thirdPartyApiIndex()
    {
        ResponseService::noFeatureThenRedirect('Website Management');
        ResponseService::noPermissionThenRedirect('school-setting-manage');
        $schoolSettings = $this->cache->getSchoolSettings();
        return view('school-settings.third-party-apis',compact('schoolSettings'));
    }

    public function thirdPartyApiUpdate(Request $request)
    {
        ResponseService::noFeatureThenRedirect('Website Management');
        ResponseService::noPermissionThenRedirect('school-setting-manage');
        // $request->validate([
        //     'SCHOOL_RECAPTCHA_SITE_KEY' => 'required',
        //     'SCHOOL_RECAPTCHA_SECRET_KEY' => 'required',
        //     // "SCHOOL_RECAPTCHA_SITE" => 'required'
        // ]);

        try {

            $data = array([
                "name" => 'SCHOOL_RECAPTCHA_SITE_KEY',
                "data" => $request->input('SCHOOL_RECAPTCHA_SITE_KEY'),
                "type" => "text"
            ],
            [
                "name" => 'SCHOOL_RECAPTCHA_SECRET_KEY',
                "data" => $request->input('SCHOOL_RECAPTCHA_SECRET_KEY'),
                "type" => "text"
            ]);

            $this->schoolSettings->upsert($data, ["name"], ["data"]);
            $this->cache->removeSchoolCache(config('constants.CACHE.SCHOOL.SETTINGS'));

            ResponseService::successResponse("Data Stored Successfully");
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e, "System Settings Controller -> Third Party Api method");
            ResponseService::errorResponse();
        }
    }
    
    public function eInvoiceSettings() {
        ResponseService::noFeatureThenSendJson('E-Invoicing');
        ResponseService::noAnyRoleThenRedirect(['School Admin']);
        $settings = $this->cache->getSchoolSettings();
        // $client_tin = DB::table('e_invoice')
        //     ->where('school_id', auth()->user()->school_id)
        //     ->value('tax_identification_number');
        return view('settings.e-invoice-settings', compact('settings'));
    }

    public function eInvoiceSettingsUpdate(Request $request){
        ResponseService::noFeatureThenSendJson('E-Invoicing');
        ResponseService::noAnyRoleThenRedirect(['School Admin']);
        try {
            DB::beginTransaction();
            foreach($request->all() as $key => $item){
                if($key != "_token"){
                    $exist = DB::table('school_settings')->where('name',$key)->first();
                    if($exist){
                        DB::table('school_settings')->where('name',$key)->update(['data' => $item]);
                    } else {
                        $data = [
                            'name'  => $key,
                            'data'  => $item ?? '',
                            'type'  => 'string',
                            'school_id' => Auth::user()->school_id
                        ];
                        DB::table('school_settings')->insert($data);
                    }
                }
            }
            DB::commit();
            $this->cache->removeSchoolCache(config('constants.CACHE.SCHOOL.SETTINGS'));
            return redirect()->route('school-settings.e-invoice.index')->with('success', 'Data Updated Successfully');
        }  catch (Throwable $e) {
            DB::rollBack();
            return redirect()->route('school-settings.e-invoice.index')->with('error', 'Something went wrong.');
        }
    }

    public function searchTaxPayerTin(Request $request) {
        $schoolId = auth()->user()->school_id;
        
        $taxPayerName = (string)$request->input('tax_payer_name', '');
        $idType = $request->input('id_type', ''); 
        $idValue = $request->input('id_value', '');
            
        if (empty($idType) || empty($idValue)) {
            return response()->json([
                'success' => false,
                'message' => 'Required fields are missing',
                'errors' => [
                    'id_type' => empty($idType) ? 'ID type is required' : null,
                    'id_value' => empty($idValue) ? 'ID value is required' : null
                ]
            ], 400);
        }
            
        return EInvoiceHelper::searchTaxPayerTin($schoolId, $taxPayerName, $idType, $idValue);
    }

    public function validateTaxPayerTin(Request $request) {
        $schoolId = auth()->user()->school_id;

        $tin = $request->input('tin', '');
        $idType = $request->input('id_type', '');
        $idValue = $request->input('id_value', '');
            
        if (empty($tin) || empty($idType) || empty($idValue)) {
            return response()->json([
                'success' => false,
                'message' => 'All fields are required',
                'errors' => [
                    'tin' => empty($tin) ? 'TIN is required' : null,
                    'id_type' => empty($idType) ? 'ID type is required' : null,
                    'id_value' => empty($idValue) ? 'ID value is required' : null,
                ]
            ], 400);
        }
            
        return EInvoiceHelper::validateTaxPayerTin($schoolId, $tin, $idType, $idValue);
    }
}
