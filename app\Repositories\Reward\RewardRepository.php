<?php

namespace App\Repositories\Reward;

use App\Models\Reward;
use App\Repositories\Saas\SaaSRepository;

class RewardRepository extends SaaSRepository implements RewardInterface {

    public function __construct(Reward $model) {
        parent::__construct($model);
    }

    public function getCurrentScore($student_id, $updated_at = null)
    {
        $query = Reward::where('student_id', $student_id)
                        ->whereNull('deleted_at');

        if ($updated_at) {
            $query->where('updated_at', '<=', $updated_at);
        } 


        return $query->value('score_total');
    }

    public function getCurrentRewardPoint($student_id, $updated_at = null,)
    {
        $query = Reward::where('student_id', $student_id)
        ->whereNull('deleted_at');

    if ($updated_at) {
        $query->where('updated_at', '<=', $updated_at);
    } 
    return $query->value('reward_point_total');
}
    
}
