<?php

namespace App\Http\Controllers;

use App\Models\Student;
use App\Models\DepositType;
use App\Models\Students;
use App\Models\StudentDeposit;
use App\Exports\StudentDepositExport;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Facades\Excel;

class StudentDepositController extends Controller
{
    public function index()
    {
        $request = request();
        $query = StudentDeposit::with(['student.user', 'depositType', 'transactions.creator'])
            ->whereHas('student', function($q) use ($request) {
                if ($request->has('search')) {
                    $search = $request->input('search');
                    $q->where('ic_no_2', 'LIKE', "%{$search}%")
                      ->orWhereHas('user', function($uq) use ($search) {
                          $uq->where(DB::raw("CONCAT(first_name, ' ', last_name)"), 'LIKE', "%{$search}%");
                      });
                }
            });

        if ($request->has('status') && $request->status === 'active') {
            $query->where('status', 'active');
        } elseif ($request->has('status') && $request->status === 'refunded') {
            $query->where('status', 'refunded');
        }

        if ($request->has('deposit_type_id') && $request->deposit_type_id) {
            $query->where('deposit_type_id', $request->deposit_type_id);
        }

        if ($request->has('show_deleted') && $request->show_deleted) {
            $query->withTrashed();
        }

        $deposits = $query->latest()->paginate(10);
        $depositTypes = DepositType::orderBy('name')->get();

        return view('student-deposits.index', compact('deposits', 'depositTypes'));
    }

    public function create()
    {
        $depositTypes = DepositType::orderBy('name')->get();
        $students = Students::with('user')
            ->where('school_id', auth()->user()->school_id)
            ->whereHas('user', function($q) {
                $q->orderBy('first_name')->orderBy('last_name');
            })
            ->get();

        return view('student-deposits.create', compact('depositTypes', 'students'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'student_ids' => 'required|array',
            'student_ids.*' => 'exists:students,id',
            'deposit_type_id' => 'required|exists:deposit_types,id',
            'amount' => 'required|numeric|min:0',
            'remark' => 'nullable|string'
        ]);

        DB::transaction(function() use ($request) {
            foreach ($request->student_ids as $studentId) {
                $deposit = StudentDeposit::create([
                    'student_id' => $studentId,
                    'deposit_type_id' => $request->deposit_type_id,
                    'amount' => $request->amount,
                    'status' => 'active',
                    'remark' => $request->remark,
                    'created_by' => Auth::id()
                ]);

                $deposit->transactions()->create([
                    'transaction_type' => 'payment',
                    'amount' => $request->amount,
                    'remark' => 'Initial deposit payment',
                    'created_by' => Auth::id()
                ]);
            }
        });

        return redirect()
            ->route('student-deposits.index')
            ->with('success', 'Deposits created successfully');
    }

    public function export()
    {
        return Excel::download(new StudentDepositExport, 'student-deposits-' . date('Y-m-d') . '.xlsx');
    }

    public function show(StudentDeposit $studentDeposit)
    {
        $studentDeposit->load(['student.user', 'depositType', 'transactions.creator']);
        return view('student-deposits.show', compact('studentDeposit'));
    }

    public function processRefund(Request $request, StudentDeposit $studentDeposit)
    {
        $request->validate([
            'amount' => [
                'required',
                'numeric',
                'min:0',
                function($attribute, $value, $fail) use ($studentDeposit) {
                    if ($value > $studentDeposit->getRemainingAmount()) {
                        $fail('Refund amount cannot be greater than remaining amount.');
                    }
                }
            ],
            'remark' => 'required|string'
        ]);

        if (!$studentDeposit->canBeRefunded()) {
            return back()->with('error', 'This deposit cannot be refunded.');
        }

        if ($studentDeposit->processRefund($request->amount, $request->remark, Auth::id())) {
            return redirect()
                ->route('student-deposits.show', $studentDeposit)
                ->with('success', 'Refund processed successfully');
        }

        return back()->with('error', 'Failed to process refund.');
    }

    public function destroy(StudentDeposit $studentDeposit)
    {
        if ($studentDeposit->status !== 'active') {
            return redirect()
                ->route('student-deposits.index')
                ->with('error', 'Only active deposits can be deleted');
        }

        $studentDeposit->delete();

        return redirect()
            ->route('student-deposits.index')
            ->with('success', 'Deposit deleted successfully');
    }

    public function restore($id)
    {
        $studentDeposit = StudentDeposit::withTrashed()->findOrFail($id);
        $studentDeposit->restore();

        return redirect()
            ->route('student-deposits.index')
            ->with('success', 'Deposit restored successfully');
    }
}