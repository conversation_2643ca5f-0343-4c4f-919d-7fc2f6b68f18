<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Document extends Model {
    protected $fillable = [
        'title', 
        'description', 
        'session_year_id', 
        'school_id', 
    ];

    public function classes() {
        return $this->hasMany(DocumentClass::class);
    }
    protected static function boot() {
        parent::boot();
        static::deleting(static function ($document) { // before delete() method call this
            if ($document->file) {
                foreach ($document->file as $file) {
                    if (Storage::disk('public')->exists($file->getRawOriginal('file_url'))) {
                        Storage::disk('public')->delete($file->getRawOriginal('file_url'));
                    }
                }

                $document->file()->delete();
            }
        });
    }
}

