<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('credit_note', function (Blueprint $table) {
            $table->string('sign_value', 1000)->nullable()->after('tax_percentage'); 
        });

        Schema::table('debit_note', function (Blueprint $table) {
            $table->string('sign_value', 1000)->nullable()->after('tax_percentage'); 
        });

        Schema::table('refund_note', function (Blueprint $table) {
            $table->string('sign_value', 1000)->nullable()->after('tax_percentage'); 
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('credit_note', function (Blueprint $table) {
            $table->dropColumn('sign_value');
        });

        Schema::table('debit_note', function (Blueprint $table) {
            $table->dropColumn('sign_value');
        });

        Schema::table('refund_note', function (Blueprint $table) {
            $table->dropColumn('sign_value');
        });
    }
};