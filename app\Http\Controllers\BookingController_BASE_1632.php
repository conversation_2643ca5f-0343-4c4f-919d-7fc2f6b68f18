<?php

namespace App\Http\Controllers;
use App\Services\ResponseService;


use App\Repositories\ClassSchool\ClassSchoolInterface;
use App\Repositories\ClassSection\ClassSectionInterface;
use App\Repositories\SubjectTeacher\SubjectTeacherInterface;
use App\Repositories\Subject\SubjectInterface;
use App\Repositories\FeesType\FeesTypeInterface;
use App\Services\CachingService;
use App\Repositories\Student\StudentInterface;
use App\Services\BootstrapTableService;
use \DateTime;


use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Auth;
use Throwable;
use Exception;

class BookingController extends Controller
{
    private StudentInterface $student;
    private SubjectTeacherInterface $subjectTeacher;
    private ClassSchoolInterface $class;
    private CachingService $cache;
    private ClassSectionInterface $classSection;
    private SubjectInterface $subject;

    public function __construct(
        CachingService $cache,
        ClassSchoolInterface $classSchool,
        StudentInterface $student,
        ClassSectionInterface $classSection,
        SubjectTeacherInterface $subjectTeacher,
        SubjectInterface $subject
    ) {

        $this->cache = $cache;
        $this->class = $classSchool;
        $this->student = $student;
        $this->classSection = $classSection;
        $this->subjectTeacher=$subjectTeacher;
        $this->subject=$subject;
        }
    

    public function index() {
        ResponseService::noFeatureThenRedirect('Booking Management');
        $teacher="";
        $classes = $this->classSection->all(['*'], ['class', 'class.stream', 'section', 'medium']);
        $simpleClassDropdown = $this->class->builder()->pluck('name','id');
        $subjects = DB::table('subjects as s')
            ->where('s.school_id', Auth::user()->school_id)
            ->whereNull('s.deleted_at')
            ->where(function($query) {
                $query->whereExists(function ($q) {
                    $q->select(DB::raw(1))
                      ->from('subject_teachers')
                      ->join('class_subjects', 'subject_teachers.class_subject_id', '=', 'class_subjects.id')
                      ->whereRaw('class_subjects.subject_id = s.id');
                })
                ->orWhereExists(function ($q) {
                    $q->select(DB::raw(1))
                      ->from('class_teachers');
                });
            })
            ->select(
                's.id',
                's.name',
                's.type'
            )
            ->distinct()
            ->get();
        
        $booking_category=DB::table('booking_category')
                ->where('school_id',Auth::user()->school_id)
                ->whereNULL('deleted_at')
                ->get();

    
        if(Auth::user()->hasRole('Teacher')) {   
            $teacher=DB::table('users')->where('id',Auth::user()->id)->select('id',DB::raw('concat(first_name," ",last_name) as name'))->first();                      
            
        } 
        
        return view('booking.index', compact('simpleClassDropdown','classes','teacher','subjects','booking_category'));

    }

    public function store(Request $request) {
        $request->validate([
            'title'             => 'required',  // This is the booking_category_id
            'teacher_id'        => 'required',
            'booking_slot_list' => 'required|array'
        ]);
        $bookingSlot=$request->booking_slot_list;

        try{
            DB::beginTransaction();
            foreach($bookingSlot as $slot){
                $start_time= strtotime($slot['start_time']);
                $end_time= strtotime($slot['end_time']);
                $date=date_create($slot['date']);
                if($end_time > $start_time){
                    if(Auth::user()->hasRole('Teacher')){
                        $data=[
                            'booking_category_id' => $request->title,  // Changed from 'title' to 'booking_category_id'
                            'title'              => DB::table('booking_category')->where('id', $request->title)->value('title'), // Add this line to include the title
                            'teacher_id'        => $request->teacher_id,
                            'date'              => date_format($date,"Y-m-d"),
                            'start_time'        => $slot['start_time'],
                            'end_time'          => $slot['end_time'],
                            'capacity'          => $slot['capacity'],
                            'subject_id'        =>$request->subject_id,
                            'school_id'         =>Auth::user()->school_id,
                            'class_section_id'  =>$request->class_section,
                            'status'            => 'pending'    
                            
                        ];
                        $notify=[
                            'user_id'       =>$request->teacher_id,
                            'school_id'     =>Auth::user()->school_id,
                            'date'          =>date('Y-m-d'),
                            'status'        =>0,
                            'type'          =>0
                        ];
                        DB::table('admission_notification')->insert($notify);
                    } else {
                        $data=[
                            'booking_category_id' => $request->title,  // Changed from 'title' to 'booking_category_id'
                            'title'              => DB::table('booking_category')->where('id', $request->title)->value('title'), // Add this line to include the title
                            'teacher_id'        => $request->teacher_id,
                            'date'              => date_format($date,"Y-m-d"),
                            'start_time'        => $slot['start_time'],
                            'end_time'          => $slot['end_time'],
                            'capacity'          => $slot['capacity'],
                            'subject_id'        =>$request->subject_id,
                            'school_id'         =>Auth::user()->school_id,
                            'class_section_id'  =>$request->class_section,
                            'status'            => 'approve'    
                        ];
                    }
                        
                    DB::table('booking_slot')->insert($data);

                    if(Auth::user()->hasRole('School Admin')){
                        $userData = DB::table('users')->where('id', $request->teacher_id)->first();
                        $userName = $userData->first_name . " " . $userData->last_name;
                        $title = 'New Booking Slots Assigned';
                        $body = "Hello ".$userName.", new booking slots have been created for you by the admin.";
                        $type = 'Booking Added';
                        send_notification([$request->teacher_id], $title, $body, $type);
                    }

                } else {
                    return ResponseService::errorResponse('The end date must be later than the start date.');
                }
            }
            DB::commit();
            ResponseService::successResponse('Data Stored Successfully');


        } catch(Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "Booking Controller -> Store Method");
            ResponseService::errorResponse();

        }
        
    }
    public function show(Request $request) {
        $offset = request('offset', 0);
        $limit = request('limit', 99999);
        $sort = request('sort', 'date');
        $order = request('order', 'DESC');
        $search = request('search');
        $showDeleted = request('show_deleted');
        $date=request('date');
        $class_section=request('class_section_id');
        $filter_subject=request('subject');
        $filter_student=request('student');
        

        $query = DB::table('booking_slot')
    ->join('users', 'booking_slot.teacher_id', '=', 'users.id')
    ->leftjoin('booking_category', 'booking_slot.booking_category_id', '=', 'booking_category.id') 
    ->where('booking_slot.school_id', Auth::user()->school_id)
    ->select(
        'booking_slot.*',
        DB::raw('concat(users.first_name, " ", users.last_name) as teacher'), 
        'booking_category.title as category_title',
        'booking_category.id as title_id'
    )
    ->when($search, function ($q) use ($search) {
        $q->where(function ($q) use ($search) {
            if (strtolower($search) == 'rejected') {
                $q->where('booking_slot.status', 'reject');
            } else if (strtolower($search) == 'approved') {
                $q->where('booking_slot.status', 'approve');
            } else if (strtolower($search) == 'pending approve') {
                $q->where('booking_slot.status', 'pending');
            } else {
                $q->whereRaw("concat(users.first_name, ' ', users.last_name) LIKE '%" . $search . "%'")
                    ->orWhere('users.first_name', 'LIKE', "%$search%")
                    ->orWhere('users.last_name', 'LIKE', "%$search%")
                    ->orWhere('booking_slot.end_time', 'LIKE', "%$search%")
                    ->orWhere('booking_slot.status', 'LIKE', "%$search%")
                    ->orWhere('booking_slot.start_time', 'LIKE', "%$search%")
                    ->orWhere('booking_category.title', 'LIKE', "%$search%");
            }
        });
    })
    ->orderByRaw("
        CASE 
            WHEN booking_slot.status = 'pending' THEN 1
            WHEN booking_slot.updated_at IS NOT NULL THEN 2
        END
    ")

    ->orderBy('booking_slot.updated_at', 'desc') // Show latest updates first
    ->orderBy('booking_slot.date', 'desc'); // Optional: Sort by date within groups
    
    // Apply other filters
    if ($date) {
        $date = date_format(date_create($date), 'Y-m-d');
        $query->where('booking_slot.date', 'LIKE', "%$date%");
    }
    if ($class_section) {
        $query->where('booking_slot.class_section_id', $class_section);
    }
    if ($filter_subject) {
        $query->where('booking_slot.subject_id', $filter_subject);
    }
    
    // Fetch results
    $slotList = $query->get();
    
        
        $booked=DB::table('booking')
        ->join('students', 'booking.student_id','=','students.id')
        ->join('users','students.user_id','=','users.id')
        ->where('booking.school_id',Auth::user()->school_id)
        ->whereNull('booking.deleted_at')
        ->select(
        'booking.slot_id','booking.student_id',
        DB::raw('concat(users.first_name," ", users.last_name) as student')
        )->get();
        $classes = $this->classSection->all(['*'], ['class', 'class.stream', 'section', 'medium']);
        $subject=DB::table('subjects')
                ->where('school_id',Auth::user()->school_id)
                ->get();


        if(Auth::user()->hasRole('Teacher')){
            $query->where('booking_slot.teacher_id',Auth::user()->id);
        }
        

        $groupBooked=$booked->mapToGroups(function($book){
            return [$book->slot_id=>[

                'student_id' =>$book->student_id,
                'student' =>$book->student

                ]
            ];
        });

        $total = $query->count();
        $query->orderBy($sort, $order)->skip($offset)->take('999');
        if(!empty($showDeleted)){
            $query->whereNotNull('booking_slot.deleted_at');
        } else{
            $query->whereNull('booking_slot.deleted_at');
        }
        $slotList=$query->get();

        $slotList = $slotList->map(function($item) use ($groupBooked,$classes,$subject) {
        
            $item->student_list = isset($groupBooked[$item->id]) ? $groupBooked[$item->id] : [];
         
            foreach($classes as $class){
                if($class->id==$item->class_section_id) {
                    $item->class_name=$class->full_name;
                }
            }
            foreach($subject as $subjects){
                if($subjects->id==$item->subject_id) {
                    $item->subject=$subjects->name . '-' .$subjects->type;
                }
            }
             
            return $item;
        });

        if($filter_student){
            $slotList=$slotList->filter(function($item) use ($filter_student){
                $studentListCollection = collect($item->student_list);
                $student = $studentListCollection->pluck('student_id')->toArray();
                return in_array($filter_student, $student);
                
            });
            $slotList = $slotList->values();
            $total=count($slotList);
        }
        $slotList = $slotList->take($limit);
        $res = $slotList;

        $bulkData['total'] = $total;
        $rows = array();
        foreach ($res as $row) {
            $operate = '';
            $status ='';
            if ($request->show_deleted) {
                    $operate .= BootstrapTableService::restoreButton(route('booking.restore', $row->id));
                    $operate .= BootstrapTableService::trashButton(route('booking.delete', $row->id)   );
                
            } else {
                if(Auth::user()->hasRole('School Admin')){
                    if($row->status =="pending") {
                        $status .= BootstrapTableService::button('fa-solid fa-check',route('booking.verifyslot', ['id' => $row->id,'status'=>'approve']), ['btn-gradient-success','btn-approve'],['title'=>'Approve']);
                        $status .= BootstrapTableService::button('fa-solid fa-xmark',route('booking.verifyslot', ['id' => $row->id,'status'=>'reject']), ['btn-gradient-danger','btn-reject'],['title'=>'Reject']);
                        $operate .= BootstrapTableService::editButton(route('booking.update', $row->id));            
                        $operate .= BootstrapTableService::deleteButton(route('booking.destroy', $row->id));
                        $operate .= BootstrapTableService::button('fa-solid fa-plus',route('booking.getstudent', $row->id),['btn-gradient-success','btn-addStudent'],['title'=>'Add Student','data-toggle'=>'modal','data-target'=>'#addstudent']);
                    } else if($row->status =="approve") {
                        $status .= "Approved";
                        $operate .= BootstrapTableService::editButton(route('booking.update', $row->id)); 
                        $operate .= BootstrapTableService::button('fa-solid fa-plus',route('booking.getstudent', $row->id),['btn-gradient-success','btn-addStudent'],['title'=>'Add Student','data-toggle'=>'modal','data-target'=>'#addstudent']);

                    } else if($row->status=="reject") {
                        $status .= "Rejected";
                        $operate .= BootstrapTableService::editButton(route('booking.update', $row->id));            
                        $operate .= BootstrapTableService::deleteButton(route('booking.destroy', $row->id));

                    } 

                } else{
                  
                    if($row->status =="pending") {
                        $status .= "Pending Approve";
                        $operate .= BootstrapTableService::editButton(route('booking.update', $row->id));            
                        $operate .= BootstrapTableService::deleteButton(route('booking.destroy', $row->id));
                    } else if($row->status =="approve") {
                        $status .= "Approved";
                        $operate .= BootstrapTableService::editButton(route('booking.update', $row->id)); 
                    } else if($row->status=="reject") {
                        $status .= "Rejected";
                        $operate .= BootstrapTableService::editButton(route('booking.update', $row->id));            
                        $operate .= BootstrapTableService::deleteButton(route('booking.destroy', $row->id));
                        
                    } 
                }
            }
            
            $tempRow = (array) $row;
            if(!$row->category_title) {
                $tempRow['category_title'] = $row->title;
            }
            $tempRow['slot_number']= count($row->student_list) . '/' . $row->capacity;    
            $day=date('D',strtotime($row->date));
            $start_time=date('h:ia',strtotime($row->start_time));
            $end_time=date('h:ia',strtotime($row->end_time));
            $time= $day . ' '. $start_time . '-' . $end_time;
            $tempRow['time'] = $time;
            $tempRow['status'] =$status;
            $tempRow['operate'] = $operate;
            if($row->remark){
                $tempRow['remark']=$row->remark;
            }
            
            $rows[] = $tempRow;
            
        }

        $bulkData['rows'] = $rows;
        return response()->json($bulkData);
    } 

    public function verifySlot($id,$status,Request $request) {
        $remark=$request->remark;
        if(!isset($remark)){
            DB::table('booking_slot')
            ->where('id',$id)
            ->update(['status'=>$status,'remark'=>Null]);
        } else{
            DB::table('booking_slot')
            ->where('id',$id)
            ->update(['status'=>$status,'remark'=>$remark]);

        }

        if($status==='approve'){
            
                $slot=DB::table('booking_slot')->where('id',intval($id))->first();
                $teacher=DB::table('users')->where('id',$slot->teacher_id)->select('id',DB::raw('concat(first_name, " " ,last_name) as name'))->first();
                $subject=DB::table('subjects')->where('id',$slot->subject_id)->select(DB::raw('concat(name, " - ",type) as name'))->first();
                $start_time=date('h:i a', strtotime($slot->start_time));
                $end_time=date('h:i a', strtotime($slot->end_time));
                $user=array();
                $user[]=$teacher->id;
                $title='Booking Approved';
                $type='booking notification';
                $body='The booking for the ' .$subject->name.$start_time . ' - '. $end_time .' on ' . $slot->date. ' has been approved by admin.';
                send_notification($user, $title, $body, $type);
                


            return response()->json(['error'=>false,'message'=>'The slot has been approved']);
            
        } else {
            $slot=DB::table('booking_slot')->where('id',intval($id))->first();
                $teacher=DB::table('users')->where('id',$slot->teacher_id)->select('id',DB::raw('concat(first_name, " " ,last_name) as name'))->first();
                $subject=DB::table('subjects')->where('id',$slot->subject_id)->select(DB::raw('concat(name, " - ",type) as name'))->first();
                $start_time=date('h:i a', strtotime($slot->start_time));
                $end_time=date('h:i a', strtotime($slot->end_time));
                $user=array();
                $user[]=$teacher->id;
                $title='Booking Rejected';
                $type='booking notification';
                $body='The booking for the ' .$subject->name.$start_time . ' - '. $end_time .' on ' . $slot->date. ' has been rejected by admin.';
                send_notification($user, $title, $body, $type);

            
            return response()->json(['error'=>false,'message'=>'The slot has been rejected']);

        }
        
    }

    public function destroy($id)
    {
        try {
            DB::beginTransaction();
            DB::table('booking_slot')->where('id', $id)->update(['deleted_at' => now()]);
            
            DB::commit();
            ResponseService::successResponse('Data Delete Successfully');
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "Booking Controller -> Trash Method");
            ResponseService::errorResponse();
        }
    }
    public function trash($id) {
        try {
            DB::beginTransaction();
            DB::table('booking_slot')->where('id', $id)->delete();
            
            DB::commit();
            ResponseService::successResponse('Data Delete Successfully');
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "Booking Controller -> Trash Method");
            ResponseService::errorResponse();
        }

    }
    public function restore($id) {

        try {
            DB::beginTransaction();
            DB::table('booking_slot')->where('id', $id)->update(['deleted_at' => null]);
            
            DB::commit();
            ResponseService::successResponse('Data Restored Successfully');
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "Booking Controller -> restore Method");
            ResponseService::errorResponse();
        }

    }

    public function update(Request $request,$id) {
        
        $slot=DB::table('booking_slot')->where('id',$id)->first();
        try{
            DB::beginTransaction();
            $start_time= strtotime($request->start_time);
            $end_time= strtotime($request->end_time);

            if($end_time > $start_time){

                if(Auth::user()->hasRole('Teacher')) {
                    
                    $data=['booking_category_id'=>$request->title,
                    'date'=>date_format(date_create($request->date),"Y-m-d"),
                    'start_time'=>$request->start_time,
                    'end_time'=>$request->end_time,
                    'capacity'=>$request->capacity,
                    'class_section_id'=>$request->class_section,
                    'subject_id'=>$request->subject_id,
                    'status' =>'pending',
                    'remark'=>null,
                ];
                
                } else{
                    
                    if($request->remark){
                        
                        $remark=$request->remark;
                        
                    } else{
                        $remark=null;
                    }
                    if($request->status){
                        $status=$request->status;
                    } else {
                        $status='approve';
                    }
                    $data=[
                        'teacher_id'=>$request->teacher_id,
                        'booking_category_id'=>$request->title,
                        'date'=>date_format(date_create($request->date),"Y-m-d"),
                        'start_time'=>$request->start_time,
                        'end_time'=>$request->end_time,
                        'capacity'=>$request->capacity,
                        'class_section_id'=>$request->class_section,
                        'subject_id'=>$request->subject_id,
                        'status' =>$status,
                        'remark'=>$remark,
                    ];
                
                }
        } else{
            return ResponseService::errorResponse('The end date must be later than the start date.');
        }
            DB::table('booking_slot')->where('id',$id)->update($data);
            DB::commit();
            ResponseService::successResponse('Data Upadated Successfully');
        } catch(Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "Booking Controller -> update Method");
            ResponseService::errorResponse();

        }
    }

    public function getSubjectByClass(Request $request){
           if(Auth::user()->hasrole('Teacher')){
               $subject=DB::table('class_teachers')
                        ->join('class_sections', 'class_sections.id', '=', 'class_teachers.class_section_id')
                        ->leftJoin('class_subjects', 'class_subjects.class_id', '=', 'class_sections.class_id')
                        ->leftJoin('subjects', 'class_subjects.subject_id', '=', 'subjects.id')
                        ->leftJoin('subject_teachers', function ($join) use($request) {
                            $join->on('subject_teachers.class_subject_id', '=', 'class_subjects.id')
                            ->on('class_sections.id','subject_teachers.class_section_id')
                            ->where('class_sections.id',$request->class_section_id);
                        })
                        ->where('subject_teachers.teacher_id',Auth::user()->id)
                        ->where(function ($query) {
                            $query->whereNull('class_subjects.deleted_at')
                            ->orWhereNull('class_subjects.id');
                        })
                        ->where(function ($query) {
                            $query->where('class_subjects.type', 'Compulsory')
                            ->orWhere('class_subjects.type', 'Elective')
                            ->orWhereNull('class_subjects.id');
                        })
                        ->select('subjects.id','subjects.name','subjects.type')
                        ->distinct()
                        ->get();
            } else {
                $subject = DB::table('class_sections')
                    ->where('class_sections.id', $request->class_section_id)
                    ->join('class_subjects', 'class_subjects.class_id', '=', 'class_sections.class_id')
                    ->join('subjects', 'class_subjects.subject_id', '=', 'subjects.id')
                    ->leftJoin('subject_teachers', function ($join) use($request) {
                        $join->on('subject_teachers.class_subject_id', '=', 'class_subjects.id')
                            ->where('subject_teachers.class_section_id', $request->class_section_id);
                    })
                    ->leftJoin('class_teachers', function($q){
                        $q->on('class_teachers.class_section_id', '=', 'class_sections.id')
                            ->where('class_teachers.school_id', Auth::user()->school_id);
                    })
                    ->where(function ($query) {
                        $query->whereNull('class_subjects.deleted_at')
                            ->orWhereNull('class_subjects.id');
                    })
                    ->where(function ($query) {
                        $query->where('class_subjects.type', 'Compulsory')
                            ->orWhere('class_subjects.type', 'Elective');
                    })
                    ->select('subjects.id', 'subjects.name', 'subjects.type')
                    ->distinct()
                    ->get();

                $teacher = DB::table('subject_teachers')
                    ->join('users', 'users.id', '=', 'subject_teachers.teacher_id')
                    ->where('subject_teachers.school_id', Auth::user()->school_id)
                    ->where('subject_teachers.class_section_id', $request->class_section_id) // Ensure correct class section
                    ->select('users.id', 'subject_teachers.subject_id', DB::raw('concat(users.first_name," ",users.last_name) as full_name'))
                    ->distinct()
                    ->get();

                $teacher = $teacher->mapToGroups(function($item){
                    return [$item->subject_id => [
                        'teacher_id' => $item->id,
                        'name' => $item->full_name
                    ]];
                });

                $subject = $subject->map(function($item) use($teacher){
                    $item->teacher = isset($teacher[$item->id]) ? $teacher[$item->id] : [];
                    return $item; 
                });
            }
                
        return response()->json(['error'=>false,'subject'=>$subject]);
                
}

public function calendar()
{
    $query = DB::table('booking_slot')
        ->join('users', 'booking_slot.teacher_id', '=', 'users.id')
        ->join('booking_category', 'booking_slot.booking_category_id', '=', 'booking_category.id')  
        ->join('subjects', 'subjects.id', '=', 'booking_slot.subject_id') // Correct the join
        ->where('booking_slot.school_id', Auth::user()->school_id)
        ->select(
            'booking_slot.*',
            DB::raw('concat(users.first_name, " ", users.last_name) as teacher'), 
            'booking_category.title as category_title',
            'booking_category.id as title_id',
            'subjects.name as subject' // Fetch the subject name
        )
        ->get();

// Fetch all student IDs as an array
$student_ids = DB::table('booking')
    ->where('school_id', Auth::user()->school_id)
    ->pluck('student_id')
    ->toArray(); // Converts collection to array

// Map over each booking slot
$query = $query->map(function ($slot) use ($student_ids) {
    // Fetch students in this particular slot
    $studentsInSlots = DB::table("booking as b")
        ->join("students as s", "s.id", "=", "b.student_id")
        ->join("users as u", "u.id", "=", "s.user_id")
        ->join("booking_slot as bs", "bs.id", "=", "b.slot_id")
        ->select(DB::raw("CONCAT(u.first_name, ' ', u.last_name) as fullname"))
        ->whereIn("b.student_id", $student_ids)  // Use whereIn to match any student ID
        ->where("b.slot_id", $slot->id)
        ->whereNull("b.deleted_at") 
        ->get();
        

    // Create a string with student names, separated by a comma
    $studentsString = $studentsInSlots->pluck('fullname')->implode(', ');
    
    // Add the studentsString to the slot object
    $slot->students = $studentsString;
    return $slot;
});



// Optionally, if you want to check the result:
   
    
    return view('booking.calendarindex', compact('query'));
}

public function getBookings(Request $request)
{
    $bookings = Booking::select('id', 'title', 'date', 'start_time', 'end_time')
        ->get()
        ->map(function ($booking) {
            return [
                'id' => $booking->id,
                'title' => $booking->title,
                'start' => $booking->date . 'T' . $booking->start_time,
                'end' => $booking->date . 'T' . $booking->end_time,
            ];
        });

    return response()->json($bookings);
}

// public function showCalendar()
// {
//     $bookings = Booking::all(); // Adjust this to fit your database schema
//     $timetableConfigurations = TimetableConfiguration::all(); // Example if configurations are needed
//     return view('calendarindex', compact('bookings', 'timetableConfigurations'));
// }




            
    public function getStudent($id){
                
        $student = DB::table('booking_slot')
                    ->join('students', 'students.class_section_id', '=', 'booking_slot.class_section_id')
                    ->join('users', 'users.id', '=', 'students.user_id')
                    ->where('booking_slot.id', $id)
                    ->where('students.school_id',Auth::user()->school_id)
                    ->select('students.id', DB::raw('concat(first_name, " " , last_name) as full_name'), 'users.status')
                    ->get();

        return response()->json($student);
    }
    

    public function studentBook(Request $request) {
        $studentId=[];
        if($request->student_list){
            $studentId =explode(',',$request->student_list);
        } 
        if($request->cancelSelected){
            $Cancelstudent=explode(',',$request->cancelSelected);
            
        }
        $capacity=DB::table('booking_slot')->where('id',intval($request->slot_id))->value('capacity');
        if(count($studentId)>$capacity) {
            return redirect()->back()->with('error','The slot is fully booked.');

        }
        
        try{
            DB::beginTransaction();
            if($studentId){
                foreach($studentId as $student){
                    $extistingStudent=DB::table('booking')
                    ->where('student_id',intval($student))
                    ->where('slot_id',intval($request->slot_id))
                    ->where('school_id',Auth::user()->school_id)
                    ->whereNull('deleted_at')
                    ->first();
                    if(isset($Cancelstudent)){
                        foreach($Cancelstudent as $key=> $cancel){
                            if($cancel!==''){
                                DB::table('booking')
                                ->where('student_id',intval($cancel))
                                ->where('slot_id',intval($request->slot_id))
                                ->where('school_id',Auth::user()->school_id)
                                ->update(['deleted_at'=>now()]);
                                $slot=DB::table('booking_slot')->where('id',intval($request->slot_id))->first();
                                $guardian=DB::table('students')->join('users','users.id','=','students.user_id')->where('students.id',intval($cancel))->select(DB::raw('concat(first_name, " " ,last_name) as student_name'),'guardian_id')->first();
                                $teacher=DB::table('users')->where('id',$slot->teacher_id)->select('id',DB::raw('concat(first_name, " " ,last_name) as name'))->first();
                                $subject=DB::table('subjects')->where('id',$slot->subject_id)->select(DB::raw('concat(name, " - ",type) as name'))->first();
                                $start_time=date('h:i a', strtotime($slot->start_time));
                                $end_time=date('h:i a', strtotime($slot->end_time));
                                $notifyUser=array();
                                $user=array();
                                $user[]=$teacher->id;
                                $notifyUser[]=$guardian->guardian_id;
                                $notifyUser[]=intval($cancel);
                                $title='Booking Canceled';
                                $type='booking notification';
                                $teacherbody = $guardian->student_name . " has successfully canceled the slot for " . $subject->name . " on " . $slot->date . " from " . $start_time . " to " . $end_time . ".";
                                $body='The booking for the ' .$subject->name. ' with ' .$teacher->name. ' at Slot '.$start_time . ' - '. $end_time .' on ' . $slot->date. ' has been canceled by admin.';
                                send_notification($notifyUser, $title, $body, $type);
                                send_notification($user, $title, $teacherbody, $type);
                                unset($Cancelstudent[$key]);

                            

                            } 
                        }
                    }
                          
                    if($extistingStudent || $student===''){
                        continue;
                    } else{

                        $data=[
                            'slot_id'=>intval($request->slot_id),
                            'student_id'=>intval($student),
                            'school_id'=>Auth::user()->school_id
                        ];
                        DB::table('booking')->insert($data);
                        $slot=DB::table('booking_slot')->where('id',intval($request->slot_id))->first();
                        $guardian=DB::table('students')->join('users','users.id','=','students.user_id')->where('students.id',intval($student))->select(DB::raw('concat(first_name, " " ,last_name) as student_name'),'guardian_id')->first();
                        $teacher=DB::table('users')->where('id',$slot->teacher_id)->select('id',DB::raw('concat(first_name, " " ,last_name) as name'))->first();
                        $subject=DB::table('subjects')->where('id',$slot->subject_id)->select(DB::raw('concat(name, " - ",type) as name'))->first();
                        $start_time=date('h:i a', strtotime($slot->start_time));
                        $end_time=date('h:i a', strtotime($slot->end_time));
                        $notifyUser=array();
                        $user=array();
                        $user[]=$teacher->id;
                        $notifyUser[]=$guardian->guardian_id;
                        $notifyUser[]=intval($student);
                        $title='Successful Booking';
                        $type='booking notification';
                        $teacherbody = $guardian->student_name . " has successfully booked a slot for " . $subject->name . " on " . $slot->date . " from " . $start_time . " to " . $end_time . ".";
                        $body='The booking for the ' .$subject->name. ' with ' .$teacher->name. ' at Slot '.$start_time . ' - '. $end_time .' on ' . $slot->date. ' has been successfully created by the admin.';
                        send_notification($notifyUser, $title, $body, $type);  
                        send_notification($user, $title, $teacherbody, $type);  

                        $user_id = DB::table('students')
                                    ->where("id",$studentId)
                                    ->select('user_id')
                                    ->first();

                        $data = [
                            'user_id' => $user_id->user_id,
                            'school_id' => Auth::user()->school_id, 
                            'date' => now(), 
                            'status' => 0, 
                            'type' => 2,
                        ];
                        DB::table('admission_notification')->insert($data);
                    }
                
                }
            } else if($Cancelstudent){
                foreach($Cancelstudent as $cancel){
                    if($cancel ===''){
                        continue;
                    }
                    DB::table('booking')
                    ->where('student_id',intval($cancel))
                    ->where('slot_id',intval($request->slot_id))
                    ->where('school_id',Auth::user()->school_id)
                    ->update(['deleted_at'=>now()]);
                    $slot=DB::table('booking_slot')->where('id',intval($request->slot_id))->first();
                    $guardian=DB::table('students')->join('users','users.id','=','students.user_id')->where('students.id',intval($cancel))->select(DB::raw('concat(first_name, " " ,last_name) as student_name'),'guardian_id')->first();
                    $teacher=DB::table('users')->where('id',$slot->teacher_id)->select('id',DB::raw('concat(first_name, " " ,last_name) as name'))->first();
                    $subject=DB::table('subjects')->where('id',$slot->subject_id)->select(DB::raw('concat(name, " - ",type) as name'))->first();
                    $start_time=date('h:i a', strtotime($slot->start_time));
                    $end_time=date('h:i a', strtotime($slot->end_time));
                    $notifyUser=array();
                    $user=array();
                    $user[]=$teacher->id;
                    $notifyUser[]=$guardian->guardian_id;
                    $notifyUser[]=intval($cancel);
                    $title='Booking Canceled';
                    $type='booking notification';
                    $teacherbody = $guardian->student_name . " has successfully canceled the slot for " . $subject->name . " on " . $slot->date . " from " . $start_time . " to " . $end_time . ".";
                    $body='The booking for the ' .$subject->name. ' with ' .$teacher->name. ' at Slot '.$start_time . ' - '. $end_time .' on ' . $slot->date. ' has been canceled by admin.';
                    send_notification($notifyUser, $title, $body, $type);
                    send_notification($user, $title, $teacherbody, $type);  

                    $user_id = DB::table('students')
                                ->where("id",$cancel)
                                ->select('user_id')
                                ->first();

                    $data = [
                        'user_id' => $user_id->user_id,
                        'school_id' => Auth::user()->school_id, 
                        'date' => now(), 
                        'status' => 0, 
                        'type' => 3,
                    ];
                    DB::table('admission_notification')->insert($data);
                }
            }
            DB::commit();
            return redirect()->back()->with('success','Data Stored Successfully');
        } catch(Throwable $e) {
            DB::rollBack();
            return redirect()->back()->with('error','Error Occured'.$e->getMessage());
        }
    }

    public function getFilterStudent(Request $request) {
        $students = DB::table('students')
            ->join('users', 'users.id', '=', 'students.user_id')
            ->where('students.class_section_id', $request->class_section_id)
            ->where('students.school_id', Auth::user()->school_id)
            ->select(
                'students.id',
                DB::raw('CONCAT(users.first_name, " ", users.last_name) as full_name'),'users.status'  
            )
            ->get();
    
        return response()->json($students);
    }
}   

