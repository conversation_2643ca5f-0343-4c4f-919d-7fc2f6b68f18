<?php

namespace App\Http\Controllers;

use Exception;
use Throwable;
use DOMDocument;
use DOMElement;
use DOMXPath;
use Carbon\Carbon;
use Illuminate\Http\Request;
use chillerlan\QRCode\QRCode;

use App\Models\StudentFeeType;
use Barryvdh\DomPDF\Facade\Pdf;
use App\Services\CachingService;
use App\Services\ResponseService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Facades\Excel;

use Klsheng\Myinvois\Helper\MyInvoisHelper;
use App\Helpers\EInvoiceHelper;
use App\Exports\StudentFeeTypesExport;
use App\Imports\StudentFeeTypesImport;
use App\Services\BootstrapTableService;
use App\Services\EInvoiceFormatService;
use App\Repositories\Student\StudentInterface;
use App\Repositories\FeesType\FeesTypeInterface;
use App\Repositories\ClassSchool\ClassSchoolInterface;
use App\Repositories\ClassSection\ClassSectionInterface;
use App\Repositories\StudentFeeType\StudentFeeTypeInterface;

class DebitNoteController extends Controller
{
    private StudentFeeTypeInterface $studentFeeTypes;
    private StudentInterface $student;
    private FeesTypeInterface $feesType;
    private ClassSchoolInterface $class;
    private CachingService $cache;
    private ClassSectionInterface $classSection;
    private EInvoiceFormatService $eInvoiceFormatService;

    public function __construct(
        StudentFeeTypeInterface $studentFeeTypes,
        CachingService $cache,
        ClassSchoolInterface $classSchool,
        FeesTypeInterface $feesType,
        StudentInterface $student,
        ClassSectionInterface $classSection,
        EInvoiceFormatService $eInvoiceFormatService,
    ) {


        $this->studentFeeTypes = $studentFeeTypes;
        $this->cache = $cache;
        $this->class = $classSchool;
        $this->feesType = $feesType;
        $this->student = $student;
        $this->classSection = $classSection;
        $this->eInvoiceFormatService=$eInvoiceFormatService;
    }

    public function debitNoteIndex(){
        ResponseService::noFeatureThenRedirect('Fees Management');
        ResponseService::noPermissionThenRedirect('fees-type-list');

        $classes = $this->class->all(['*'], ['stream', 'medium', 'stream']);
        $simpleClassDropdown = $this->class->builder()->pluck('name','id');
        $itemCode = DB::table('fees_type_master')->where('school_id',Auth::user()->school_id)->get();
        // $feesTypeData = $this->feesType->all();
        return view('student-fee-types.debit_note', compact('simpleClassDropdown','classes','itemCode'));
    }

    public function debitNoteGetInvoice(Request $request){ 
        $studentId = $request->student_id;
        $invoices = DB::table('student_fees_paids as sfp')
                    ->join('student_fees as sf','sf.id','=','sfp.student_fees_id')
                    ->where('sf.student_id',$studentId)
                    ->get(['sf.id','sf.uid','sf.name']);

        return response()->json(['invoices' => $invoices]);
        
    }

    public function debitNoteStore(Request $request) {
        ResponseService::noFeatureThenRedirect('Fees Management');
        ResponseService::noPermissionThenRedirect('fees-create');
        $request->validate([
            'class_id'                                => 'required|numeric',
            'student_id'                              => 'required',
            'invoice_no'                              => 'required',
            'debit_note_types'                       => 'required|array',
            'debit_note_types.*'                     => 'required|array',
            'debit_note_types.*.debit_note_name'    => 'required',
            'debit_note_types.*.debit_note_amount'  => 'required|numeric',
            'debit_note_types.*.classification_code'                     => 'required',
            'debit_note_types.*.unit'                                    => 'required',
            'debit_note_types.*.quantity'                                => 'required'
        ]);

        if(isset($request->tax_type) && $request->tax_percentage == null) {
            $request->validate([
                'tax_percentage' => 'required|numeric',
            ]);
        }


        try {
            $classData = $this->class->builder()->find($request->class_id);
            DB::beginTransaction();

            $debitDetails = [];
            $sessionYear = $this->cache->getDefaultSessionYear();
            if ($request->student_id) {
                $uid = $this->generateUniqueUid();
                $data = [
                    'class_id'          => $request->class_id,
                    'student_id'        => $request->student_id,
                    'student_fee_id'    => $request->invoice_no,
                    'school_id'         => $classData->school_id,
                    'session_year_id'   => $sessionYear->id,
                    'status'            => 'draft',
                    'uid'               => $uid,
                    'tax_type'          => $request->tax_type,
                    'tax_percentage'    => $request->tax_percentage ?? '0.00',
                ];

                $debit_note_id = DB::table('debit_note')->insertGetId($data);
                foreach ($request->debit_note_types as $data) {
                    $debitDetails[] = array(
                        'school_id'             => $classData->school_id,
                        'debit_note_id'        => $debit_note_id,
                        'classification_code'   => $data['classification_code'],
                        'unit'                  => $data['unit'],
                        'quantity'              => $data['quantity'],
                        "debit_note_name"        => $data['debit_note_name'],
                        "debit_note_amount"      => $data['debit_note_amount'],
                        "tax"                => $data['tax_percentage']
                    );
                }
            }
            if (count($debitDetails) > 0) {
                DB::table('debit_note_details')->insert($debitDetails);
            }

            DB::commit();
            ResponseService::successResponse('Data Stored Successfully');
        } catch (Throwable $e) {
            DB::rollback();
            ResponseService::logErrorResponse($e,"DebitNoteController -> Store Method");
            ResponseService::errorResponse();
        }
    }

    public function debitNoteShow(){
        ResponseService::noFeatureThenRedirect('Fees Management');
        ResponseService::noPermissionThenRedirect('fees-list');
        $offset = request('offset', 0);
        $limit = request('limit', 99999);
        $sort = request('sort', 'id');
        $order = request('order', 'DESC');
        $search = request('search');
        $showDeleted = request('show_deleted');
        // $classId = request('class_id');
        $start_date = request('start_date');
        $start_date = $start_date ? Carbon::createFromFormat('d-m-Y', $start_date)->startOfDay()->format('Y-m-d H:i:s') : null;
        $end_date = request('end_date');
        $end_date = $end_date ? Carbon::createFromFormat('d-m-Y', $end_date)->endOfDay()->format('Y-m-d H:i:s') : null;

        // $sessionYear = $this->cache->getDefaultSessionYear();


        $sql = DB::table('debit_note as cn')
        ->join('students as s', 's.id', '=', 'cn.student_id')
        ->join('users as u', 'u.id', '=', 's.user_id')
        ->leftJoin('debit_note_details as cnd','cnd.debit_note_id','=','cn.id')
        ->select(
            DB::raw("CONCAT_WS(' ', u.first_name, u.last_name) AS full_name"),
            'cn.*',
            DB::raw("
            FORMAT(SUM((cnd.debit_note_amount + (cnd.debit_note_amount * cnd.tax / 100)) * cnd.quantity), 2) AS total_amount,
            FORMAT(cnd.tax, 2) AS tax
        ")
        )
        // ->where('cn.session_year_id', $sessionYear->id)
        ->where('u.school_id', Auth::user()->school_id)
        ->whereNull('u.deleted_at')
        ->when($start_date && $end_date, function ($query) use ($start_date, $end_date) {
            $query->whereBetween('cn.created_at', [$start_date, $end_date]);
        })
        ->when($start_date && !$end_date, function ($query) use ($start_date) {
            $query->where('cn.created_at', '>=', $start_date);
        })
        ->when(!$start_date && $end_date, function ($query) use ($end_date) {
            $query->where('cn.created_at', '<=', $end_date);
        })
        ->when(!$showDeleted, function ($query) {
            $query->whereNull('cn.deleted_at');
        }, function ($query) {
            $query->whereNotNull('cn.deleted_at');
        })->groupBy('cn.id');

        // if($classId){
        //     $sql->where('cn.class_id',$classId);
        // }

        if($search) {
            $sql->where(function ($query) use ($search) {
                $query->where(DB::raw("CONCAT_WS(' ', u.first_name, u.last_name)"), 'LIKE', "%$search%")
                      ->orWhere('cn.status', 'LIKE', "%$search%")
                      ->orWhere(DB::raw("CONCAT('DB', LPAD(cn.uid, 6, '0'))"), 'LIKE', "%$search%");
            });
        }

        $total = $sql->count();
        $sql->orderBy($sort, $order)->skip($offset)->take($limit);
        $res = $sql->get();
        $bulkData = array();
        $bulkData['total'] = $total;
        $rows = array();
        $no = 1;
        foreach ($res as $row) {
            $operate = '';
            $debitNoteEinvoice=DB::table('debit_note_einvoice')->where('debit_note_id',$row->id)->orderBy('id','DESC')->first();
            
            if ($showDeleted) {
                $operate .= BootstrapTableService::restoreButton(route('student-fee-types.debit-note-restore', $row->id));
            } else {
                $operate .= BootstrapTableService::menuEditButton('edit',route('student-fee-types.debit-note-edit', $row->id),false);
                $operate .= BootstrapTableService::menuButton('Invoice', route('student-fee-types.debit-note-pdf', ['id' => $row->id]), ['generate-debit-note-pdf'], ['target' => "_blank", 'data-id' => $row->id, 'title' => trans('debit_note') . ' ' . trans('pdf')]);
                $operate .= BootstrapTableService::menuButton('Update Status','javascript:doSetDebitNoteStatus('. $row->id.');');                   
                
                if (!isset($debitNoteEinvoice) || $debitNoteEinvoice->status != 1) {
                    $operate .= BootstrapTableService::menuDeleteButton('delete',route('student-fee-types.debit-note-delete', $row->id));
                }

                if(isset($debitNoteEinvoice) && $debitNoteEinvoice->status == 1){
                    $operate .= BootstrapTableService::menuButton('cancel e-invoice',route('student-fee-types.cancelDebitEinvoice',['id' => $row->id]),['cancel-debit-einvoice']);
                } else if($row->status == 'published' && (!isset($debitNoteEinvoice) || $debitNoteEinvoice->status != 0)){ 
                    $operate .= BootstrapTableService::menuButton('submit e-invoice',route('student-fee-types.submitDebitEinvoice',['id'=>$row->id]),['submit-debit-einvoice']);
                }

                if(isset($debitNoteEinvoice)){
                    $operate .= BootstrapTableService::menuButton('check_status',route('student-fee-types.check-debit-note-e-invoice-status',['id' => $row->id]),['check-debit-note-e-invoice-status']);
                }
            }

            $e_invoice_status = false;
            $e_invoice_exist = DB::table('e_invoice_guardian')->join('students', 'students.guardian_id', '=', 'e_invoice_guardian.guardian_id')->where('e_invoice_guardian.school_id',Auth::user()->school_id)->where('students.id', $row->student_id)->select('e_invoice_guardian.guardian_id','e_invoice_guardian.sql_code')->first();
            if ($e_invoice_exist) {
                $incompleteDataCount = DB::table('e_invoice_guardian')
                    ->where('guardian_id', $e_invoice_exist->guardian_id)
                    ->where(function ($query) {
                        $query->whereNull('name')
                            ->orWhere('name', '')
                            ->orWhereNull('ic_no')
                            ->orWhere('ic_no', '')
                            ->orWhereNull('tax_identification_number')
                            ->orWhere('tax_identification_number', '')
                            ->orWhereNull('address')
                            ->orWhere('address', '')
                            ->orWhereNull('city')
                            ->orWhere('city', '')
                            ->orWhereNull('postal_code')
                            ->orWhere('postal_code', '')
                            ->orWhereNull('country')
                            ->orWhere('country', '')
                            ->orWhereNull('state')
                            ->orWhere('state', '');
                    })
                    ->count();
                if ($incompleteDataCount === 0) {
                    $e_invoice_status = true;
                }
            }

            $tempRow = [];
            $totalAmount = $row->total_amount;
            $tempRow['submitted_e_invoice'] = isset($debitNoteEinvoice->status) ? $debitNoteEinvoice->status: null;
            $tempRow ['e_invoice_status'] = $e_invoice_status;
            $tempRow['no'] = $no++ ?? '';
            $tempRow['id'] = $row->id ?? '';
            $tempRow['full_name'] = $row->full_name ?? '';
            $tempRow['student_id'] = $row->student_id ?? '';
            $tempRow['debit_note_no'] =  ('DB'.sprintf('%06d', $row->uid));
            if(isset($row->tax_percentage) && $row->tax_percentage > 0){
                $extraTax = ($totalAmount * ($row->tax_percentage / 100));
                $totalAmount += $extraTax;
                $tempRow['extra_tax'] = number_format($extraTax,2);
            }
            $tempRow['total_amount'] = number_format($totalAmount,2);
            $tempRow['tax'] = $row->tax;
            $tempRow['status'] = $row->status ?? '';
            $tempRow['created_at'] = date('d-m-Y', strtotime($row->created_at));
            $tempRow['operate'] = BootstrapTableService::menuItem($operate);
            $rows[] = $tempRow;
        }
        $bulkData['rows'] = $rows;
        return response()->json($bulkData);
    }

    public function DebitNoteRestore($id){
        ResponseService::noFeatureThenRedirect('Fees Management');

        try {
            DB::beginTransaction();
            DB::table('debit_note')->where('id',$id)->update(["deleted_at" => null]);
            DB::commit();
            ResponseService::successResponse("Data Restored Successfully");
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function debitNoteEdit($id) {
        ResponseService::noFeatureThenRedirect('Fees Management');
        ResponseService::noPermissionThenRedirect('fees-edit');

        $student = DB::table('students as s')
                   ->join('debit_note as cn','cn.student_id','=','s.id')
                   ->join('users as u','u.id','=','s.user_id')
                   ->join('class_sections as cs','cs.id','=','s.class_section_id')
                   ->where('cn.id',$id)
                   ->select(DB::raw("CONCAT_WS(' ', u.first_name, u.last_name) AS full_name"),'s.id','cs.class_id','s.school_id')
                   ->first();
        $debitNoteDetails = DB::table('debit_note_details')
                    ->join('debit_note','debit_note.id','=','debit_note_details.debit_note_id')
                    ->where('debit_note_id',$id)
                    ->select('debit_note.uid','debit_note_details.*')
                    ->get();
        $itemCode = DB::table('fees_type_master')->where('school_id',Auth::user()->school_id)->get();
        $debitNote = DB::table('debit_note')->where('id',$id)->first();

        return view('student-fee-types.debit_note_edit', compact('student','debitNoteDetails','itemCode','debitNote'));
    }

    public function debitNoteUpdate(Request $request, $id) {
        ResponseService::noFeatureThenRedirect('Fees Management');
        ResponseService::noPermissionThenRedirect('fees-edit');
        $request->validate([
            'debit_note_details'                       => 'required|array',
            'debit_note_details.*'                     => 'required|array',
            'debit_note_details.*.debit_note_name'    => 'required',
            'debit_note_details.*.debit_note_amount'  => 'required|numeric',
            'debit_note_details.*.classification_code'                     => 'required',
            'debit_note_details.*.unit'                                    => 'required',
            'debit_note_details.*.quantity'                                => 'required'
        ]);

        if(isset($request->tax_type) && $request->tax_percentage == null) {
            $request->validate([
                'tax_percentage' =>'required|numeric',
            ]);
        }

        try {
            DB::beginTransaction();
            $debitNote = [];
            foreach ($request->debit_note_details as $data) {
                if(isset($data['id'])){
                    $updateData = [
                        "debit_note_name"        => $data['debit_note_name'],
                        "debit_note_amount"      => $data['debit_note_amount'],
                        'classification_code'   => $data['classification_code'],
                        'unit'                  => $data['unit'],
                        'quantity'              => $data['quantity'],
                        'tax'                   => $data['tax'],
                    ];
                    DB::table('debit_note_details')->where('id',$data['id'])->update($updateData);
                } else {
                    $debitNote[] = array(
                        'school_id'             => $request->school_id,
                        'debit_note_id'        => $id,
                        "debit_note_name"        => $data['debit_note_name'],
                        "debit_note_amount"      => $data['debit_note_amount'],
                        'classification_code'   => $data['classification_code'],
                        'unit'                  => $data['unit'],
                        'quantity'              => $data['quantity'],
                        'tax'                   => $data['tax'],
                    );
                }
            }
            if (isset($debitNote)) {
                DB::table('debit_note_details')->insert($debitNote);
            }

            $taxUpdate = [
                'tax_type'          => $request->tax_type,
                'tax_percentage'    => $request->tax_percentage,
            ];
            DB::table('debit_note')->where('id',$id)->update($taxUpdate);

            DB::commit();
            ResponseService::successRedirectResponse(route('student-fee-types.debit-note'), 'Data Update Successfully');
        } catch (Throwable $e) {
            DB::rollback();
            ResponseService::errorRedirectResponse();
        }
    }
    public function generateUniqueUid(){
        $uid = 1;
        while (DB::table('debit_note')->where('uid', $uid)->where('school_id',Auth::user()->school_id)->exists()) {
            $uid++; 
        }

        return $uid;
    }
    public function debitNoteDetailDelete($id){
        if($id){
            DB::table('debit_note_details')->where('id',$id)->delete();
            DB::commit();
            ResponseService::successResponse('Data Deleted Successfully');
        }
    }
    public function debitNoteDelete($id){
        ResponseService::noFeatureThenRedirect('Fees Management');
        ResponseService::noPermissionThenSendJson('fees-delete');

        try {
            DB::beginTransaction();
            DB::table('debit_note')->where('id',$id)->update(["deleted_at" => now()]);
            DB::commit();
            ResponseService::successResponse("Data Deleted Successfully");
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }
    public function debitNotePdf($id) {
        ResponseService::noFeatureThenRedirect('Fees Management');
        ResponseService::noPermissionThenRedirect('fees-list');
        try {
            $debit_note = DB::table('debit_note')
                        ->join('student_fees','student_fees.id','=','debit_note.student_fee_id')
                        ->select('debit_note.*','student_fees.uid as sf_uid')
                        ->where('debit_note.id',$id)->first(); 
            $debit_note_details = DB::table('debit_note_details')->where('debit_note_id',$id)->get();
            $debit_note_einvoice=DB::table('debit_note_einvoice')->where('debit_note_id',$id)->orderBy('id','DESC')->first();
            $student = DB::table('students as s')
                    ->join('users as u','u.id','=','s.user_id')
                    ->join('class_sections as cs','cs.id','=','s.class_section_id')
                    ->join('classes as c','c.id','=','cs.class_id')
                    ->where('s.id',$debit_note->student_id)
                    ->select(DB::raw("CONCAT_WS(' ', u.first_name, u.last_name) AS full_name"),'s.*','c.name as class_name','u.current_address')
                    ->first();
            $parent = DB::table('users')->where('id',$student->guardian_id)->first();
            $school = $this->cache->getSchoolSettings();
            $e_invoice_guardian = DB::table('e_invoice_guardian')->where('guardian_id',$student->guardian_id)->where('school_id',Auth::user()->school_id)->where('status',1)->first();
            $schoolSettings = DB::select("SELECT data FROM school_settings WHERE name = 'horizontal_logo' AND school_id = ".$student->school_id);
            $schoolLogo = '';
            if(COUNT($schoolSettings)){
                $schoolLogo = $schoolSettings[0]->data;
            }
            if(isset($debit_note_einvoice) && $debit_note_einvoice->status != 0){
                $documentSummary = json_decode($debit_note_einvoice->document_summary);
                $url = env('E_INVOICE_URL').'/'.$debit_note_einvoice->uuid.'/share/'.$documentSummary[0]->longId;
                $debit_note_einvoice->e_invoice_url = (new QRCode)->render($url);
            }

            if(isset($debit_note->tax_percentage) && $debit_note->tax_percentage > 0){
                $taxTypeJSON = json_decode(file_get_contents('assets/JSON/eInvoice/TaxTypes.json'),true);
                foreach ($taxTypeJSON as $tax) {
                    if (isset($tax['Code']) && $tax['Code'] === $debit_note->tax_type) {
                        $debit_note->tax_type = $tax['Description'];
                        break;
                    }
                }
            }

            $pdf = Pdf::loadView('student-fee-types.debit_note_invoice', compact('debit_note', 'debit_note_details', 'student', 'parent', 'school', 'schoolSettings', 'schoolLogo','debit_note_einvoice','e_invoice_guardian'));

            return $pdf->stream('debit-note-invoice.pdf');
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
            return false;
        }
    }

    public function debitNoteStatus(Request $request){
        ResponseService::noFeatureThenRedirect('Fees Management');
        ResponseService::noPermissionThenRedirect('fees-list');

        $debit_note_id = $request->id;
        if($debit_note_id && $request->status){
            if($request->status == 'draft'){
                DB::table('debit_note')->where('id',$debit_note_id)->update(['status' => $request->status,'date' => null]);
            } else {
                DB::table('debit_note')->where('id',$debit_note_id)->update(['status' => $request->status,'date' => date('Y-m-d')]);
                $debit_note = DB::table('debit_note as cn')
                            ->join('debit_note_details as cnd', 'cn.id', '=', 'cnd.debit_note_id')
                            ->select('cn.*', DB::raw('SUM(cnd.debit_note_amount) as total_amount'))
                            ->where('cn.id', $debit_note_id)
                            ->groupBy('cn.id')
                            ->first();
                $guardian_id = DB::table('students as s')
                                ->join('debit_note as cn','cn.student_id','=','s.id')
                                ->where('cn.id',$debit_note_id)
                                ->pluck('s.guardian_id')
                                ->first();
                if($guardian_id){
                    $title = 'debit Note Issued';
                    $body = 'A debit note has been issued for your account.';
                    $type = "debit Note";
                    send_notification([$guardian_id], $title, $body, $type);
                }
            }
            return response()->json([
                'error'   => false,
                'message' => "Status Set Successful:"
            ]);
        }
    }
    public function submitDebitEInvoicing($id) {
        // Check if e-invoice already submitted
        $debitNoteSubmitted = DB::table('debit_note_einvoice')->where('debit_note_id',$id)->where('status',1)->first();
        if($debitNoteSubmitted) {
            $cre_number = DB::table('debit_note')->where('id',$id)->select('uid')->first();
            $cre_number = str_pad($cre_number->uid, 8, '0', STR_PAD_LEFT);
            ResponseService::errorResponse("E-Invoice already submitted and validated for INV".$cre_number);
        }
    
        // Get debit note data
        $debitNote = DB::table('debit_note')->where('id',$id)->first();
        $debitNoteDetails = DB::table('debit_note_details')->where('debit_note_id',$id)->get();
    
        $invoiceId = $debitNote->invoice_number ?? 'CN' . sprintf('%09d', $debitNote->uid ?? '');
       
        // Calculate monetary totals with rounding
        $lineExtensionAmount = 0;
        $taxExclusiveAmount = 0;
        $taxInclusiveAmount = 0;
        $allowanceTotalAmount = round($debitNote->early_offer_amount ?? 0, 2);
        $chargeTotalAmount = round($debitNote->due_charges_amount ?? 0, 2);
        $totalTaxAmount = 0;
    
        foreach ($debitNoteDetails as $detail) {
            // if (in_array($detail->debit_note_name, ['Early Discount', 'Overdue Fees'])) {
            //     $detail->debit_note_amount = 0;
            // }
            
            $priceExtensionAmount = round(
                ($detail->debit_note_amount * $detail->quantity),
                2
            );
            
            $taxAmount = round($priceExtensionAmount * ($detail->tax / 100), 2);
    
            $lineExtensionAmount += $priceExtensionAmount;
            $taxExclusiveAmount += $priceExtensionAmount;
            $taxInclusiveAmount += ($priceExtensionAmount + $taxAmount);
            $totalTaxAmount += $taxAmount;
        }
    
        // Apply final rounding to all amounts
        $debitNote->line_extension_amount = round($lineExtensionAmount, 2);
        $debitNote->tax_exclusive_amount = round($taxExclusiveAmount, 2);
        $debitNote->tax_inclusive_amount = round($taxInclusiveAmount, 2);
        $debitNote->allowance_total_amount = $allowanceTotalAmount;
        $debitNote->charge_total_amount = $chargeTotalAmount;
        $debitNote->payable_rounding_amount = 0;
        $debitNote->payable_amount = round(
            $debitNote->total_amount ?? ($taxInclusiveAmount - $allowanceTotalAmount + $chargeTotalAmount),
            2
        );
        $debitNote->total_tax_amount = round($totalTaxAmount, 2);
        $debitNote->total_taxable_amount = round($taxExclusiveAmount, 2);
        $debitNote->total_tax_percent = ($taxExclusiveAmount > 0)
            ? round(($totalTaxAmount / $taxExclusiveAmount) * 100, 2)
            : 0;
    
        $student = $this->student->builder()->where('id',$debitNote->student_id)->first();
        $e_invoice = DB::table('e_invoice')->where('school_id',Auth::user()->school_id)->first();
        $school = DB::table('schools')->where('id',Auth::user()->school_id)->first();
    
        if(!$e_invoice || $e_invoice->status != 1) {
            ResponseService::errorResponse("School's TIN number not verified");
        }
    
        // Get guardian e-invoice data
        $e_invoice_guardian = DB::table('e_invoice_guardian')->where('guardian_id',$student->guardian_id)
            ->where('school_id',Auth::user()->school_id)->first();
        
        if($e_invoice_guardian && $e_invoice_guardian->status != 1) {
            ResponseService::errorResponse("TIN number not verified");
        }
    
        $supplierAddressLines = array_filter([
            trim(preg_replace('/\s+/', ' ', $school->address ?? '')),
            // $school->address2 ?? null,
            // $school->address3 ?? null
        ]);
    
        $customerAddressLines = array_filter([
            trim(preg_replace('/\s+/', ' ', $e_invoice_guardian->address ?? '')),
            // $e_invoice_guardian->address2 ?? null,
            // $e_invoice_guardian->address3 ?? null
        ]);
    
        $studentFee = \App\Models\StudentFee::with('student_fees_details')->find($debitNote->student_fee_id);

        $eInvoiceUuid = DB::table('student_fees_einvoice')
            ->where('student_fees_id', $debitNote->student_fee_id)
            ->value('uuid');

        $eInvoiceInternalId = DB::table('student_fees_einvoice')
            ->where('student_fees_id', $debitNote->student_fee_id)
            ->value('internal_id');

        // Prepare invoice data structure
        $invoiceData = [
            'invoice_type' => 'debit_note',
            'id' => $invoiceId,
            'billing_references' => [
                'invoice_document' => [
                    'id' => $eInvoiceInternalId ?? '',
                    'uuid' => $eInvoiceUuid ?? '',
                ]
            ],
            'supplier' => [
                'address' => [
                    'city' => $e_invoice->city ?? '',
                    'postcode' => $e_invoice->postal_code ?? '',
                    'country_subentity_code' => $e_invoice->state ?? ''
                ],
                'address_line' => $supplierAddressLines,
                'country_code' => 'MYS',
                'legal_entity' => $school->name ?? '',
                'contact_phone' => $school->support_phone ?? '',
                'contact_email' => $school->support_email ?? '',
                'msic_code' => $e_invoice->company_msic_code ?? '',
                'party_identification' => [
                    'TIN' => $e_invoice->tax_identification_number ?? '',
                    ($e_invoice->id_type == 'NRIC' ? 'NRIC' :
                     ($e_invoice->id_type == 'BRN' ? 'BRN' : 'ID'))
                        => $e_invoice->registration_id_passport_number ?? ''
                ],
            ],
            'customer' => [
                'address' => [
                    'city' => $e_invoice_guardian->city ?? ($student->guardian->city ?? ''),
                    'postcode' => $e_invoice_guardian->postal_code ?? ($student->guardian->postcode ?? ''),
                    'country_subentity_code' => $e_invoice_guardian->state ?? ($student->guardian->state_code ?? '')
                ],
                'address_line' => $customerAddressLines,
                'country_code' => $e_invoice_guardian->country ?? ($student->guardian->country_code ?? 'MYS'),
                'legal_entity' => $e_invoice_guardian->name ?? ($student->guardian->name ?? ''),
                'contact_phone' => $e_invoice_guardian->contact_no ?? ($student->guardian->phone ?? ''),
                'contact_email' => $e_invoice_guardian->email ?? ($student->guardian->email ?? ''),
                'party_identification' => [
                    'TIN' => $e_invoice_guardian->tax_identification_number ?? '',
                    'NRIC' => $e_invoice_guardian->ic_no ?? ''
                ],
            ],
            'document_line' => $debitNoteDetails->map(function ($debitNoteDetail) use ($school) {
                $itemCode = DB::table('fees_type_master')
                    ->where('school_id', $school->id)
                    ->where('name', $debitNoteDetail->debit_note_name)
                    ->value('item_code');
    
                if (empty($itemCode)) {
                    $itemCode = DB::table('fees_type_master')
                        ->where('school_id', $school->id)
                        ->where('classification_code', $debitNoteDetail->classification_code)
                        ->value('item_code');
                }
    
                if (empty($itemCode)) {
                    return null;
                }
    
                $priceExtensionAmount = round(
                    ($debitNoteDetail->debit_note_amount * $debitNoteDetail->quantity),
                    2
                );
                
                $priceExtensionAmountTrue = round($debitNoteDetail->quantity * $debitNoteDetail->debit_note_amount, 2);
                $taxTotalAmount = round($priceExtensionAmount * ($debitNoteDetail->tax / 100), 2);
    
                return [
                    'id' => $itemCode ?? '',
                    'quantity' => $debitNoteDetail->quantity ?? 1,
                    'unit' => !empty($debitNoteDetail->unit) ? explode(' - ', $debitNoteDetail->unit)[0] : '',
                    'line_amount' => $priceExtensionAmount ?? 0,
                    'item' => [
                        'description' => $debitNoteDetail->debit_note_name ?? '',
                        'classifications' => [
                            [
                                'code' => '010',
                                'type' => 'CLASS',
                            ]
                        ]
                    ],
                    'price' => [
                        'amount' => round($debitNoteDetail->debit_note_amount ?? 0, 2)
                    ],
                    'price_extension' => [
                        'amount' => $priceExtensionAmountTrue
                    ],
                    'tax_total' => [
                        'amount' => $taxTotalAmount
                    ],
                    'tax_sub_totals' => [
                        [
                            'taxable_amount' => $priceExtensionAmount,
                            'tax_amount' => $taxTotalAmount,
                            'percent' => (float) $debitNoteDetail->tax ?? 0,
                            'tax_scheme' => [
                                'id' => 'OTH'
                            ],
                            'tax_category' => [
                                'id' => '01',
                                'percent' => (float) $debitNoteDetail->tax ?? 0,
                                'tax_exemption_reason' => 'None'
                            ]
                        ]
                    ],
                ];
            })->filter()->values()->toArray(),
    
            "legal_monetary_total"=> [
                'line_extension_amount' => $debitNote->line_extension_amount,
                'tax_exclusive_amount' => $debitNote->tax_exclusive_amount,
                'tax_inclusive_amount' => $debitNote->tax_inclusive_amount,
                'allowance_total_amount' => $debitNote->allowance_total_amount,
                'charge_total_amount' => $debitNote->charge_total_amount,
                'payable_rounding_amount' => $debitNote->payable_rounding_amount,
                'payable_amount' => $debitNote->payable_amount
            ],
    
            'allowance_charges' => [
                [
                    'charge_indicator' => true,
                    'reason' => '',
                    'amount' => 0
                ],
            ],

            $totalTaxAmount = $debitNote->tax_inclusive_amount * ($debitNote->tax_percentage / 100),
    
            'tax_total' => [
                'tax_amount' => round($totalTaxAmount, 2),
                'tax_sub_totals' => [
                    [
                        'taxable_amount' => round($debitNote->tax_inclusive_amount, 2),
                        'tax_amount' => round($totalTaxAmount, 2),
                        'percent' => round($debitNote->tax_percentage, 2),
                        'tax_category' => [
                            'id' => $debitNote->tax_type ?? '06',
                            'tax_exemption_reason' => 'None'
                        ],
                        'tax_scheme' => [
                            'id' => 'OTH'
                        ]
                    ]
                ]
            ]
        ];
    
        // Get school settings for API credentials
        $schoolSettings = $this->cache->getSchoolSettings();
        if(!$schoolSettings || !$schoolSettings['client_id'] || empty($schoolSettings['client_secret_1'])) {
            ResponseService::errorResponse("School API credentials not configured");
        }
    
        $schoolId = auth()->user()->school_id;
    
        // New submission logic
        $submitDocument = null;
        $invoice = null;
    
        try {
            $data = [
                'invoice_type' => 'debit_note',
                'id' => $invoiceId,
                'billing_references' => $invoiceData['billing_references'],
                'supplier' => $invoiceData['supplier'],
                'customer' => $invoiceData['customer'],
                'document_line' => $invoiceData['document_line'],
                'legal_monetary_total' => $invoiceData['legal_monetary_total'],
                'allowance_charges' => $invoiceData['allowance_charges'],
                'tax_total' => $invoiceData['tax_total'],
            ];
    
            $invoiceType = EInvoiceHelper::mapInvoiceTypeCode($data['invoice_type']);
            //\Log::info('Invoice Type:', [$invoiceType]);

            $invoice = EInvoiceHelper::createXmlDocument($invoiceType, $data);
            //\Log::info('Invoice:', [$invoice]);
    
            $documents = [];
            $document = MyInvoisHelper::getSubmitDocument($id, $invoice);
            $documents[] = $document;

            $signatureValue = '';
            $dom = new DOMDocument();
            $dom->loadXML($invoice);
            $xpath = new DOMXPath($dom);
            $xpath->registerNamespace('ds', 'http://www.w3.org/2000/09/xmldsig#');
            $xpath->registerNamespace('cac', 'urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2');
            $xpath->registerNamespace('ext', 'urn:oasis:names:specification:ubl:schema:xsd:CommonExtensionComponents-2');
            $signatureValueNodes = $xpath->query('//ds:SignatureValue');
            if ($signatureValueNodes->length > 0) {
                $signatureValue = $signatureValueNodes->item(0)->nodeValue;
            }
            
            $submissionResult = EInvoiceHelper::submitDocument($schoolId, $documents);
            


            if ($submissionResult['success'] == true) {
                \Log::info('E-Invoice Submission Data: '. json_encode($data));
                //\Log::info('E-Invoice Submission Result: '. json_encode($submissionResult));
            }
    
        } catch (\Exception $exception) {
            $errorMessage = $exception->getMessage();
            $submitDocument = json_decode($errorMessage);
    
            if (json_last_error() !== JSON_ERROR_NONE) {
                $submitDocument = $errorMessage;
            }
    
            \Log::error('E-Invoice Submission Exception: ' . $errorMessage, ['exception' => $exception]);
            return ResponseService::errorResponse("An error occurred during e-invoice submission: " . $errorMessage);
        }
    
        if(isset($submissionResult['success']) && $submissionResult['success'] == true) {
            $resultData = $submissionResult['data'] ?? null;
    
            if ($resultData) {
                $dataToInsert = [
                    'debit_note_id' => $id,
                    'submission_uid' => null,
                    'rejected_documents' => null,
                    'uuid' => null,
                    'debit_note_number' => null,
                ];
    
                if (is_object($resultData)) {
                    $dataToInsert['submission_uid'] = $resultData->submissionUid ?? null;
                    $dataToInsert['rejected_documents'] = !empty($resultData->rejectedDocuments) ? json_encode($resultData->rejectedDocuments) : null;
    
                    if(isset($resultData->acceptedDocuments) && is_array($resultData->acceptedDocuments) && count($resultData->acceptedDocuments) > 0) {
                        foreach($resultData->acceptedDocuments as $document) {
                            if(isset($document->uuid)) $dataToInsert['uuid'] = $document->uuid;
                            if(isset($document->invoiceCodeNumber)) $dataToInsert['debit_note_number'] = $invoiceId;
                        }
                    }
                } elseif (is_array($resultData)) {
                    $dataToInsert['submission_uid'] = $resultData['submissionUid'] ?? null;
                    $dataToInsert['rejected_documents'] = !empty($resultData['rejectedDocuments']) ? json_encode($resultData['rejectedDocuments']) : null;
    
                    if(isset($resultData['acceptedDocuments']) && is_array($resultData['acceptedDocuments']) && count($resultData['acceptedDocuments']) > 0) {
                        foreach($resultData['acceptedDocuments'] as $document) {
                            if(isset($document['uuid'])) $dataToInsert['uuid'] = $document['uuid'];
                            if(isset($document['invoiceCodeNumber'])) $dataToInsert['debit_note_number'] = $invoiceId;
                        }
                    }
                }
    
                try {
                    DB::table('debit_note_einvoice')->insert($dataToInsert);

                    DB::table('debit_note')->where('id', $id)->update(['sign_value' => $signatureValue]);

                    // Call getEInvoiceStatus to immediately check and update the status
                    return $this->getDebitNoteEInvoiceStatus($id);
                    
                } catch (\Illuminate\Database\QueryException $ex) {
                    \Log::error('Database Insert Error: '. $ex->getMessage(), ['query' => $ex->getSql(), 'bindings' => $ex->getBindings()]);
                    return ResponseService::errorResponse("E-Invoice submitted, but failed to save e-invoice data to database. " . $ex->getMessage());
                }
    
            } else {
                \Log::warning('E-Invoice submission successful, but resultData is null or empty.');
                return ResponseService::errorResponse($submissionResult['message'] ?? "E-Invoice submission successful, but response data is missing or invalid.");
            }
        } else {
            $errorMessage = "Failed to submit e-invoice.";
            if (isset($submissionResult['message']) && !empty($submissionResult['message'])) {
                $errorMessage = $submissionResult['message'];
            }
            \Log::error('E-Invoice Submission Failed: ' . $errorMessage, $submissionResult);
            return ResponseService::errorResponse($errorMessage);
        } 
    }

    public function getDebitNoteEInvoiceStatus($id){
        try {
            $debitNoteEinvoice = DB::table('debit_note_einvoice')->where('debit_note_id', $id)->orderByDesc('id')->first();
            
            if (!$debitNoteEinvoice) {
                return ResponseService::errorResponse("No e-invoice submission found for this debit note");
            }
            
            // Check if submission_uid exists
            if (empty($debitNoteEinvoice->submission_uid)) {
                return ResponseService::errorResponse("No submission UID found for this e-invoice");
            }
            
            // Log the submission UID being used
            //\Log::info('Checking e-invoice status with submission UID: ' . $debitNoteEinvoice->submission_uid);
            
            // Make sure to pass the exact submission_uid from the database
            $result = EInvoiceHelper::getSubmission(Auth::user()->school_id, $debitNoteEinvoice->submission_uid);
            
            if ($result['success']) {
                $submissionData = $result['data'];
                $status = 0; // Default: pending
                
                // Log the submission data received
                //\Log::info('Submission data received: ' . json_encode($submissionData));
                
                if ($submissionData['overallStatus'] == 'Invalid') {
                    $status = 2; // Invalid
                } else if ($submissionData['overallStatus'] == 'Valid') {
                    $status = 1; // Valid
                }
                
                $updateData = [
                    'status' => $status,
                    'document_summary' => json_encode($submissionData['documentSummary'])
                ];
                
                if (!empty($submissionData['documentSummary'])) {
                    $documentSummary = $submissionData['documentSummary'][0];
                    if (isset($documentSummary['uuid'])) {
                        $updateData['uuid'] = $documentSummary['uuid'];
                    }
                    
                    // if (isset($documentSummary['longId'])) {
                    //     $updateData['long_id'] = $documentSummary['longId'];
                    // }
                    
                    // if (isset($documentSummary['internalId'])) {
                    //     $updateData['internal_id'] = $documentSummary['internalId'];
                    // }
                }
                
                if ($status == 2) {
                    $updateData['deleted_at'] = now();
                }
                
                DB::table('debit_note_einvoice')
                    ->where('id', $debitNoteEinvoice->id)
                    ->update($updateData);
                    
                return ResponseService::successResponse("E-Invoice status updated successfully", [
                    'status' => $status,
                    'status_text' => $submissionData['overallStatus']
                ]);
            } else {
                \Log::error('Error in e-invoice submission validation: ' . json_encode($result));
                \Log::error('Full result from EInvoiceHelper::getSubmission: ' . json_encode($result));
                return ResponseService::errorResponse("Failed to retrieve e-invoice status: " . ($result['message'] ?? "Unknown error"));
            }
        } catch (\Throwable $th) {
            \Log::error('Error in getting e-invoice status: ' . $th->getMessage(), [
                'exception' => $th,
                'stack_trace' => $th->getTraceAsString(),
            ]);
            return ResponseService::errorResponse("An error occurred while retrieving e-invoice status");
        }
    }
  
    public function cancelDebitEInvoicing($id, Request $request) {
        // Validate request
        if (empty($request->reason)) {
            return ResponseService::errorResponse("Cancellation reason is required");
        }
        
        // Get e-invoice record
        $debitNoteEinvoice = DB::table('debit_note_einvoice')
            ->where('debit_note_id', $id)
            ->where('status', 1)
            ->first();
        
        if (!$debitNoteEinvoice) {
            return ResponseService::errorResponse("No valid e-invoice found for this debit note");
        }
        
        if (empty($debitNoteEinvoice->uuid)) {
            return ResponseService::errorResponse("E-invoice UUID not found");
        }
        
        // Call the helper method to cancel the document
        $result = EInvoiceHelper::cancelDocument(
            Auth::user()->school_id, 
            $debitNoteEinvoice->uuid, 
            $request->reason
        );
        
        if ($result['success']) {
            // Update the database record
            DB::table('debit_note_einvoice')
                ->where('id', $debitNoteEinvoice->id)
                ->update([
                    'status' => 3,
                    'cancel_reason' => $request->reason,
                    'deleted_at' => now()
                ]);
            
            return ResponseService::successResponse("E-Invoice cancelled successfully");
        } else {
            // Handle error
            $errorMessage = $result['message'] ?? "Failed to cancel e-invoice";
            if (isset($result['errors']) && is_object($result['errors'])) {
                if (isset($result['errors']->error) && isset($result['errors']->error->details[0]->message)) {
                    $errorMessage = $result['errors']->error->details[0]->message;
                } else if (isset($result['errors']->message)) {
                    $errorMessage = $result['errors']->message;
                }
            }
            \Log::error('Full result from EInvoiceHelper::cancelDocument: ' . json_encode($result));
            return ResponseService::errorResponse($errorMessage);
        }
    }
}