<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SelfBillingBillingInfo extends Model
{
    use HasFactory;
    protected $table ='self_billing_billing_info';
    protected $fillable = [
        'self_billing_id',
        'frequency_billing',
        'billing_start_date',
        'billing_end_date',
        'payment_mode',
        'supplier_bank_acc',
        'payment_terms',
        'prepayment_amount',
        'prepayment_datetime',
        'prepayment_reference_num',
        'bill_reference_num',
        'created_at',
        'updated_at',
    ];
}
