<?php

namespace App\Http\Controllers;

use App\Models\Reward;
use App\Repositories\Reward\RewardInterface;
use App\Repositories\ClassSchool\ClassSchoolInterface;
use App\Repositories\Student\StudentInterface;
use App\Services\BootstrapTableService;
use App\Services\CachingService;
use App\Services\ResponseService;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Throwable;




class RewardRedeemController extends Controller
{
    private RewardInterface $rewardTable;
    private ClassSchoolInterface $class;
    private StudentInterface $student;
    private CachingService $cache;
    public function __construct(
        RewardInterface $rewardTable,
        ClassSchoolInterface $classSchool,
        CachingService $cache,
        StudentInterface $student,
    ) {
        $this->rewardTable = $rewardTable;
        $this->class = $classSchool;
        $this->cache = $cache;
        $this->student = $student;

    }


    public function index()
    {
        $classes = $this->class->all(['*'], ['stream', 'medium', 'stream']);
        $simpleClassDropdown = $this->class->builder()->pluck('name','id');

        return view('reward.RewardRedeem' ,compact('simpleClassDropdown','classes'));
    }

    public function show()
    {
        $offset = request('offset', 0);
        $limit = request('limit', 10);
        $sort = request('sort', 'id');
        $order = request('order', 'DESC');
        $search = request('search');
        $showDeleted = request('show_deleted');
        $classId = (int)request('class_id');
        $studentId = (int)request('student_id');
        $categoryId = (int)request('category_id');
        $startDate = request('start_date');
        $endDate = request('end_date');

        $query = Reward::query()
        ->where('school_id', Auth::user()->school_id);
    


        if ($startDate && $endDate) {
            // Filter records based on the datetime range
            $query->whereBetween(DB::raw('DATE(updated_at)'), [date('Y-m-d', strtotime($startDate)), date('Y-m-d', strtotime($endDate))]);
        }

        if ($showDeleted) {
            // If "Trashed" is selected, show records where deleted_at is NOT NULL
            $query->whereNotNull('deleted_at');
        } else {
            // Otherwise, only show records where deleted_at is NULL
            $query->whereNull('deleted_at');
        }

        $query->orderBy('updated_at', 'ASC');


        $records = $query->get();

        $total = DB::table('rewards')
            ->where('reward_point_amount', '<', 0)
            ->whereNull('deleted_at')
            ->count();


        $bulkData = array();
        $rows = array();
        $no = 1;

        foreach ($records as $row) {
            //if($row->category_id === 0){
                $operate = '';
                $operate .= BootstrapTableService::editButton(route('reward-redeem.edit', $row->id), false);

                $tempRow=$row;
                
                $tempRow['operate'] = $operate;
                //$tempRow['balance']=$current_balance;
                $tempRow['debit']=0;

                $student_id=$tempRow['student_id'];

                $student_name=DB::select("
                SELECT  concat(u.first_name,' ', u.last_name) as full_name
                FROM    students s
                JOIN    users u
                ON      s.user_id = u.id
                WHERE   s.id IN ($student_id)");

                $tempRow['category_name'] = 0;

                $tempRow['full_name']=$student_name[0]->full_name ?? '';

                if ($tempRow['reward_point_amount'] < 0){
                    $tempRow['debit']=($tempRow['reward_point_amount']);
                }


                // filter based on class and student 
                if(($tempRow['class_id']==$classId || !$classId) && ($tempRow['student_id']==$studentId|| !$studentId)){
                            $rows[] = $tempRow;
                } 
            //}
        }

        usort($rows, function ($a, $b) {
            return $b['updated_at'] <=> $a['updated_at'];  
        });


        foreach ($rows as $index => $tempRow) {
            $rows[$index]['no'] = $index + 1;  
        }


        $bulkData['total'] = $total;
        $bulkData['rows'] = $rows;
        return response()->json($bulkData);
    }

    public function edit($id) {
        $reward = Reward::findOrFail($id);
        $rewardPoints = $reward->reward_point_total;
        $classes = $this->class->all(['*'], ['stream', 'medium', 'stream']);
        $student = $this->student->builder()->with(["class_section.class"])->find($reward->student_id);
        $remark = $reward->remark;
        $selectedClass = $this->class->find($reward->class_id);
    
        return view('reward.RewardPointEdit', compact('reward', 'classes', 'student', 'selectedClass','rewardPoints','remark'));
    }

    public function getClassStudent(Request $request){

        $classStudents = DB::select("
            SELECT  s.id
                    , concat(u.first_name,' ', u.last_name) AS fullname
                    , s.school_id
            FROM    students s
            JOIN    class_sections cs
            ON      s.class_section_id = cs.id
            JOIN    users u
            ON      s.user_id = u.id
            WHERE   cs.class_id = ?
            AND     s.session_year_id = ?
            AND     u.status = 1
        ",[$request->class_id,$this->cache->getDefaultSessionYear()->id]);

        foreach($classStudents as &$item){
            
            $rewards=DB::table('rewards')
            ->where('student_id',$item->id)
            ->whereNull('deleted_at')
            ->orderby('updated_at','desc')
            ->first();

            if($rewards){
                $item->reward_point = $rewards->reward_point_total;

            } else{
                $item->reward_point =0;
            }

        }

        return response()->json($classStudents);
    }

    public function update(Request $request, $id) {
        $request->validate([
            'change_points' => 'required|integer', 
        ]);

        $change_points = $request->input('change_points') * -1;
        $change_remark = $request->input('change_remark') ;
    
        try {
            DB::beginTransaction();

            // Find reward record by ID
            $reward = Reward::findOrFail($id);

            $new_point = $change_points + $reward->reward_point_amount * -1;

            $mostRecentRewardTotal = DB::table('rewards')
            ->where('student_id','=',$reward->student_id)
            ->whereNull('deleted_at')
            ->orderBy('updated_at', 'desc')
            ->value('reward_point_total');

            // update reward
            if($new_point * -1 > $mostRecentRewardTotal){
                DB::rollBack();
                return redirect()->back()->with('error', 'Student Does Not Have Enough Reward Points.');
            }



            $initialRewardAmount = $reward->reward_point_amount * -1;

            $revertedReward = $mostRecentRewardTotal + $initialRewardAmount;
          
            $updatedReward = $revertedReward + $change_points;
        
            $reward->update([
                'reward_point_amount' => $change_points,
                'reward_point_total' => $updatedReward,
                'remark' => $change_remark,
                // 'reward_point' => $current_reward_point
            ]);

            DB::commit();
            ResponseService::successRedirectResponse(route('reward-redeem.index'), 'Data Update Successfully');
        } catch (Throwable) {
            DB::rollback();
            ResponseService::errorRedirectResponse();
        }
    }



    public function store(Request $request){

        $request->validate([
            'class_id'                              => 'required|numeric',
            'student_id'                            => 'required|array',
            'student_id.*'                          => 'required|numeric',
            'remark' => 'nullable|string',
        ]);

        $rewardPoint = $request->input('reward_point')*-1;
        $remark = $request->input('remark');


        try {

            $classData = $this->class->builder()->find($request->class_id);

            // Get the data from the request
            $class_id = $request->class_id;
            $school_id = $classData->school_id;


            DB::beginTransaction();

            foreach ($request->student_id as $student_id) {

                $reward = Reward::where('student_id', $student_id)
                ->orderBy('updated_at', 'desc')
                ->whereNull('deleted_at')
                ->first();

                $currentRewardPoints = $reward ? $reward->reward_point_total : 0;
                $currentScore = $reward ? $reward->score_total : 0;
                
                $newRewardPoints = $currentRewardPoints + $rewardPoint;

                if ($newRewardPoints < 0) {
                    DB::rollBack();
    
                    return response()->json([
                        'error' => 'true',
                        'message'=>'Student does not have enough point'
                    ]);
                }

            

                $reward = [
                    'school_id'          => $school_id,
                    'class_id'           => $class_id,
                    'student_id'         => $student_id,
                    'score_amount' => 0,
                    'score_total'=> $currentScore,
                    'remark' => $remark,
                    'reward_point_amount'=> $rewardPoint,
                    'reward_point_total' => $newRewardPoints,
                    'category_id' => 0
                ];

                  //notify
                  $notifyUser = DB::table('students')
                  ->where('id', $student_id)
                  ->select('user_id')
                  ->get()
                  ->pluck('user_id');

                if ($notifyUser !== null){
                    $guardianIds = collect();
                    foreach ($notifyUser as $user){
                        $guardianId = DB::table('students')
                        ->where('user_id', $user)
                        ->pluck('guardian_id');
                        $guardianIds = $guardianIds->merge($guardianId);
                    }

                    $notifyUser = $notifyUser->merge($guardianIds);
                    if($newRewardPoints < $currentRewardPoints){
                    $title = 'Reward Claimed!';
                    if($remark){
                        $body ="Congratulations! You've successfully redeemed ". $remark . ". Enjoy your reward!";
                    }else{
                        $body ="Congratulations! You've successfully redeemed a reward. Enjoy your reward!";
                    }
                    $type = "Notification";
                    send_notification($notifyUser, $title, $body, $type);
                    }
                  
                }
          
                
                $this->rewardTable->create($reward);

                
            }

            DB::commit();
            ResponseService::successResponse('Data Stored Successfully');
        } catch (Throwable) {
            DB::rollback();
            ResponseService::errorRedirectResponse();
        }
    }
    
    
}