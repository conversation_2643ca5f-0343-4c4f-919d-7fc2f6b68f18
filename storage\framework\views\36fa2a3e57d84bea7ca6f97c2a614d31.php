<!-- <PERSON>ton trigger modal -->

<style>
    .companyMsicCode{
        background-color: var(--primary-color);
        color: white !important;
        padding: 2px 10px;
        border-radius: 2px;
        font-size: 14px;
        font-weight: 500;
        margin-left: 10px;
        transition: background-color 0.3s;
    }

    .inputWrapper {
        margin-bottom: 30px; /* Adjust the value as needed */
    }

    .companyMsicCode:hover, .companyMsicCode:focus, .companyMsicCode:active {
            background-color: #45b889; 
            color: white; 
        }
</style>
<div class="modal fade formModal" id="staticBackdrop" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered  modal-xl">
        <div class="modal-content row">
            <div class="col-12 rightSide">
                <div class="modal-header">
                    <h1 class="modal-title fs-5" id="staticBackdropLabel"><?php echo e(__('registration_form')); ?></h1>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"
                        aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form class="create-form" enctype="multipart/form-data" action="<?php echo e(url('schools/registration')); ?>" method="post">
                        <?php echo csrf_field(); ?>
                        <div class="schoolFormWrapper">
                            <div class="headingWrapper">
                                <span><?php echo e(__('create_school')); ?></span>
                            </div>
                            <div class="formWrapper">
                                <div class="row">
                                    <div class="col-lg-6">
                                        <div class="inputWrapper">
                                            <label for="name"><?php echo e(__('name')); ?> <span class="text-danger">*</span></label>
                                            <input type="text" name="school_name" id="school_name" placeholder="<?php echo e(__('enter_your_school_name')); ?>" required>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="inputWrapper">
                                            <label><?php echo e(__('logo')); ?> <span class="text-danger">*</span></label>
                                            <input type="file" required name="school_image" id="school_image" class="file-upload-default" accept="image/png, image/jpg, image/jpeg, image/svg+xml"/>
                                            </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="inputWrapper">
                                            <label for="supportEmail"><?php echo e(__('email')); ?> <span class="text-danger">*</span></label>
                                            <input type="email" name="school_support_email" id="school_support_email"
                                                placeholder="<?php echo e(__('enter_your_school_email')); ?>" required>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="inputWrapper">
                                            <label for="supportPhone"><?php echo e(__('mobile')); ?> <span class="text-danger">*</span></label>
                                            <input type="text" oninput="this.value=this.value.replace(/[^0-9]/g,'');" name="school_support_phone" id="school_support_phone"
                                                placeholder="<?php echo e(__('enter_your_school_mobile_number')); ?>" maxlength="16" required>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="inputWrapper">
                                            <label for="address"><?php echo e(__('address')); ?> <span class="text-danger">*</span></label>
                                            <input type="text" name="school_address" id="school_address"
                                                placeholder="<?php echo e(__('enter_your_school_address')); ?>" required>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="inputWrapper">
                                            <label for="tagline"><?php echo e(__('tagline')); ?></label>
                                            <input type="text" name="school_tagline" id="school_tagline" placeholder="<?php echo e(__('tagline')); ?>">
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="inputWrapper">
                                            <label for="business_registration_no"><?php echo e(__('business_registration_no')); ?></label>
                                            <input type="text" name="business_registration_no" id="business_registration_no" placeholder="<?php echo e(__('business_registration_no')); ?>" class="form-control remove-number-increment" >
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="inputWrapper">
                                            <label for="remark"><?php echo e(__('payment_remark')); ?></label>
                                            <textarea name="remark" id="remark" cols="30" rows="3" class="form-control" placeholder="<?php echo e(__('remark')); ?>" ></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="adminFormWrapper schoolFormWrapper">
                            <div class="headingWrapper">
                                <span><?php echo e(__('add_admin')); ?></span>
                            </div>
                            <div class="formWrapper">
                                <div class="row">
                                    <div class="col-lg-6">
                                        <div class="inputWrapper">
                                            <label for="FirstName"><?php echo e(__('first_name')); ?> <span class="text-danger">*</span></label>
                                            <input type="text" name="admin_first_name" id="admin_first_name"
                                                placeholder="<?php echo e(__('enter_your_first_name')); ?>" required>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="inputWrapper">
                                            <label for="lastName"><?php echo e(__('last_name')); ?> <span class="text-danger">*</span></label>
                                            <input type="text" name="admin_last_name" id="admin_last_name"
                                                placeholder="<?php echo e(__('enter_your_last_name')); ?>" required>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="inputWrapper">
                                            <label for="adminEmail"><?php echo e(__('email')); ?> <span class="text-danger">*</span></label>
                                            <input type="email" name="admin_email" id="admin_email"
                                                placeholder="<?php echo e(__('enter_your_email')); ?>" required>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="inputWrapper">
                                            <label for="contact"><?php echo e(__('contact')); ?> <span class="text-danger">*</span></label>
                                            <input type="text" oninput="this.value=this.value.replace(/[^0-9]/g,'');" maxlength="16" name="admin_contact" id="admin_contact"
                                                placeholder="<?php echo e(__('enter_your_contact_number')); ?>" required>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="adminFormWrapper schoolFormWrapper">
                            <div class="headingWrapper">
                                <span><?php echo e(__('e-invoice')); ?></span>
                            </div>
                            <div class="formWrapper">
                                <div class="row">
                                    <div class="col-lg-6">
                                        <div class="inputWrapper">
                                            <label for="new_company_registration_number"><?php echo e(__('new_company_registration_number')); ?> <span class="text-danger">*</span></label>
                                            <input type="text" name="new_company_registration_number" id="new_company_registration_number" placeholder="<?php echo e(__('Eg. 202201021234')); ?>" required>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="inputWrapper">
                                            <label for="old_company_registration_number"><?php echo e(__('old_company_registration_number')); ?> <span class="text-danger">*</span></label>
                                            <input type="text" name="old_company_registration_number" id="old_company_registration_number" placeholder="<?php echo e(__('Eg. 1471234-W')); ?>" required>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="inputWrapper">
                                            <label for="company_business_activity"><?php echo e(__('company_business_activity')); ?> <span class="text-danger">*</span></label>
                                            <input type="text" name="company_business_activity" id=company_business_activity placeholder="<?php echo e(__('Eg. Education')); ?>" required>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="inputWrapper">
                                            <label for="tax_identification_number"><?php echo e(__('tax_identification_number')); ?> <span class="text-danger">*</span></label>
                                            <input type="text" name="tax_identification_number" id="tax_identification_number" placeholder="<?php echo e(__('C29965081234')); ?>" required>
                                        </div>
                                    </div>

                                    <div class="col-lg-6">
                                        <div class="inputWrapper">
                                            <label for="sst_registration_number"><?php echo e(__('sst_registration_number')); ?> <br> <span class="text-danger"><?php echo e(__('(For non-SST registered companies, Please fill in ‘NA’)')); ?></span></label>
                                            <input type="text" name="sst_registration_number" id="sst_registration_number" placeholder="<?php echo e(__('STN-YYMM-12345678')); ?>" >
                                        </div>
                                    </div>

                                    <div class="col-lg-6">
                                        <div class="inputWrapper">
                                            <label for="tourism_tax_registration_number"><?php echo e(__('tourism_tax_registration_number')); ?> <br> <span class="text-danger"><?php echo e(__('For companies not registered for Tourism Tax, Please fill in ‘NA’.')); ?></span></label>
                                            <input type="text" name="tourism_tax_registration_number" id="tourism_tax_registration_number" placeholder="<?php echo e(__('tourism_tax_registration_number')); ?>" >
                                        </div>
                                    </div>

                                    <div class="col-lg-6">
                                        <div class="inputWrapper">
                                            <label for="company_msic_code"><?php echo e(__('company_msic_code')); ?> <a href="https://sdk.myinvois.hasil.gov.my/codes/msic-codes/" target="_blank" class="companyMsicCode">Check your company code here</a></label>
                                            <input type="text" name="company_msic_code" id="company_msic_code" placeholder="<?php echo e(__('Eg. 00000 Please provide your company MSIC code.')); ?>" >
                                        </div>
                                    </div>
                                </div>

                                    
                                <div class="row">
                                    <!-- Left Side: Address Lines and City -->
                                    <div class="col-lg-6">
                                        <div class="inputWrapper">
                                            <label for="address_line1"><?php echo e(__('address_line1')); ?><span class="text-danger">*</span></label>
                                            <input type="text" name="address_line1" id="address_line1" placeholder="<?php echo e(__('address_line1')); ?>" required>
                                        </div>
                                        <div class="inputWrapper">
                                            <label for="address_line2"><?php echo e(__('address_line2')); ?></label>
                                            <input type="text" name="address_line2" id="address_line2" placeholder="<?php echo e(__('address_line2')); ?>">
                                        </div>
                                        <div class="inputWrapper">
                                            <label for="address_line3"><?php echo e(__('address_line3')); ?></label>
                                            <input type="text" name="address_line3" id="address_line3" placeholder="<?php echo e(__('address_line3')); ?>">
                                        </div>
                                        <div class="inputWrapper">
                                            <label for="city"><?php echo e(__('city')); ?> <span class="text-danger">*</span></label>
                                            <input type="text" name="city" id="city" placeholder="<?php echo e(__('city')); ?>" required>
                                        </div>
                                    </div>
                                    
                                    <!-- Right Side: Postal Code, State, and Country -->
                                    <div class="col-lg-6">
                                        <div class="inputWrapper">
                                            <label for="postal_code"><?php echo e(__('postal_code')); ?> <span class="text-danger">*</span></label>
                                            <input type="text" name="postal_code" id="postal_code" placeholder="<?php echo e(__('postal_code')); ?>" required>
                                        </div>
                                        <div class="inputWrapper">
                                            <label for="state"><?php echo e(__('state')); ?> <span class="text-danger">*</span></label>
                                            <input type="text" name="state" id="state" placeholder="<?php echo e(__('state')); ?>" required>
                                        </div>
                                        <div class="inputWrapper">
                                            <label for="country"><?php echo e(__('country')); ?> <span class="text-danger">*</span></label>
                                            <input type="text" name="country" id="country" placeholder="<?php echo e(__('country')); ?>" required>
                                        </div>
                                    </div>
                                </div>
                                    
                                    <!-- <?php if($trail_package): ?>
                                    <div class="col-lg-6">
                                        
                                        <div class="">
                                            <?php echo Form::checkbox('trial_package', $trail_package, false, ['class' => 'm-1']); ?>

                                            <?php echo e(__('start_trial_package')); ?>

                                        </div>
                                        
                                    </div>    
                                    <?php endif; ?> -->
                                    
                                    <div class="col-12 modalfooter">

                                        <div class="inputWrapper">
                                            
                                        </div>
                                        <div>
                                            <button class="commonBtn"><?php echo e(__('submit')); ?></button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div><?php /**PATH D:\laragon\www\schola\resources\views/registration_form.blade.php ENDPATH**/ ?>