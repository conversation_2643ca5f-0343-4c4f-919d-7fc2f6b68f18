<?php

namespace App\Http\Controllers;

use App\Models\StudentFeesDetail;
use App\Models\StudentFeesPaid;
use App\Models\Students;
use App\Models\UserGroup;
use App\Models\UserGroupDetail;
use App\Services\ResponseService;
use App\Repositories\SessionYear\SessionYearInterface;
use App\Repositories\Medium\MediumInterface;
use App\Repositories\SchoolSetting\SchoolSettingInterface;
use App\Repositories\FeesType\FeesTypeInterface;
use App\Repositories\ClassSchool\ClassSchoolInterface;
use App\Repositories\User\UserInterface;
use App\Repositories\CompulsoryFee\CompulsoryFeeInterface;
use App\Services\CachingService;
use App\Repositories\PaymentConfiguration\PaymentConfigurationInterface;
use App\Repositories\PaymentTransaction\PaymentTransactionInterface;
use App\Repositories\Student\StudentInterface;
use App\Repositories\StudentFees\StudentFeesInterface;
use App\Repositories\StudentFeesDetail\StudentFeesDetailInterface;
use App\Repositories\StudentFeesPaid\StudentFeesPaidInterface;
use App\Repositories\StudentFeeType\StudentFeeTypeInterface;
use App\Repositories\SystemSetting\SystemSettingInterface;
use App\Services\BootstrapTableService;
use Carbon\Carbon;
use Barryvdh\DomPDF\Facade\Pdf;
use App\Repositories\Subject\SubjectInterface;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Throwable;

class UserGroupController extends Controller
{
    private StudentFeesInterface $studentFees;
    private SessionYearInterface $sessionYear;
    private SchoolSettingInterface $schoolSettings;
    private MediumInterface $medium;
    private FeesTypeInterface $feesType;
    private ClassSchoolInterface $classes;
    private ClassSchoolInterface $class;
    private StudentFeesDetailInterface $studentFeesDetail;
    private UserInterface $user;
    private StudentFeesPaidInterface $studentFeesPaid;
    private CompulsoryFeeInterface $compulsoryFee;
    private CachingService $cache;
    private PaymentConfigurationInterface $paymentConfigurations;
    private StudentInterface $student;
    private PaymentTransactionInterface $paymentTransaction;
    private SystemSettingInterface $systemSetting;
    private SubjectInterface $subject;
    private StudentFeeTypeInterface $studentFeeType;

    public function __construct(StudentFeesInterface $studentFees, SessionYearInterface $sessionYear, SchoolSettingInterface $schoolSettings, MediumInterface $medium, FeesTypeInterface $feesType, ClassSchoolInterface $classes, StudentFeesDetailInterface $studentFeesDetail, UserInterface $user, StudentFeesPaidInterface $studentFeesPaid, CompulsoryFeeInterface $compulsoryFee, CachingService $cache, PaymentConfigurationInterface $paymentConfigurations, ClassSchoolInterface $classSchool, StudentInterface $student, PaymentTransactionInterface $paymentTransaction, SystemSettingInterface $systemSetting,StudentFeeTypeInterface $studentFeeType,SubjectInterface $subject) {
        $this->studentFees = $studentFees;
        $this->sessionYear = $sessionYear;
        $this->schoolSettings = $schoolSettings;
        $this->medium = $medium;
        $this->feesType = $feesType;
        $this->classes = $classes;
        $this->studentFeesDetail = $studentFeesDetail;
        $this->user = $user;
        $this->studentFeesPaid = $studentFeesPaid;
        $this->compulsoryFee = $compulsoryFee;
        $this->cache = $cache;
        $this->paymentConfigurations = $paymentConfigurations;
        $this->class = $classSchool;
        $this->subject = $subject;
        $this->student = $student;
        $this->paymentTransaction = $paymentTransaction;
        $this->systemSetting = $systemSetting;
        $this->studentFeeType = $studentFeeType;
    }

    public function index(){
        $simpleClassDropdown = $this->class->builder()->pluck('name','id');
        $schoolId = Auth::getUser()->school_id;
        $subjects = DB::table('subjects')->select('id', 'name')->where('school_id',$schoolId)->get();
        $roles = DB::table('user_groups')->select('id', 'group_name')->where('school_id',$schoolId)->get();
        $allteacher = DB::table('staffs')
            ->join('users', 'staffs.user_id', '=', 'users.id')
            ->select('users.id', DB::raw('CONCAT(users.first_name, " ", users.last_name) as allname')) 
            ->where('users.school_id', $schoolId) 
            ->get(); 

        return view('commission.user_grp', compact('roles','simpleClassDropdown','subjects','allteacher'));
    }

    public function store(Request $request) {
        $request->validate([
            'group_name'          => 'required|string|max:191',
            'commission_amount'   => 'nullable|numeric',
            'subject_id'          => 'required|array',
            'subject_id.*'        => 'required|numeric',
            'teacher_id'          => 'array',
            'teacher_id.*'        => 'numeric',
            'commission_type'     => 'required|in:0,1',
            'exclude_absent_students' => 'nullable|in:0,1'
        ]);
    
        DB::beginTransaction();
    
        try {
            $userGroup = UserGroup::create([
                'group_name'        => $request->input('group_name'),
                'commission_type'   => $request->input('commission_type'),
                'commission_amount' => $request->input('commission_amount'),
                'school_id'         => auth()->user()->school_id,
                'exclude_absent_students' => $request->input('exclude_absent_students', 0), // Default to 0
            ]);
    
            if ($request->has('teacher_id')) {
                foreach ($request->subject_id as $subjectId) {
                    foreach ($request->teacher_id as $teacherId) {
                        UserGroupDetail::create([
                            'group_id'    => $userGroup->id,
                            'teacher_id'  => $teacherId,
                            'subject_id'  => $subjectId,
                            'school_id'   => auth()->user()->school_id,
                        ]);
                    }
                }
            }
    
            DB::commit();
            return ResponseService::successResponse('Data updated successfully');
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, 'UserGroupController -> store method');
            return ResponseService::errorResponse();
        }
    }
    
    

    public function show() {
        $schoolId = Auth::user()->school_id;
        $search = request('search');
    
        // Fetch user group details with related teacher and subject information
        $query = DB::table('user_group_details')
            ->join('user_groups', 'user_group_details.group_id', '=', 'user_groups.id')
            ->join('subjects', 'user_group_details.subject_id', '=', 'subjects.id')
            ->join('users', 'user_group_details.teacher_id', '=', 'users.id')
            ->select(
                'user_groups.id',
                'user_groups.group_name',
                'user_groups.commission_type',
                'user_groups.commission_amount',
                'user_groups.exclude_absent_students',
                'user_group_details.teacher_id',
                'user_group_details.subject_id',
                DB::raw('GROUP_CONCAT(DISTINCT users.id) as teacher_ids'),
                DB::raw('GROUP_CONCAT(DISTINCT subjects.id) as subject_ids'), 
                DB::raw('GROUP_CONCAT(DISTINCT CONCAT(users.first_name, " ", users.last_name)) as teachers'),
                DB::raw('GROUP_CONCAT(DISTINCT subjects.name) as subjects')
            )
            ->where('user_group_details.school_id', $schoolId);
            
        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('user_groups.group_name', 'LIKE', "%$search%")
                  ->orWhere('user_groups.commission_amount', 'LIKE', "%$search%")
                  ->orWhere('subjects.name', 'LIKE', "%$search%")
                  ->orWhere(DB::raw("CONCAT(users.first_name, ' ', users.last_name)"), 'LIKE', "%$search%");
            });
        }
        
        $rows = $query->groupBy('user_groups.id', 'user_groups.group_name', 'user_groups.commission_type', 'user_groups.commission_amount', 'user_groups.exclude_absent_students')
            ->get();
        
        $no = 1;
        
        foreach ($rows as $row) {
            $row->no = $no++;
            $row->commission_type = $row->commission_type == 1 ? 'Fixed Amount' : 'Percentage'; 
            $row->exclude_absent_students = $row->exclude_absent_students == 1 ? 'Yes' : 'No';
            $operate = BootstrapTableService::editButton(route('usergroup.update', $row->id));
            $operate .= BootstrapTableService::deleteButton(route('usergroup.destroy', $row->id));

            $row->operate = $operate; 
        }

        return response()->json($rows);
    }
    

    public function update(Request $request, $id) {
        $request->validate([
            'edit_group_name' => 'required|string|max:191',
            'edit_commission_amount' => 'required|numeric',
            'edit_subject_id' => 'required|array',
            'edit_subject_id.*' => 'required|numeric',
            'edit_teacher_id' => 'array',
            'edit_teacher_id.*' => 'numeric',
            'edit_commission_type' => 'required|in:0,1',
            'edit_exclude_absent_students' => 'nullable|in:0,1'
        ]);
    
        DB::beginTransaction();
    
        try {
            $userGroup = UserGroup::findOrFail($id);
    
            // ✅ Update the UserGroup main data
            $userGroup->update([
                'group_name'              => $request->input('edit_group_name'),
                'commission_type'         => $request->input('edit_commission_type'),
                'commission_amount'       => $request->input('edit_commission_amount'),
                'exclude_absent_students' => $request->input('edit_exclude_absent_students', 0),
            ]);
    
            // ✅ Remove old user group details
            UserGroupDetail::where('group_id', $id)->delete();
    
            // ✅ Insert new subjects and teachers into user_group_details
            if ($request->has('edit_teacher_id')) {
                foreach ($request->edit_subject_id as $subjectId) {
                    foreach ($request->edit_teacher_id as $teacherId) {
                        UserGroupDetail::create([
                            'group_id'   => $id,
                            'teacher_id' => $teacherId,
                            'subject_id' => $subjectId,
                            'school_id'  => auth()->user()->school_id,
                        ]);
                    }
                }
            }
    
            DB::commit();
            return ResponseService::successResponse('Data updated successfully');
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, 'UserGroupController -> update method');
            return ResponseService::errorResponse();
        }
    }
    
    
    
    
    
    public function destroy($id)
    {
        DB::beginTransaction();
    
        try {
            // Find the user group by ID
            $userGroup = UserGroup::findOrFail($id);
    
            // Delete related user group details
            UserGroupDetail::where('group_id', $userGroup->id)->delete();
    
            // Delete the user group
            $userGroup->delete();
    
            DB::commit();
            return ResponseService::successResponse('User group deleted successfully');
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, 'UserGroupController -> destroy method');
            return ResponseService::errorResponse();
        }
    }
    

    public function search(Request $request) {
        try {

            $data = [];
            $chkCnt = $this->studentFees->builder()
                        ->where('session_year_id', $request->session_year_id)
                        ->count();

            if($chkCnt>0){
                $data = $this->studentFees->builder()
                            ->where('session_year_id', $request->session_year_id)
                            ->select([DB::raw("MIN(id) AS id"),'name'])
                            ->groupBy("name")
                            ->get();
            }

            ResponseService::successResponse("Data Restored Successfully", $data);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function getClassSubject(Request $request){  
        $filterVal = $request->class_id;
        if(gettype($request->class_id)=="array"){
            $filterVal = implode(",",$filterVal);
        }

        $results = DB::select("
        SELECT DISTINCT u.id, CONCAT(u.first_name, ' ', u.last_name) AS full_name
        FROM users AS u
        JOIN subject_teachers AS st ON st.teacher_id = u.id
        WHERE st.subject_id IN ($filterVal)
        AND u.deleted_at IS NULL
        ");

        return response()->json($results);
    }


    public function trash($id) {
        try {
            $this->UserGroup->findOnlyTrashedById($id)->forceDelete();
            $this->UserGroupDetail->findOnlyTrashedById($id)->forceDelete();
            ResponseService::successResponse("Data Deleted Permanently");
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }
}
