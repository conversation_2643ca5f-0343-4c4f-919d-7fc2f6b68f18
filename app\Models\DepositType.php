<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class DepositType extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'description',
        'amount',
        'is_refundable',
        'refund_policy',
        'school_id'
    ];

    protected $casts = [
        'is_refundable' => 'boolean',
        'amount' => 'decimal:2'
    ];

    public function school()
    {
        return $this->belongsTo(School::class);
    }

    public function studentDeposits()
    {
        return $this->hasMany(StudentDeposit::class);
    }

    public function getActiveDepositsCount()
    {
        return $this->studentDeposits()
            ->where('status', 'active')
            ->count();
    }

    public function getTotalActiveAmount()
    {
        return $this->studentDeposits()
            ->where('status', 'active')
            ->sum('amount');
    }
}