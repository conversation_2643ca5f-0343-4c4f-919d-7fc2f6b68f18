<?php

namespace App\Models;

use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class StudentFeesPaid extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'date',
        'school_id',
        'is_fully_paid',
        'student_fees_id',
        'mode',
        'cheque_no',
        'amount',
        'due_charges',
        'status',
        'remark',
        'payment_transaction_id',
        'payment_detail',
        'fees_status',
        'rfid_id',
    ];

    public function scopeOwner($query)
    {
        if(Auth::user()){
            if (Auth::user()->hasRole('Super Admin')) {
                return $query;
            }

            if (Auth::user()->hasRole('School Admin') || Auth::user()->hasRole('Teacher')) {
                return $query->where('school_id', Auth::user()->school_id);
            }

            if (Auth::user()->hasRole('Student')) {
                return $query->where('school_id', Auth::user()->school_id);
            }
        }

        return $query;
    }

    public function student_fees()
    {
        return $this->belongsTo(StudentFee::class, 'student_fees_id')->withTrashed();
    }

    public function getMode(){
        switch($this->mode){
            case 1:
                return 'cash';
                break;
            case 2:
                return 'cheque';
                break;
            case 3:
                return 'online';
            case 4:
                return 'bank';
                break;
        }
    }

    public static function viewMode($mode)
    {
        $school_id = Auth::user()->school_id;
    
        switch ($mode) {
            case 1:
                return 'cash';
            case 2:
                return 'cheque';
            case 3:
                return 'online';
            case 4:
                return 'bank_transfer';
            default:
                return "-";
        }
    }
}
