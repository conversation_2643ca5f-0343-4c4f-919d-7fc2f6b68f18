<?php

namespace App\Models;

use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class SelfBillingDetails extends Model
{
    use HasFactory;

    protected $table ='self_billing_details';

    protected $fillable = [
        'id',
        'classification_code',
        'description',
        'tariff_code',
        'quantity',
        'measurement',
        'unit_price',
        'total_amount',
        'discount_rate',
        'total_discount_amount',
        'discount_description',
        'charge_rate',
        'total_charge_rate',
        'charge_description',
        'tax_exempted',
        'tax_exempted_details',
        'total_excluding_tax',
        'country_of_origin',
        'self_billing_id',
        'school_id',    
        'created_at',
        'updated_at',
    ];

    protected static function booted() {
        static::addGlobalScope('school', static function (Builder $builder) {
            if (Auth::check()) {
                if (!empty(Auth::user()->school_id) || Auth::user()->hasRole('School Admin')) {
                    $builder->where('school_id', Auth::user()->school_id);
                }
            }
        });

        // Add creating event
        static::creating(function ($model) {
            if (Auth::check() && Auth::user()->school_id) {
                $model->school_id = Auth::user()->school_id;
            }
        });
    }

    public function taxDetails()
    {
        return $this->hasMany(SelfBillingTaxDetails::class, 'self_billing_details_id');
    }
}
