<?php

namespace App\Http\Controllers\Api;

use Carbon\Carbon;
use DateTime;
use Throwable;
use App\Models\Subject;
use Illuminate\Http\Request;
use App\Models\StudentFeesPaid;
use chillerlan\QRCode\QRCode;
use Barryvdh\DomPDF\Facade\Pdf;
use App\Services\CachingService;
use JetBrains\PhpStorm\NoReturn;
use App\Models\SubjectAttendance;
use App\Models\UserNotifications;
use App\Services\FeaturesService;
use App\Services\ResponseService;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use App\Http\Resources\UserDataResource;
use App\Repositories\Exam\ExamInterface;
use App\Repositories\Fees\FeesInterface;
use App\Repositories\User\UserInterface;
use App\Services\Payment\PaymentService;
use Illuminate\Support\Facades\Validator;
use App\Http\Resources\TimetableCollection;
use App\Repositories\Topics\TopicsInterface;
use App\Repositories\Holiday\HolidayInterface;
use App\Repositories\Lessons\LessonsInterface;
use App\Repositories\Sliders\SlidersInterface;
use App\Repositories\Student\StudentInterface;
use App\Repositories\FeesPaid\FeesPaidInterface;
use App\Repositories\Timetable\TimetableInterface;
use App\Repositories\Assignment\AssignmentInterface;
use App\Repositories\Attendance\AttendanceInterface;
use App\Repositories\ExamResult\ExamResultInterface;
use App\Repositories\OnlineExam\OnlineExamInterface;
use App\Repositories\StudentFees\StudentFeesInterface;
use App\Repositories\Announcement\AnnouncementInterface;
use App\Repositories\SchoolSetting\SchoolSettingInterface;
use App\Repositories\SystemSetting\SystemSettingInterface;
use App\Repositories\SubjectTeacher\SubjectTeacherInterface;
use App\Repositories\StudentFeesPaid\StudentFeesPaidInterface;
use App\Repositories\PaymentTransaction\PaymentTransactionInterface;
use App\Repositories\AssignmentSubmission\AssignmentSubmissionInterface;
use App\Repositories\PaymentConfiguration\PaymentConfigurationInterface;
use Symfony\Component\VarDumper\VarDumper;

class ParentApiController extends Controller {

    private StudentInterface $student;
    private UserInterface $user;
    private AssignmentInterface $assignment;
    private AssignmentSubmissionInterface $assignmentSubmission;
    private CachingService $cache;
    private TimetableInterface $timetable;
    private ExamInterface $exam;
    private ExamResultInterface $examResult;
    private LessonsInterface $lesson;
    private TopicsInterface $lessonTopic;
    private AttendanceInterface $attendance;
    private HolidayInterface $holiday;
    private SubjectTeacherInterface $subjectTeacher;
    private AnnouncementInterface $announcement;
    private OnlineExamInterface $onlineExam;
    private FeesInterface $fees;
    private StudentFeesInterface $studentFees;
    private PaymentTransactionInterface $paymentTransaction;
    private SlidersInterface $sliders;
    private FeesPaidInterface $feesPaid;
    private StudentFeesPaidInterface $studentFeesPaid;
    private SubjectTeacherInterface $subjectTeachers;
    private PaymentConfigurationInterface $paymentConfigurations;
    private SystemSettingInterface $systemSetting;
    private SchoolSettingInterface $schoolSetting;

    public function __construct(StudentInterface $student, UserInterface $user, AssignmentInterface $assignment, AssignmentSubmissionInterface $assignmentSubmission, CachingService $cache, TimetableInterface $timetable, ExamInterface $exam, ExamResultInterface $examResult, LessonsInterface $lesson, TopicsInterface $lessonTopic, AttendanceInterface $attendance, HolidayInterface $holiday, SubjectTeacherInterface $subjectTeacher, AnnouncementInterface $announcement, OnlineExamInterface $onlineExam, FeesInterface $fees, PaymentTransactionInterface $paymentTransaction, SlidersInterface $sliders, PaymentConfigurationInterface $paymentConfigurations, FeesPaidInterface $feesPaid, SubjectTeacherInterface $subjectTeachers, SystemSettingInterface $systemSetting, SchoolSettingInterface $schoolSetting, StudentFeesInterface $studentFees,StudentFeesPaidInterface $studentFeesPaid) {
        $this->student = $student;
        $this->user = $user;
        $this->assignment = $assignment;
        $this->assignmentSubmission = $assignmentSubmission;
        $this->cache = $cache;
        $this->timetable = $timetable;
        $this->exam = $exam;
        $this->examResult = $examResult;
        $this->lesson = $lesson;
        $this->lessonTopic = $lessonTopic;
        $this->attendance = $attendance;
        $this->holiday = $holiday;
        $this->subjectTeacher = $subjectTeacher;
        $this->announcement = $announcement;
        $this->onlineExam = $onlineExam;
        $this->fees = $fees;
        $this->paymentTransaction = $paymentTransaction;
        $this->sliders = $sliders;
        $this->paymentConfigurations = $paymentConfigurations;
        $this->feesPaid = $feesPaid;
        $this->subjectTeachers = $subjectTeachers;
        $this->systemSetting = $systemSetting;
        $this->schoolSetting = $schoolSetting;
        $this->studentFees = $studentFees;
        $this->studentFeesPaid = $studentFeesPaid;
    }

    #[NoReturn] public function login(Request $request) {
        if (Auth::attempt([
            'email'    => $request->email,
            'password' => $request->password
        ])) {
            // $auth = Auth::user()->load('child.user', 'child.class_section.class', 'child.class_section.section', 'child.class_section.medium', 'child.user.school');

            // Only active child
            $auth = Auth::user()->load(['child' => function($q) {
                $q->whereHas('user', function($q) {
                    $q->where('status',1);
                })->with('class_section.class', 'class_section.section', 'class_section.medium', 'user.school');
            }]);
            // ==============================

            if (!$auth->hasRole('Guardian')) {
                ResponseService::errorResponse('Invalid Login Credentials', null, config('constants.RESPONSE_CODE.INVALID_LOGIN'));
            }

            if ($request->fcm_id) {
                $auth->fcm_id = $request->fcm_id;
                $auth->save();
            }

            $token = $auth->createToken($auth->first_name)->plainTextToken;

            $user = $auth;
            ResponseService::successResponse('User logged-in!', new UserDataResource($user), ['token' => $token], config('constants.RESPONSE_CODE.LOGIN_SUCCESS'));
        } else {
            ResponseService::errorResponse('Invalid Login Credentials', null, config('constants.RESPONSE_CODE.INVALID_LOGIN'));
        }
    }

    public function subjects(Request $request) {
        $validator = Validator::make($request->all(), ['child_id' => 'required|numeric',]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            //$children = Auth::user()->guardianRelationChild()->where('id', $request->child_id)->first();
            $children = Auth::user()->guardianRelationChild()->where('id', $request->child_id)->whereHas('user', function ($q) {
                $q->whereNull('deleted_at');
            })->first();

            if (empty($children)) {
                ResponseService::errorResponse("Child's Account is not Active.Contact School Support", NULL, config('constants.RESPONSE_CODE.INACTIVE_CHILD'));
            }
            $subjects = $children->currentSemesterSubjects();
            ResponseService::successResponse('Student Subject Fetched Successfully.', $subjects);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function classSubjects(Request $request) {
        $validator = Validator::make($request->all(), ['child_id' => 'required|numeric',]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            //$children = $request->user()->guardianRelationChild()->where('id', $request->child_id)->first();
            $children = Auth::user()->guardianRelationChild()->where('id', $request->child_id)->whereHas('user', function ($q) {
                $q->whereNull('deleted_at');
            })->first();

            if (empty($children)) {
                ResponseService::errorResponse("Child's Account is not Active.Contact School Support", NULL, config('constants.RESPONSE_CODE.INACTIVE_CHILD'));
            }
            $subjects = $children->currentSemesterClassSubjects();
            ResponseService::successResponse('Class Subject Fetched Successfully.', $subjects);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function getTimetable(Request $request) {
        $validator = Validator::make($request->all(), ['child_id' => 'required|numeric',]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            //$children = $request->user()->guardianRelationChild()->where('id', $request->child_id)->first();
            $children = Auth::user()->guardianRelationChild()->where('id', $request->child_id)->whereHas('user', function ($q) {
                $q->whereNull('deleted_at');
            })->first();

            if (empty($children)) {
                ResponseService::errorResponse("Child's Account is not Active.Contact School Support", NULL, config('constants.RESPONSE_CODE.INACTIVE_CHILD'));
            }
            $timetable = $this->timetable->builder()
                ->where('class_section_id', $children->class_section_id)
                ->whereIn('subject_id', function ($query) use ($children) {
                    $query->select('cs.subject_id')
                        ->from('class_subjects as cs')
                        ->join('class_sections as csec', 'csec.class_id', '=', 'cs.class_id')
                        ->where('csec.id', $children->class_section_id)
                        ->where(function ($q) use ($children) {
                            $q->where('cs.type', '!=', 'elective')
                                ->orWhere(function ($q2) use ($children) {
                                    $q2->where('cs.type', 'elective')
                                        ->whereExists(function ($q3) use ($children) {
                                            $q3->select(DB::raw(1))
                                                ->from('student_subjects')
                                                ->whereRaw('student_subjects.class_subject_id = cs.id')
                                                ->where('student_subjects.student_id', $children->user_id);
                                        });
                                });
                        });
                })
                ->with('subject_teacher')
                ->orderBy('day')
                ->orderBy('start_time')
                ->get();
            foreach($timetable as $key => $value){
                if(isset($value->timetable_configurations_id)){
                    $data = DB::table('timetable_configurations')->where('id', $value->timetable_configurations_id)->first();
                    if(isset($data->image)){
                        $timetable[$key]->timetable_configuration_image = asset('storage/'.$data->image);
                    }
                }
            }
            ResponseService::successResponse("Timetable Fetched Successfully", new TimetableCollection($timetable));
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function getLessons(Request $request) {
        $validator = Validator::make($request->all(), [
            'child_id'         => 'required|numeric',
            'lesson_id'        => 'nullable|numeric',
            'class_subject_id' => 'required',
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }

        try {
            //$children = $request->user()->guardianRelationChild()->where('id', $request->child_id)->first();
            $children = Auth::user()->guardianRelationChild()->where('id', $request->child_id)->whereHas('user', function ($q) {
                $q->whereNull('deleted_at');
            })->first();

            if (empty($children)) {
                ResponseService::errorResponse("Child's Account is not Active.Contact School Support", NULL, config('constants.RESPONSE_CODE.INACTIVE_CHILD'));
            }
            $data = $this->lesson->builder()->where(['class_section_id' => $children->class_section_id, 'class_subject_id' => $request->class_subject_id])->with('topic', 'file');
            if ($request->lesson_id) {
                $data->where('id', $request->lesson_id);
            }
            $data = $data->get();
            ResponseService::successResponse("Lessons Fetched Successfully", $data);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function getLessonTopics(Request $request) {
        $validator = Validator::make($request->all(), [
            'child_id'  => 'required|numeric',
            'lesson_id' => 'required|numeric',
            'topic_id'  => 'nullable|numeric',
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }

        try {
            $student = Auth::user()->guardianRelationChild()->where('id', $request->child_id)->whereHas('user', function ($q) {
                $q->whereNull('deleted_at');
            })->first();

            if (empty($student)) {
                ResponseService::errorResponse("Child's Account is not Active.Contact School Support", NULL, config('constants.RESPONSE_CODE.INACTIVE_CHILD'));
            }
            $data = $this->lessonTopic->builder()->where('lesson_id', $request->lesson_id)->with('file');
            if ($request->topic_id) {
                $data->where('id', $request->topic_id);
            }
            $data = $data->get();
            ResponseService::successResponse("Topics Fetched Successfully", $data);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function getAssignments(Request $request) {
        $validator = Validator::make($request->all(), [
            'child_id'         => 'required|numeric',
            'assignment_id'    => 'nullable|numeric',
            'class_subject_id' => 'nullable|numeric',
            'is_submitted'     => 'nullable|numeric',
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            //$children = $request->user()->guardianRelationChild()->where('id', $request->child_id)->first();
            $children = Auth::user()->guardianRelationChild()->where('id', $request->child_id)->whereHas('user', function ($q) {
                $q->whereNull('deleted_at');
            })->first();

            if (empty($children)) {
                ResponseService::errorResponse("Child's Account is not Active.Contact School Support", NULL, config('constants.RESPONSE_CODE.INACTIVE_CHILD'));
            }
            $data = $this->assignment->builder()->where('class_section_id', $children->class_section_id)->with(['file', 'class_subject.subject', 'submission' => function ($query) use ($children) {
                $query->where('student_id', $children->user_id)->with('file');
            }]);
            if ($request->assignment_id) {
                $data->where('id', $request->assignment_id);
            }
            if ($request->class_subject_id) {
                $data->where('class_subject_id', $request->class_subject_id);
            }
            if (isset($request->is_submitted)) {
                if ($request->is_submitted) {
                    $data->whereHas('submission', function ($q) use ($children) {
                        $q->where('student_id', $children->user_id);
                    });
                } else {
                    $data->whereDoesntHave('submission', function ($q) use ($children) {
                        $q->where('student_id', $children->user_id);
                    });
                }
            }
            $data = $data->orderBy('id', 'desc')->paginate(15);
            ResponseService::successResponse("Assignments Fetched Successfully", $data);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function getAttendance(Request $request) {
        $validator = Validator::make($request->all(), [
            'child_id' => 'required|numeric',
            'month'    => 'nullable|numeric',
            'year'     => 'nullable|numeric',
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            //$children = $request->user()->guardianRelationChild()->where('id', $request->child_id)->first();
            $children = Auth::user()->guardianRelationChild()->where('id', $request->child_id)->whereHas('user', function ($q) {
                $q->whereNull('deleted_at');
            })->first();

            if (empty($children)) {
                ResponseService::errorResponse("Child's Account is not Active.Contact School Support", NULL, config('constants.RESPONSE_CODE.INACTIVE_CHILD'));
            }
            $sessionYear = $this->cache->getDefaultSessionYear($children->school_id);

            $attendance = $this->attendance->builder()->where(['student_id' => $children->user_id/*, 'session_year_id' => $sessionYear->id*/]);
            $holidays = $this->holiday->builder();
            if (isset($request->month)) {
                $attendance = $attendance->whereMonth('date', $request->month);
                $holidays = $holidays->whereMonth('date', $request->month);
            }

            if (isset($request->year)) {
                $attendance = $attendance->whereYear('date', $request->year);
                $holidays = $holidays->whereYear('date', $request->year);
            }
            $attendance = $attendance->get();
            $holidays = $holidays->get();

            $data = ['attendance' => $attendance, 'holidays' => $holidays, 'session_year' => $sessionYear];

            ResponseService::successResponse("Attendance Details Fetched Successfully", $data);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function getAnnouncements(Request $request) {
        $validator = Validator::make($request->all(), [
            'type'             => 'required|in:subject,class',
            'child_id'         => 'required_if:type,subject,class|numeric',
            'class_subject_id' => 'required_if:type,subject|numeric'
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            //$children = $request->user()->guardianRelationChild()->where('id', $request->child_id)->first();
            $children = Auth::user()->guardianRelationChild()->where('id', $request->child_id)->whereHas('user', function ($q) {
                $q->whereNull('deleted_at');
            })->first();

            if (empty($children)) {
                ResponseService::errorResponse("Child's Account is not Active.Contact School Support", NULL, config('constants.RESPONSE_CODE.INACTIVE_CHILD'));
            }
            $classSectionId = $children->class_section_id;

            $sessionYear = $this->cache->getDefaultSessionYear($children->school_id);
            if (isset($request->type) && $request->type == "subject") {
                $table = $this->subjectTeacher->builder()->where(['class_section_id' => $children->class_section_id, 'class_subject_id' => $request->class_subject_id])->get()->pluck('id');
                if ($table === null) {
                    ResponseService::errorResponse("Invalid Subject ID", null, config('constants.RESPONSE_CODE.INVALID_SUBJECT_ID'));
                }
            }
            $data = $this->announcement->builder()->with('file')->where('session_year_id', $sessionYear->id);

            if (isset($request->type) && $request->type == "class") {
                $data = $data->whereHas('announcement_class', function ($query) use ($classSectionId) {
                    $query->where(['class_section_id' => $classSectionId, 'class_subject_id' => null]);
                });
            }


            if (isset($request->type) && $request->type == "subject") {
                $data = $data->whereHas('announcement_class', function ($query) use ($classSectionId, $request) {
                    $query->where(['class_section_id' => $classSectionId, 'class_subject_id' => $request->class_subject_id]);
                });
            }

            $data = $data->orderBy('id', 'desc')->paginate(15);
            ResponseService::successResponse("Announcement Details Fetched Successfully", $data);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function getTeachers(Request $request) {
        $validator = Validator::make($request->all(), [
            'child_id' => 'required|numeric',
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            //$children = Auth::user()->guardianRelationChild()->where('id', $request->child_id)->first();
            $children = Auth::user()->guardianRelationChild()->where('id', $request->child_id)->whereHas('user', function ($q) {
                $q->whereNull('deleted_at');
            })->first();

            if (empty($children)) {
                ResponseService::errorResponse("Child's Account is not Active.Contact School Support", NULL, config('constants.RESPONSE_CODE.INACTIVE_CHILD'));
            }
            $class_subject_id = $children->selectedStudentSubjects()->pluck('class_subject_id');
            $subjectTeachers = $this->subjectTeachers->builder()->select('id', 'subject_id', 'teacher_id', 'school_id')->whereIn('class_subject_id', $class_subject_id)->where('class_section_id', $children->class_section_id)->with('subject:id,name,type', 'teacher:id,first_name,last_name,image,mobile')->get();

            for($i = 0; $i < COUNT($subjectTeachers); $i++){
                if(isset($subjectTeachers[$i]->teacher)){
                    $subjectTeachers[$i]->teacher->mobile = '**********';
                }
            }
            ResponseService::successResponse("Teacher Details Fetched Successfully", $subjectTeachers);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function getSessionYear(Request $request) {
        $validator = Validator::make($request->all(), [
            'child_id' => 'required|numeric',
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            //$children = $request->user()->guardianRelationChild()->where('id', $request->child_id)->first();
            $children = Auth::user()->guardianRelationChild()->where('id', $request->child_id)->whereHas('user', function ($q) {
                $q->whereNull('deleted_at');
            })->first();

            if (empty($children)) {
                ResponseService::errorResponse("Child's Account is not Active.Contact School Support", NULL, config('constants.RESPONSE_CODE.INACTIVE_CHILD'));
            }
            $sessionYear = $this->cache->getDefaultSessionYear($children->school_id);
            ResponseService::successResponse("Session Year Fetched Successfully", $sessionYear ?? []);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function getChildProfileDetails(Request $request) {
        $validator = Validator::make($request->all(), [
            'child_id' => 'required|numeric',
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            //$childData = Auth::user()->guardianRelationChild()->where('id', $request->child_id)->with(['class_section' => function ($query) {
            //  $query->with('section', 'class', 'medium', 'class.shift', 'class.stream');
            //}, 'guardian', 'user'                                                                                      => function ($q) {
            //  $q->with('extra_student_details.form_field', 'school');
            //}])->first();

            $childData = Auth::user()->guardianRelationChild()->with(['class_section' => function ($query) {
                $query->with('section', 'class', 'medium', 'class.shift', 'class.stream');
            }, 'guardian', 'user'                                                     => function ($q) {
                $q->with('extra_student_details.form_field', 'school');
            }])->where('id', $request->child_id)->whereHas('user', function ($q) {
                $q->whereNull('deleted_at');
            })->first();

            if (empty($childData)) {
                ResponseService::errorResponse("Child's Account is not Active.Contact School Support", NULL, config('constants.RESPONSE_CODE.INACTIVE_CHILD'));
            }

            $data = array(
                'id'                => $childData->id,
                'first_name'        => $childData->user->first_name,
                'last_name'         => $childData->user->last_name,
                'mobile'            => $childData->user->mobile,
                'roll_number'       => $childData->roll_number,
                'admission_no'      => $childData->admission_no,
                'admission_date'    => $childData->admission_date,
                'gender'            => $childData->user->gender,
                'image'             => $childData->user->image,
                'dob'               => $childData->user->dob,
                'current_address'   => $childData->user->current_address,
                'permanent_address' => $childData->user->permanent_address,
                'occupation'        => $childData->user->occupation,
                'status'            => $childData->user->status,
                'fcm_id'            => $childData->user->fcm_id,
                'school_id'         => $childData->school_id,
                'session_year_id'   => $childData->session_year_id,
                'email_verified_at' => $childData->user->email_verified_at,
                'created_at'        => $childData->created_at,
                'updated_at'        => $childData->updated_at,
                'class_section'     => $childData->class_section,
                'guardian'          => $childData->guardian,
                // 'extra_details'     => $childData->user->extra_student_details,
                'school'            => $childData->user->school,
            );
            ResponseService::successResponse('Data Fetched Successfully', $data);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function getExamList(Request $request) {
        $validator = Validator::make($request->all(), [
            'child_id' => 'required|numeric',
            'status'   => 'nullable:digits:0,1,2,3'
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            //$student = $this->student->findById($request->child_id, ['id', 'user_id', 'class_section_id'], ['class_section']);
            $student = Auth::user()->guardianRelationChild()->where('id', $request->child_id)->whereHas('user', function ($q) {
                $q->whereNull('deleted_at');
            })->first();

            if (empty($student)) {
                ResponseService::errorResponse("Child's Account is not Active.Contact School Support", NULL, config('constants.RESPONSE_CODE.INACTIVE_CHILD'));
            }
            $classId = $student->class_section->class_id;
            $exam = $this->exam->builder()
                ->where('class_id', $classId)
                ->with([
                    'timetable' => function ($query) {
                        $query->selectRaw('* , SUM(total_marks) as total_marks')
                            ->groupBy('exam_id');
                    }
                ])->get();

            $exam_data = array();
            foreach ($exam as $data) {
                if (isset($request->status) && $request->status != $data->exam_status && $request->status != 3) {
                    continue;
                }

                $exam_data[] = [
                    'id'                 => $data->id,
                    'name'               => $data->name,
                    'description'        => $data->description,
                    'publish'            => $data->publish,
                    'session_year'       => $data->session_year->name,
                    'exam_starting_date' => $data->start_date,
                    'exam_ending_date'   => $data->end_date,
                    'exam_status'        => $data->exam_status,
                ];
            }

            ResponseService::successResponse("Exam List fetched Successfully", $exam_data ?? []);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e, 'parentApiController :- getExamList Method');
            ResponseService::errorResponse();
        }
    }

    public function getExamDetails(Request $request) {
        $validator = Validator::make($request->all(), [
            'child_id' => 'required|numeric',
            'exam_id'  => 'required|nullable'
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            //$studentData = $this->student->findById($request->child_id, ['id', 'user_id', 'class_section_id'], ['class_section']);
            $studentData = Auth::user()->guardianRelationChild()->where('id', $request->child_id)->whereHas('user', function ($q) {
                $q->whereNull('deleted_at');
            })->first();

            if (empty($studentData)) {
                ResponseService::errorResponse("Child's Account is not Active.Contact School Support", NULL, config('constants.RESPONSE_CODE.INACTIVE_CHILD'));
            }
            $classId = $studentData->class_section->class_id;
            $examData = $this->exam->builder()
                ->where([
                    'id'       => $request->exam_id,
                    'class_id' => $classId
                ])
                ->with([
                    'timetable' => function ($query) {
                        $query->owner()->with(['class_subject.subject'])->orderby('date');
                    }
                ])->first();


            if (!$examData) {
                ResponseService::successResponse("", []);
            }


            foreach ($examData->timetable as $data) {
                $exam_data[] = array(
                    'exam_timetable_id' => $data->id,
                    'total_marks'       => $data->total_marks,
                    'passing_marks'     => $data->passing_marks,
                    'date'              => $data->date,
                    'starting_time'     => $data->start_time,
                    'ending_time'       => $data->end_time,
                    'subject'           => array(
                        'id'               => $data->class_subject->subject->id,
                        'class_subject_id' => $data->class_subject_id,
                        'name'             => $data->class_subject->subject->name,
                        'bg_color'         => $data->class_subject->subject->bg_color,
                        'image'            => $data->class_subject->subject->image,
                        'type'             => $data->class_subject->subject->type,
                    )
                );
            }
            ResponseService::successResponse("", $exam_data ?? []);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e, 'parentApiController :- getExamDetails Method');
            ResponseService::errorResponse();
        }
    }

    public function getExamMarks(Request $request) {
        $validator = Validator::make($request->all(), [
            'child_id' => 'required|numeric',
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            // Student Data
            //$studentData = $this->student->findById($request->child_id, ['id', 'user_id', 'class_section_id'], ['class_section']);
            $studentData = Auth::user()->guardianRelationChild()->where('id', $request->child_id)->whereHas('user', function ($q) {
                $q->whereNull('deleted_at');
            })->first();

            if (empty($studentData)) {
                ResponseService::errorResponse("Child's Account is not Active.Contact School Support", NULL, config('constants.RESPONSE_CODE.INACTIVE_CHILD'));
            }
            // Exam Result Data
            $examResultDB = $this->examResult->builder()->with([
                'user'       => function ($q) {
                    $q->select('id', 'first_name', 'last_name')->with('student:id,user_id,roll_number');
                },
                'exam.timetable:id,exam_id,start_time,end_time',
                'session_year',
                'exam.marks' => function ($q) use ($studentData) {
                    $q->where('student_id', $studentData->user_id);
                }
            ])->where('student_id', $studentData->user_id)->get();


            // Check that Exam Result DB is not empty
            if (count($examResultDB)) {
                foreach ($examResultDB as $examResultData) {
                    $exam_result = array(
                        'result_id'      => $examResultData->id,
                        'exam_id'        => $examResultData->exam_id,
                        'exam_name'      => $examResultData->exam->name,
                        'class_name'     => $studentData->class_section->full_name,
                        'student_name'   => $examResultData->user->full_name,
                        'exam_date'      => $examResultData->exam->start_date,
                        'total_marks'    => $examResultData->total_marks,
                        'obtained_marks' => $examResultData->obtained_marks,
                        'percentage'     => $examResultData->percentage,
                        'grade'          => $examResultData->grade,
                        'session_year'   => $examResultData->session_year->name,
                    );
                    $exam_marks = array();
                    foreach ($examResultData->exam->marks as $marks) {
                        $exam_marks[] = array(
                            'marks_id'       => $marks->id,
                            'subject_name'   => $marks->class_subject->subject->name,
                            'subject_type'   => $marks->class_subject->subject->type,
                            'total_marks'    => $marks->timetable->total_marks,
                            'obtained_marks' => $marks->obtained_marks,
                            'teacher_review' => $marks->teacher_review,
                            'grade'          => $marks->grade,
                        );
                    }
                    $data[] = array(
                        'result'     => $exam_result,
                        'exam_marks' => $exam_marks,
                    );
                }
                ResponseService::successResponse("Exam Result Fetched Successfully", $data ?? null);
            } else {
                ResponseService::successResponse("Exam Result Fetched Successfully", []);
            }
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function getOnlineExamList(Request $request) {
        $validator = Validator::make($request->all(), [
            'child_id'         => 'required|numeric',
            'class_subject_id' => 'nullable|numeric'
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            //$student = $this->student->findById($request->child_id, ['id', 'user_id', 'class_section_id', 'school_id'], ['class_section']);
            $student = Auth::user()->guardianRelationChild()->where('id', $request->child_id)->whereHas('user', function ($q) {
                $q->whereNull('deleted_at');
            })->first();

            if (empty($student)) {
                ResponseService::errorResponse("Child's Account is not Active.Contact School Support", NULL, config('constants.RESPONSE_CODE.INACTIVE_CHILD'));
            }
            $classSectionId = $student->class_section->id;
            $sessionYear = $this->cache->getDefaultSessionYear($student->school_id);

            $onlineExamData = $this->onlineExam->builder()
                // ->where(['class_section_id' => $classSectionId, 'session_year_id' => $sessionYear->id])
                ->where(['class_section_id' => $classSectionId])
                ->where('end_date', '>=', now())
                ->has('question_choice')
                ->with('class_subject', 'question_choice:id,online_exam_id,marks')
                ->whereDoesntHave('student_attempt', function ($q) use ($student) {
                    $q->where('student_id', $student->user_id);
                })
                ->when($request->class_subject_id, function ($query, $classSubjectId) {
                    return $query->where('class_subject_id', $classSubjectId);
                })
                ->orderby('start_date')
                ->paginate(15);

            ResponseService::successResponse('Data Fetched Successfully', $onlineExamData);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e, "Parent API ");
            ResponseService::errorResponse();
        }
    }

    public function getOnlineExamResultList(Request $request) {
        $validator = Validator::make($request->all(), [
            'child_id'         => 'required|numeric',
            'class_subject_id' => 'nullable|numeric'
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            //$student = $this->student->findById($request->child_id, ['id', 'user_id', 'class_section_id', 'school_id'], ['class_section']);
            $student = Auth::user()->guardianRelationChild()->where('id', $request->child_id)->whereHas('user', function ($q) {
                $q->whereNull('deleted_at');
            })->first();

            if (empty($student)) {
                ResponseService::errorResponse("Child's Account is not Active.Contact School Support", NULL, config('constants.RESPONSE_CODE.INACTIVE_CHILD'));
            }
            $classSectionId = $student->class_section_id;
            $sessionYear = $this->cache->getDefaultSessionYear($student->school_id);

            $studentSubjects = $student->selectedStudentSubjects()->pluck('class_subject_id');
            // Get Online Exam Data Where Logged in Student have attempted data and Relation Data with Question Choice , Student's answer with user submitted question with question and its option
            $onlineExamData = $this->onlineExam->builder()
                ->when($request->class_subject_id, function ($query) use ($request) {
                    $query->where('class_subject_id', $request->class_subject_id);
                })
                //->where(['class_section_id' => $classSectionId, 'session_year_id' => $sessionYear->id])
                ->where(['class_section_id' => $classSectionId])
                ->whereHas('student_attempt', function ($q) use ($student) {
                    $q->where('student_id', $student->user_id);
                })
                ->whereIn('class_subject_id', $studentSubjects)
                ->with('question_choice:id,online_exam_id,marks', 'student_answers.user_submitted_questions.questions:id', 'student_answers.user_submitted_questions.questions.options:id,question_id,is_answer', 'class_subject.subject:id,name,type,code,bg_color,image')
                ->paginate(15)->toArray();

            $examListData = array(); // Initialized Empty examListData Array

            // Loop through Exam data
            foreach ($onlineExamData['data'] as $data) {

                // Get Total Marks of Particular Exam
                $totalMarks = collect($data['question_choice'])->sum('marks');

                // Initialized totalObtainedMarks with 0
                $totalObtainedMarks = 0;

                // Group Student's Answers by question_id
                $grouped_answers = [];
                foreach ($data['student_answers'] as $student_answer) {
                    $grouped_answers[$student_answer['question_id']][] = $student_answer;
                }

                // Loop through Student's Grouped answers
                foreach ($grouped_answers as $student_answers) {

                    // Filter the options whose is_answer values is 1
                    $correct_option_ids = array_filter($student_answers[0]['user_submitted_questions']['questions']['options'], static function ($option) {
                        return $option['is_answer'] == 1;
                    });

                    // Get All Correct Options
                    $correct_option_ids = array_column($correct_option_ids, 'id');

                    // Get Student's Correct Options
                    $student_option_ids = array_column($student_answers, 'option_id');

                    // Check if the student's answers exactly match the correct answers then add marks with totalObtainedMarks
                    if (!array_diff($correct_option_ids, $student_option_ids) && !array_diff($student_option_ids, $correct_option_ids)) {
                        $totalObtainedMarks += $student_answers[0]['user_submitted_questions']['marks'];
                    }
                }

                // Make Exam List Data
                $examListData[] = array(
                    'online_exam_id'      => $data['id'],
                    'subject'             => array(
                        'id'   => $data['class_subject']['subject']['id'],
                        'name' => $data['class_subject']['subject']['name'] . ' - ' . $data['class_subject']['subject']['type'],
                    ),
                    'title'               => $data['title'],
                    'obtained_marks'      => $totalObtainedMarks ?? "0",
                    'total_marks'         => $totalMarks ?? "0",
                    'exam_submitted_date' => date('Y-m-d', strtotime($data['end_date']))
                );
            }

            $examList = array(
                'current_page' => $onlineExamData['current_page'],
                'data'         => $examListData,
                'from'         => $onlineExamData['from'],
                'last_page'    => $onlineExamData['last_page'],
                'per_page'     => $onlineExamData['per_page'],
                'to'           => $onlineExamData['to'],
                'total'        => $onlineExamData['total'],
            );

            ResponseService::successResponse("", $examList);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function getOnlineExamResult(Request $request) {
        $validator = Validator::make($request->all(), [
            'child_id'       => 'required|numeric',
            'online_exam_id' => 'required|numeric',
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            //$student = $this->student->findById($request->child_id, ['id', 'user_id', 'class_section_id'], ['class_section']);
            $student = Auth::user()->guardianRelationChild()->where('id', $request->child_id)->whereHas('user', function ($q) {
                $q->whereNull('deleted_at');
            })->first();

            if (empty($student)) {
                ResponseService::errorResponse("Child's Account is not Active.Contact School Support", NULL, config('constants.RESPONSE_CODE.INACTIVE_CHILD'));
            }
            // Online Exam Data
            $onlineExam = $this->onlineExam->builder()
                ->where('id', $request->online_exam_id)
                ->whereHas('student_attempt', function ($q) use ($student) {
                    $q->where('student_id', $student->user_id);
                })
                ->with([
                    'question_choice:id,online_exam_id,marks',
                    'student_answers.user_submitted_questions.questions:id',
                    'student_answers.user_submitted_questions.questions.options:id,question_id,is_answer',
                ])
                ->first();

            if (isset($onlineExam) && $onlineExam != null) {

                //Get Total Question Count and Total Marks
                $totalQuestions = $onlineExam->question_choice->count();
                $totalMarks = $onlineExam->question_choice->sum('marks');

                // Group Student's Answers by question_id
                $grouped_answers = [];
                foreach ($onlineExam->student_answers as $student_answer) {
                    $grouped_answers[$student_answer['question_id']][] = $student_answer->toArray();
                }

                // Initialized the variables
                $correctQuestionData = array();
                $correctQuestions = 0;
                $totalObtainedMarks = "0";

                // Loop through Student's Grouped answers
                foreach ($grouped_answers as $student_answers) {

                    // Filter the options whose is_answer values is 1
                    $correct_option_ids = array_filter($student_answers[0]['user_submitted_questions']['questions']['options'], static function ($option) {
                        return $option['is_answer'] == 1;
                    });

                    // Get All Correct Options
                    $correct_option_ids = array_column($correct_option_ids, 'id');

                    // Get Student's Correct Options
                    $student_option_ids = array_column($student_answers, 'option_id');

                    // Check if the student's answers exactly match the correct answers then add marks with totalObtainedMarks
                    if (!array_diff($correct_option_ids, $student_option_ids) && !array_diff($student_option_ids, $correct_option_ids)) {

                        // Sum Question marks with ObtainedMarks
                        $totalObtainedMarks += $student_answers[0]['user_submitted_questions']['marks'];

                        // Get Correct Questions Ids
                        $correctQuestionIds[] = $student_answers[0]['user_submitted_questions']['id'];

                        // Increment Correct Question by 1
                        ++$correctQuestions;

                        // Correct Question Data
                        $correctQuestionData[] = array(
                            'question_id' => $student_answers[0]['user_submitted_questions']['id'],
                            'marks'       => $student_answers[0]['user_submitted_questions']['marks']
                        );
                    }
                }


                // Check correctQuestionIds exists and not empty
                if (!empty($correctQuestionIds)) {
                    // Get Incorrect Questions Excluding Correct answer using correctQuestionIds
                    $incorrectQuestionsData = $onlineExam->question_choice->whereNotIn('id', $correctQuestionIds);
                } else {
                    // Get All Question Choice as incorrectQuestionsData
                    $incorrectQuestionsData = $onlineExam->question_choice;
                }

                // Total Incorrect Questions
                $incorrectQuestions = $incorrectQuestionsData->count();

                // Incorrect Question Data
                $inCorrectQuestionData = array();
                foreach ($incorrectQuestionsData as $incorrectData) {
                    $inCorrectQuestionData[] = array(
                        'question_id' => $incorrectData->id,
                        'marks'       => $incorrectData->marks
                    );
                }

                // Final Array Data
                $onlineExamResult = array(
                    'total_questions'      => $totalQuestions,
                    'correct_answers'      => array(
                        'total_questions' => $correctQuestions,
                        'question_data'   => $correctQuestionData
                    ),
                    'in_correct_answers'   => array(
                        'total_questions' => $incorrectQuestions,
                        'question_data'   => $inCorrectQuestionData
                    ),
                    'total_obtained_marks' => $totalObtainedMarks,
                    'total_marks'          => $totalMarks ?? '0'
                );
                ResponseService::successResponse("", $onlineExamResult);
            } else {
                ResponseService::successResponse("", []);
            }
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function getOnlineExamReport(Request $request) {
        $validator = Validator::make($request->all(), [
            'child_id'         => 'required|numeric',
            'class_subject_id' => 'required|numeric'
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            //$student = $this->student->findById($request->child_id, ['id', 'user_id', 'class_section_id', 'school_id'], ['class_section']);
            $student = Auth::user()->guardianRelationChild()->where('id', $request->child_id)->whereHas('user', function ($q) {
                $q->whereNull('deleted_at');
            })->first();

            if (empty($student)) {
                ResponseService::errorResponse("Child's Account is not Active.Contact School Support", NULL, config('constants.RESPONSE_CODE.INACTIVE_CHILD'));
            }
            $sessionYear = $this->cache->getDefaultSessionYear($student->school_id);

            $onlineExams = $this->onlineExam->builder()
                ->has('question_choice')
                // ->where(['class_section_id' => $student->class_section_id, 'class_subject_id' => $request->class_subject_id, 'session_year_id' => $sessionYear->id])
                ->where(['class_section_id' => $student->class_section_id, 'class_subject_id' => $request->class_subject_id])
                ->whereHas('student_attempt', function ($q) use ($student) {
                    $q->where('student_id', $student->user_id);
                })
                ->with([
                    'question_choice:id,online_exam_id,marks',
                    'student_answers.user_submitted_questions.questions:id',
                    'student_answers.user_submitted_questions.questions.options:id,question_id,is_answer',
                    'class_subject.subject:id,name,type,code,bg_color,image'
                ])
                ->paginate(10);
            $totalMarks = 0;
            $totalObtainedMarks = 0;
            if ($onlineExams->count() > 0) {
                $totalExamIds = $onlineExams->pluck('id')->toArray();
                $totalExamsAttempted = $this->user->builder()->role('Student')->where('id', $student->user_id)->has('online_exam_attempts')->count();

                $examList = array();
                foreach ($onlineExams->toArray()['data'] as $onlineExam) {
                    $totalMarks = collect($onlineExam['question_choice'])->sum('marks');

                    // Initialized totalObtainedMarks with 0
                    $totalObtainedMarks = "0";

                    // Group Student's Answers by question_id
                    $grouped_answers = [];
                    foreach ($onlineExam['student_answers'] as $student_answer) {
                        $grouped_answers[$student_answer['question_id']][] = $student_answer;
                    }

                    // Loop through Student's Grouped answers
                    foreach ($grouped_answers as $student_answers) {

                        // Filter the options whose is_answer values is 1
                        $correct_option_ids = array_filter($student_answers[0]['user_submitted_questions']['questions']['options'], static function ($option) {
                            return $option['is_answer'] == 1;
                        });

                        // Get All Correct Options
                        $correct_option_ids = array_column($correct_option_ids, 'id');

                        // Get Student's Correct Options
                        $student_option_ids = array_column($student_answers, 'option_id');

                        // Check if the student's answers exactly match the correct answers then add marks with totalObtainedMarks
                        if (!array_diff($correct_option_ids, $student_option_ids) && !array_diff($student_option_ids, $correct_option_ids)) {
                            $totalObtainedMarks += $student_answers[0]['user_submitted_questions']['marks'];
                        }
                    }

                    // Add exam to the list
                    $examList[] = [
                        'online_exam_id' => $onlineExam['id'],
                        'title'          => $onlineExam['title'],
                        'obtained_marks' => (string)$totalObtainedMarks,
                        'total_marks'    => (string)$totalMarks,
                    ];
                }


                // Calculate Percentage
                if ($totalMarks > 0) {
                    // Avoid division by zero error
                    $percentage = number_format(($totalObtainedMarks * 100) / max($totalMarks, 1), 2);
                } else {
                    // If total marks is zero, then percentage is also zero
                    $percentage = 0;
                }

                // Build the final data array
                $onlineExamReportData = array(
                    'total_exams'          => count($totalExamIds),
                    'attempted'            => $totalExamsAttempted,
                    'missed_exams'         => count($totalExamIds) - $totalExamsAttempted,
                    'total_marks'          => (string)$totalMarks,
                    'total_obtained_marks' => (string)$totalObtainedMarks,
                    'percentage'           => (string)$percentage,
                    'exam_list'            => [
                        'current_page' => (string)$onlineExams->currentPage(),
                        'data'         => array_values($examList),
                        'from'         => (string)$onlineExams->firstItem(),
                        'last_page'    => (string)$onlineExams->lastPage(),
                        'per_page'     => (string)$onlineExams->perPage(),
                        'to'           => (string)$onlineExams->lastItem(),
                        'total'        => (string)$onlineExams->total(),
                    ],
                );
            } else {
                $onlineExamReportData = [];
            }


            // Return the response
            ResponseService::successResponse("", $onlineExamReportData);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function getAssignmentReport(Request $request) {
        $validator = Validator::make($request->all(), [
            'child_id'         => 'required|numeric',
            'class_subject_id' => 'required|numeric'
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            //$student = $this->student->findById($request->child_id, ['id', 'user_id', 'class_section_id', 'school_id'], ['class_section']);
            $student = Auth::user()->guardianRelationChild()->where('id', $request->child_id)->whereHas('user', function ($q) {
                $q->whereNull('deleted_at');
            })->first();

            if (empty($student)) {
                ResponseService::errorResponse("Child's Account is not Active.Contact School Support", NULL, config('constants.RESPONSE_CODE.INACTIVE_CHILD'));
            }
            $sessionYear = $this->cache->getDefaultSessionYear($student->school_id);

            // Assignment Data
            $assignments = $this->assignment->builder()
                ->where(['class_section_id' => $student->class_section_id, /*'session_year_id' => $sessionYear->id,*/ 'class_subject_id' => $request->class_subject_id])
                ->get();

            // Get the assignment submissions
            $submitted_assignment_ids = $this->assignmentSubmission->builder()->where('student_id', $student->user_id)->where('status', 1)->whereIn('assignment_id', $assignments->pluck('id'))->pluck('assignment_id');

            // Calculate various statistics
            $total_assignments = $assignments->count();
            $total_submitted_assignments = $submitted_assignment_ids->count();
            $total_assignment_submitted_points = $assignments->sum('points');
            $total_points_obtained = $this->assignmentSubmission->builder()->whereIn('assignment_id', $submitted_assignment_ids)->where('student_id', $student->user_id)->sum('points');

            // Calculate the percentage
            $percentage = $total_assignment_submitted_points ? number_format(($total_points_obtained * 100) / $total_assignment_submitted_points, 2) : 0;

            // Get the submitted assignment data with points (using pagination manually)
            $perPage = 15;
            $currentPage = $request->input('page', 1);
            $offset = ($currentPage - 1) * $perPage;
            $submitted_assignment_data_with_points = $assignments->filter(function ($assignment) use ($submitted_assignment_ids) {
                return $assignment->points !== null && $submitted_assignment_ids->contains($assignment->id);
            })->slice($offset, $perPage)->map(function ($assignment) use($student) {
                return [
                    'assignment_id'   => $assignment->id,
                    'assignment_name' => $assignment->name,
                    'obtained_points' => optional($assignment->submission->where('student_id', $student->user_id)->where('assignment_id', $assignment->id)->first())->points ?? 0,
                    'total_points'    => $assignment->points
                ];
            });

            $assignment_report = [
                'assignments'                           => $total_assignments,
                'submitted_assignments'                 => $total_submitted_assignments,
                'unsubmitted_assignments'               => $total_assignments - $total_submitted_assignments,
                'total_points'                          => $total_assignment_submitted_points,
                'total_obtained_points'                 => $total_points_obtained,
                'percentage'                            => $percentage,
                'submitted_assignment_with_points_data' => [
                    'current_page' => $currentPage,
                    'data'         => array_values($submitted_assignment_data_with_points->toArray()),
                    'from'         => $offset + 1,
                    'to'           => $offset + $submitted_assignment_data_with_points->count(),
                    'per_page'     => $perPage,
                    'total'        => $total_submitted_assignments,
                    'last_page'    => ceil($total_submitted_assignments / $perPage),
                ],
            ];


            ResponseService::successResponse("Data Fetched Successfully", $assignment_report);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    //Get Fees Details
    public function getFees(Request $request) {
        $validator = Validator::make($request->all(), [
            'child_id'        => 'required',
            'session_year_id' => 'nullable|numeric'
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            $student = Auth::user()->guardianRelationChild()->where('id', $request->child_id)->whereHas('user', function ($q) {
                $q->whereNull('deleted_at');
            })->first();

            if (empty($student)) {
                ResponseService::errorResponse("Child's Account is not Active.Contact School Support", NULL, config('constants.RESPONSE_CODE.INACTIVE_CHILD'));
            }
            $classId = $student->class_section->class_id;
            $schoolId = $student->user->school_id;

            $currentSessionYear = $this->cache->getDefaultSessionYear($schoolId);
            $sessionYearId = $request->session_year_id ?? $currentSessionYear->id;

            $fees = $this->fees->builder()->where('class_id', $classId)
                ->with(['fees_class_type.fees_type',
                        'fees_paid' => function ($query) use ($student) {
                            $query->where(['student_id' => $student->user_id])->with('compulsory_fee.advance_fees', 'optional_fee');
                        }, 'session_year', 'class.medium', 'class.stream'
                ])->where(['session_year_id' => $sessionYearId])->get();

            $currentDateTimestamp = new DateTime(date('Y-m-d'));

            foreach ($fees as $fee) {
                $feesDateTimestamp = new DateTime($fee->due_date);

                // Set Optional Fees Data in response
                if (count($fee->optional_fees) > 0) {
                    collect($fee->optional_fees)->map(function ($optionalFees) use ($student) {
                        $isOptionalFeesPaid = $student->user->optional_fees->first(function ($optionalFeesPaid) use ($optionalFees, $student) {
                            return $optionalFeesPaid->fees_class_id == $optionalFees->id && $optionalFeesPaid->student_id == $student->user->id;
                        });
                        $optionalFees['is_paid'] = $isOptionalFeesPaid ? true : false;
                        return $optionalFees;
                    });
                }


                // Set Compulsory Fees Data in response
                if (count($fee->compulsory_fees) > 0) {
                    $fee->is_overdue = $currentDateTimestamp > $feesDateTimestamp; // true/false
                    collect($fee->compulsory_fees)->map(function ($compulsoryFees) use ($student) {
                        $isCompulsoryFeesPaid = $student->user->compulsory_fees->first(function ($compulsoryFeesPaid) use ($student) {
                            return $compulsoryFeesPaid->type == 'Full Payment' && $compulsoryFeesPaid->student_id == $student->user->id;
                        });
                        $compulsoryFees['is_paid'] = $isCompulsoryFeesPaid ? true : false;
                        return $compulsoryFees;
                    });
                }

                // Set Installment Data in Response
                if (count($fee->installments) > 0) {
                    $totalFeesAmount = $fee->total_compulsory_fees;
                    $totalInstallments = count($fee->installments);

                    $previousInstallmentDate = new DateTime('now -1 day');
                    collect($fee->installments)->map(function ($installment) use ($student, &$totalFeesAmount, &$totalInstallments, $currentDateTimestamp, &$previousInstallmentDate) {
                        $installmentDueDateTimestamp = new DateTime($installment['due_date']);

                        $installmentPaid = $student->user->compulsory_fees->first(function ($compulsoryFeesPaid) use ($installment, $student) {
                            return $compulsoryFeesPaid->type == "Installment Payment" && $compulsoryFeesPaid->installment_id == $installment->id && $compulsoryFeesPaid->student_id == $student->user->id;
                        });

                        // If installment is not Paid
                        if (!empty($installmentPaid)) {
                            --$totalInstallments;
                            $totalFeesAmount -= $installmentPaid->amount;
                            $installment['minimum_amount'] = $installmentPaid->amount;
                            $installment['maximum_amount'] = $installmentPaid->amount;
                            $installment['due_charges_amount'] = $installmentPaid->due_charges;
                        } else {
                            // If installment is paid
                            $installment['minimum_amount'] = $totalFeesAmount / $totalInstallments;
                            $installment['maximum_amount'] = $totalFeesAmount;

                            //Calculate Due Charges amount for not paid installment
                            if ($currentDateTimestamp > $installmentDueDateTimestamp) {
                                if ($installment->due_charges_type == "percentage") {
                                    $installment['due_charges_amount'] = ($installment['minimum_amount'] * $installment['due_charges']) / 100;
                                } else if ($installment->due_charges_type == "fixed") {
                                    $installment['due_charges_amount'] = $installment->due_charges;
                                }

                            } else {
                                $installment['due_charges_amount'] = 0;
                            }
                        }
                        $installment['is_paid'] = $installmentPaid ? true : false;

                        //identify which installment is the correct installment

                        /* Current date should be less then the due date && greater than the due date of previous installments  */
                        /* In case of first installment , previous installment date will be current date - 1 */
                        if ($currentDateTimestamp <= $installmentDueDateTimestamp && $currentDateTimestamp > $previousInstallmentDate) {
                            $installment['is_current'] = true;
                        } else {
                            $installment['is_current'] = false;
                        }
                        $previousInstallmentDate = new DateTime($installment['due_date']);
                        return $installment;
                    });
                }

                // Unsetting fees_class_type at the end of the loop
                // unset($fee['fees_class_type']);
            }

            ResponseService::successResponse("Fees Fetched Successfully", $fees);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function payCompulsoryFees(Request $request) {
        $validator = Validator::make($request->all(), [
            'child_id'          => 'required',
            'fees_id'           => 'required',
            'installment_ids'   => 'nullable|array',
            'installment_ids.*' => 'required|integer',
            'advance'           => 'present|numeric',
            'payment_method'    => 'required|in:Stripe,Razorpay,Fiuu',
        ]);
        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }

        if($request->payment_method == 'Fiuu'){
            ResponseService::successResponse("", ["payment_intent" => [], "payment_transaction" => []]);
        }
        try {
            DB::beginTransaction();

            $paymentConfigurations = $this->paymentConfigurations->builder()->where(['status' => 1, 'payment_method' => $request->payment_method])->first();

            if (empty($paymentConfigurations)) {
                ResponseService::errorResponse("Payment is not Enabled", [], config('constants.RESPONSE_CODE.ENABLE_PAYMENT_GATEWAY'));
            }

            $parentId = Auth::user()->id;

            $studentData = Auth::user()->guardianRelationChild()->where('id', $request->child_id)->whereHas('user', function ($q) {
                $q->whereNull('deleted_at');
            })->first();

            if (empty($studentData)) {
                ResponseService::errorResponse("Child's Account is not Active.Contact School Support", NULL, config('constants.RESPONSE_CODE.INACTIVE_CHILD'));
            }
            $schoolId = $studentData->user->school_id;
            $compulsory_fees = $studentData->user->compulsory_fees()->whereHas('fees_paid', function ($q) use ($request) {
                $q->where('fees_id', $request->fees_id);
            })->get();

            $sessionYear = $this->cache->getDefaultSessionYear($schoolId);

            $fees = $this->fees->builder()
                ->where('id', $request->fees_id)
                ->with(['fees_class_type'              => function ($q) {
                    $q->where('optional', 0);
                }, 'fees_class_type.fees_type',
                        'installments.compulsory_fees' => function ($query) use ($studentData) {
                            $query->where(['student_id' => $studentData->user_id]);
                        },
                        'fees_paid'                    => function ($query) use ($studentData) {
                            $query->where(['student_id' => $studentData->user_id])->with('compulsory_fee', 'optional_fee');
                        }
                ])->firstOrFail();
            $fees->append(['total_compulsory_fees']);

            if (!empty($fees->fees_paid) && !empty($fees->fees_paid->is_fully_paid)) {
                ResponseService::errorResponse("Fees Already Paid", "", config('constants.RESPONSE_CODE.FEE_ALREADY_PAID'));
            }

            // If installment id is not empty then user is going to pay in installment
            $installmentDetails = [];
            if (isset($request->installment_ids) && count($request->installment_ids)) {
                $amount = 0;
                $dueChargesAmount = 0;
                $validInstallmentIDS = array_intersect($fees->installments->pluck('id')->toArray(), $request->installment_ids);

                if (empty($validInstallmentIDS)) {
                    ResponseService::errorResponse('Invalid Installment ID');
                }

                $totalInstallments = count($fees->installments);
                $remainingAmount = $fees->total_compulsory_fees;
                if (count($compulsory_fees) > 0) {
                    $validInstallmentIDS = array_diff($validInstallmentIDS, $compulsory_fees->pluck('installment_id')->toArray());
                    //if (empty($validInstallmentIDS)) {
                    //  ResponseService::errorResponse('Please Select Only Unpaid Installment');
                    //}
                    // Removing the Paid installments from total installments so that minimum amount can be calculated for the remaining installments.
                    foreach ($compulsory_fees as $paidInstallment) {
                        if (!empty($paidInstallment->installment_id)) {
                            --$totalInstallments;
                            $remainingAmount -= $paidInstallment->amount;
                        }
                    }
                }


                // Calculate amount per installment
                $installmentAmount = $remainingAmount / $totalInstallments;

                foreach ($validInstallmentIDS as $key => $installment_id) {
                    $installment = $fees->installments->first(function ($data) use ($installment_id) {
                        return $data->id == $installment_id;
                    });

                    // Calculate Due Charges amount if installment is overdue
                    if (new DateTime(date('Y-m-d')) > new DateTime($installment['due_date'])) {
                        if ($installment->due_charges_type == "percentage") {
                            $dueChargesAmount = ($installmentAmount * $installment['due_charges']) / 100;
                        } else if ($installment->due_charges_type == "fixed") {
                            $dueChargesAmount = $installment->due_charges;
                        }
                        $amount += $installmentAmount + $dueChargesAmount;
                    } else {
                        $dueChargesAmount = 0;
                        $amount += $installmentAmount;
                    }
                    // Removing installment amount from remaining amount so that we can add validation for advance amount
                    $remainingAmount -= $installmentAmount;
                    $installmentDetails[$key] = [
                        'id'               => $installment_id,
                        'amount'           => $installmentAmount,
                        'dueChargesAmount' => $dueChargesAmount,
                    ];
                }

                if ($request->advance > $remainingAmount) {
                    ResponseService::errorResponse("Advance Amount cannot be greater then : " . $remainingAmount);
                }

            } else {
                /* Full Payment */
                $dueChargesAmount = 0;
                $amount = $fees->total_compulsory_fees;

                if (new DateTime(date('Y-m-d')) > new DateTime($fees->due_date)) {
                    $dueChargesAmount = $fees->due_charges_amount;
                    $amount += $dueChargesAmount;
                }
            }

            $finalAmount = $amount + $request->advance;
            //Add Payment Data to Payment Transactions Table
            $paymentTransactionData = $this->paymentTransaction->create([
                'user_id'         => $parentId,
                'amount'          => $finalAmount,
                'payment_gateway' => $request->payment_method,
                'payment_status'  => 'Pending',
                'school_id'       => $schoolId,
                'order_id'        => null
            ]);

            $paymentIntent = PaymentService::create($request->payment_method, $schoolId)->createPaymentIntent(round($finalAmount, 2), [
                'fees_id'                => $request->fees_id,
                'student_id'             => $studentData->user_id,
                'parent_id'              => $parentId,
                'session_year_id'        => $sessionYear->id,
                'payment_transaction_id' => $paymentTransactionData->id,
                'installment'            => json_encode($installmentDetails, JSON_THROW_ON_ERROR),
                'total_amount'           => $finalAmount,
                'advance_amount'         => $request->advance,
                'dueChargesAmount'       => $dueChargesAmount,
                'school_id'              => $schoolId,
                'type'                   => 'fees',
                'fees_type'              => 'compulsory',
                'is_fully_paid'          => $amount > $fees->total_compulsory_fees
            ]);
            $this->paymentTransaction->update($paymentTransactionData->id, ['order_id' => $paymentIntent->id, 'school_id' => $schoolId]);

            $paymentTransactionData = $this->paymentTransaction->findById($paymentTransactionData->id);
            // Custom Array to Show as response
            $paymentGatewayDetails = array(
                ...$paymentIntent->toArray(),
                'payment_transaction_id' => $paymentTransactionData->id,
            );

            DB::commit();
            ResponseService::successResponse("", ["payment_intent" => $paymentGatewayDetails, "payment_transaction" => $paymentTransactionData]);
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function payOptionalFees(Request $request) {
        $validator = Validator::make($request->all(), [
            'child_id'       => 'required',
            'fees_id'        => 'required',
            'optional_id'    => 'required|array',
            'optional_id.*'  => 'required|integer',
            'payment_method' => 'required|in:Stripe,Razorpay,Fiuu',
        ]);
        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            DB::beginTransaction();
            $parentId = Auth::user()->id;
            //$studentData = $this->student->findById($request->child_id, ['id', 'user_id', 'class_section_id', 'school_id'], ['class_section']);
            $studentData = Auth::user()->guardianRelationChild()->where('id', $request->child_id)->whereHas('user', function ($q) {
                $q->whereNull('deleted_at');
            })->first();

            if (empty($studentData)) {
                ResponseService::errorResponse("Child's Account is not Active.Contact School Support", NULL, config('constants.RESPONSE_CODE.INACTIVE_CHILD'));
            }
            $classId = $studentData->class_section->class_id;
            $schoolId = $studentData->user->school_id;
            $sessionYear = $this->cache->getDefaultSessionYear($schoolId);

            $paymentConfigurations = $this->paymentConfigurations->builder()->where(['status' => 1, 'payment_method' => $request->payment_method])->first();

            if (empty($paymentConfigurations)) {
                ResponseService::errorResponse("Payment is not Enabled", [], config('constants.RESPONSE_CODE.ENABLE_PAYMENT_GATEWAY'));
            }


            // Fees Data
            $fees = $this->fees->builder()
                ->where('id', $request->fees_id)
                ->with(['fees_class_type' => function ($q) use ($request) {
                    $q->where('optional', 1)->whereIn('id', $request->optional_id);
                }, 'fees_class_type.fees_type',
                        'fees_paid'       => function ($query) use ($studentData) {
                            $query->where(['student_id' => $studentData->user_id])->with('optional_fee');
                        }
                ])->firstOrFail();

            $optional_fees = $studentData->user->optional_fees()->whereIn('fees_class_id', $request->optional_id)->whereHas('fees_paid', function ($q) use ($request) {
                $q->where('fees_id', $request->fees_id);
            })->get();

            if (count($optional_fees) > 0) {
                ResponseService::errorResponse("Please select only unpaid fees");
            }
            $amount = $fees->total_optional_fees;

            if ($amount <= 0) {
                ResponseService::errorResponse("No Optional Fees Found");
            }

            $optional_fee = [];
            foreach ($fees->fees_class_type as $row) {
                $optional_fee[] = [
                    'id'     => $row->id,
                    'amount' => $row->amount
                ];
            }
            // Add Payment Data to Payment Transactions Table
            $paymentTransactionData = $this->paymentTransaction->create([
                'user_id'         => $parentId,
                'amount'          => $amount,
                'payment_gateway' => $request->payment_method,
                'payment_status'  => 'Pending',
                'school_id'       => $schoolId,
                'order_id'        => null
            ]);

            $paymentIntent = PaymentService::create($request->payment_method, $schoolId)->createPaymentIntent($amount, [
                'fees_id'                => $request->fees_id,
                'student_id'             => $studentData->user_id,
                'parent_id'              => $parentId,
                'session_year_id'        => $sessionYear->id,
                'payment_transaction_id' => $paymentTransactionData->id,
                'total_amount'           => $amount,
                'school_id'              => $schoolId,
                'class_id'               => $classId,
                'optional_fees_id'       => json_encode($optional_fee, JSON_THROW_ON_ERROR),
                'type'                   => 'fees',
                'fees_type'              => 'optional',
            ]);
            $this->paymentTransaction->update($paymentTransactionData->id, ['order_id' => $paymentIntent->id, 'school_id' => $schoolId]);
            $paymentTransactionData = $this->paymentTransaction->findById($paymentTransactionData->id);
            // Custom Array to Show as response
            $paymentGatewayDetails = array(
                ...$paymentIntent->toArray(),
                'payment_transaction_id' => $paymentTransactionData->id,
            );

            DB::commit();
            ResponseService::successResponse("", ["payment_intent" => $paymentGatewayDetails, "payment_transaction" => $paymentTransactionData]);
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function feesPaidReceiptPDF(Request $request) {
        $validator = Validator::make($request->all(), [
            'child_id' => 'required|integer',
            'fees_id'  => 'required|integer'
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }

        try {
            //$student = $this->student->findById($request->child_id, ['*'], ['user:id,first_name,last_name']);
            $student = Auth::user()->guardianRelationChild()->where('id', $request->child_id)->whereHas('user', function ($q) {
                $q->whereNull('deleted_at');
            })->first();

            if (empty($student)) {
                ResponseService::errorResponse("Child's Account is not Active.Contact School Support", NULL, config('constants.RESPONSE_CODE.INACTIVE_CHILD'));
            }
            $feesPaid = $this->feesPaid->builder()->where(['fees_id' => $request->fees_id, 'student_id' => $student->user_id])
                ->with([
                    'fees'         => function ($q) {
                        $q->with('class:id,medium_id,name', 'class.medium', 'fees_class_type.fees_type');
                    },
                    'compulsory_fee.installment_fee:id,name',
                    'optional_fee' => function ($q) {
                        $q->with(['fees_class_type' => function ($q) {
                            $q->select('id', 'fees_type_id')->with('fees_type:id,name');
                        }]);
                    },
                ])->first();
            $systemVerticalLogo = $this->systemSetting->builder()->where('name', 'vertical_logo')->first();
            $schoolVerticalLogo = $this->schoolSetting->builder()->where('name', 'vertical_logo')->first();
            $school = $this->cache->getSchoolSettings();
            //return view('fees.fees_receipt', compact('systemVerticalLogo', 'school', 'feesPaid', 'student', 'schoolVerticalLogo'));
            $output = Pdf::loadView('fees.fees_receipt', compact('systemVerticalLogo', 'school', 'feesPaid', 'student', 'schoolVerticalLogo'))->output();

            $response = array(
                'error' => false,
                'pdf'   => base64_encode($output),
            );
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
        return response()->json($response);
    }

    public function getSchoolSettings(Request $request) {
        $validator = Validator::make($request->all(), [
            'child_id'        => 'required|numeric',
            'session_year_id' => 'nullable'
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            // $child = Auth::user()->guardianRelationChild()->where('id', $request->child_id)->whereHas('user', function ($q) {
            //     $q->whereNull('deleted_at');
            // })->first();
            $child = Auth::user()->guardianRelationChild()->where('id', $request->child_id)->first();

            if (empty($child)) {
                ResponseService::errorResponse("Child's Account is not Active.Contact School Support", NULL, config('constants.RESPONSE_CODE.INACTIVE_CHILD'));
            }

            $settings = $this->cache->getSchoolSettings(['*'], $child->user->school_id);
            $sessionYear = $this->cache->getDefaultSessionYear($child->user->school_id);
            $semester = $this->cache->getDefaultSemesterData($child->user->school_id);
            $features = FeaturesService::getFeatures($child->user->school_id);

            $specialSchoolIds = [51,57,58,59,60,61,62,63,64];
            if (in_array($child->user->school_id, $specialSchoolIds)) {
                unset($features[27]); // Unset the key 29: Booking Management
                // "features": {
                //     "1": "Student Management",
                //     "2": "Academics Management",
                //     "3": "Slider Management",
                //     "4": "Teacher Management",
                //     "5": "Session Year Management",
                //     "6": "Holiday Management",
                //     "7": "Timetable Management",
                //     "8": "Attendance Management",
                //     "9": "Exam Management",
                //     "10": "Lesson Management",
                //     "11": "Assignment Management",
                //     "12": "Announcement Management",
                //     "13": "Staff Management",
                //     "14": "Expense Management",
                //     "15": "Staff Leave Management",
                //     "16": "Fees Management",
                //     "17": "School Gallery Management",
                //     "18": "ID Card - Certificate Generation",
                //     "19": "Website Management",
                //     "20": "Student Progress",
                //     "22": "Reporting",
                //     "23": "Credit Management",
                //     "24": "Commission Management",
                //     "25": "Package Management",
                //     "26": "Reward Management",
                //     "27": "Chat System",
                //     "28": "Statement Management",
                //     "29": "Booking Management"
                // },
            }

            $paymentGateways = $this->paymentConfigurations->builder()->select(['id', 'payment_method', 'api_key', 'currency_code', 'verify_key', 'merchant_id', 'secret_key', 'app_name', 'channel'])->where('status', 1)->get();
            for($i = 0; $i < COUNT($paymentGateways); $i++){
                $paymentGateways[$i]->dev_mode = false;
                $paymentGateways[$i]->username = 'RMSxdk_2022';
                $paymentGateways[$i]->password = 'RMSpwd@2022';
                //$paymentGateways[$i]->merchant_id = 'leapseed_Dev';
                //$paymentGateways[$i]->app_name = 'leapseed_Dev';
                //$paymentGateways[$i]->app_name = 'Learnn';
                //$paymentGateways[$i]->app_name = 'schola';
                $paymentGateways[$i]->currency = 'MYR';
                $paymentGateways[$i]->country = 'MY';
                //$paymentGateways[$i]->verify_key = '6218787ae63e5fc166a138fed185bc22';
            }
            $data = [
                'school_id'       => $child->user->school_id,
                'settings'        => $settings,
                'session_year'    => $sessionYear,
                'semester'        => $semester,
                'features'        => (count($features) > 0) ? $features : (object)[],
                'payment_gateway' => $paymentGateways
            ];

            ResponseService::successResponse('Settings Fetched Successfully.', $data);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function getSliders(Request $request) {
        $validator = Validator::make($request->all(), [
            'child_id' => 'required|numeric',
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            $child = $this->student->findById($request->child_id);
            $data = $this->sliders->builder()->where('school_id', $child->user->school_id)->whereNotIn('type', [2])->orderByDesc('created_at')->get();
            ResponseService::successResponse("Sliders Fetched Successfully", $data);
        } catch (Throwable $e) {
            ResponseService::successResponse("Sliders Fetched Successfully", []);
            //ResponseService::logErrorResponse($e);
            //ResponseService::errorResponse();
        }
    }


    // add the transaction data in transaction table
//    public function completeFeeTransaction(Request $request) {
//        $validator = Validator::make($request->all(), [
//            'child_id'               => 'required',
//            'payment_transaction_id' => 'required',
//            'payment_id'             => 'required',
//            'payment_signature'      => 'required',
//        ]);
//        if ($validator->fails()) {
//            ResponseService::validationError($validator->errors()->first());
//        }
//        try {
//            DB::beginTransaction();
//            $child = $this->student->findById($request->child_id);
//            $this->paymentTransaction->update($request->payment_transaction_id, ['payment_id' => $request->payment_id, 'payment_signature' => $request->payment_signature, 'school_id' => $child->school_id]);
//            DB::commit();
//            ResponseService::successResponse("Data Updated Successfully");
//        } catch (Throwable $e) {
//            DB::rollBack();
//            ResponseService::logErrorResponse($e);
//            ResponseService::errorResponse();
//        }
//    }

    //get the fees paid list
//    public function feesPaidList(Request $request) {
//        $validator = Validator::make($request->all(), [
//            'child_id'        => 'required',
//            'session_year_id' => 'nullable'
//        ]);
//
//        if ($validator->fails()) {
//            ResponseService::validationError($validator->errors()->first());
//        }
//        try {
//            $child = $this->student->findById($request->child_id);
//            $currentSessionYear = $this->cache->getDefaultSessionYear($child->user->school_id);
//            $sessionYearId = $request->session_year_id ?? $currentSessionYear->id;
//            $fees_paid = $this->feesPaid->builder()->where(['student_id' => $child->user_id, 'session_year_id' => $sessionYearId])->with('session_year:id,name', 'class.medium')->get();
//
//            ResponseService::successResponse("", $fees_paid);
//        } catch (Throwable $e) {
//            DB::rollBack();
//            ResponseService::logErrorResponse($e);
//            ResponseService::errorResponse();
//        }
//    }


    // // Make Transaction Fail API
    // public function failPaymentTransactionStatus(Request $request){
    //     try{
    //         $update_status = PaymentTransaction::findOrFail($request->payment_transaction_id);
    //         $update_status->payment_status = 0;
    //         $update_status->save();
    //         $response = array(
    //             'error' => false,
    //             'message' => 'Data Updated Successfully',
    //             'code' => 200,
    //         );
    //     } catch (\Exception $e) {
    //         $response = array(
    //             'error' => true,
    //             'message' => trans('error_occurred'),
    //             'code' => 103,
    //         );
    //     }
    // }

    //Get Fees Details
    public function getStudentFees(Request $request) {
        $validator = Validator::make($request->all(), [
            'child_id'        => 'required',
            'session_year_id' => 'nullable|numeric'
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            $student = Auth::user()->guardianRelationChild()->where('id', $request->child_id)->whereHas('user', function ($q) {
                $q->whereNull('deleted_at');
            })->first();

            if (empty($student)) {
                ResponseService::errorResponse("Child's Account is not Active.Contact School Support", NULL, config('constants.RESPONSE_CODE.INACTIVE_CHILD'));
            }
            $classId = $student->class_section->class_id;
            $schoolId = $student->user->school_id;

            $currentSessionYear = $this->cache->getDefaultSessionYear($schoolId);
            $sessionYearId = $request->session_year_id ?? $currentSessionYear->id;

            $fees = $this->studentFees->builder()->where('class_id', $classId)
            ->with([
                'student_fees_details', 'student_fees_paid', 'session_year',
                'class.medium', 'class.stream'
            ])
            ->where([
                'session_year_id' => $sessionYearId,
                'student_id' => $student->id,
                'status' => 'published'
            ])
            ->whereDoesntHave('student_fees_details', function ($query) {
                $query->whereIn('fees_type_name', ['Overdue Fees', 'Early Discount']);
            })
            ->orderByDesc('created_at')
            ->get();

            $currentDateTimestamp = new DateTime(date('Y-m-d'));

            foreach ($fees as $fee) {
                $feesDateTimestamp = new DateTime($fee->due_date);
                // Set Compulsory Fees Data in response
                if (count($fee->student_fees_details) > 0) {
                    $fee->is_overdue = $currentDateTimestamp > $feesDateTimestamp; // true/false
                    if($fee->is_overdue){
                        if($fee->due_charges > 0){
                            $fee->due_charges_amount = $fee->total_compulsory_fees*$fee->due_charges/100;
                        }
                    }
                }

                $fee->is_fully_paid = 0;
                if(count($fee->student_fees_paid) > 0){
                    $fee->is_fully_paid = $fee->student_fees_paid[0]->is_fully_paid;
                }
            }

            $outFees = $fees->sortBy([
                ["is_fully_paid","asc"],
                ["created_at","desc"],
            ]);

            $fmtFees = [];
            foreach($outFees as $out){
                if($request->type == '0'){
                    if($out->is_fully_paid == 0){
                        $fmtFees[] = $out;
                    }
                }
                else if($request->type == '1'){
                    if($out->is_fully_paid == 1){
                        $fmtFees[] = $out;
                    }
                }
                else if($request->type == '2'){
                    
                }
                else{
                    $fmtFees[] = $out;
                }
            }

            ResponseService::successResponse("Fees Fetched Successfully", $fmtFees);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    //Get Fees Details
    public function getStudentFeesByPage(Request $request) {
        $validator = Validator::make($request->all(), [
            'child_id'        => 'required',
            'session_year_id' => 'nullable|numeric'
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        
        $student = Auth::user()->guardianRelationChild()->where('id', $request->child_id)->whereHas('user', function ($q) {
            $q->whereNull('deleted_at');
        })->first();

        if (empty($student)) {
            ResponseService::errorResponse("Child's Account is not Active.Contact School Support", NULL, config('constants.RESPONSE_CODE.INACTIVE_CHILD'));
        }
        $classId = $student->class_section->class_id;
        $schoolId = $student->user->school_id;
        $currentSessionYear = $this->cache->getDefaultSessionYear($schoolId);
        $sessionYearId = $request->session_year_id ?? $currentSessionYear->id;
        try {
            $query = null;
            if($request->status){
                if($request->status == 'unpaid'){
                    $query = DB::table('student_fees as sf')
                        ->join('classes as c', 'c.id', '=', 'sf.class_id')
                        ->leftJoin('student_fees_paids as sfp', 'sf.id', '=', 'sfp.student_fees_id')
                        ->where('sf.student_id', $student->id)
                        ->whereNull('sf.deleted_at')
                        ->where('sf.status', 'published')
                        ->whereNull('sfp.student_fees_id')
                        ->select(
                            'sf.early_date',
                            'sf.early_offer',
                            'sf.early_offer_amount',
                            'sf.id',
                            'sf.name',
                            'sf.invoice_date',
                            'sf.due_date',
                            'sf.due_charges',
                            'sf.due_charges_amount',
                            'sf.created_at',
                            'c.name as class',
                            'sfp.payment_detail',
                            'sf.uid',
                            'sf.tax_type',
                            'sf.tax_percentage'
                        )
                        ->orderBy('sf.created_at', 'DESC')
                        ->paginate(10);
                }
                else if($request->status == 'pending'){
                    $query = DB::table('student_fees as sf')
                        ->join('classes as c', 'c.id', '=', 'sf.class_id')
                        ->leftJoin('student_fees_paids as sfp', 'sf.id', '=', 'sfp.student_fees_id')
                        ->where('sf.student_id', $student->id)
                        ->whereNull('sf.deleted_at')
                        ->where('sf.status', 'published')
                        ->where('sfp.status', 2) // Ensure student_fees_paids.status = 2
                        ->select(
                            'sf.early_date',
                            'sf.early_offer',
                            'sf.early_offer_amount',
                            'sf.id',
                            'sf.name',
                            'sf.invoice_date',
                            'sf.due_date',
                            'sf.due_charges',
                            'sf.due_charges_amount',
                            'sf.created_at',
                            'c.name as class',
                            'sfp.payment_detail',
                            'sf.uid',
                            'sf.tax_type',
                            'sf.tax_percentage'
                        )
                        ->orderBy('sf.created_at', 'DESC')
                        ->paginate(10);
                }
                else if($request->status == 'paid'){
                    $query = DB::table('student_fees as sf')
                        ->join('classes as c', 'c.id', '=', 'sf.class_id')
                        ->leftJoin('student_fees_paids as sfp', 'sf.id', '=', 'sfp.student_fees_id')
                        ->where('sf.student_id', $student->id)
                        ->whereNull('sf.deleted_at')
                        ->where('sf.status', 'published')
                        ->where('sfp.status', 1) // Ensure student_fees_paids.status = 2
                        ->select(
                            'sf.early_date',
                            'sf.early_offer',
                            'sf.early_offer_amount',
                            'sf.id',
                            'sf.name',
                            'sf.invoice_date',
                            'sf.due_date',
                            'sf.due_charges',
                            'sf.due_charges_amount',
                            'sf.created_at',
                            'c.name as class',
                            'sfp.payment_detail',
                            'sf.uid',
                            'sf.tax_type',
                            'sf.tax_percentage'
                        )
                        ->orderBy('sf.created_at', 'DESC')
                        ->paginate(10);
    
                    //$query = DB::table('student_fees as sf')
                        // ->join('classes as c', 'c.id', '=', 'sf.class_id')
                        // ->leftJoin('student_fees_paids as sfp', 'sf.id', '=', 'sfp.student_fees_id')
                        // ->where('sf.student_id', $student->id)
                        // ->whereNull('sf.deleted_at')
                        // ->where('sf.status', 'published')
                        // ->where('sfp.status', 1) // Ensure student_fees_paids.status = 1
                        // ->select(
                        //     'sf.early_date',
                        //     'sf.early_offer',
                        //     'sf.early_offer_amount',
                        //     'sf.id',
                        //     'sf.name',
                        //     'sf.due_date',
                        //     'sf.due_charges',
                        //     'sf.due_charges_amount',
                        //     'sf.created_at',
                        //     'c.name as class',
                        //     'sfp.payment_detail'
                        // )
                        // ->orderBy('sf.created_at', 'DESC')
                        // ->paginate(10);
                }
                else{

                }
            }
            else{
                if($request->is_paid){
                    // $query = DB::table('student_fees AS sf')
                    //     ->join('classes AS c', 'c.id', '=', 'sf.class_id')
                    //     ->join('student_fees_paids AS sfp', 'sfp.student_fees_id', '=', 'sf.id')
                    //     ->select(
                    //         'sf.early_date',
                    //         'sf.early_offer',
                    //         'sf.early_offer_amount',
                    //         'sf.id',
                    //         'sf.name',
                    //         'sf.due_date',
                    //         'sf.due_charges',
                    //         'sf.due_charges_amount',
                    //         'sf.created_at',
                    //         'c.name AS class'
                    //     )
                    //     ->whereNull('sf.deleted_at')
                    //     //->where('sf.session_year_id', $sessionYearId)
                    //     ->where('sf.status', 'published')
                    //     ->where('sfp.status',1)
                    //     ->where('sf.student_id', $student->id)
                    //     ->orderByDesc('sf.created_at')->paginate(10);
                    $query = DB::table('student_fees as sf')
                        ->join('student_fees_paids as sfp', 'sfp.student_fees_id', '=', 'sf.id')
                        ->join('classes as c', 'c.id', '=', 'sf.class_id')
                        ->select(
                            'sf.early_date',
                            'sf.early_offer',
                            'sf.early_offer_amount',
                            'sf.id',
                            'sf.name',
                            'sf.invoice_date',
                            'sf.due_date',
                            'sf.due_charges',
                            'sf.due_charges_amount',
                            'sf.created_at',
                            'c.name AS class',
                            'sf.tax_type',
                            'sf.tax_percentage'
                        )
                        ->where('sf.student_id', $student->id)
                        ->whereNull('sf.deleted_at')
                        ->where('sf.status', 'published')
                        ->where(function($query) {
                            $query->whereNull('sfp.is_fully_paid')
                                ->orWhere('sfp.status', '=', 1);
                        })
                        ->orderBy('sf.created_at', 'DESC')
                        ->paginate(10);
                }
                else{
                    // $query = DB::table('student_fees AS sf')
                    //     ->join('classes AS c', 'c.id', '=', 'sf.class_id')
                    //     ->leftJoin('student_fees_paids AS sfp','sfp.student_fees_id','=','sf.id')
                    //     ->select(
                    //         'sf.early_date',
                    //         'sf.early_offer',
                    //         'sf.early_offer_amount',
                    //         'sf.id',
                    //         'sf.name',
                    //         'sf.due_date',
                    //         'sf.due_charges',
                    //         'sf.due_charges_amount',
                    //         'sf.created_at',
                    //         'c.name AS class'
                    //     )
                    //     ->whereNull('sf.deleted_at')
                    //     //->where('sf.session_year_id', $sessionYearId)
                    //     ->where('sf.status', 'published')
                    //     ->where('sf.student_id', $student->id)
                    //     ->whereNull('sfp.is_fully_paid')
                    //     ->orWhere('sfp.status','!=',1)
                    //     // ->whereNotIn(
                    //     //     'sf.id',
                    //     //     DB::table('student_fees_paids')->pluck('student_fees_id')
                    //     // )
                    //     ->orderByDesc('sf.created_at')->paginate(10);



                    // $query = DB::table('student_fees as sf')
                    //     ->join('student_fees_paids as sfp', 'sfp.student_fees_id', '=', 'sf.id')
                    //     ->join('classes as c', 'c.id', '=', 'sf.class_id')
                    //     ->select(
                    //         'sf.early_date',
                    //         'sf.early_offer',
                    //         'sf.early_offer_amount',
                    //         'sf.id',
                    //         'sf.name',
                    //         'sf.due_date',
                    //         'sf.due_charges',
                    //         'sf.due_charges_amount',
                    //         'sf.created_at',
                    //         'c.name AS class'
                    //     )
                    //     ->where('sf.student_id', $student->id)
                    //     ->whereNull('sf.deleted_at')
                    //     ->where('sf.status', 'published')
                    //     ->where(function($query) {
                    //         $query->whereNull('sfp.is_fully_paid')
                    //             ->orWhere('sfp.status', '!=', 1);
                    //     })
                    //     ->orderBy('sf.created_at', 'DESC')
                    //     ->paginate(10);

                    // $query = DB::table('student_fees')
                    //     ->join('classes as c', 'c.id', '=', 'student_fees.class_id')
                    //     ->leftJoin('student_fees_paids as sfp', 'student_fees.id', '=', 'sfp.student_fees_id')
                    //     ->select(
                    //         'student_fees.early_date',
                    //         'student_fees.early_offer',
                    //         'student_fees.early_offer_amount',
                    //         'student_fees.id',
                    //         'student_fees.name',
                    //         'student_fees.due_date',
                    //         'student_fees.due_charges',
                    //         'student_fees.due_charges_amount',
                    //         'student_fees.created_at',
                    //         'c.name as class'
                    //     )
                    //     ->where('student_fees.student_id', $student->id)
                    //     ->whereNull('student_fees.deleted_at')
                    //     ->where('student_fees.status', 'published')
                    //     ->whereNull('sfp.student_fees_id') // Ensure student_fees.id does not exist in student_fees_paids.student_fees_id
                    //     ->orderBy('student_fees.created_at', 'DESC')
                    //     ->paginate(10);

                    $query = DB::table('student_fees as sf')
                    ->join('classes as c', 'c.id', '=', 'sf.class_id')
                    ->leftJoin('student_fees_paids as sfp', 'sf.id', '=', 'sfp.student_fees_id')
                    ->where('sf.student_id', $student->id)
                    ->whereNull('sf.deleted_at')
                    ->where('sf.status', 'published')
                    ->where(function ($query) {
                        $query->whereNull('sfp.student_fees_id')
                            ->orWhere(function ($subQuery) {
                                $subQuery->whereNotNull('sfp.student_fees_id')
                                        ->where('sfp.status', 2);
                            });
                    })
                    ->select(
                        'sf.early_date',
                        'sf.early_offer',
                        'sf.early_offer_amount',
                        'sf.id',
                        'sf.name',
                        'sf.invoice_date',
                        'sf.due_date',
                        'sf.due_charges',
                        'sf.due_charges_amount',
                        'sf.created_at',
                        'c.name as class',
                        'sfp.payment_detail',
                        'sf.tax_type',
                        'sf.tax_percentage'
                    )
                    ->orderBy('sf.created_at', 'DESC')
                    ->paginate(10);
                
                }
            }
            
            $currentDate = date('Y-m-d');
            for($i = 0; $i < COUNT($query); $i++){
                $feesPaid = DB::select('SELECT * FROM student_fees_paids WHERE student_fees_id = ?', [$query[$i]->id]);
                $currentDateTime = new DateTime('now');
                $dueDate = new DateTime($query[$i]->due_date);
                $isDue = $dueDate < $currentDateTime;
                $query[$i]->total_due_charges = 0;
                
                $dueCharges = 0;
                
                
                $totalCompulsoryFees = 0;
               
                $queryInner = DB::select(
                    'SELECT fees_type_name AS name, fees_type_amount AS amount, quantity, discount, tax 
                    FROM student_fees_details 
                    WHERE student_fees_id = ? AND fees_type_name NOT IN (?, ?)', 
                    [$query[$i]->id, 'Overdue Fees', 'Early Discount']
                );
                
                for($j = 0; $j < COUNT($queryInner); $j++){
                    $discount = 0;
                    $tax = 0;
                    if($queryInner[$j]->discount > 0){
                        $discount = ($queryInner[$j]->amount * $queryInner[$j]->quantity) * $queryInner[$j]->discount / 100;
                    }
                    if($queryInner[$j]->tax > 0){
                        $tax = ($queryInner[$j]->amount * $queryInner[$j]->quantity) * $queryInner[$j]->tax / 100;
                    }
                    $totalCompulsoryFees += ($queryInner[$j]->amount*$queryInner[$j]->quantity) - $discount + $tax;
                    $queryInner[$j]->amount -= $discount; 
                    $queryInner[$j]->amount += $tax; 
                }

                // Extra Tax added into feesDetail to show
                if(isset($query[$i]->tax_percentage) && $query[$i]->tax_percentage > 0){
                    $taxTypeJSON = json_decode(file_get_contents('assets/JSON/eInvoice/TaxTypes.json'),true);
                    $extraTax = 0;
                    $taxType = '';
                    foreach ($taxTypeJSON as $tax) {
                        if (isset($tax['Code']) && $tax['Code'] === $query[$i]->tax_type) {
                            $taxType = $tax['Description'];
                            break;
                        }
                    }
                    $extraTax = ($totalCompulsoryFees * ($query[$i]->tax_percentage / 100));
                    $totalCompulsoryFees += $extraTax;

                    $queryInner[] = (object)[
                        'name' => $taxType,
                        'amount' => round((float) $extraTax, 2),
                        'quantity' => 1,
                    ];
                }

                $query[$i]->total_fee_amount = $totalCompulsoryFees;
                if(COUNT($feesPaid) == 0 && $isDue){
                    if (!$query[$i]->due_charges) {
                        $dueCharges += $query[$i]->due_charges_amount;
                    } else {
                        $dueCharges += ($totalCompulsoryFees * ($query[$i]->due_charges / 100));
                    }
                
                    $query[$i]->total_due_charges = $dueCharges;
                }
                else if (COUNT($feesPaid) > 0 && $isDue){
                    if (!$query[$i]->due_charges) {
                        $dueCharges += $query[$i]->due_charges_amount;
                    } else {
                        $dueCharges += ($totalCompulsoryFees * ($query[$i]->due_charges / 100));
                    }
                    $query[$i]->total_due_charges = $dueCharges;
                }

                $early_date = $query[$i]->early_date;
                $early_date = date("Y-m-d",strtotime($early_date));
                
                $earlyFee = 0;
                $isEarly = false;
                if($currentDate <= $early_date && !$feesPaid){
                    if(!$query[$i]->early_offer){
                        $earlyFee = $query[$i]->early_offer_amount;
                    }else{
                        $earlyFee = ($totalCompulsoryFees * ($query[$i]->early_offer / 100));
                    }
                    $isEarly = true;
                }

                $query[$i]->is_paid = 0;
                if(COUNT($feesPaid) > 0){
                    if($feesPaid[0]->status == 1){
                        $query[$i]->is_paid = 1;
                    }
                }
                $query[$i]->paid_date = COUNT($feesPaid) > 0 ? $feesPaid[0]->date : '';
                $query[$i]->is_due = $isDue;
                $query[$i]->early_fee = $earlyFee;
                $query[$i]->is_early = $isEarly;
                $query[$i]->total_fee_amount = $totalCompulsoryFees;
                $query[$i]->total_final_amount = $totalCompulsoryFees + $dueCharges - $earlyFee;
                $query[$i]->fee_detail = $queryInner;
                if(COUNT($feesPaid) > 0){
                    $query[$i]->total_fee_amount = $feesPaid[0]->amount;
                    $query[$i]->total_final_amount = $feesPaid[0]->amount;
                    if($feesPaid[0]->mode == 4 && !empty($feesPaid[0]->payment_detail)){
                        $query[$i]->image = asset("storage/".$feesPaid[0]->payment_detail);
                    }
                }
                $query[$i]->student_id = $student->id;

                $eInvoiceStatus = DB::table('student_fees_einvoice')->where('student_fees_id',$query[$i]->id)->select('status')->first();
                if(isset($eInvoiceStatus)){
                    $query[$i]->e_invoice_status = $eInvoiceStatus->status;
                }
            }

             ResponseService::successResponse("Fees Fetched Successfully", $query);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    //Get Fees Credit Note Details
    public function getStudentFeesCreditNoteByPage(Request $request) {
        $validator = Validator::make($request->all(), [
            'child_id'        => 'required',
            'session_year_id' => 'nullable|numeric'
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        
        $student = Auth::user()->guardianRelationChild()->where('id', $request->child_id)->whereHas('user', function ($q) {
            $q->whereNull('deleted_at');
        })->first();
        
        if (empty($student)) {
            ResponseService::errorResponse("Child's Account is not Active.Contact School Support", NULL, config('constants.RESPONSE_CODE.INACTIVE_CHILD'));
        }
        $classId = $student->class_section->class_id;
        $schoolId = $student->user->school_id;
        $currentSessionYear = $this->cache->getDefaultSessionYear($schoolId);
        $sessionYearId = $request->session_year_id ?? $currentSessionYear->id;
        try {
            $creditNotes = DB::table('credit_note AS cn')
            ->join('student_fees AS sf', 'cn.student_fee_id', '=', 'sf.id')
            ->join('classes AS c', 'c.id', '=', 'cn.class_id')
            ->select(
                'cn.id AS note_id',
                'cn.date AS note_date',
                'cn.uid AS note_uid',
                'sf.name AS fee_name',
                'sf.created_at',
                'c.name AS class',
                'cn.tax_type',
                'cn.tax_percentage',
                DB::raw("'credit' AS note_type") // To differentiate
            )
            ->whereNull('cn.deleted_at')
            ->where('cn.status', 'published')
            ->where('cn.student_id', $student->id)
            ->orderByDesc('cn.created_at')
            ->get(); 
            
            // Get debit notes
            $debitNotes = DB::table('debit_note AS dn')
                ->join('student_fees AS sf', 'dn.student_fee_id', '=', 'sf.id')
                ->join('classes AS c', 'c.id', '=', 'dn.class_id')
                ->select(
                    'dn.id AS note_id',
                    'dn.date AS note_date',
                    'dn.uid AS note_uid',
                    'sf.name AS fee_name',
                    'sf.created_at',
                    'c.name AS class',
                    'dn.tax_type',
                    'dn.tax_percentage',
                    DB::raw("'debit' AS note_type") // To differentiate
                )
                ->whereNull('dn.deleted_at')
                ->where('dn.status', 'published')
                ->where('dn.student_id', $student->id)
                ->orderByDesc('dn.created_at')
                ->get(); // Using get() instead of paginate() here for merging

            $refundNotes = DB::table('refund_note AS rn')
            ->join('student_fees AS sf', 'rn.student_fee_id', '=', 'sf.id')
            ->join('classes AS c', 'c.id', '=', 'rn.class_id')
            ->select(
                'rn.id AS note_id',
                'rn.date AS note_date',
                'rn.uid AS note_uid',
                'sf.name AS fee_name',
                'sf.created_at',
                'c.name AS class',
                'rn.tax_type',
                'rn.tax_percentage',
                DB::raw("'refund' AS note_type") // To differentiate
            )
            ->whereNull('rn.deleted_at')
            ->where('rn.status', 'published')
            ->where('rn.student_id', $student->id)
            ->orderByDesc('rn.created_at')
            ->get(); // Using get() instead of paginate() here for merging
            // Merge the credit and debit notes
            $notes = $creditNotes->merge($debitNotes)->merge($refundNotes);

                // Calculate total amounts for each note (credit or debit)
                foreach ($notes as $note) {
                    // Determine the appropriate table for fetching note details
                    switch ($note->note_type) {
                        case 'credit':
                            $queryInner = DB::select('SELECT credit_note_name AS name, credit_note_amount AS amount, quantity,tax FROM credit_note_details WHERE credit_note_id = ?', [$note->note_id]);
                            break;
                        
                        case 'debit':
                            $queryInner = DB::select('SELECT debit_note_name AS name, debit_note_amount AS amount, quantity,tax FROM debit_note_details WHERE debit_note_id = ?', [$note->note_id]);
                            break;
                        
                        case 'refund':
                            $queryInner = DB::select('SELECT refund_note_name AS name, refund_note_amount AS amount, quantity,tax FROM refund_note_details WHERE refund_note_id = ?', [$note->note_id]);
                            break;
                        
                        default:
                            $queryInner = []; // Default to empty array if the note type is unknown
                            break;
                    }
                
                    // Calculate the total amount for this note
                    $note->total_amount = 0;
                    foreach ($queryInner as $item) {
                        $note->total_amount += $item->amount * $item->quantity;
                        if($item->tax > 0){
                            $tax = ($item->amount * $item->quantity) * $item->tax / 100;
                            $note->total_amount += $tax;
                        }
                    }

                    // Extra Tax added into feesDetail to show
                    if(isset($note->tax_percentage) && $note->tax_percentage > 0){
                        $taxTypeJSON = json_decode(file_get_contents('assets/JSON/eInvoice/TaxTypes.json'),true);
                        $extraTax = 0;
                        $taxType = '';
                        foreach ($taxTypeJSON as $tax) {
                            if (isset($tax['Code']) && $tax['Code'] === $note->tax_type) {
                                $taxType = $tax['Description'];
                                break;
                            }
                        }
                        $extraTax = ($note->total_amount * ($note->tax_percentage / 100));
                        $note->total_amount += $extraTax;

                        $queryInner[] = (object)[
                            'name' => $taxType,
                            'amount' => round((float) $extraTax, 2),
                            'quantity' => 1,
                        ];
                    }
                
                    // Store the details of the note
                    $note->fee_detail = $queryInner;
                }

            // Paginate the result after merging (using collection paginate)
            $paginatedNotes = new \Illuminate\Pagination\LengthAwarePaginator(
                $notes->forPage($request->page ?? 1, 10), // Define current page and items per page
                $notes->count(), // Total count of items
                10, // Items per page
                $request->page ?? 1, // Current page
                ['path' => \Illuminate\Pagination\Paginator::resolveCurrentPath()] // Maintain the path
            );

             ResponseService::successResponse("Fees Fetched Successfully", $paginatedNotes);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }
    // public function getStudentFeesCreditNoteByPage(Request $request) {
    //     $validator = Validator::make($request->all(), [
    //         'child_id'        => 'required',
    //         'session_year_id' => 'nullable|numeric'
    //     ]);

    //     if ($validator->fails()) {
    //         ResponseService::validationError($validator->errors()->first());
    //     }
        
    //     $student = Auth::user()->guardianRelationChild()->where('id', $request->child_id)->whereHas('user', function ($q) {
    //         $q->whereNull('deleted_at');
    //     })->first();
        
    //     if (empty($student)) {
    //         ResponseService::errorResponse("Child's Account is not Active.Contact School Support", NULL, config('constants.RESPONSE_CODE.INACTIVE_CHILD'));
    //     }
    //     $classId = $student->class_section->class_id;
    //     $schoolId = $student->user->school_id;
    //     $currentSessionYear = $this->cache->getDefaultSessionYear($schoolId);
    //     $sessionYearId = $request->session_year_id ?? $currentSessionYear->id;
    //     try {
    //         $query = DB::table('credit_note AS cn')
    //             ->join('student_fees AS sf', 'cn.student_fee_id', '=', 'sf.id')
    //             ->join('classes AS c', 'c.id', '=', 'cn.class_id')
    //             ->select(
    //                 'cn.id AS cn_id',
    //                 'cn.date AS cn_date',
    //                 'cn.uid AS cn_uid',
    //                 'sf.name AS sf_name',
    //                 'sf.created_at',
    //                 'c.name AS class'
    //             )
    //             ->whereNull('cn.deleted_at')
    //             //->where('cn.session_year_id', $sessionYearId)
    //             ->where('cn.status', 'published')
    //             ->where('cn.student_id', $student->id)
    //             ->orderByDesc('cn.created_at')->paginate(10);
                

    //         for($i = 0; $i < COUNT($query); $i++){
    //             $queryInner = DB::select('SELECT credit_note_name AS name, credit_note_amount AS amount, quantity FROM credit_note_details WHERE credit_note_id = ?', [$query[$i]->cn_id]);
    //             $query[$i]->total_amount = 0;
    //             for($j = 0; $j < COUNT($queryInner); $j++){
    //                 $query[$i]->total_amount += $queryInner[$j]->amount * $queryInner[$j]->quantity;
    //             }
    //             $query[$i]->fee_detail = $queryInner;
    //         }

    //          ResponseService::successResponse("Fees Fetched Successfully", $query);
    //     } catch (Throwable $e) {
    //         ResponseService::logErrorResponse($e);
    //         ResponseService::errorResponse();
    //     }
    // }

    public function payStudentCompulsoryFees(Request $request) {
        $validator = Validator::make($request->all(), [
            'child_id'          => 'required',
            'fees_id'           => 'required',
            'payment_method'    => 'required|in:Stripe,Fiuu',
        ]);
        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }

        
        if($request->payment_method == 'Fiuu'){
             ResponseService::successResponse("", [
                 "payment_intent" => array(
                     "id"=> "pi_3PnaJIB4WN9Jq10p1zZg7ryI",
                    "object"=> "payment_intent",
                    "amount"=> 20000,
                    "amount_capturable"=> 0,
                    "amount_details"=> array(
                        "tip"=> []
                    ),
                    "amount_received"=> 0,
                    "application"=> null,
                    "application_fee_amount"=> null,
                    "automatic_payment_methods"=> null,
                    "canceled_at"=> null,
                    "cancellation_reason"=> null,
                    "capture_method"=> "automatic",
                    "charges"=> array(
                        "object"=> "list",
                        "data"=> [],
                        "has_more"=> false,
                        "total_count"=> 0,
                        "url"=> "/v1/charges?payment_intent=pi_3PnaJIB4WN9Jq10p1zZg7ryI"
                    ),
                    "client_secret"=> "pi_3PnaJIB4WN9Jq10p1zZg7ryI_secret_TcmRY8Fn3m3e3d4fSxns4bB3W",
                    "confirmation_method"=> "automatic",
                    "created"=> 1723615432,
                    "currency"=> "myr",
                    "customer"=> null,
                    "description"=> null,
                    "invoice"=> null,
                    "last_payment_error"=> null,
                    "latest_charge"=> null,
                    "livemode"=> true,
                    "metadata"=> array(
                        "dueChargesAmount"=> "0",
                        "is_fully_paid"=> "1",
                        "parent_id"=> "8",
                        "payment_transaction_id"=> "146",
                        "school_id"=> "1",
                        "session_year_id"=> "9",
                        "student_fees_id"=> "470",
                        "student_id"=> "1",
                        "total_amount"=> "200",
                        "type"=> "fees"
                    ),
                    "next_action"=> null,
                    "on_behalf_of"=> null,
                    "payment_method"=> null,
                    "payment_method_configuration_details"=> null,
                    "payment_method_options"=> array(
                        "card"=> array(
                            "installments"=> null,
                            "mandate_options"=> null,
                            "network"=> null,
                            "request_three_d_secure"=> "automatic"
                        )
                    ),
                    "payment_method_types"=> [
                        "card"
                    ],
                    "processing"=> null,
                    "receipt_email"=> null,
                    "review"=> null,
                    "setup_future_usage"=> null,
                    "shipping"=> null,
                    "source"=> null,
                    "statement_descriptor"=> null,
                    "statement_descriptor_suffix"=> null,
                    "status"=> "requires_payment_method",
                    "transfer_data"=> null,
                    "transfer_group"=> null,
                    "payment_transaction_id"=> 146
                 ),
                 "payment_transaction" => 
                     array(
                         "id"=> 146,
                        "user_id"=> 8,
                        "amount"=> 200,
                        "payment_gateway"=> "Stripe",
                        "order_id"=> "pi_3PnaJIB4WN9Jq10p1zZg7ryI",
                        "payment_id"=> null,
                        "payment_signature"=> null,
                        "payment_status"=> "pending",
                        "school_id"=> 1,
                        "created_at"=> "2024-08-14T06:03:51.000000Z",
                        "updated_at"=> "2024-08-14T06:03:52.000000Z"
                     )
             ]);   
        }



        try {
            DB::beginTransaction();

            $parentId = Auth::user()->id;

            $studentData = Auth::user()->guardianRelationChild()->where('id', $request->child_id)->whereHas('user', function ($q) {
                $q->whereNull('deleted_at');
            })->first();

            if (empty($studentData)) {
                ResponseService::errorResponse("Child's Account is not Active.Contact School Support", NULL, config('constants.RESPONSE_CODE.INACTIVE_CHILD'));
            }
            $schoolId = $studentData->user->school_id;
            $paymentConfigurations = $this->paymentConfigurations->builder()->where(['status' => 1, 'payment_method' => $request->payment_method, 'school_id' => $schoolId])->first();

            if (empty($paymentConfigurations)) {
                ResponseService::errorResponse("Payment is not Enabled", [], config('constants.RESPONSE_CODE.ENABLE_PAYMENT_GATEWAY'));
            }


            $sessionYear = $this->cache->getDefaultSessionYear($schoolId);

            $fees = $this->studentFees->builder()
                ->where('id', $request->fees_id)
                ->with(['student_fees_paid','student_fees_details' => function ($query) {
                    $query->whereNotIn('fees_type_name', ['Overdue Fees', 'Early Discount']);
                    }
                ])
                ->firstOrFail();

            if (!empty($fees->student_fees_paid) && !empty($fees->student_fees_paid->is_fully_paid)) {
                ResponseService::errorResponse("Student Fees Already Paid", "", config('constants.RESPONSE_CODE.FEE_ALREADY_PAID'));
            }


            /* Full Payment */
            $dueChargesAmount = 0;
            $amount = $fees->student_fees_details->sum("fees_type_amount");

            if (new DateTime(date('Y-m-d')) > new DateTime($fees->due_date)) {
                $dueChargesAmount = $fees->due_charges_amount;
                $amount += $dueChargesAmount;
            }


            $finalAmount = $amount;
            //Add Payment Data to Payment Transactions Table
            $paymentTransactionData = $this->paymentTransaction->create([
                'user_id'         => $parentId,
                'amount'          => $finalAmount,
                'payment_gateway' => 'Stripe',
                'payment_status'  => 'Pending',
                'school_id'       => $schoolId,
                'order_id'        => null
            ]);

            $paymentIntent = PaymentService::create($request->payment_method, $schoolId)->createPaymentIntent(round($finalAmount, 2), [
                'student_fees_id'                => $request->fees_id,
                'student_id'             => $studentData->id,
                'parent_id'              => $parentId,
                'session_year_id'        => $sessionYear->id,
                'payment_transaction_id' => $paymentTransactionData->id,
                'total_amount'           => $finalAmount,
                'dueChargesAmount'       => $dueChargesAmount,
                'school_id'              => $schoolId,
                'type'                   => 'fees',
                'is_fully_paid'          => 1
            ]);
            $this->paymentTransaction->update($paymentTransactionData->id, ['order_id' => $paymentIntent->id, 'school_id' => $schoolId]);

            $paymentTransactionData = $this->paymentTransaction->findById($paymentTransactionData->id);
            // Custom Array to Show as response
            $paymentGatewayDetails = array(
                ...$paymentIntent->toArray(),
                'payment_transaction_id' => $paymentTransactionData->id,
            );

            $data = [
                'student_id'=> $studentData->id,
                'school_id' => $schoolId,
                'type'      => 4,
                'date' => now(),
                'status'    => 0,
            ];
            DB::table('admission_notification')->insert($data);
        


            DB::commit();
            ResponseService::successResponse("", ["payment_intent" => $paymentGatewayDetails, "payment_transaction" => $paymentTransactionData]);
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function studentFeesPaidReceiptPDF(Request $request) {
        $validator = Validator::make($request->all(), [
            'child_id' => 'required|integer',
            'fees_id'  => 'required|integer'
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }

        if($request->cn_id != ''){
            try {
                // $student = Auth::user()->guardianRelationChild()->where('id', $request->child_id)->whereHas('user', function ($q) {
                //     $q->whereNull('deleted_at');
                // })->first();

                // if (empty($student)) {
                //     ResponseService::errorResponse("Child's Account is not Active.Contact School Support", NULL, config('constants.RESPONSE_CODE.INACTIVE_CHILD'));
                // }

                $credit_note = DB::table('credit_note')->where('id',$request->cn_id)->first();
                $credit_note_details = DB::table('credit_note_details')->where('credit_note_id',$request->cn_id)->get();
                $student = DB::table('students as s')
                    ->join('users as u','u.id','=','s.user_id')
                    ->join('class_sections as cs','cs.id','=','s.class_section_id')
                    ->join('classes as c','c.id','=','cs.class_id')
                    ->where('s.id',$credit_note->student_id)
                    ->select(DB::raw("CONCAT_WS(' ', u.first_name, u.last_name) AS full_name"),'s.*','c.name as class_name','u.current_address')
                    ->first();
                $parent = DB::table('users')->where('id',$student->guardian_id)->first();
                $school = $this->cache->getSchoolSettings();
                $schoolSettings = DB::select("SELECT data FROM school_settings WHERE name = 'horizontal_logo' AND school_id = ".$student->school_id);
                $schoolLogo = '';
                if(COUNT($schoolSettings)){
                    $schoolLogo = $schoolSettings[0]->data;
                }
                $output = Pdf::loadView('student-fee-types.credit_note_invoice', compact('credit_note', 'credit_note_details', 'student', 'parent', 'school', 'schoolSettings', 'schoolLogo'))->output();
                $response = array(
                    'error' => false,
                    'pdf'   => base64_encode($output),
                );
            } catch (Throwable $e) {
                ResponseService::logErrorResponse($e);
                return response()->json([
                    'error' => true,
                    'message' => $e->getMessage(),
                    'code' => 103,
                    'details' => $e->getTraceAsString()
                ]);
            }
            return response()->json($response);
        }

        try {
            //$student = $this->student->findById($request->child_id, ['*'], ['user:id,first_name,last_name']);
            $student = Auth::user()->guardianRelationChild()->where('id', $request->child_id)->whereHas('user', function ($q) {
                $q->whereNull('deleted_at');
            })->first();

            if (empty($student)) {
                ResponseService::errorResponse("Child's Account is not Active.Contact School Support", NULL, config('constants.RESPONSE_CODE.INACTIVE_CHILD'));
            }

            $studentUser = DB::select("SELECT * FROM users WHERE id = ".$student->user_id);
            $studentDetail = $studentUser[0];
            $parent = DB::select("SELECT * FROM users WHERE id = ".$student->guardian_id);
            $parentDetail = $parent[0];
            $feesPaid = $this->studentFeesPaid->builder()
            ->with([
                'student_fees',
                'student_fees.class',
                'student_fees.student_fees_details'
            ])
            ->where('student_fees_id', $request->fees_id)
            ->first();
            $studentFeesDetail = DB::select(
                "SELECT * FROM student_fees_details WHERE student_fees_id = ? AND fees_type_name NOT IN ('Overdue Fees', 'Early Discount')",
                [$request->fees_id]
            );
            $e_invoice_school = DB::table('e_invoice')->where('school_id',Auth::user()->school_id)->first();
            $e_invoice_guardian = DB::table('e_invoice_guardian')->where('guardian_id',$student->guardian_id)->first();
            $dueCharges = 0;
            $earlyoffer = 0;
            $earlyy = 0;
            $due_fees = 0;

            $totalDiscount=[];
            $totalTax=[];
            $sumDiscount = 0;
            $sumTax = 0;
            $i=0;

            foreach($studentFeesDetail as $studentFeeDetail){

                // Initialize array elements if not set
                if (!isset($totalDiscount[$i])) {
                    $totalDiscount[$i] = 0;
                }
                if (!isset($totalTax[$i])) {
                    $totalTax[$i] = 0;
                }

                // calculate discount for each item
                if($studentFeeDetail->discount > 0){
                    $itemDiscount = $studentFeeDetail->fees_type_amount * $studentFeeDetail->discount / 100;
                    $totalDiscount[$i] += $itemDiscount * $studentFeeDetail->quantity;
                    $sumDiscount += $totalDiscount[$i];
                  
                }

                // calculate tax for each item
                if($studentFeeDetail->tax > 0){
                    $itemTax = $studentFeeDetail->fees_type_amount * $studentFeeDetail->tax / 100;
                    $totalTax[$i] += $itemTax * $studentFeeDetail->quantity;
                    $sumTax += $totalTax[$i];
                    
                }

                $i++;
            }


            $studentFees = DB::select("SELECT * FROM student_fees WHERE id = ".$request->fees_id);
            if ($studentFees) {
                $studentFees[0]->total_due_charges = 0;
                $dueFee = DB::select('SELECT due_date FROM student_fees WHERE id= ? AND due_date < ?',[$studentFees[0]->id, date('Y-m-d')]);
                $totalCompulsoryFees = DB::table('student_fees_details')
                        ->where('student_fees_id', $studentFees[0]->id)
                        ->whereNotIn('fees_type_name', ['Overdue Fees', 'Early Discount'])
                        ->sum(DB::raw('ROUND((fees_type_amount - (fees_type_amount * COALESCE(discount, 0) / 100) + (fees_type_amount * COALESCE(tax, 0) / 100)) * quantity)'));
                if(!isset($feesPaid) && $dueFee){
                    if (!$studentFees[0]->due_charges) {
                        $dueCharges += $studentFees[0]->due_charges_amount;
                    } else {
                        $dueCharges += ($totalCompulsoryFees * ($studentFees[0]->due_charges / 100));
                    }
                 
                    $studentFees[0]->total_due_charges = $dueCharges;
                    $due_fees = $dueCharges;
                }
                else if (isset($feesPaid) && $dueFee){
                    if ($feesPaid->date > $dueFee[0]->due_date){
                        if (!$studentFees[0]->due_charges) {
                            $dueCharges += $studentFees[0]->due_charges_amount;
                        } else {
                            $dueCharges += ($totalCompulsoryFees * ($studentFees[0]->due_charges / 100));
                        }
                        $studentFees[0]->total_due_charges = $dueCharges;
                        $due_fees = $dueCharges;
                    }   
                }
            }
            if($studentFees) {
                $studentFees[0]->total_discount_charges = 0;
                $student_early_discount = DB::table("student_fees_details")
                ->select("fees_type_amount")
                ->where("fees_type_name", "Early Discount")
                ->where("student_fees_id", $studentFees[0]->id) 
                ->first();
                $earlyFee = DB::select('SELECT early_date FROM student_fees WHERE id= ? AND early_date >= ?',[$studentFees[0]->id, date('Y-m-d')]); 
                $totalCompulsoryFees = DB::table('student_fees_details')
                        ->where('student_fees_id', $studentFees[0]->id)
                        ->whereNotIn('fees_type_name', ['Overdue Fees', 'Early Discount'])
                        ->sum(DB::raw('ROUND((fees_type_amount - (fees_type_amount * COALESCE(discount, 0) / 100) + (fees_type_amount * COALESCE(tax, 0) / 100)) * quantity, 2)'));
                if(!isset($feesPaid) && $earlyFee){
                    if (!$studentFees[0]->early_offer) {
                        $earlyoffer -= $studentFees[0]->early_offer_amount;
                    } else {
                        $earlyoffer -= ($totalCompulsoryFees * ($studentFees[0]->early_offer / 100));
                    }
                 
                    $studentFees[0]->total_discount_charges = $earlyoffer;
                    $earlyy = $earlyoffer;
                }
                else if (isset($feesPaid) && $earlyFee){
                    if ($feesPaid->date <= $earlyFee[0]->early_date){
                        if (!$studentFees[0]->early_offer) {
                            $earlyoffer -= $studentFees[0]->early_offer_amount;
                        } else {
                            $earlyoffer -= ($totalCompulsoryFees * ($studentFees[0]->early_offer / 100));
                        }
                        $studentFees[0]->total_discount_charges = $earlyoffer;
                        $earlyy = $earlyoffer;
                    }   
                }
            }

            $school = $this->cache->getSchoolSettings();
            $schoolSettings = DB::select("SELECT data FROM school_settings WHERE name = 'horizontal_logo' AND school_id = ".$student->school_id);
            $schoolLogo = '';
            if(COUNT($schoolSettings)){
                $schoolLogo = $schoolSettings[0]->data;
            }
            
            $dueQuery = DB::select('SELECT due_date FROM student_fees WHERE id= ?',[$studentFees[0]->id]);
            $dueDate = COUNT($dueQuery) ? $dueQuery[0]->due_date : '';
            $isPaid = null;
            if($request->is_paid && $feesPaid){
                if($request->is_paid == "true"){
                    $isPaid = true;
                } else {
                    $isPaid = false;
                }
            }

            $studentFeeEinvoice = DB::table('student_fees_einvoice')->where('student_fees_id',$studentFees[0]->id)->orderByDesc('id')->first();
            if(isset($studentFeeEinvoice) && $studentFeeEinvoice->status != 0){
                $documentSummary = json_decode($studentFeeEinvoice->document_summary);
                // dd($documentSummary);
                $url = env('E_INVOICE_URL').'/'.$studentFeeEinvoice->uuid.'/share/'.$documentSummary[0]->longId;
                // dd($url);
                $studentFeeEinvoice->e_invoice_url = (new QRCode)->render($url);
            }

            //return view('student-fees.student_fees_receipt', compact('school', 'feesPaid', 'student','studentDetail', 'parentDetail', 'studentFeesDetail', 'studentFees', 'schoolLogo', 'due_fees','earlyy','dueDate','e_invoice_school','e_invoice_guardian','totalTax','totalDiscount','sumTax','sumDiscount','isPaid'));
            $output = Pdf::loadView('student-fees.student_fees_receipt', compact('school', 'feesPaid', 'student','studentDetail', 'parentDetail', 'studentFeesDetail', 'studentFees', 'schoolLogo', 'due_fees','earlyy','dueDate','e_invoice_school','e_invoice_guardian','totalTax','totalDiscount','sumTax','sumDiscount','isPaid','studentFeeEinvoice'))->output();

            $response = array(
                'error' => false,
                'pdf'   => base64_encode($output),
            );
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
        return response()->json($response);
    }

    public function getScanAttendance(Request $request) {
        $validator = Validator::make($request->all(), [
            'child_id' => 'required'
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        
        $parentId = Auth::user()->id;
        $student = Auth::user()->guardianRelationChild()->where('id', $request->child_id)->whereHas('user', function ($q) {
            $q->whereNull('deleted_at');
        })->first();

        if (empty($student)) {
            ResponseService::errorResponse("Child's Account is not Active.Contact School Support", NULL, config('constants.RESPONSE_CODE.INACTIVE_CHILD'));
        }

        try {
            $attendances = SubjectAttendance::where('user_id', '=', $student->user_id)
            ->orderByDesc('created_at')->paginate(10);
            for($i = 0; $i < COUNT($attendances); $i++){
                $attendances[$i]->remark_picture = $attendances[$i]->remark_picture != '' ? asset('storage/'.$attendances[$i]->remark_picture) : '';
                if(isset($attendances[$i]->in_temperature)){
                    $attendances[$i]->in_temperature = number_format($attendances[$i]->in_temperature, 1);
                }
                if(isset($attendances[$i]->out_temperature)){
                    $attendances[$i]->out_temperature = number_format($attendances[$i]->out_temperature, 1);
                }
            }

            $subjects = Subject::where('school_id', '=', $student->school_id)->get();

            ResponseService::successResponse("Attendance Fetched Successfully", ['attendance' => $attendances, 'subjects' => $subjects]);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function userNotifications(Request $request){
        $userId = Auth::user()->id;

        try {
            if(isset($_GET['page'])){
                $notifications = UserNotifications::where('user_id', '=', $userId)
                ->orderByDesc('created_at')->paginate(10);
                for($i = 0; $i < COUNT($notifications); $i++){
                    $notifications[$i]->image = str_replace('https://schola.one/storage/https://schola.one/storage/','', $notifications[$i]->image ?? '');
                    $notifications[$i]->createdAt = $notifications[$i]->created_at;
                }
                ResponseService::successResponse("Notifications Fetched Successfully", $notifications);
            }
            else{
                $notifications = UserNotifications::where('user_id', '=', $userId)->orderBy('created_at', 'DESC')->get();
                for($i = 0; $i < COUNT($notifications); $i++){
                    $notifications[$i]->image = str_replace('https://schola.one/storage/https://schola.one/storage/','', $notifications[$i]->image ?? '');
                    $notifications[$i]->createdAt = $notifications[$i]->created_at;
                }
                ResponseService::successResponse("Notifications Fetched Successfully", $notifications);
            }
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }
    
    public function getStudentProgress(Request $request) {
        $validator = Validator::make($request->all(), [
            'child_id' => 'required'
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        
        $parentId = Auth::user()->id;
        $student = Auth::user()->guardianRelationChild()->where('id', $request->child_id)->whereHas('user', function ($q) {
            $q->whereNull('deleted_at');
        })->first();

        if (empty($student)) {
            ResponseService::errorResponse("Child's Account is not Active.Contact School Support", NULL, config('constants.RESPONSE_CODE.INACTIVE_CHILD'));
        }

        try {
            $query = DB::table('student_progress AS sp')
                ->join('students AS s', 's.id', '=', 'sp.student_id')
                ->join('users AS u', 'u.id', '=', 's.user_id')
                ->join('class_subjects AS cs', 'cs.id', '=', 'sp.class_subject_id')
                ->join('subjects AS sub', 'sub.id', '=', 'cs.subject_id')
                ->join('users AS t', 't.id', '=', 'sp.teacher_id') 
                ->select(
                    'sp.*',
                    'u.first_name',
                    'u.last_name',
                    'sp.class_subject_id',
                    's.id AS student_id',
                    'sub.name as subject_name',
                    'sub.type',
                    'sp.files',
                    't.first_name AS teacher_first_name',
                    't.last_name AS teacher_last_name'
                )
                ->whereNull('u.deleted_at')
                ->where('s.id', $student->id)
                ->whereIn('sp.status', ['approve', 'auto-approve'])
                ->orderByDesc('created_at')->paginate(10);

            for($i = 0; $i < COUNT($query); $i++){
                $query[$i]->files = $query[$i]->files != '' ? 'https://schola.one/storage/'.$query[$i]->files: '';
            }

            $subjects = Subject::where('school_id', '=', $student->school_id)->get();

            ResponseService::successResponse("Attendance Fetched Successfully", $query);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function eInvoiceProfile(Request $request){
        $userId = Auth::user()->id;        
        if(isset($_POST['name']) && isset($_POST['tin']) && isset($_POST['ic_number'])){
            if($_POST['name'] == ''){
                ResponseService::errorResponse("Invalid username", null, null);
            }
            if($_POST['ic_number'] == ''){
                ResponseService::errorResponse("Invalid IC number", null, null);
            }
            if($_POST['tin'] == ''){
                ResponseService::errorResponse("Invalid tax identification number", null, null);
            }

            $regex = '/^[0-9]{6}[0-9]{2}[0-9]{4}$/';
            if (!preg_match($regex, $_POST['ic_number'])) {
                ResponseService::errorResponse("Invalid IC Number format. Use format: XXXXXXYYZZZZ", null, null);
            }

            $isTINIndividual = false;
            $isTINError = false;
            if(str_starts_with($_POST['tin'], 'IG')){
                $isTINIndividual = true;
            }
            // else{
            //     //2 Cooperative Societies CS
            //     if(str_starts_with($_POST['tin'], 'CS')){
            //     }
            //     //6 Non-Resident Public Entertainers FA
            //     else if(str_starts_with($_POST['tin'], 'FA')){
            //     }
            //     //7 Limited Liability Partnerships PT
            //     else if(str_starts_with($_POST['tin'], 'PT')){
            //     }
            //     //8 Trust Bodies TA
            //     else if(str_starts_with($_POST['tin'], 'TA')){
            //     }
            //     //9 Unit Trusts/ Property Trusts TC
            //     else if(str_starts_with($_POST['tin'], 'TC')){
            //     }
            //     //10 Business Trusts TN
            //     else if(str_starts_with($_POST['tin'], 'TN')){
            //     }
            //     //11 Real Estate Investment Trusts/Property Trust Funds TR
            //     else if(str_starts_with($_POST['tin'], 'TR')){
            //     }
            //     //12 Deceased Person’s Estate TP
            //     else if(str_starts_with($_POST['tin'], 'TP')){
            //     }
            //     //14 Labuan Entities LE
            //     else if(str_starts_with($_POST['tin'], 'LE')){
            //     }
            //     //1 Companies C
            //     else if(str_starts_with($_POST['tin'], 'C')){
            //     }
            //     //3 Partnerships D
            //     else if(str_starts_with($_POST['tin'], 'D')){
            //     }
            //     //4 Employers E
            //     else if(str_starts_with($_POST['tin'], 'E')){
            //     }
            //     //5 Associations F
            //     else if(str_starts_with($_POST['tin'], 'F')){
            //     }
            //     //13 Hindu Joint Families J
            //     else if(str_starts_with($_POST['tin'], 'J')){
            //     }
            //     else{
            //         $isTINError = true;
            //     }
                
            //     if(!str_ends_with($_POST['tin'], '0')){
            //         $isTINError = true;
            //     }
            // }

            if(strlen($_POST['tin']) >= 11 && strlen($_POST['tin']) <= 13){
            }
            else{
                $isTINError = true;
            }
            if($isTINError) {
                // ResponseService::errorResponse("Invalid TIN format Refer the following.\nIndividual TIN: IGXXXXXXXXX\nCompanies TIN: CXXXXXXXXX0\nCooperative Societies TIN: CSXXXXXXXXX0\nPartnerships TIN: DXXXXXXXXX0\nEmployers TIN: EXXXXXXXXX0\nAssociations TIN: FXXXXXXXXX0\nNon-Resident Public Entertainers TIN: FAXXXXXXXXX0\nLimited Liability Partnerships TIN: PTXXXXXXXXX0\nTrust Bodies TIN: TAXXXXXXXX0\nUnit Trusts/ Property Trusts TIN: TCXXXXXXXXX0\nBusiness Trusts TIN: TNXXXXXXXXX0\nReal Estate Investment Trusts/Property Trust Funds TIN: TRXXXXXXXXX0\nDeceased Person’s Estate TIN: TNXXXXXXXXX0\nHindu Joint Families TIN: JXXXXXXXXX0\nLabuan Entities  TIN: LEXXXXXXXXX0\n11 to 13 characters\nNon individual TIN ends with 0", null, null);
                
                ResponseService::errorResponse("Invalid TIN format.\nIndividual TIN: IGXXXXXXXXX\n11 to 13 characters", null, null);
            }

            if (!empty($_POST['email']) && !filter_var($_POST['email'], FILTER_VALIDATE_EMAIL)) {
                ResponseService::errorResponse("Invalid email format.", null, null);
            }

            if (!empty($_POST['contact']) && !preg_match('/^\d{8,15}$/', $_POST['contact'])) {
                ResponseService::errorResponse("Invalid contact format. Use digits only, 8 to 15 digits.", null, null);
            }

            $result = DB::SELECT('SELECT * FROM e_invoice_guardian WHERE guardian_id = ?', [$userId]);
            if(COUNT($result)){
                DB::table('e_invoice_guardian')->where(['guardian_id' => $userId])->update(
                    array('name' => $_POST['name'],
                          'ic_no' => $_POST['ic_number'],
                          'tax_identification_number' => $_POST['tin'], 
                          'email' => $_POST['email'],
                          'contact_no' => $_POST['contact'],
                          'address' => $_POST['address'],
                          'city' => $_POST['city'],
                          'postal_code' => $_POST['postal_code'],
                          'country' => $_POST['country'],
                          'state' => $_POST['state'])
                );
            }
            else{
                DB::table('e_invoice_guardian')->insert(
                    array('name' => $_POST['name'],
                          'ic_no' => $_POST['ic_number'],
                          'tax_identification_number' => $_POST['tin'],
                          'email' => $_POST['email'],
                          'contact_no' => $_POST['contact'],
                          'address' => $_POST['address'],
                          'city' => $_POST['city'],
                          'postal_code' => $_POST['postal_code'],
                          'country' => $_POST['country'],
                          'state' => $_POST['state'],
                          'guardian_id' => $userId)
                );
            }
            ResponseService::successResponse("Record updated successfully", []);
        }
        else{
           $result = DB::SELECT('SELECT * FROM e_invoice_guardian WHERE guardian_id = ?', [$userId]);
           if(COUNT($result)){
                if($result[0]->name == '' || $result[0]->tax_identification_number == '' || $result[0]->ic_no == ''){
                    ResponseService::errorResponse("Record found but missing detail ", null, null);
                }
                else{
                    ResponseService::successResponse("Record found", $result[0]);
                }
           }
           else{
                ResponseService::errorResponse("No record found", null, null);
           }
        }
    }

    public function getChildAttendanceToday(Request $request){
        $validator = Validator::make($request->all(), [
            'child_id' => 'required'
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        
        $user = DB::table('users as u')
                ->join('students as s','s.user_id','=','u.id')
                ->where('s.id',$request->child_id)
                ->select('u.*')
                ->first();

        if($user){
            $attendance = DB::table('subject_attendances')
                          ->where('date',now()->format('Y-m-d'))
                          ->where('user_id',$user->id)
                          ->orderBy('id','DESC')
                          ->first();

            $data = array();
            if($attendance){
                $data = [
                    'in_temperature' => $attendance->in_temperature,
                    'out_temperature' => $attendance->out_temperature,
                    'temperature' => $attendance->out_temperature ?? $attendance->in_temperature ?? null,
                    'clock_in' => $attendance->clock_in,
                    'clock_out' => $attendance->clock_out,
                    'in_picture' => $attendance->in_picture,
                    'out_picture' => $attendance->out_picture,
                    'status' => $attendance->status
                ];
            }

            return ResponseService::successResponse("Attendance Retrieved Successfully",$data);
        }
    }

    public function getRewardPoints(Request $request){
        $validator = Validator::make($request->all(), [
            'child_id' => 'required'
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        
        $reward = DB::table('rewards')
                  ->where('student_id',$request->child_id)
                  ->wherenull('deleted_at')
                  ->orderBy('created_at','desc')
                  ->select('reward_point_total','score_total')
                  ->first();

        ResponseService::successResponse("Attendance Retrieved Successfully",$reward);
    }

    public function getDocument(Request $request){

        $validator = Validator::make($request->all(), [
            'child_id' => 'required'
        ]);
     
        
        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }

        $document=DB::table('documents')
        ->join('document_classes', 'documents.id', '=', 'document_classes.document_id')
        ->join('files', function ($join) {
            $join->on('documents.id', '=', 'files.modal_id')
            ->where('files.modal_type', '=', 'App\Models\Document');
        })
        ->where('document_classes.student_id','=',$request->child_id)
        ->select(
            'documents.title',
            'documents.description',
            'files.type',
            'files.file_name',
            'files.file_url'
        )->get();

        foreach($document as $item){
            $item->file_url=asset("storage/".$item->file_url);  

        }
        ResponseService::successResponse("Document Retrieved Successfully",$document);



    }

    public function uploadFileFees(Request $request){
        $validator = Validator::make($request->all(), [
            'fee_id'    => 'required',
            'child_id'  => 'required'
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        $schoolId = Auth::user()->school_id;
        $res = DB::select("
            SELECT      sf.id AS student_fees_id,
                        s.id AS student_id,
                        s.school_id,
                        CONCAT(u.first_name , u.last_name) AS full_name,
                        s.guardian_id,
                        (
                           SELECT  SUM((fees_type_amount - (fees_type_amount * COALESCE(discount,0) / 100) + 
                            (fees_type_amount * COALESCE(tax,0) /100)) * quantity)
                            FROM    student_fees_details
                            WHERE   student_fees_id = sf.id
							AND     fees_type_name NOT IN ('Early Discount', 'Overdue Fees')
                        ) AS total_compulsory_fees,
                        c.name AS class_name,
                        sfp.is_fully_paid,
                        sfp.mode,
                        sfp.cheque_no,
                        sfp.date
            FROM        users u
            JOIN        students s
            ON          u.id = s.user_id
            JOIN        class_sections cs
            ON          cs.id = s.class_section_id
            JOIN        classes c
            ON          c.id = cs.class_id
            JOIN        student_fees sf
            ON          sf.student_id = s.id
            AND         sf.status = 'published'
            LEFT JOIN   student_fees_paids sfp
            ON          sfp.student_fees_id = sf.id
            WHERE       s.id = ?
            AND         sf.id = ?
            AND         sf.deleted_at IS NULL
            ORDER BY    sfp.is_fully_paid, sf.created_at desc
        ", [$request->child_id, $request->fee_id]);
        $student = $res[0];
        if ($student) {
            $currentDate = date('Y-m-d');
            $due_date = DB::select('SELECT due_date FROM student_fees WHERE id= ?', [$student->student_fees_id]);
            $due_date = $due_date[0]->due_date;
            $due_date = date("Y-m-d", strtotime($due_date));
            $early_date = DB::select('SELECT early_date FROM student_fees WHERE id= ?', [$student->student_fees_id]);
            $early_date = $early_date[0]->early_date;
            $early_date = date("Y-m-d", strtotime($early_date));
            $total_compulsory_fees = floatval(str_replace(',', '', $student->total_compulsory_fees));

            if ($student->is_fully_paid == 0 && $currentDate > $due_date) {
                $student_overdue_fees = DB::table("student_fees_details")
                ->select("fees_type_amount")
                ->where("fees_type_name", "Overdue Fees")
                ->where("student_fees_id", $student->student_fees_id) 
                ->get();
                if ($student_overdue_fees->isNotEmpty()) {
                    $total_compulsory_fees += $student_overdue_fees->first()->fees_type_amount;
                    $student->due_charges = number_format($student_overdue_fees->first()->fees_type_amount, 2);
                    $student->total_compulsory_fees = $total_compulsory_fees;   
                }
            }
            if ($student->is_fully_paid == 0 && $currentDate <= $early_date && $currentDate < $due_date) {
                $student_early_discount = DB::table("student_fees_details")
                    ->select("fees_type_amount")
                    ->where("fees_type_name", "Early Discount")
                    ->where("student_fees_id", $student->student_fees_id) 
                    ->get();
            
                if ($student_early_discount->isNotEmpty()) {
                    $total_compulsory_fees += $student_early_discount->first()->fees_type_amount;
                    $student->early_offer = number_format($student_early_discount->first()->fees_type_amount, 2);
                    $student->total_compulsory_fees = $total_compulsory_fees;
                }
            }
        }


        try {
            DB::beginTransaction();
            $fees = $this->studentFees->findById($request->fee_id, ['*'], ['student_fees_details', 'student_fees_paid']);

            $feesPaid = $this->studentFeesPaid->builder()->where([
                'student_fees_id'    => $request->fee_id,
                'school_id' => $schoolId
            ])->first();
            if (empty($feesPaid)) {
                if ($request->hasFile('file')) {
                    $file = $request->file('file');
                    $filePath = $file->store('bank_transfer', 'public');
                }
                
                $feesPaidResult = $this->studentFeesPaid->create([
                    'date'                => date('Y-m-d'),
                    'school_id'           => $schoolId,
                    'is_fully_paid'       => 1,
                    'student_fees_id'     => $request->fee_id,
                    'mode'                => 4,
                    'amount'              =>(double)number_format(str_replace(',', '', $student->total_compulsory_fees), 2, '.', ''),
                    'payment_detail'      => $filePath,
                    'due_charges'         => $dueCharges ?? null,
                    'status'              => 2
                ]);
            } else {
                if ($request->hasFile('file')) {
                    if($feesPaid->mode == '4' && !empty($feesPaid->payment_detail)){
                        Storage::disk('public')->delete($feesPaid->payment_detail);
                    } 
                    $file = $request->file('file');
                    $filePath = $file->store('bank_transfer', 'public');
                    DB::table('student_fees_paids')->where('id',$feesPaid->id)->update(['payment_detail' => $filePath]);
                }
            }

            $compulsoryFee = $student->total_compulsory_fees;
            $students = DB::table('students')
                ->join('users', 'students.user_id', '=', 'users.id')
                ->whereIn('students.id', collect($request->child_id))
                ->whereNull('users.deleted_at')
                ->get(['students.user_id', 'students.guardian_id', 'users.first_name', 'users.last_name']);

            foreach ($students as $student) {
                $notifyUser = [];
                $studentName = $student->first_name . ' ' . $student->last_name;
                $notifyUser[] = $student->user_id;

                if ($student->guardian_id) {
                    $notifyUser[] = $student->guardian_id;
                }

                $title = 'Payment Successfully Processed';
                $body = 'Dear ' . $studentName . ', your payment of ' . number_format($compulsoryFee, 2) . 
                        ' has been successfully uploaded. It is now under review and approval.';
                $type = "Payment Notification";
                send_notification($notifyUser, $title, $body, $type);

                $data = [
                    'user_id' => $student->user_id,
                    'school_id' => Auth::user()->school_id, 
                    'date' => now(), 
                    'status' => 0, 
                    'type' => 6, 
                    
                ];
                DB::table('admission_notification')->insert($data);
            }
            DB::commit();
            ResponseService::successResponse("Data Updated Successfully");
        } catch (Throwable $e) {
            DB::rollback();
            ResponseService::logErrorResponse($e, 'StudentFeesController -> compulsoryFeesPaidStore method ');
            ResponseService::errorResponse();
        }
    }

    public function getPackageDetails(Request $request)
    {
        $childId = $request->input('child_id');

        if (!$childId) {
            return response()->json(['error' => 'Child ID is required'], 400);
        }

        $query = DB::table('purchase_package as pp')
            ->join('subject_package as sp', 'pp.package_id', '=', 'sp.id')
            ->leftJoin('files as f', 'sp.thumbnail_id', '=', 'f.id')
            ->join('students as s', 's.id', '=', 'pp.student_id')
            ->join('users as u', 'u.id', '=', 's.user_id')
            ->leftJoin('package_usage as pu', function($join) {
                $join->on('pu.purchase_package_id', '=', 'pp.id')
                    ->whereRaw('pu.id = (SELECT MAX(id) FROM package_usage WHERE purchase_package_id = pp.id)');
            })
            ->where('pp.school_id', Auth::user()->school_id)
            ->where('s.id', $childId)
            ->select(
                'pp.id as purchase_package_id',
                'sp.name as package_name',
                'sp.total_sessions',
                's.id as student_id',
                DB::raw("CONCAT(u.first_name, ' ', u.last_name) AS student_name"),
                'sp.expiry_days',
                'pu.remaining_session',
                'pu.deduct',
                'pu.updated_at',
                'pp.date as purchase_date',
                'pp.status',
                'f.file_url as thumbnail_url'
            )
            ->groupBy('pp.id', 'sp.name', 'sp.total_sessions', 's.id', 'u.first_name', 'u.last_name', 'sp.expiry_days', 'pu.remaining_session', 'pu.deduct', 'pu.updated_at', 'pp.date', 'pp.status', 'f.file_url')
            ->get();

        // **Group deducted sessions where `deduct = -1`**
        $packageDetails = $query->map(function ($package) {
            $package->deducted_details = DB::table('package_usage')
                ->where('purchase_package_id', $package->purchase_package_id)
                ->select('deduct', 'updated_at')
                ->get();

            // Determine package status
            if ($package->status == 1) {
                $package->package_status = 1; //Expired
            } else {
                $expiryDate = Carbon::parse($package->purchase_date)->addDays($package->expiry_days);
                if ($expiryDate->diffInDays(Carbon::now()) <= 7) {
                    $package->package_status = 2; //Expiring soon
                } else {
                    $package->package_status = 0; //Active
                }
            }

            return $package;
        });

        if ($packageDetails->isEmpty()) {
            return response()->json(['message' => 'No package details found'], 404);
        }

        return response()->json([
            'packageDetails' => $packageDetails
        ]);
    }

    public function getSubjectPackages(Request $request)
    {
        $schoolId = Auth::user()->school_id;
        
        $packages = DB::table('subject_package as sp')
            ->leftJoin('files', 'files.id', '=', 'sp.thumbnail_id')
            ->select(
                'sp.id',
                'sp.name',
                'sp.description',
                'sp.thumbnail_id',
                'sp.price',
                'sp.expiry_days',
                'sp.total_sessions',
                DB::raw('CONCAT("' . 'https://'.$_SERVER['SERVER_NAME'] . '/storage/", files.file_url) as thumbnail_url')
            )
            ->where('sp.school_id', $schoolId)  // Filter by school ID
            ->get();

        if ($packages->isEmpty()) {
            return response()->json(['error' => true, 'message' => 'No packages found'], 404);
        }

        return response()->json(['error' => false, 'packages' => $packages], 200);
    }



    public function purchasePackage(Request $request)
    {
        try {
            $request->validate([
                'child_id' => 'required|numeric',
                'package_id' => 'required|numeric',
                'date' => 'required|string',
                'class_id' => 'nullable|numeric',
                'student_id' => 'nullable|array',
                'payment_status' => 'nullable|string',
            ]);

        $childId = $request->input('child_id');

        // Check if the child_id exists in the students table AND belongs to the user's school
        $studentExists = DB::table('students')
            ->where('id', $childId)
            ->where('school_id', Auth::user()->school_id)
            ->exists();

        if (!$studentExists) {
            return response()->json(['error' => true, 'message' => 'Invalid Child ID Passed.', 'code' => 105], 400);
        }

        $sessionYear = $this->cache->getDefaultSessionYear();
        $schoolId = Auth::user()->school_id;

        // Get package details
        $package = DB::table('subject_package')
            ->where('id', $request->package_id)
            ->first();

        if (!$package) {
            return response()->json(['error' => true, 'message' => 'Package not found'], 404);
        }

        DB::beginTransaction();
        try {
            // Get class_id from request or from student record if not provided
            $classId = $request->class_id;
            if (!$classId) {
                $classId = DB::table('students')
                    ->where('id', $request->child_id)
                    ->join('class_sections', 'students.class_section_id', '=', 'class_sections.id')
                    ->value('class_sections.class_id');
            }

            // Create student fees record
            $studentFeesData = [
                'name' => 'Package Purchase - ' . $package->name,
                'due_date' => Carbon::now()->addDays(7),
                'class_id' => $classId,
                'school_id' => $schoolId,
                'session_year_id' => $sessionYear->id,
                'student_id' => $request->child_id,
                'status' => 'published',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
                'invoice_date'=>Carbon::now(),
            ];

            $studentFeesId = DB::table('student_fees')->insertGetId($studentFeesData);

            // Generate and update UID
            $latestUID = DB::table('student_fees')
                ->where('school_id', $schoolId)
                ->whereNull('deleted_at')
                ->select('uid')
                ->orderBy('uid', 'desc')
                ->value('uid');

            $uid = $latestUID ? $latestUID + 1 : 1;

            while (DB::table('student_fees')
                ->where('uid', $uid)
                ->where('school_id', $schoolId)
                ->whereNull('deleted_at')
                ->exists()
            ) {
                $uid++;
            }

            DB::table('student_fees')
                ->where('id', $studentFeesId)
                ->update(["uid" => $uid]);

            // Add student fees details
            $studentFeesDetailsData = [
                'student_fees_id' => $studentFeesId,
                'fees_type_name' => $package->name,
                'fees_type_amount' => $package->price,
                'quantity' => 1,
                'school_id' => $schoolId,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ];

            DB::table('student_fees_details')->insert($studentFeesDetailsData);
            
            DB::commit();

            return response()->json([
                'error' => false,
                'message' => 'Package reserved successfully. Complete payment to activate.',
                'data' => [
                    'student_fees_id' => $studentFeesId
                ]
            ], 200);

        } catch (\Exception $e) {
            DB::rollback();
            \Log::error('Database error during package purchase', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'error' => true, 
                'message' => 'Database error: ' . $e->getMessage()
            ], 500);
        }

        } catch (\Exception $e) {
            \Log::error('Purchase package error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'error' => true,
                'message' => 'Failed to purchase package: ' . $e->getMessage()
            ], 500);
        }      
    }
    


    public function getCreditDetails(Request $request){

        $validator = Validator::make($request->all(), [
            'child_id' => 'required'
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }


        $studentDetails = DB::table('students as s')
        ->where('s.id',$request->child_id)
        ->first();

       
        

        $creditDetails = DB::table('credit_system as c')
        ->where('c.user_id',$studentDetails->user_id)
        ->select(
            'c.credit_amount',
            'c.balance',
            'c.detail',
            'c.created_at as date',
            DB::raw('DATE_FORMAT(c.created_at, "%h:%i %p") as time')
        )
        ->get();

        $recentCredit = DB::table('credit_system as c')
        ->where('c.user_id',$studentDetails->user_id)
        ->orderBy('c.updated_at', 'desc')
        ->select(
            'c.credit_amount',
            'c.balance',
            'c.created_at as date',
            DB::raw('DATE_FORMAT(c.created_at, "%h:%i %p") as time')
        )
        ->first();

        $data = ['creditDetails'=>$creditDetails,'recentCredit'=>$recentCredit];

    
        
        ResponseService::successResponse("Document Retrieved Successfully",$data);
    }

    public function getSubjectTeachers(Request $request){

        $validator = Validator::make($request->all(), [
            'child_id' => 'required',
            'subject_id' => 'required',
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try{
            $children = Auth::user()->guardianRelationChild()->where('id', $request->child_id)->whereHas('user', function ($q) {
                $q->whereNull('deleted_at');
            })->first();

            if (empty($children)) {
                ResponseService::errorResponse("Child's Account is not Active.Contact School Support", NULL, config('constants.RESPONSE_CODE.INACTIVE_CHILD'));
            }

            /// Fetch Teachers based on subject and child id
            $class_subject_id = $children->selectedStudentSubjects()->pluck('class_subject_id');
            $subjectTeachers = $this->subjectTeachers->builder()
            ->select('id', 'subject_id', 'teacher_id', 'school_id')
            ->whereIn('class_subject_id', $class_subject_id)
            ->where('class_section_id', $children->class_section_id)
            ->where('subject_id', $request->subject_id) 
            ->with('teacher:id,first_name,last_name,image,mobile')            
            ->get();
            ResponseService::successResponse("Teacher Details Fetched Successfully", $subjectTeachers);
        }catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function getBookingSlot(Request $request){
        $validator = Validator::make($request->all(), [
            'child_id' => 'required',
            'subject_id' => 'required',
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }

        $children = Auth::user()->guardianRelationChild()->where('id', $request->child_id)->whereHas('user', function ($q) {
            $q->whereNull('deleted_at');
        })->first();

        if (empty($children)) {
            ResponseService::errorResponse("Child's Account is not Active.Contact School Support", NULL, config('constants.RESPONSE_CODE.INACTIVE_CHILD'));
        }

        $sessionYear = $this->cache->getDefaultSessionYear($children->school_id);
        $allSlot = DB::table("booking_slot")
            ->where("teacher_id", $request['teacher_id'])
            ->where("subject_id", $request["subject_id"])
            ->where("status", "approve")
            ->get();

        /// All Student Booked Slot Calculate Capacity
        $bookedSlotCounts = DB::table('booking')
        ->whereIn('slot_id', $allSlot->pluck('id')->toArray())
        ->select('slot_id', DB::raw('COUNT(*) as count'))
        ->groupBy('slot_id')
        ->pluck('count', 'slot_id')
        ->toArray();

        $canceledSlotIds = DB::table('booking')
        ->whereIn('slot_id', $allSlot->pluck('id')->toArray())
        ->whereNotNull('deleted_at')  
        ->pluck('slot_id')
        ->toArray();

        $allSlot = $allSlot->map(function ($slot) use ($bookedSlotCounts, $canceledSlotIds) {
            // Reduce capacity by the number of bookings for this slot
            if (isset($bookedSlotCounts[$slot->id])) {
                $slot->capacity -= $bookedSlotCounts[$slot->id];
            }
        
            $slot->is_canceled = in_array($slot->id, $canceledSlotIds);
        
            return $slot;
        });

        $allSlot->each(function ($slot) {
            if ($slot->booking_category_id !== null) {
                $categoryTitle = DB::table('booking_category')
                    ->select('title')
                    ->where('id', $slot->booking_category_id)
                    ->first();
        
                $slot->title = $categoryTitle->title ?? null;
            }
        });
        
        /// Student Booked Slot
        $bookedSlot = DB::table("booking_slot")
            ->where("teacher_id", $request['teacher_id'])
            ->where("subject_id", $request["subject_id"])
            ->whereIn('id', function($query) use ($request) {
                $query->select('slot_id') // Assuming slot_id is the relevant field in the booking table
                    ->from('booking')
                    ->where('student_id',$request['child_id'])
                    ->where('teacher_id', $request['teacher_id'])
                    ->orderBy('created_at', 'desc') 
                    ->where('subject_id', $request['subject_id']);
            })
            ->get();
        $bookedSlot->each(function ($slot) {
            if ($slot->booking_category_id !== null) {
                $categoryTitle = DB::table('booking_category')
                    ->select('title')
                    ->where('id', $slot->booking_category_id)
                    ->first();
        
                $slot->title = $categoryTitle->title ?? null;
            }
        });

            
            $isCanceled = DB::table('booking')
            ->where('student_id',$request['child_id'])
            ->whereIn('slot_id', $bookedSlot->pluck('id')->toArray())  
            ->whereNotNull('deleted_at') 
            ->pluck('slot_id')
            ->toArray();

            $bookedSlot = $bookedSlot->map(function ($slot) use ($isCanceled) {
                // Set is_canceled to true if the slot_id is in the $isCanceled array
                $slot->is_canceled = in_array($slot->id, $isCanceled);
                return $slot;
            });


            ///Map The Parent Student that booked slot into allSLot
            $allSlot = $allSlot->map(function ($slot) use ($bookedSlotCounts, $canceledSlotIds,$request) {
            
                $slot->is_canceled = in_array($slot->id, $canceledSlotIds);
            
                // Fetch students in this particular slot
                $studentsInSlots = DB::table("booking as b")
                    ->join("students as s", "s.id", "=", "b.student_id")
                    ->join("users as u", "u.id", "=", "s.user_id")
                    ->join("booking_slot as bs", "bs.id", "=", "b.slot_id")
                    ->select(DB::raw("CONCAT(u.first_name, ' ', u.last_name) as fullname"), "u.image")
                     ->where("b.student_id", $request->child_id)
                    ->where("b.slot_id", $slot->id)
                    ->whereNull("b.deleted_at") 
                    ->get();
            
                // Add the students to the slot
                $slot->students = $studentsInSlots->map(function ($student) {
                    return [
                        'full_name' => $student->fullname,
                        'image' => $student->image
                    ];
                });
            
                return $slot;
            });
            
            $allSlot->each(function ($slot) {
                if ($slot->booking_category_id !== null) {
                    $categoryTitle = DB::table('booking_category')
                        ->select('title')
                        ->where('id', $slot->booking_category_id)
                        ->first();
            
                    $slot->title = $categoryTitle->title ?? null;
                }
            });

            

        $data = ['allSlot'=>$allSlot,'sessionYear'=>$sessionYear,'bookedSlot' => $bookedSlot,];
        
        ResponseService::successResponse("Booking Slot Fetched Successfully", $data);

    }

    public function bookSlot(Request $request){
        $validator = Validator::make($request->all(), [
            'slot_id'       => 'required',
            'child_id'       => 'required',
        ]);
        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }

        try{
            DB::beginTransaction();
            

            $studentData = Auth::user()->guardianRelationChild()->where('id', $request->child_id)->whereHas('user', function ($q) {
                $q->whereNull('deleted_at');
            })->first();

            if (empty($studentData)) {
                ResponseService::errorResponse("Child's Account is not Active.Contact School Support", NULL, config('constants.RESPONSE_CODE.INACTIVE_CHILD'));
            }
            $schoolId = $studentData->user->school_id;
            $slot_id = $request->slot_id;
            $student_id = $request->child_id;


            DB::table('booking')->insert([
                'slot_id'     => $slot_id,
                'student_id'    => $student_id,
                'school_id'   => $schoolId,
            ]);


            $user_id = DB::table('students')
                                    ->where("id",$student_id)
                                    ->select('user_id')
                                    ->first();

            $data = [
                'user_id' => $user_id->user_id,
                'school_id' => Auth::user()->school_id, 
                'date' => now(), 
                'status' => 0, 
                'type' => 2,
            ];
            DB::table('admission_notification')->insert($data);

            //Notification to apps
            $slot=DB::table('booking_slot')->where('id',intval($slot_id))->first();
            $guardian=DB::table('students')->join('users','users.id','=','students.user_id')->where('students.id',intval($student_id))->select(DB::raw('concat(first_name, " " ,last_name) as student_name'),'guardian_id')->first();
            $teacher=DB::table('users')->where('id',$slot->teacher_id)->select('id',DB::raw('concat(first_name, " " ,last_name) as name'))->first();
            $subject=DB::table('subjects')->where('id',$slot->subject_id)->select(DB::raw('concat(name, " - ",type) as name'))->first();
            $start_time=date('h:i a', strtotime($slot->start_time));
            $end_time=date('h:i a', strtotime($slot->end_time));
            $user=array();
            $user[]=$teacher->id;
            $title='Successful Booking';
            $type='booking notification';
            $teacherbody = $guardian->student_name . " has successfully booked a slot for " . $subject->name . " on " . $slot->date . " from " . $start_time . " to " . $end_time . ".";
            send_notification($user, $title, $teacherbody, $type);  
                    



            DB::commit();
            return ResponseService::successResponse("Slot booked successfully.");
        }catch (\Exception $e) {
            DB::rollBack();
            return ResponseService::errorResponse("An error occurred while booking the slot.", $e->getMessage(), 500);
        }
    }

    public function sendBookingReminders()
    {
        try {
            // Get tomorrow's date in Y-m-d format
            $tomorrow = date('Y-m-d', strtotime('+1 day'));
            
            // Get all approved booking slots for tomorrow that haven't had reminders sent
            $bookingSlots = DB::table('booking_slot')
                ->where('date', $tomorrow)
                ->where('status', 'approve')
                ->where('sent_reminder', 0) // Only get slots where reminders haven't been sent
                ->get();
                
            if ($bookingSlots->isEmpty()) {
                return response()->json(['message' => 'No new bookings found for tomorrow or reminders already sent']);
            }
            
            $notificationsSent = 0;
            $processedSlotIds = [];
            
            foreach ($bookingSlots as $slot) {
                // Get teacher information
                $teacher = DB::table('users')
                    ->where('id', $slot->teacher_id)
                    ->select('id', DB::raw("CONCAT(first_name, ' ', last_name) as name"))
                    ->first();
                    
                // Get subject information
                $subject = DB::table('subjects')
                    ->where('id', $slot->subject_id)
                    ->select('name')
                    ->first();
                
                if (!$teacher || !$subject) {
                    continue; // Skip if teacher or subject not found
                }
                
                // Format times for notification
                $start_time = date('h:i a', strtotime($slot->start_time));
                $end_time = date('h:i a', strtotime($slot->end_time));
                $formatted_date = date('d/m/Y', strtotime($slot->date));
                
                // Get all bookings for this slot
                $bookings = DB::table('booking')
                    ->join('students', 'students.id', '=', 'booking.student_id')
                    ->join('users', 'users.id', '=', 'students.user_id')
                    ->where('booking.slot_id', $slot->id)
                    ->whereNull('booking.deleted_at')
                    ->select(
                        'booking.id',
                        'students.guardian_id',
                        'students.user_id as student_user_id',
                        DB::raw("CONCAT(users.first_name, ' ', users.last_name) as student_name")
                    )
                    ->get();
                
                if ($bookings->isEmpty()) {
                    continue; // Skip if no bookings for this slot
                }
                
                // Send reminder to each student and guardian
                foreach ($bookings as $booking) {
                    // Notify student and guardian
                    $notifyUser = [];
                    
                    if ($booking->student_user_id) {
                        $notifyUser[] = $booking->student_user_id;
                    }
                    
                    if ($booking->guardian_id) {
                        $notifyUser[] = $booking->guardian_id;
                    }
                    
                    if (!empty($notifyUser)) {
                        $title = 'Booking Reminder';
                        $body = 'Reminder: You have a booking for ' . $subject->name . 
                               ' with ' . $teacher->name . ' tomorrow (' . $formatted_date . ') from ' . 
                               $start_time . ' to ' . $end_time . '.';
                        $type = 'booking_reminder';
                        
                        send_notification($notifyUser, $title, $body, $type);
                        $notificationsSent++;
                    }
                    
                    // Notify teacher
                    $title = 'Booking Reminder';
                    $body = 'Reminder: You have a booking for ' . $subject->name . 
                           ' with ' . $booking->student_name . ' tomorrow (' . $formatted_date . ') from ' . 
                           $start_time . ' to ' . $end_time . '.';
                    $type = 'booking_reminder';
                    
                    send_notification([$teacher->id], $title, $body, $type);
                    $notificationsSent++;
                }
                
                // Add this slot to the list of processed slots
                $processedSlotIds[] = $slot->id;
            }
            
            // Mark all processed slots as having had reminders sent
            if (!empty($processedSlotIds)) {
                DB::table('booking_slot')
                    ->whereIn('id', $processedSlotIds)
                    ->update(['sent_reminder' => 1]);
            }
            
            return response()->json([
                'message' => 'Booking reminders sent successfully',
                'notifications_sent' => $notificationsSent
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    public function ranking(Request $request){
        $latestRewards = DB::table('rewards')
            ->select('student_id', DB::raw('MAX(created_at) as latest_created_at'))
            ->groupBy('student_id');

        $ranking = DB::table('rewards')
            ->joinSub($latestRewards, 'latest_rewards', function ($join) {
                $join->on('rewards.student_id', '=', 'latest_rewards.student_id')
                     ->on('rewards.created_at', '=', 'latest_rewards.latest_created_at');
            })
            ->join('students', 'students.id', '=', 'rewards.student_id')
            ->join('users', 'users.id', '=', 'students.user_id')
            ->where('students.school_id', Auth::user()->school_id)
            ->whereNull('rewards.deleted_at')
            ->select(
                'rewards.student_id',
                DB::raw('concat(users.first_name," ",users.last_name) as name'),
                'users.image as url',
                'rewards.reward_point_total',
                'rewards.score_total'
            )
            ->orderBy('rewards.score_total', 'DESC')
            ->get();

        foreach ($ranking as $item) {
            $item->url = asset("storage/" . $item->url);
        }

        ResponseService::successResponse("ranking Retrieved Successfully", $ranking);
    }

    public function getRewardHistory(Request $request){
        $history = DB::table('rewards')
        ->leftJoin('rewards_category','rewards_category.id','=','rewards.category_id')
        ->where('rewards.student_id', $request->child_id)
        ->whereNull('rewards.deleted_at')
        ->select(
            DB::raw('(SELECT r.score_total 
                    FROM rewards as r 
                    WHERE r.student_id = rewards.student_id 
                    AND r.created_at = (SELECT MAX(created_at) 
                                        FROM rewards 
                                        WHERE student_id = r.student_id)
                    order by r.id DESC 
                    LIMIT 1) as score_total'),
            DB::raw('(SELECT r.reward_point_total 
                    FROM rewards as r 
                    WHERE r.student_id = rewards.student_id 
                    AND r.created_at = (SELECT MAX(created_at) 
                                        FROM rewards 
                                        WHERE student_id = r.student_id)
                    order by r.id DESC
                    LIMIT 1) as reward_point_total'),
            'rewards.score_amount',
            'rewards.reward_point_amount',
            DB::raw('CASE 
                WHEN rewards.remark IS NOT NULL THEN rewards.remark
                WHEN rewards_category.category_name IS NOT NULL THEN rewards_category.category_name
                ELSE "N/A"
            END as category_name')
        )
        ->orderBy('rewards.created_at', 'DESC')
        ->get();
        
        ResponseService::successResponse("Reward History Retrieved Successfully",$history);
    }
}
