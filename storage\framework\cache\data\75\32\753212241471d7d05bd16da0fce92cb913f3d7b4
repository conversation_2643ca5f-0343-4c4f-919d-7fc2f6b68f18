1749173697O:22:"App\Models\SessionYear":31:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"session_years";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:51;s:4:"name";s:4:"2025";s:7:"default";i:1;s:10:"start_date";s:10:"2025-01-01";s:8:"end_date";s:10:"2025-12-31";s:9:"school_id";i:1;s:10:"created_at";s:19:"2024-11-08 14:44:39";s:10:"updated_at";s:19:"2025-03-14 14:44:44";s:10:"deleted_at";N;}s:11:" * original";a:9:{s:2:"id";i:51;s:4:"name";s:4:"2025";s:7:"default";i:1;s:10:"start_date";s:10:"2025-01-01";s:8:"end_date";s:10:"2025-12-31";s:9:"school_id";i:1;s:10:"created_at";s:19:"2024-11-08 14:44:39";s:10:"updated_at";s:19:"2025-03-14 14:44:44";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:9:{i:0;s:4:"name";i:1;s:10:"start_date";i:2;s:8:"end_date";i:3;s:9:"school_id";i:4;s:7:"default";i:5;s:9:"school_id";i:6;s:24:"include_fee_installments";i:7;s:12:"fee_due_date";i:8;s:15:"fee_due_charges";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * forceDeleting";b:0;}