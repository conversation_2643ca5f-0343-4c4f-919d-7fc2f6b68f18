<?php

namespace App\Http\Controllers;

use App\Repositories\Expense\ExpenseInterface;
use App\Repositories\ExpenseCategory\ExpenseCategoryInterface;
use App\Repositories\SessionYear\SessionYearInterface;
use Illuminate\Support\Facades\DB;
use App\Models\StudentFeesDetail;
use App\Models\StudentFeesPaid;
use App\Models\Students;
use App\Services\ResponseService;
use App\Repositories\Medium\MediumInterface;
use App\Repositories\SchoolSetting\SchoolSettingInterface;
use App\Repositories\FeesType\FeesTypeInterface;
use App\Repositories\ClassSchool\ClassSchoolInterface;
use App\Repositories\User\UserInterface;
use App\Repositories\CompulsoryFee\CompulsoryFeeInterface;
use App\Services\CachingService;
use App\Repositories\PaymentConfiguration\PaymentConfigurationInterface;
use App\Repositories\PaymentTransaction\PaymentTransactionInterface;
use App\Repositories\Student\StudentInterface;
use App\Repositories\StudentFees\StudentFeesInterface;
use App\Repositories\StudentFeesDetail\StudentFeesDetailInterface;
use App\Repositories\StudentFeesPaid\StudentFeesPaidInterface;
use App\Repositories\StudentFeeType\StudentFeeTypeInterface;
use App\Repositories\SystemSetting\SystemSettingInterface;
use App\Services\BootstrapTableService;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Throwable;

use Illuminate\Support\Facades\Log;




class FinancialReportController extends Controller {

    private ExpenseInterface $expense;
    private ExpenseCategoryInterface $expenseCategory;
    private SessionYearInterface $sessionYear;
    private CachingService $cache;

    public function __construct(ExpenseInterface $expense, ExpenseCategoryInterface $expenseCategory, SessionYearInterface $sessionYear, CachingService $cache,StudentFeesPaidInterface $studentFeesPaid) {
        $this->expense = $expense;
        $this->expenseCategory = $expenseCategory;
        $this->sessionYear = $sessionYear;
        $this->cache = $cache;
        $this->studentFeesPaid = $studentFeesPaid;

    }

    public function index() {
        ResponseService::noFeatureThenRedirect('Reporting');
        ResponseService::noAnyPermissionThenRedirect(['expense-create', 'expense-list']);

        $expenseCategory = $this->expenseCategory->builder()->pluck('name', 'id')->toArray();
        $sessionYear = $this->sessionYear->builder()->pluck('name', 'id');
        $current_session_year = app(CachingService::class)->getDefaultSessionYear();

        $months = sessionYearWiseMonth();

        return view('expense.financial_report', compact('expenseCategory', 'sessionYear', 'current_session_year','months'));
    }

    public function show($id) {
        ResponseService::noFeatureThenRedirect('Reporting');
        ResponseService::noPermissionThenRedirect('expense-list');
        $offset = request('offset', 0);
        $limit = request('limit', 10);
        $sort = request('sort', 'date');
        $order = request('order', 'DESC');

        $startDate = request('start_date');
        $endDate = request('end_date');

        $requestSessionYearId = (int)request('session_year_id');
        $session_year_id = $requestSessionYearId ?? $this->cache->getDefaultSessionYear()->id;

        // filter session year id
        $searchParm = [$requestSessionYearId];
        $searchFilter = " WHERE sf.session_year_id = ?";

        $expenseFilter= " WHERE session_year_id = ?";

        //filter date ....
        if($startDate && !$endDate){
            $searchFilter .= empty($searchFilter)?" WHERE ":" AND ";
            $searchFilter .= " sfp.date >= ?";

            $expenseFilter .= empty($expenseFilter)?" WHERE ":" AND ";
            $expenseFilter .= " date >= ?";

            $searchParm[] = date('Y-m-d', strtotime($startDate));

        }else if(!$startDate && $endDate){
            $searchFilter .= empty($searchFilter)?" WHERE ":" AND ";
            $searchFilter .= " sfp.date <= ? ";

            $expenseFilter .= empty($expenseFilter)?" WHERE ":" AND ";
            $expenseFilter .= " date <= ? ";

            $searchParm[] = date('Y-m-d', strtotime($endDate));

        }else if($startDate && $endDate){
            $searchFilter .= empty($searchFilter)?" WHERE ":" AND ";
            $searchFilter .= " (sfp.date BETWEEN ? AND ?) ";

            $expenseFilter .= empty($expenseFilter)?" WHERE ":" AND ";
            $expenseFilter .= " (date BETWEEN ? AND ?) ";

            $searchParm[] = date('Y-m-d', strtotime($startDate)) . " 00:00:00";
            $searchParm[] = date('Y-m-d', strtotime($endDate))  . " 23:59:59";
        }

        $total_fees_sql = DB::SELECT("
            SELECT      SUM(sfp.amount) AS total
            FROM        users u
            JOIN        students s
            ON          u.id = s.user_id
            JOIN        class_sections cs
            ON          cs.id = s.class_section_id
            JOIN        classes c
            ON          c.id = cs.class_id
            JOIN        student_fees sf
            ON          sf.student_id = s.id
            AND         sf.status = 'published'
            LEFT JOIN   student_fees_paids sfp
            ON          sfp.student_fees_id = sf.id
            {$searchFilter}
        ",$searchParm);

        $total_fees = $total_fees_sql[0]->total ?? 0;

        $total_expenses_sql = DB::SELECT("
            SELECT      SUM(e.amount) AS total
            FROM        expenses e
            {$expenseFilter}
            AND       category_id != 3
            AND       category_id IS NOT NULL
        ",$searchParm);

        $total_expenses = $total_expenses_sql[0]->total ?? 0;

        $total_salary_sql = DB::SELECT("
            SELECT      SUM(e.amount) AS total
            FROM        expenses e
            {$expenseFilter}
            AND       category_id IS NULL
        ",$searchParm);

        $total_salary = $total_salary_sql[0]->total ?? 0;

        $total_credit_note_sql = DB::SELECT("
            SELECT SUM(cnd.credit_note_amount * cnd.quantity) AS total
            FROM credit_note_details cnd
            JOIN credit_note cn
            ON cn.id = cnd.credit_note_id
            {$expenseFilter}
            AND cn.status = 'published'
        ",$searchParm);
        $total_credit_note = $total_credit_note_sql[0]->total ?? 0;

        $total_debit_note_sql = DB::SELECT("
            SELECT SUM(dnd.debit_note_amount * dnd.quantity) AS total
            FROM debit_note_details dnd
            JOIN debit_note dn
            ON dn.id = dnd.debit_note_id
            {$expenseFilter}
            AND dn.status = 'published'
        ",$searchParm);
        $total_debit_note = $total_debit_note_sql[0]->total ?? 0;

        $total_refund_note_sql = DB::SELECT("
            SELECT SUM(rnd.refund_note_amount * rnd.quantity) AS total
            FROM refund_note_details rnd
            JOIN refund_note rn
            ON rn.id = rnd.refund_note_id
            {$expenseFilter}
            AND rn.status = 'published'
        ",$searchParm);
        $total_refund_note = $total_refund_note_sql[0]->total ?? 0;

        $grand_total = $total_fees - $total_salary - $total_expenses - $total_credit_note +  $total_debit_note - $total_refund_note ;

        $bulkData = array();
        $rows = array();

        $rows = [
            ['description' => 'Fees Collection (+)', 'amount' => number_format($total_fees, 2)],
            ['description' => 'Expenses (-)', 'amount' => number_format($total_expenses, 2)],
            ['description' => 'Salary (-)', 'amount' => number_format($total_salary, 2)],
            ['description' => 'Credit Note (-)', 'amount' => number_format($total_credit_note, 2)],
            ['description' => 'Debit Note (+)', 'amount' => number_format($total_debit_note, 2)],
            ['description' => 'Refund Note (-)', 'amount' => number_format($total_refund_note, 2)],
            ['description' => 'Grand Total (RM)', 'amount' => number_format($grand_total, 2)],
        ];        

        //$rows[]=$totals;
        $bulkData['rows'] = $rows;

        return response()->json($bulkData);



    }

}
