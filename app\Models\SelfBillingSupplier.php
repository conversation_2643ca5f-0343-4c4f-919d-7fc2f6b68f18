<?php

namespace App\Models;

use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletes;

class SelfBillingSupplier extends Model
{
    use SoftDeletes;
    protected $table = 'self_billing_supplier';
    
    protected $fillable = [
        'name',
        'id_type',
        'id_type_no',
        'tin_number',
        'msic_code',
        'sst_registration_num',
        'tourism_tax_num',
        'email',
        'phone_number',
        'addressLine1',
        'addressLine2',
        'addressLine3',
        'postcode',
        'city',
        'country',
        'state',
        'school_id', 
    ];

    protected static function booted() {
        static::addGlobalScope('school', static function (Builder $builder) {
            if (Auth::check()) {
                if (!empty(Auth::user()->school_id) || Auth::user()->hasRole('School Admin')) {
                    $builder->where('school_id', Auth::user()->school_id);
                }
            }
        });

        // Add creating event
        static::creating(function ($model) {
            if (Auth::check() && Auth::user()->school_id) {
                $model->school_id = Auth::user()->school_id;
            }
        });
    }
}