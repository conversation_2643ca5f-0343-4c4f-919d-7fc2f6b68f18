@php
    $totalUnitPrice = 0;
    $totalItemDiscount = 0;
    $totalItemCharge = 0;
    $totalExcldTax = 0;
    $paymentMethods = [
        '01' => 'Cash',
        '02' => 'Cheque',
        '03' => 'Bank Transfer',
        '04' => 'Credit Card',
        '05' => 'Debit Card',
        '06' => 'E-Wallet',
        '07' => 'Digital Bank',
        '08' => 'Others',
    ];
    $billingInfo = $selfBilling->billingInfo;
    $supplier = $selfBilling->supplier;
@endphp
<html>
    <head>
        <meta charset="utf-8">
        <title>{{ $selfBilling->invoice_type }}</title>
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400&display=swap" rel="stylesheet">
    </head>

    <body>
        <style>
            @font-face {
                font-family: "WenQuanYi Micro Hei";
                font-weight: normal;
                font-style: normal;
                src: url('{{ storage_path('fonts/WenQuanYiMicroHeiRegular.ttf') }}') format('truetype');
            }

            body {
                font-family: 'WenQuanYi Micro Hei', system-ui, sans-serif;
                line-height: 1.0;
            }
            
            /* reset */
            * {
                border: 0;
                box-sizing: content-box;
                color: inherit;
                font-family: inherit;
                font-size: inherit;
                font-style: inherit;
                font-weight: inherit;
                line-height: inherit;
                list-style: none;
                margin: 0;
                padding: 0;
                text-decoration: none;
                vertical-align: top;
            }

            /* content editable */
            *[contenteditable] {
                border-radius: 0.25em;
                min-width: 1em;
                outline: 0;
            }

            *[contenteditable] {
                cursor: pointer;
            }

            *[contenteditable]:hover,
            *[contenteditable]:focus,
            td:hover *[contenteditable],
            td:focus *[contenteditable],
            img.hover {
                background: #DEF;
                box-shadow: 0 0 1em 0.5em #DEF;
            }

            span[contenteditable] {
                display: inline-block;
            }

            /* heading */
            h1 {
                font: bold 100% sans-serif;
                letter-spacing: 0.5em;
                text-align: center;
                text-transform: uppercase;
            }

            /* table */
            table {
                font-size: 75%;
                table-layout: fixed;
                width: 100%;
            }

            table {
                border-collapse: separate;
                border-spacing: 2px;
            }

            th,
            td {
                border-width: 1px;
                padding: 0.5em;
                position: relative;
                text-align: left;
            }

            th,
            td {
                border-radius: 0.25em;
                border-style: solid;
            }

            th {
                background: #EEE;
                border-color: #BBB;
            }

            td {
                border-color: #DDD;
            }

            /* page */
            html {
                font: 16px/1 'Open Sans', sans-serif;
                overflow: auto;
                padding: 0.5in;
            }

            html {
                background: #999;
                cursor: default;
            }

            body {
                box-sizing: border-box;
                margin: 40px;
            }

            body {
                background: #FFF;
                border-radius: 1px;
                box-shadow: 0 0 1in -0.25in rgba(0, 0, 0, 0.5);
            }

            /* header */
            header {
                margin: 0 0 3em;
            }

            header:after {
                clear: both;
                content: "";
                display: table;
            }

            header h1 {
                background: #C0C0C0;
                border-radius: 0.25em;
                color: #FFF;
                margin: 0 0 1em;
                padding: 0.5em 0;
            }

            header address {
                float: left;
                width: 50%;
                font-size: 70%;
                font-style: normal;
                /* font-weight: bold; */
                line-height: 1.25;
                margin: 0 1em 1em 0;
            }

            header address p {
                margin: 0 0 0.25em;
            }

            header address p strong {
                font-weight: bold;
            }

            header rightsection {
                float: right;
                width: 220px;
                text-align: right;
                font-size: 75%;
                font-style: normal;
                line-height: 1.25;
            }

            header span,
            header img {
                display: block;
                float: right;
            }

            header span {
                margin: 0 0 1em 1em;
                max-height: 25%;
                max-width: 60%;
                position: relative;
            }

            header img {
                max-height: 100%;
                max-width: 100%;
            }

            header input {
                cursor: pointer;
                -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
                height: 100%;
                left: 0;
                opacity: 0;
                position: absolute;
                top: 0;
                width: 100%;
            }

            /* article */
            article,
            article address,
            table.meta,
            table.inventory {
                margin: 0 0 3em;
            }

            table.inventory td:empty {
                background-color: white;
                border: none;
            }

            table.inventory td.amount {
                text-align: right;
            }

            article:after {
                clear: both;
                content: "";
                display: table;
            }

            article h1 {
                clip: rect(0 0 0 0);
                position: absolute;
            }

            article address {
                float: left;
                font-size: 75%;
                /* font-weight: bold; */
                line-height: 20px;
            }

            h2 {
                font-size: 1em;
                font-weight: bold;
                margin-bottom: 10px;
                color: #333;
                text-align: right;
            }

            /* table meta & balance */
            table.meta,
            table.balance {
                float: right;
                width: 51%;
            }

            table.meta:after,
            table.balance:after {
                clear: both;
                content: "";
                display: table;
            }

            /* table meta */
            table.meta th {
                width: 40%;
            }

            table.meta td {
                width: 60%;
            }

            /* table items */
            table.inventory {
                clear: both;
                width: 100%;
            }

            table.inventory th {
                font-weight: bold;
                text-align: center;
            }

            /* table balance */
            table.balance th,
            table.balance td {
                width: 50%;
            }

            table.balance td {
                text-align: right;
            }

            /* aside */
            aside h2 {
                border: none;
                border-width: 0 0 1px;
                margin: 0 0 1em;
            }

            aside h2 {
                border-color: #999;
                border-bottom-style: solid;
            }

            /* javascript */
            .add,
            .cut {
                border-width: 1px;
                display: block;
                font-size: .8rem;
                padding: 0.25em 0.5em;
                float: left;
                text-align: center;
                width: 0.6em;
            }

            .add,
            .cut {
                background: #9AF;
                box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
                background-image: -moz-linear-gradient(#00ADEE 5%, #0078A5 100%);
                background-image: -webkit-linear-gradient(#00ADEE 5%, #0078A5 100%);
                border-radius: 0.5em;
                border-color: #0076A3;
                color: #FFF;
                cursor: pointer;
                font-weight: bold;
                text-shadow: 0 -1px 2px rgba(0, 0, 0, 0.333);
            }

            .add {
                margin: -2.5em 0 0;
            }

            .add:hover {
                background: #00ADEE;
            }

            .cut {
                opacity: 0;
                position: absolute;
                top: 0;
                left: -1.5em;
            }

            .cut {
                -webkit-transition: opacity 100ms ease-in;
            }

            tr:hover .cut {
                opacity: 1;
            }

            @media print {
                * {
                    -webkit-print-color-adjust: exact;
                }

                html {
                    background: none;
                    padding: 0;
                }

                body {
                    box-shadow: none;
                    margin: 0;
                }

                span:empty {
                    display: none;
                }

                .add,
                .cut {
                    display: none;
                }
            }

            @page {
                margin: 0;
            }

            .footer {
                display: block;
                padding: 10px;
                font-size: 72%;
            }

            .footer-text {
                height: auto;
            }

            .footer-image img {
                max-width: 90px;
                height: auto;
                /* float: right; */
            }

            .footer-table tr td {
                border:none;
            }

            .footer-table p {
                margin: 4px 0;
                line-height: 1.2;
            }

            p {
                line-height: 1.0;
            }
        </style>
        <header>
            <h1 style="text-transform: uppercase;">{{ $selfBilling->invoice_type }}</h1>
            <address contenteditable>
                <p><strong>{{ $school['school_name'] ?? '' }}</strong></p>
                <p style="font-size:90% !important;"><strong>Main Address:</strong><span style="display:inline-block;float:none; vertical-align:middle; margin: 2px 0 0 4px;">{{ $school['school_address'] ?? '-' }}</span></p>
                <p style="font-size:90% !important"><strong>Branch Address:</strong> <?php echo nl2br(e($school['branch_address'] ?? '-')); ?></p>
            </address>
            <rightsection>
                <i><img style="height: auto;width:220px;" src="{{ asset('/storage/' . $schoolLogo) }}"
                        alt="logo"></i><br>
            </rightsection>
        </header>
        <article style="margin-bottom:1rem !important;">
       

        <address contenteditable>
        </br>
        
			<p>Supplier TIN: {{$supplier->tin_number ?? 'N/A'}}</p>
			<p>Supplier BRN/NRIC Number: {{$supplier->id_type_no ?? 'N/A'}}</p>
			<p>Supplier SST ID: {{$supplier->sst_registration_num ?? 'N/A'}}</p>
			<p>Supplier MSIC code: {{$supplier->msic_code ?? 'N/A'}}</p>
			<p>Buyer TIN: {{$schoolEInvoice->tax_identification_number ?? 'N/A'}}</p>
			<p>Buyer Name: {{$schoolSettings['school_name'] ?? 'N/A'}}</p>
			<p>Buyer IC/BRN: {{isset($schoolSettings['client_id_number']) ? $schoolSettings['client_id_number'] : 'N/A'}}</p>
		</address>
		

            <h2>E-INVOICE</h2>
            <table class="meta">
                <tr>
                    <th><span contenteditable>{{ 'Unique Identifier #' }}</span></th>
                <td><span contenteditable>{{$selfBillingEInvoice->uuid ?? '-'}}</span></td>
                </tr>
                <tr>
                    <th><span contenteditable>{{ $selfBilling->invoice_type }} #</span></th>
                    <td><span
                            contenteditable>{{ $selfBilling->invoice_no ?? '-' }}</span>
                    </td>
                </tr>
                @if(isset($selfBilling->reference_invoice_no) && !empty($selfBilling->reference_invoice_no))
                <tr>
                    <th><span contenteditable>Invoice Reference #</span></th>
                    <td><span
                            contenteditable>{{ $selfBilling->reference_invoice_no ?? '-' }}</span>
                    </td>
                </tr>
                @endif
                <tr>
                    <th><span contenteditable>Issue Date</span></th>
                    <td><span contenteditable>{{ $selfBilling->issue_date_time ? \Carbon\Carbon::parse($selfBilling->issue_date_time)->format('d M Y H:i:s') : '-' }}</span></td>
                </tr>
                <th><span contenteditable>Payment Mode</span></th>
                <td><span contenteditable>{{ isset($billingInfo->payment_mode) ? $paymentMethods[$billingInfo->payment_mode] : ($billingInfo->payment_mode ?? '-') }}</span></td>
                </tr>
                <tr>
                    <th><span contenteditable>Total Payable</span></th>
                    <td><span id="prefix"
                            contenteditable>{{ $school['currency_symbol'] ?? '' }}</span><span>{{ number_format($selfBilling->total_payable_amount,2) }}</span>
                    </td>
                </tr>
            </table>
            <table class="inventory">
                <thead>
                    <tr>
                        <th style="width:7%;text-align: left;"><span contenteditable>Code</span></th>
                        <th style="width:37%;text-align: left;"><span contenteditable>Description</span></th>
                        <th style="width:7%;text-align: left;"><span contenteditable>UOM</span></th>
                        <th style="width:7%;text-align: left;"><span contenteditable>Qty</span></th>
                        <th style="width:9%;text-align: left;"><span contenteditable>Unit Price</span></th>
                        <th style="width:9%;text-align: left;"><span contenteditable>Amount</span></th>
                        <th style="width:8%;text-align: left;"><span contenteditable>Disc</span></th>
                        <th style="width:10%;text-align: left;"><span contenteditable>Charges</span></th>
                        <th style="width:15%;text-align: left;"><span contenteditable>Total Amount</span></th>
                    </tr>
                </thead>
                <tbody>
                    @for ($i = 0; $i < COUNT($selfBilling->details); $i++)
                        @php
                            $self_billing_details = $selfBilling->details;
                            // Split the classification_code by ' - ' and get the first part
                            $classificationCodeParts = explode(' - ', $self_billing_details[$i]->classification_code);
                            $classificationCodeOnly = $classificationCodeParts[0];
                            
                            // Split the unit by ' - ' and get the first part
                            $measurementParts = explode(' - ', $self_billing_details[$i]->measurement);
                            $measurementOnly = $measurementParts[0];

                            $totalUnitPrice += ($self_billing_details[$i]->unit_price ?? 0) * ($self_billing_details[$i]->quantity ?? 0);                            
                            $totalItemDiscount += $self_billing_details[$i]->total_discount_amount;
                            $totalItemCharge += $self_billing_details[$i]->total_charge_rate;
                            $totalExcldTax += $self_billing_details[$i]->total_excluding_tax;
                        @endphp
                        <tr>
                            <td>
                                <span
                                    contenteditable>{{ !empty($classificationCodeOnly) ? $classificationCodeOnly : '-' }}</span>
                            </td>
                            <td>
                                <span contenteditable>{{ $self_billing_details[$i]->description ?? '-' }}</span>
                                @if($self_billing_details[$i]->remarks != null)<br/><span contenteditable>{!! nl2br(e($self_billing_details[$i]->remarks)) !!}</span>@endif
                            </td>
                            <td>
                                <span contenteditable>{{ !empty($measurementOnly) ? $measurementOnly : '-' }}</span>
                            </td>
                            <td>
                                <span contenteditable>{{ $self_billing_details[$i]->quantity ?? '-' }}</span>
                            </td>
                            <td class="amount">
                                <span>{{ number_format((float) $self_billing_details[$i]->unit_price, 2, '.', '') }}</span>
                            </td>
                            <td class="amount">
                                <span>{{ number_format((float) $self_billing_details[$i]->unit_price * (float) $self_billing_details[$i]->quantity, 2, '.', '') }}</span>
                            </td>
                            <td class="amount">
                                <span contenteditable>{{ number_format((float) $self_billing_details[$i]->total_discount_amount, 2, '.', '') }}
                                </span>
                            </td>
                            <td class="amount">
                                <span contenteditable>{{ number_format((float) $self_billing_details[$i]->total_charge_rate, 2, '.', '') }} </span>
                            </td>
                            <td class="amount">
                                <span>RM</span>
                                <span>{{ number_format((float) $self_billing_details[$i]->total_excluding_tax, 2, '.', '') }}</span>
                            </td>
                        </tr>
                    @endfor
                    <tr>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td>Subtotal</td>
                        <td class="amount">
                            <span>{{ number_format($totalUnitPrice, 2, '.', '') }}</span>
                        </td>
                        <td class="amount">
                            <span contenteditable>{{ number_format((float) $totalItemDiscount, 2, '.', '') }}</span>
                        </td>
                        <td class="amount">
                            <span contenteditable>{{ number_format((float) $totalItemCharge, 2, '.', '') }}</span>
                        </td>
                        <td class="amount">
                            <span>RM</span>
                            <span>{{ number_format($selfBilling->total_net_amount, 2, '.', '') }}</span>
                        </td>
                    </tr>
                    <tr>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td colspan="4">Total Net Amount</td>
                        <td class="amount">
                            <span>RM</span>
                            <span>{{ number_format($selfBilling->total_net_amount, 2, '.', '') }}</span>
                        </td>
                    </tr>
                </tbody>
            </table>

            <table class="inventory">
                <thead>
                    <tr>
                        <th style="width: 25%;text-align: left;"><span contenteditable>Total Product/Service
                                Price</span></th>
                        <th style="width: 25%;text-align: left;"><span contenteditable>Tax Type</span></th>
                        <th style="width: 25%;text-align: left;"><span contenteditable>Tax Rate</span></th>
                        <th style="width: 25%;text-align: left;"><span contenteditable>Tax Amount</span></th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            <span>RM</span>
                            <span>{{  number_format($totalExcldTax, 2, '.', '') }}</span>
                        </td>
                        <td><span>{{ 'N/A'}}</span></td>
                        <td>
                            <span>N/A</span>                     
                        </td>
                        <td>
                            @if($selfBilling->total_tax_amount > 0)
                                <span>RM</span><span>{{ number_format($selfBilling->total_tax_amount, 2) }}</span>
                            @else
                                <span>N/A</span>
                            @endif
                        </td>
                    </tr>
                </tbody>
            </table>

            <table class="balance">
                <tr>
                    <th><span contenteditable>Invoice Discount Value</span></th>
                    <td><span
                            data-prefix>{{ $school['currency_symbol'] ?? '' }}</span><span>{{ number_format($selfBilling->invoice_discount_value, 2, '.', '') }}</span>
                    </td>
                </tr>
                <tr>
                    <th><span contenteditable>Invoice Charge Value</span></th>
                    <td><span
                            data-prefix>{{ $school['currency_symbol'] ?? '' }}</span><span>{{ number_format($selfBilling->invoice_charge_value, 2, '.', '') }}</span>
                    </td>
                </tr>
                <tr>
                    <th><span contenteditable>Total Excluding Tax</span></th>
                    <td><span
                            data-prefix>{{ $school['currency_symbol'] ?? '' }}</span><span>{{ number_format($selfBilling->total_excluding_tax, 2, '.', '') }}</span>
                    </td>
                </tr>
                <tr>
                    <th><span contenteditable>Total Tax Amount</span></th>
                    <td><span
                            data-prefix>{{ $school['currency_symbol'] ?? '' }}</span><span>{{ number_format($selfBilling->total_tax_amount, 2, '.', '') }}</span>
                    </td>
                </tr>
                <tr>
                    <th><span contenteditable>Total Including Tax</span></th>
                    <td><span
                            data-prefix>{{ $school['currency_symbol'] ?? '' }}</span><span>{{ number_format($selfBilling->total_including_tax, 2)  }}</span>
                    </td>
                </tr>
                <tr>
                    <th><span contenteditable>Total Rounding Amount</span></th>
                    <td><span
                            data-prefix>{{ $school['currency_symbol'] ?? '' }}</span><span>{{ number_format($selfBilling->total_rounding_amount, 2)  }}</span>
                    </td>
                </tr>
                <tr>
                    <th><span contenteditable>Total Payable Amount</span></th>
                    <td><span
                            data-prefix>{{ $school['currency_symbol'] ?? '' }}</span><span>{{ number_format($selfBilling->total_payable_amount, 2)  }}</span>
                    </td>
                </tr>
            </table>
        </article>

        <!-- <aside style="page-break-inside: avoid;">
            <div class="footer">
                <div class="footer-text-with-qrcode" style="display: flex;">
                    <div class="footer-text" style="">
                        <span>Remarks:</span>
                        <p><?php echo nl2br(e($school['remark'])); ?></p>
                        <br>
                        <span style="line-height:1.2;">Digital Signature:</span><br>
                        <span style="word-break: break-word; max-width: 500px; display:block; margin-bottom: 5px; line-height: 1;">{{$selfBillingEInvoice->signature_value ?? 'N/A'}}</span>
                        <span>Date and Time of Validation: {{$selfBillingEInvoice->date_time_validated ?? 'N/A'}}</span><br>
                        @if(isset($selfBillingEInvoice))
                        <span>This invoice is an validated e-invoice by LHDN. Scan the QR to verify:</span>
                        @else
                        <span>This invoice is an unvalidated e-invoice by LHDN</span>
                        @endif
                    </div>
                    <div class="footer-image" style="">
                        @if(isset($selfBillingEInvoice->e_invoice_url))
                            <img src="{{ $selfBillingEInvoice->e_invoice_url }}" style="width: 100px; height: 100px;" alt="QR Code">
                        @else
                            <img src="assets/qrcode_demo.jpg" style="width: 100px; height: 100px;" alt="QR Code">
                        @endif
                    </div>
                </div>
            </div>
        </aside> -->
        <aside style="page-break-inside: avoid;">
            <div class="footer" style="font-size: 13px;">
                <table class="footer-table" style="width: 100%; border-collapse: collapse; border:none;">
                    <tr valign="bottom">
                        <td style="width: 80%; padding-right: 10px; vertical-align: bottom;">
                            <strong>Remarks:</strong>
                            <p><?php echo nl2br(e($school['remark'])); ?></p>

                            <strong>Digital Signature:</strong><br>
                            <p style="word-break: break-word; max-width: 90%;">
                                {{$selfBillingEInvoice->signature_value ?? 'N/A'}}
                            </p>
                            
                            <p><strong>Date and Time of Validation:</strong> {{$selfBillingEInvoice->date_time_validated ?? 'N/A'}}</p>
                            @if(isset($selfBillingEInvoice))
                                <p>This invoice is a validated e-invoice by LHDN. Scan the QR to verify:</p>
                            @else
                                <p>This invoice is an unvalidated e-invoice by LHDN.</p>
                            @endif
                        </td>

                        <td style="width: 20%; text-align: right; vertical-align: bottom;">
                            @if(isset($selfBillingEInvoice->e_invoice_url))
                                <img src="{{ $selfBillingEInvoice->e_invoice_url }}" style="width: 100px; height: 100px;" alt="QR Code">
                            @else
                                <img src="assets/qrcode_demo.jpg" style="width: 100px; height: 100px;" alt="QR Code">
                            @endif
                        </td>
                    </tr>
                </table>
            </div>
        </aside>

        <div contenteditable>
            <p style="font-size:smaller;line-height: 2;"></p>
        </div>
    </body>

</html>
