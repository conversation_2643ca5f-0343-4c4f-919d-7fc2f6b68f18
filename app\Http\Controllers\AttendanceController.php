<?php

namespace App\Http\Controllers;

use Throwable;
use Carbon\Carbon;
use App\Models\User;
use App\Models\Subject;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Services\CachingService;
use App\Models\SubjectAttendance;
use App\Models\TeacherAttendance;
use App\Services\ResponseService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use App\Services\BootstrapTableService;
use Illuminate\Support\Facades\Validator;
use App\Repositories\Student\StudentInterface;
use App\Repositories\Subject\SubjectInterface;
use App\Repositories\Attendance\AttendanceInterface;
use App\Repositories\ClassSection\ClassSectionInterface;

class AttendanceController extends Controller
{

    private AttendanceInterface $attendance;
    private ClassSectionInterface $classSection;
    private StudentInterface $student;
    private CachingService $cache;
    private SubjectInterface $subject;

    public function __construct(AttendanceInterface $attendance, ClassSectionInterface $classSection, StudentInterface $student, CachingService $cachingService,SubjectInterface $subject)
    {
        $this->attendance = $attendance;
        $this->classSection = $classSection;
        $this->student = $student;
        $this->subject = $subject;
        $this->cache = $cachingService;
    }


    public function index()
    {
        ResponseService::noFeatureThenRedirect('Attendance Management');
        ResponseService::noAnyPermissionThenRedirect(['class-teacher', 'attendance-list']);
        $classSections = $this->classSection->builder()->ClassTeacher()->with('class', 'class.stream', 'section', 'medium')->get();
        return view('attendance.index', compact('classSections'));
    }

    public function SubjectAttendanceindex()
    {
        ResponseService::noFeatureThenRedirect('Attendance Management');
        ResponseService::noAnyPermissionThenRedirect(['class-teacher', 'attendance-list']);
        // $classSections = $this->classSection->builder()->ClassTeacher()->with('class', 'class.stream', 'section', 'medium')->get();
        $classSections = $this->classSection->all(['*'], ['class', 'class.stream', 'section', 'medium']);
        $currentSchoolId = auth()->user()->school_id;
        $subjectIdss = DB::table('subject_students')
            ->join('students', 'subject_students.student_id', '=', 'students.id')
            ->where('students.school_id', $currentSchoolId)
            ->pluck('subject_students.subject_id');

        $teachers = DB::table('staffs')
            ->join('users', 'staffs.user_id', '=', 'users.id')
            ->select('users.id', DB::raw("CONCAT(users.first_name, ' ', users.last_name) as full_name"))
            ->where('users.school_id', $currentSchoolId)
            ->distinct()
            ->get();

        $subject_id = [];
        
        if (Auth::user()->hasRole('Teacher')) {
            $subject_id = DB::table('subject_teachers as st')
                ->join('subjects as s', 'st.subject_id', '=', 's.id')
                ->where('st.teacher_id', Auth::user()->id)
                ->select('s.id', 's.name')
                ->distinct()
                ->get();
        }
        else{
            $subject_id = DB::table('subjects as s')
                ->distinct()
                ->select('s.id', 's.name')
                ->leftJoin('subject_students as ss', 'ss.subject_id', '=', 's.id')
                ->leftJoin('class_subjects as cs', 'cs.subject_id', '=', 's.id')
                ->where('s.school_id', $currentSchoolId)
                ->whereNull('s.deleted_at')
                ->get();
        }
        return view('attendance.subject_attendance', compact('classSections', 'currentSchoolId', 'subject_id', 'teachers'));
    }


    public function view()
    {
        ResponseService::noFeatureThenRedirect('Attendance Management');
        ResponseService::noAnyPermissionThenRedirect(['class-teacher', 'attendance-list']);
        $class_sections = $this->classSection->builder()->ClassTeacher()->with('class', 'class.stream', 'section', 'medium')->get();
        return view('attendance.view', compact('class_sections'));
    }

    public function scan()
    {
        ResponseService::noFeatureThenRedirect('Attendance Management');
        ResponseService::noAnyPermissionThenRedirect(['class-teacher', 'attendance-list']);
        $class_sections = $this->classSection->builder()->ClassTeacher()->with('class', 'class.stream', 'section', 'medium')->get();
        $sessionYear = $this->cache->getDefaultSessionYear();
        $schoolId = Auth::user()->school_id;
      
        $subjects = [];
        if (Auth::user()->hasRole('Teacher')) {
            $subjects = DB::table('subject_teachers as st')
                ->join('subjects as s', 'st.subject_id', '=', 's.id')
                ->where('st.teacher_id', Auth::user()->id)
                ->select('s.id', 's.name')
                ->distinct()
                ->get();
        }
        else{
             $subjects = DB::table('subjects')
            ->where('school_id', $schoolId)
            ->get(); // Fetch all subjects
        }
        $staffs = DB::table('staffs as s')
        ->join('users as u', 's.user_id', '=', 'u.id')
        ->where('u.school_id', $schoolId)
        ->whereNull('u.deleted_at')
        ->select('u.id', DB::raw("CONCAT(u.first_name, ' ', u.last_name) AS full_name"))
        ->get();
            
        return view('attendance.scan', compact('class_sections', 'subjects', 'staffs'));
    }

    public function getAttendanceData(Request $request)
    {
        ResponseService::noFeatureThenRedirect('Attendance Management');
        $response = $this->attendance->builder()->select('type')->where(['date' => date('Y-m-d', strtotime($request->date)), 'class_section_id' => $request->class_section_id])->pluck('type')->first();
        return response()->json($response);
    }

    public function getAttData(Request $request)
    {
        ResponseService::noFeatureThenRedirect('Attendance Management');

        // Retrieve the subject_id and date from the request
        $subject_id = $request->input('subject_id');
        $date = $request->input('date');

        // Retrieve attendance data from the subject_students table
        $attendanceData = DB::table('subject_students')
            ->where('subject_id', $subject_id)
            ->where('created_at', $date) // Assuming you want to filter by created_at
            ->get();

        // Return the response as needed
        return response()->json($attendanceData);
    }

    public function store(Request $request)
    {
        ResponseService::noFeatureThenRedirect('Attendance Management');
        ResponseService::noAnyPermissionThenRedirect(['class-teacher', 'attendance-create', 'attendance-edit']);
        $request->validate([
            'class_section_id' => 'required',
            'date'             => 'required',
        ]);
        try {
            DB::beginTransaction();
            $attendanceData = array();
            $sessionYear = $this->cache->getDefaultSessionYear();
            // $student_ids = array();
            $student_ids_absent = array();
            $student_ids_present = array();
            $student_ids_holiday = array();

            foreach ($request->attendance_data as $value) {
                $data = (object)$value;
                if ($data->id && !$request->holiday) {
                    $scanAttendanceExist = DB::table('subject_attendances')
                        ->where('subject_attendances.date', date('Y-m-d', strtotime($request->date)))
                        ->whereNull('subjects_id')
                        ->where('user_id',$data->student_id)
                        ->select('subject_attendances.id as subject_attendance_id')
                        ->first();
                    if ($scanAttendanceExist) {
                        DB::table('subject_attendances')->where('id', $scanAttendanceExist->subject_attendance_id)->update(['status' => $data->type]);

                        $updateAttendanceData = [
                            'class_section_id' => $request->class_section_id,
                            'student_id'       => $data->student_id,
                            'session_year_id'  => $sessionYear->id,
                            'type'             => $request->holiday ?? $data->type,
                            'date'             => date('Y-m-d', strtotime($request->date))
                        ];

                        DB::table('attendances')->where('id', $data->id)->update($updateAttendanceData);

                        $class =  DB::table('class_sections')
                        ->where('id',$request->class_section_id)
                        ->select('class_id','school_id')
                        ->first();

                        $classId = $class->class_id;
                        $schoolId = $class->school_id;
                        $student = DB::table('students')
                        ->where('user_id',$data->student_id)
                        ->get();


                        // Add Default Clock Out Points
                        $clockOutPoints = DB::table('rewards_category')
                        ->where('school_id', $schoolId )
                        ->where('category_name','Clock Out')
                        ->where('is_default',1)
                        ->select('points_amount','mode','id')
                        ->orderBy('updated_at', 'desc')
                        ->first();
        
                        if ($clockOutPoints && $clockOutPoints->points_amount > 0){
                            $points = DB::table('rewards')
                            ->where('school_id',$schoolId )
                            ->where('class_id',$classId)
                            ->where('student_id',$student[0]->id)
                            ->select('score_total','reward_point_total')
                            ->orderBy('updated_at', 'desc')
                            ->first();

                            // update score and reward points
                            $updateScoreReward = array(
                                'school_id' =>$schoolId ,
                                'class_id' =>$classId,
                                'student_id' => $student[0]->id,
                                'score_amount' => $clockOutPoints->points_amount,
                                'score_total' => $points->score_total + $clockOutPoints->points_amount,
                                'remark' => 'Clock Out',
                                'category_id' => $clockOutPoints->id
                            );

                            // update reward points if mode 1
                            $updateScoreReward['reward_point_amount'] = $clockOutPoints->mode == 1 ? $clockOutPoints->points_amount : 0;
                            $updateScoreReward['reward_point_total'] = $clockOutPoints->mode == 1 ? $points->reward_point_total + $clockOutPoints->points_amount : $points->reward_point_total;
                            $result = DB::table('rewards')->insert($updateScoreReward);

                        }
                    } else {
                        $scanAttendanceData = [
                            'school_id' => Auth::user()->school_id,
                            'user_id' => $data->student_id,
                            'status' => $data->type,
                            'date' => date('Y-m-d', strtotime($request->date)),
                            'created_at' => now(),
                            'updated_at' => now()
                        ];
                        $result = DB::table('subject_attendances')->insertGetId($scanAttendanceData);
                        if($result){
                            $attendanceData[] = array(
                                "id"               => $data->id,
                                'class_section_id' => $request->class_section_id,
                                'student_id'       => $data->student_id,
                                'session_year_id'  => $sessionYear->id,
                                'type'             => $request->holiday ?? $data->type,
                                'date'             => date('Y-m-d', strtotime($request->date))
                            );
                        }

                        $class =  DB::table('class_sections')
                        ->where('id',$request->class_section_id)
                        ->select('class_id','school_id')
                        ->first();

                        $classId = $class->class_id;
                        $schoolId = $class->school_id;
                        $student = DB::table('students')
                        ->where('user_id',$data->student_id)
                        ->select('id')
                        ->first();
                        $studentId=$student->id;

                        //Add Default Clock in Points
                        $clockInPoints = DB::table('rewards_category')
                        ->where('school_id', $class->school_id)
                        ->where('category_name','Clock In')
                        ->where('is_default',1)
                        ->select('points_amount','mode','id')
                        ->first();

                        if ($clockInPoints && $clockInPoints->points_amount > 0){
                            $points = DB::table('rewards')
                            ->where('school_id',$schoolId)
                            ->where('student_id',$studentId)
                            ->where('class_id',$classId)
                            ->select('score_total','reward_point_total')
                            ->orderBy('updated_at', 'desc')
                            ->first();

                            // If student doesnt have previous reward records
                            if ($points) {
                                $scoreTotal = $points->score_total;
                                $rewardPointTotal = $points->reward_point_total;
                            } else {
                                $scoreTotal = 0;
                                $rewardPointTotal = 0;
                            }
                            

                            // update score and reward points
                            $updateScoreReward = array(
                                'school_id' =>$schoolId,
                                'class_id' =>$class->class_id,
                                'student_id' => $studentId,
                                'score_amount' => $clockInPoints->points_amount,
                                'score_total' => $scoreTotal + $clockInPoints->points_amount,
                                'remark' => 'Clock In',
                                'category_id' => $clockInPoints->id
                            );
                            
                            // update reward points if mode 1
                            $updateScoreReward['reward_point_amount'] = $clockInPoints->mode == 1 ? $clockInPoints->points_amount : 0;
                            $updateScoreReward['reward_point_total'] = $clockInPoints->mode == 1 ? $rewardPointTotal + $clockInPoints->points_amount : $rewardPointTotal;
                            $result = DB::table('rewards')->insert($updateScoreReward);
                        }


                    }
                }

                if ($request->holiday == 3) {
                    $student_ids_holiday[] = $data->student_id;
                } else {
                    if ($data->type == 0) {
                        $student_ids_absent[] = $data->student_id;
                    } elseif ($data->type == 1) {
                        $student_ids_present[] = $data->student_id;
                    }
                }
            }
            $this->attendance->upsert($attendanceData, ["id"], ["class_section_id", "student_id", "session_year_id", "type", "date", "subject_attendance_id"]);
            $date = Carbon::parse(date('Y-m-d', strtotime($request->date)))->format('F jS, Y');
            $type = "attendance";
            if (!empty($student_ids_absent)) {
                $user_absent = $this->student->builder()->whereIn('user_id', $student_ids_absent)->select('guardian_id', 'user_id')->get();
                foreach ($user_absent as $user) {
                    $notifyUser = array();
                    $notifyUser[] = $user->guardian_id;
                    $notifyUser[] = $user->user_id;
                    $userData = DB::table('users')->where('id', $user->user_id)->first();
                    $userName = $userData->first_name . " " . $userData->last_name;
                    $title = 'Absent';
                    $body = $userName . ' is absent on ' . $date;
                    send_notification($notifyUser, $title, $body, $type);
                }
            }

            // Send notifications for present
            if (!empty($student_ids_present)) {
                $user_present = $this->student->builder()->whereIn('user_id', $student_ids_present)->select('guardian_id', 'user_id')->get();
                foreach ($user_present as $user) {
                    $notifyUser = array();
                    $notifyUser[] = $user->guardian_id;
                    $notifyUser[] = $user->user_id;
                    $userData = DB::table('users')->where('id', $user->user_id)->first();
                    $userName = $userData->first_name . " " . $userData->last_name;
                    $title = 'Present';
                    $body = $userName . ' is present on ' . $date;
                    send_notification($notifyUser, $title, $body, $type);
                }
            }

            // Send notifications for holiday
            if (!empty($student_ids_holiday)) {
                $user_holiday = $this->student->builder()->whereIn('user_id', $student_ids_holiday)->pluck('guardian_id');
                $title = 'Holiday';
                $body = 'Today is a holiday on ' . $date;
               send_notification($user_holiday, $title, $body, $type);
            }
            if ($request->absent_notification) {
                $user = $this->student->builder()->whereIn('user_id', $student_ids_absent)->pluck('guardian_id');
                $date = Carbon::parse(date('Y-m-d', strtotime($request->date)))->format('F jS, Y');
                $title = 'Absent';
                $body = 'Your child is absent on ' . $date;
                $type = "attendance";

                send_notification($user, $title, $body, $type);
            }

            DB::commit();
            ResponseService::successResponse('Data Stored Successfully');
        } catch (Throwable $e) {
            if (Str::contains($e->getMessage(), [
                'does not exist',
                'file_get_contents'
            ])) {
                DB::commit();
                ResponseService::warningResponse("Data Stored successfully. But App push notification not send.");
            } else {
                DB::rollback();
                ResponseService::logErrorResponse($e, "Attendance Controller -> Store method");
                ResponseService::errorResponse();
            }
        }
    }

    public function show(Request $request)
    {
        ResponseService::noFeatureThenRedirect('Attendance Management');
        ResponseService::noAnyPermissionThenRedirect(['class-teacher', 'attendance-list']);

        //        $offset = $request->input('offset', 0);
        //        $limit = $request->input('limit', 10);
        $sort = $request->input('sort', 'roll_number');
        $order = $request->input('order', 'ASC');
        $search = $request->input('search');

        $class_section_id = $request->class_section_id;
        $date = date('Y-m-d', strtotime($request->date));
        $sessionYear = $this->cache->getDefaultSessionYear();

        $attendanceData = array();
        $attendanceQuery = $this->attendance->builder()->with('user.student')->where(['date' => $date, 'class_section_id' => $class_section_id, 'session_year_id' => $sessionYear->id])->whereHas('user', function ($q) {
            $q->whereNull('deleted_at');
        })->whereHas('user.student', function ($q) use ($sessionYear) {
            $q->where('session_year_id', $sessionYear->id);
        });

        // if ($date != '' && $attendanceQuery->count() > 0) {
        //     $attendanceQuery->when($search, function ($query) use ($search) {
        //         $query->where('id', 'LIKE', "%$search%")->orWhereHas('user', function ($q) use ($search) {
        //             $q->whereRaw("concat(users.first_name,' ',users.last_name) LIKE '%" . $search . "%'");
        //         });
        //     })->where('date', $date)->whereHas('user.student',function($q) use($sessionYear){
        //         $q->where('session_year_id',$sessionYear->id);
        //     });

        $total = 0;
        $existingAttendanceData = $attendanceQuery->get();
        if ($class_section_id) {
            $studentQuery = $this->student->builder()->where('session_year_id', $sessionYear->id)->where('class_section_id', $class_section_id)->with('user')
                ->whereHas('user', function ($q) {
                    $q->whereNull('deleted_at');
                })
                ->when($search, function ($query) use ($search) {
                    $query->where('id', 'LIKE', "%$search%")->orWhereHas('user', function ($q) use ($search) {
                        $q->whereRaw("concat(users.first_name,' ',users.last_name) LIKE '%" . $search . "%'")->where('deleted_at', NULL);
                    });
                })->where('session_year_id', $sessionYear->id)->where('class_section_id', $class_section_id);

            $total = $studentQuery->count();
            // $studentQuery->orderBy($sort, $order)->skip($offset)->take($limit);
            $studentQuery->orderBy($sort, $order);
            $attendanceData = $studentQuery->get();
        }
        $attendanceLookup = $existingAttendanceData->keyBy('student_id');
        $rows = [];
        $no = 1;

        foreach ($attendanceData as $row) {
            if ($attendanceLookup->has($row->user_id)) {
                $attendance = $attendanceLookup->get($row->user_id);
                $rows[] = [
                    'id'           => $attendance->id,
                    'no'           => $no,
                    'student_id'   => $attendance->student_id,
                    'user_id'      => $attendance->student_id,
                    'admission_no' => $attendance->user->student->admission_no ?? '',
                    'roll_no'      => $attendance->user->student->roll_number ?? '',
                    'name'         => '<input type="hidden" value="' . $attendance->student_id . '" name="attendance_data[' . $no . '][id]"><input type="hidden" value="' . $attendance->student_id . '" name="attendance_data[' . $no . '][student_id]">' . ($attendance->user->first_name ?? '') . ' ' . ($attendance->user->last_name ?? ''),
                    'type'         => $attendance->type,
                ];
            } else {
                $rows[] = [
                    'id'           => null,
                    'no'           => $no,
                    'student_id'   => $row->user_id,
                    'user_id'      => $row->user_id,
                    'admission_no' => $row->admission_no ?? '',
                    'roll_no'      => $row->roll_number ?? '',
                    'name'         => '<input type="hidden" value="' . $row->user_id . '" name="attendance_data[' . $no . '][id]"><input type="hidden" value="' . $row->user_id . '" name="attendance_data[' . $no . '][student_id]">' . ($row->user->first_name ?? '') . ' ' . ($row->user->last_name ?? ''),
                    'type'         => null,
                ];
            }
            $no++;
        }

        $bulkData['total'] = $total;
        $bulkData['rows'] = $rows;

        return response()->json($bulkData);
    }

    public function SubjectAttendancestore(Request $request)
    {
        ResponseService::noFeatureThenRedirect('Attendance Management');
        ResponseService::noAnyPermissionThenRedirect(['class-teacher', 'attendance-create', 'attendance-edit']);
        $subject_id = $request->subject_id;
        $request->validate([
            'date' => 'required|date',
        ]);
        try {
            DB::beginTransaction();

            $sessionYear = $this->cache->getDefaultSessionYear();
            $student_ids_absent = array();
            $student_ids_present = array();
            $teacher_id = $request->teacher_id ?? null;

            if (Auth::user()->hasRole('Teacher')) {
                $teacher_id = Auth::user()->id;
            }

            // Fetch fees_per_section from the subjects table
            $fees_per_section = DB::table('subjects')->where('id', $subject_id)->value('commission');
            $fees_per_month = DB::table('subjects')->where('id', $subject_id)->value('commission_month');

            // Fetch group_id for the teacher from user_group_details
            $group_id = DB::table('user_group_details')
                ->where('subject_id', $request->subject_id)
                ->where('teacher_id', $teacher_id) // Add this condition
                ->value('group_id');

            // Fetch commission type and amount from user_groups
            $commission_typee = DB::table('user_groups')->where('id', $group_id)->value('commission_type');
            $commission_amountt = DB::table('user_groups')->where('id', $group_id)->value('commission_amount');
            // $exclude_absent_students = DB::table('user_groups')->where('id',$group_id)->value('exclude_absent_students');

            
            $studentsInsufficientFunds = [];
            $lowSessionCount = [];
            $noSessionCount = [];
            $alreadyTakenStudents = []; // Initialize an empty array for already taken students
            foreach ($request->attendance_data as $value) {

                $data = (object)$value;

                // Check one-time attendance setting
                $oneTimeAttendance = DB::table('school_settings')
                    ->where('school_id', Auth::user()->school_id)
                    ->where('name', 'attendance_setting')
                    ->where('data', 'LIKE', '%4%')
                    ->first();

                if ($oneTimeAttendance) {
                    // Check if attendance exists for today
                    $existingAttendance = DB::table('subject_attendances')
                        ->where('school_id', Auth::user()->school_id)
                        ->where('user_id', $data->student_id)
                        ->where('subjects_id', $subject_id) 
                        ->whereDate('date', date('Y-m-d', strtotime($request->date)))
                        ->first();

                        if ($existingAttendance) {
                            // Get all students with existing attendance for this date and subject
                            $studentsWithAttendance = DB::table('subject_attendances as sa')
                                ->join('users as u', 'sa.user_id', '=', 'u.id')
                                ->join('subjects as s', 'sa.subjects_id', '=', 's.id')
                                ->where('sa.school_id', Auth::user()->school_id)
                                ->where('sa.subjects_id', $subject_id)
                                ->whereDate('sa.date', date('Y-m-d', strtotime($request->date)))
                                ->select(
                                    DB::raw("DISTINCT CONCAT(u.first_name, ' ', u.last_name) as student_name"),
                                    's.name as subject_name'
                                )
                                ->groupBy('sa.user_id', 's.name')  // Add groupBy to ensure uniqueness
                                ->get();
    
                            $formattedDate = date('d/m/Y', strtotime($request->date));
                            
                            //$studentsList = $studentsWithAttendance->pluck('student_name')->unique()->implode(', ');
                            
                            ResponseService::errorResponse(
                                'Attendance already marked for students on ' . $formattedDate . 
                                ' for subject ' . $studentsWithAttendance->first()->subject_name 
                            );
                        }
                    }
                
                if ($data->id && !$request->holiday) {
                    $scanAttendanceExist = DB::table('subject_attendances')
                        ->join('attendances', 'attendances.subject_attendance_id', '=', 'subject_attendances.id')
                        ->where('attendances.id', $data->id)
                        ->where('subject_attendances.date', date('Y-m-d', strtotime($request->date)))
                        ->select('subject_attendances.id as subject_attendance_id')
                        ->first();

                    if ($scanAttendanceExist) {
                        // Update existing attendance
                        DB::table('subject_attendances')->where('id', $scanAttendanceExist->subject_attendance_id)->update([
                            'status' => $data->type,
                            'fees_per_section' => $fees_per_section,
                            'fees_per_month' => $fees_per_month,  // Individual fee
                            'commission_typee' => $commission_typee,
                            'commission_amountt' => $commission_amountt // Set to 0 if absent
                        ]);

                        $updateAttendanceData = [
                            'class_section_id' => $request->class_section_id,
                            'student_id' => $data->student_id,
                            'session_year_id' => $sessionYear->id,
                            'type' => $request->holiday ?? $data->type,
                            'date' => date('Y-m-d', strtotime($request->date))
                        ];

                        DB::table('attendances')->where('id', $data->id)->update($updateAttendanceData);
                    } else {
                        // Insert new attendance record
                        $scanAttendanceData = [
                            'school_id' => Auth::user()->school_id,
                            'user_id' => $data->student_id,
                            'teacher_id' => $teacher_id,
                            'subjects_id' => $subject_id,
                            'status' => $data->type,
                            'date' => date('Y-m-d', strtotime($request->date)),
                            'fees_per_section' => $fees_per_section,
                            'fees_per_month' => $fees_per_month,  // Individual fee
                            'commission_typee' => $commission_typee,
                            'commission_amountt' => $commission_amountt, // Set to 0 if absent
                            'created_at' => now(),
                            'updated_at' => now()
                        ];
                        $subject_attendance_id = DB::table('subject_attendances')->insertGetId($scanAttendanceData);
                    }

                    //here add the logic

                    $schoolId = Auth::user()->school_id;

                    $deductionMethod = DB::table('school_settings')
                        ->select('name', 'data')
                        ->where('name', 'deduction_method')
                        ->where('school_id', $schoolId)
                        ->first();
                    $deductionMethodActivation = false;

                    $userFullName = DB::table('users')
                                    ->where('id',$data->student_id)
                                    ->value(DB::raw("CONCAT(first_name, ' ', last_name) AS full_name"));
                    $subject = DB::table('subjects')
                                ->where('id',$subject_id)
                                ->first();
                    $student = DB::table('students as s')
                                ->join('class_sections as cs','cs.id','=','s.class_section_id')
                                ->where('s.user_id',$data->student_id)
                                ->first();

                    if($subject_id){
                        if ($deductionMethod) {
                            if ($deductionMethod->data === "1") {
                                if ($subject_attendance_id && in_array($data->type, [1, 2, 3])) {
                                    $query = DB::table('subject_attendances as sa')
                                        ->join('users as u', 'sa.user_id', '=', 'u.id')
                                        ->join('credit_system as cs', 'sa.user_id', '=', 'cs.user_id')
                                        ->join('students as s', 's.user_id', '=', 'sa.user_id')
                                        ->join('class_sections as cls','cls.id','=','s.class_section_id')
                                        ->join('classes as c','c.id','=','cls.class_id')
                                        ->join('subjects as sub', 'sa.subjects_id', '=', 'sub.id',)
                                        ->select('cs.user_id as credit_user', DB::raw("CONCAT(u.first_name, ' ', u.last_name) AS full_name"), 'c.id as class_id', 'cs.balance', 'cs.credit_amount', 'sub.commission')
                                        ->where('sa.date', date('Y-m-d', strtotime($request->date)))
                                        ->where('sa.subjects_id', $subject_id)
                                        ->where('sa.status', 1)
                                        ->where('cs.school_id', Auth::user()->school_id)
                                        ->where('cs.user_id', $data->student_id)
                                        ->first();
                                    if($query){
                                        // Check student's credit_status
                                        $creditStatus = DB::table('students')
                                            ->where('user_id', $query->credit_user)
                                            ->value('credit_status');

                                        // Only proceed with credit deduction if credit_status is not 1
                                        if($creditStatus != 1) {
                                            $subjectPrice = -$query->commission;
                                            $totalBalance = DB::table('credit_system')
                                                ->where('user_id', $query->credit_user)
                                                ->sum('credit_amount');
                                            
                                            $balance = $totalBalance + $subjectPrice;

                                            DB::table('credit_system')->insert([
                                                'school_id' => Auth::user()->school_id,
                                                'class_id' => $query->class_id,
                                                'user_id' => $query->credit_user,
                                                'credit_amount' => $subjectPrice,
                                                'balance' => $balance,
                                                'detail' => 'Attended 1 Class' .'('.$subject->name. ')',
                                                'created_at' => Carbon::now(),
                                                'updated_at' => Carbon::now()
                                            ]);

                                            if ($balance <= 0) {
                                                $studentsInsufficientFunds[] = $query->full_name;
                                            }
                                        }
                                    } else {
                                        $subjectPrice = -$subject->commission;
                                        $value = [
                                            'school_id' => Auth::user()->school_id,
                                            'class_id'  => $student->class_id,
                                            'user_id'   => $data->student_id,
                                            'credit_amount' => $subjectPrice,
                                            'balance'       => $subjectPrice,
                                            'detail' => 'Attended 1 Class' .'('.$subject->name. ')',
                                            'created_at' => Carbon::now(),
                                            'updated_at' => Carbon::now()
                                        ];
                                        DB::table('credit_system')->insert($value);
                                        $studentsInsufficientFunds[] = $userFullName;
                                    }
                                }
                            } else if ($deductionMethod->data === "2" && in_array($data->type, [1, 2, 3])) {
                                $userId = DB::table('students as s')
                                    ->join('users as u', 'u.id', '=', 's.user_id')
                                    ->select('s.id', DB::raw("CONCAT(u.first_name, ' ', u.last_name) AS full_name"))
                                    ->where('u.id', $data->student_id)
                                    ->first();
    
                                $package = DB::table('purchase_package as pp')
                                    ->join('subject_package as sp', 'pp.package_id', '=', 'sp.id')
                                    ->select('pp.id as purchase_id')
                                    ->where('pp.student_id', $userId->id)
                                    ->whereIn('pp.status', [0, 2])
                                    ->whereRaw("FIND_IN_SET(?, sp.subject_id)", [$subject_id])
                                    ->first();
                                if ($package) {
                                    $packageUsage = DB::table('package_usage as pu')
                                        ->where('pu.student_id', $userId->id)
                                        ->where('pu.purchase_package_id', $package->purchase_id)
                                        ->orderBy('created_at', 'DESC')
                                        ->first();
                                    if ($packageUsage && $packageUsage->remaining_session > 0) {
                                        if($packageUsage->remaining_session != 0){
                                            $remain = $packageUsage->remaining_session - 1;
                                        }
    
                                        DB::table('package_usage')->insert([
                                            'school_id' => Auth::user()->school_id,
                                            'purchase_package_id' => $package->purchase_id,
                                            'student_id' => $userId->id,
                                            'deduct' => -1,
                                            'remaining_session' => $remain ?? 0,
                                            'created_at' => Carbon::now(),
                                            'updated_at' => Carbon::now(),
                                        ]);
    
    
                                        if (in_array($remain, [1,2,3,4,5,6,7,8,9,10])) {
                                            $lowSessionCount[] = ['student_id' => $packageUsage->student_id, 'remaining_session' => $remain];
                                        }else if ($remain == 0) {
                                            $noSessionCount[] =['student_id' => $packageUsage->student_id, 'remaining_session' => $remain];
                                        }
                                        if ($packageUsage && $remain == 0) {
                                            DB::table('purchase_package as pp')
                                                ->where('pp.id', $package->purchase_id)
                                                ->update([
                                                    'status' => 1
                                                ]);
                                        }
                                    } else {
                                        $studentsInsufficientFunds[] = $userId->full_name;
                                    }
                                } else {
                                    $studentsInsufficientFunds[] = $userFullName;
                                }
                            } else if ($deductionMethod->data === "3" && in_array($data->type, [1, 2, 3])) {
                                $packageUsed = false;
                                $userId = DB::table('students as s')
                                    ->join('users as u', 'u.id', '=', 's.user_id')
                                    ->select('s.id')
                                    ->where('u.id', $data->student_id)
                                    ->first();
    
                                $package = DB::table('purchase_package as pp')
                                    ->join('subject_package as sp', 'pp.package_id', '=', 'sp.id')
                                    ->select('pp.id as purchase_id', 'pp.package_id', 'pp.student_id')
                                    ->where('pp.student_id', $userId->id)
                                    ->whereIn('pp.status', [0, 2])
                                    ->whereRaw("FIND_IN_SET(?, sp.subject_id)", [$subject_id])
                                    ->first();
    
                                if ($package) {
                                    $packageUsage = DB::table('package_usage as pu')
                                    ->where('pu.student_id', $userId->id)
                                    ->where('pu.purchase_package_id', $package->purchase_id)
                                    ->orderBy('created_at', 'DESC')
                                    ->first();
                                    if ($packageUsage && $packageUsage->remaining_session > 0) {
                                        if($packageUsage->remaining_session != 0){
                                            $remain = $packageUsage->remaining_session - 1;
                                            $packageUsed = true;
                                        }
    
                                        DB::table('package_usage')->insert([
                                            'school_id' => Auth::user()->school_id,
                                            'purchase_package_id' => $package->purchase_id,
                                            'student_id' => $userId->id,
                                            'deduct' => -1,
                                            'remaining_session' => $remain ?? 0,
                                            'created_at' => Carbon::now(),
                                            'updated_at' => Carbon::now(),
                                        ]);
    
    
                                        if (in_array($remain, [1,2,3,4,5,6,7,8,9,10])) {
                                            $lowSessionCount[] = ['student_id' => $packageUsage->student_id, 'remaining_session' => $remain];
                                        }else if ($remain == 0) {
                                            $noSessionCount[] =['student_id' => $packageUsage->student_id, 'remaining_session' => $remain];
                                        }
                                        if ($packageUsage && $remain == 0) {
                                            DB::table('purchase_package as pp')
                                                ->where('pp.id', $package->purchase_id)
                                                ->update([
                                                    'status' => 1
                                                ]);
                                        }
                                    } 
                                } 
                                 
                                $query = DB::table('subject_attendances as sa')
                                        ->join('users as u', 'sa.user_id', '=', 'u.id')
                                        ->join('credit_system as cs', 'sa.user_id', '=', 'cs.user_id')
                                        ->join('students as s', 's.user_id', '=', 'sa.user_id')
                                        ->join('class_sections as cls','cls.id','=','s.class_section_id')
                                        ->join('classes as c','c.id','=','cls.class_id')
                                        ->join('subjects as sub', 'sa.subjects_id', '=', 'sub.id',)
                                        ->select('cs.user_id as credit_user', DB::raw("CONCAT(u.first_name, ' ', u.last_name) AS full_name"), 'c.id as class_id', 'cs.balance', 'cs.credit_amount', 'sub.commission')
                                        ->where('sa.date', date('Y-m-d', strtotime($request->date)))
                                        ->where('sa.subjects_id', $subject_id)
                                        ->where('sa.status', 1)
                                        ->where('cs.school_id', Auth::user()->school_id)
                                        ->where('cs.user_id', $data->student_id)
                                        ->first();
                                if(!$packageUsed){
                                    if($query){
                                        // Check student's credit_status
                                        $creditStatus = DB::table('students')
                                            ->where('user_id', $query->credit_user)
                                            ->value('credit_status');

                                        // Only proceed with credit deduction if credit_status is not 1
                                        if($creditStatus != 1) {
                                            $subjectPrice = -$query->commission;
                                            $totalBalance = DB::table('credit_system')
                                                ->where('user_id', $query->credit_user)
                                                ->sum('credit_amount');
                                            
                                            $balance = $totalBalance + $subjectPrice;

                                            DB::table('credit_system')->insert([
                                                'school_id' => Auth::user()->school_id,
                                                'class_id' => $query->class_id,
                                                'user_id' => $query->credit_user,
                                                'credit_amount' => $subjectPrice,
                                                'balance' => $balance,
                                                'detail' => 'Attended 1 Class' .'('.$subject->name. ')',
                                                'created_at' => Carbon::now(),
                                                'updated_at' => Carbon::now()
                                            ]);

                                            if ($balance <= 0) {
                                                $studentsInsufficientFunds[] = $query->full_name;
                                            }
                                        }
                                    } else {
                                        $subjectPrice = -$subject->commission;
                                        $value = [
                                            'school_id' => Auth::user()->school_id,
                                            'class_id'  => $student->class_id,
                                            'user_id'   => $data->student_id,
                                            'credit_amount' => $subjectPrice,
                                            'balance'       => $subjectPrice,
                                            'detail' => 'Attended 1 Class' .'('.$subject->name. ')',
                                            'created_at' => Carbon::now(),
                                            'updated_at' => Carbon::now()
                                        ];
                                        DB::table('credit_system')->insert($value);
                                        $studentsInsufficientFunds[] = $userFullName;
                                    }
                                }
                            
                            } else {
                                //$deductionMethodActivation = true;
                            }
                        } 
                    }
                }

                if ($request->holiday == 3) {
                    $student_ids_holiday[] = $data->student_id;
                } else {
                    if ($data->type == 0) {
                        $student_ids_absent[] = $data->student_id;
                    } elseif ($data->type == 1) {
                        $student_ids_present[] = $data->student_id;
                    }
                }
            }

            $date = Carbon::parse(date('Y-m-d', strtotime($request->date)))->format('F jS, Y');
            $type = "attendance";
            // Send notifications for present
            if (!empty($student_ids_present)) {
                $user_present = $this->student->builder()->whereIn('user_id', $student_ids_present)->select('guardian_id', 'user_id')->get();
                foreach ($user_present as $user) {
                    $notifyUser = array();
                    $notifyUser[] = $user->guardian_id;
                    $notifyUser[] = $user->user_id;
                    $userData = DB::table('users')->where('id', $user->user_id)->first();
                    $userName = $userData->first_name . " " . $userData->last_name;
                    $title = 'Present';
                    $body = $userName . ' is present on ' . $date;
                    send_notification($notifyUser, $title, $body, $type);
                }
            }
            if (!empty($student_ids_absent)) {
                $user = $this->student->builder()->whereIn('user_id', $student_ids_absent)->pluck('guardian_id');
                $date = Carbon::parse(date('Y-m-d', strtotime($request->date)))->format('F jS, Y');
                $title = 'Absent';
                $body = 'Your child is absent on ' . $date;
                $type = "attendance";

                send_notification($user, $title, $body, $type);
            }
            if (!empty($lowSessionCount)) {
                $studentIds = array_column($lowSessionCount, 'student_id');
                $lowSessionId = DB::table('users as u')
                ->join('students as s', 'u.id', '=', 's.user_id')
                ->select('u.id', 's.guardian_id', 'u.first_name', 'u.last_name')
                ->whereIn('s.id', $studentIds)
                ->get();

                foreach ($lowSessionId as $user) {
                    // Get package info to calculate expiry date
                    $packageInfo = DB::table('purchase_package as pp')
                        ->join('subject_package as sp', 'pp.package_id', '=', 'sp.id')
                        ->join('students as s', 'pp.student_id', '=', 's.id')
                        ->where('s.id', $lowSessionCount[0]['student_id'])  // Use the student_id from lowSessionCount
                        ->whereRaw("FIND_IN_SET(?, sp.subject_id)", [$subject_id])
                        ->select('pp.date as purchase_date', 'sp.expiry_days')
                        ->orderBy('pp.id', 'DESC')
                        ->first();

                    // Calculate expiry date
                    $formattedExpirationDate = "";
                    if ($packageInfo) {
                        $purchaseDate = Carbon::parse($packageInfo->purchase_date);
                        $expiryDays = $packageInfo->expiry_days;
                        $expirationDate = $purchaseDate->addDays($expiryDays);
                        $formattedExpirationDate = $expirationDate->format('d-m-Y');
                    }

                    $notifyUser = array();
                    $notifyUser[] = $user->guardian_id;
                    $notifyUser[] = $user->id;
                    $userName = $user->first_name . " " . $user->last_name;
                    $title = "Session Updated";
                    $type = "session_update";
                    $body = "Clock-in successful! You have ".$lowSessionCount[0]['remaining_session']. " sessions left (expires ".$formattedExpirationDate.")";
                    send_notification($notifyUser, $title, $body, $type);
                }
            } 

            if (!empty($noSessionCount)) {
                $studentIds = array_column($noSessionCount, 'student_id');
                $lowSessionId = DB::table('users as u')
                ->join('students as s', 'u.id', '=', 's.user_id')
                ->select('u.id', 's.guardian_id' , 'u.first_name', 'u.last_name')
                ->whereIn('s.id', $studentIds)
                ->get();

                foreach ($lowSessionId as $user) {
                    $notifyUser = array();
                    $notifyUser[] = $user->guardian_id;
                    $notifyUser[] = $user->id;
                    $userName = $user->first_name . " " . $user->last_name;
                    $title = "Session Updated";
                    $type = "session_update";
                    $body = "Clock-in successful! Your sessions have finished.";
                    send_notification($notifyUser, $title, $body, $type);
                }
            }
            if($deductionMethodActivation){
                DB::commit();
                return response()->json([
                    "message" => "activation"
                ]);
            }
            else if (!empty($studentsInsufficientFunds)) {
                // Get list of students with their credit status - more efficient query
                $studentsWithWarning = [];

                // Get all student names at once to avoid multiple queries
                $studentInfos = DB::table('users as u')
                    ->join('students as s', 's.user_id', '=', 'u.id')
                    ->whereIn(DB::raw("CONCAT(u.first_name, ' ', u.last_name)"), $studentsInsufficientFunds)
                    ->select('s.credit_status', DB::raw("CONCAT(u.first_name, ' ', u.last_name) as full_name"))
                    ->get();

                // Create a lookup array for faster access
                $studentStatusMap = [];
                foreach ($studentInfos as $info) {
                    $studentStatusMap[$info->full_name] = $info->credit_status;
                }

                // Filter students who should get warnings
                foreach ($studentsInsufficientFunds as $studentName) {
                    if (isset($studentStatusMap[$studentName]) && $studentStatusMap[$studentName] != 1) {
                        $studentsWithWarning[] = $studentName;
                    }
                }

                DB::commit();

                if (!empty($studentsWithWarning)) {
                    // Show both success and warning for students who should get it
                    return response()->json([
                        "message" => "successWithWarning",
                        'namesInsufficient' => implode(', ', $studentsWithWarning),
                        'date' => $request->date
                    ]);
                }
                else {
                    // Show normal success message if no warnings needed
                    return response()->json([
                        "message" => "success",
                        'date' => $request->date
                    ]);
                }
            } else {
                // Show normal success message when no students have insufficient funds
                DB::commit();
                return response()->json([
                    "message" => "success",
                    'date' => $request->date
                ]);
            }
        } catch (Throwable $e) {
            if (Str::contains($e->getMessage(), ['does not exist', 'file_get_contents'])) {
                DB::commit();
                ResponseService::warningResponse("Data Stored successfully. But App push notification not sent.");
            } else {
                DB::rollback();
                ResponseService::logErrorResponse($e, "Attendance Controller -> Store method");
                ResponseService::errorResponse();
            }
        }
    }

    public function subjectattendanceshow(Request $request)
    {
        ResponseService::noFeatureThenRedirect('Attendance Management');
        ResponseService::noAnyPermissionThenRedirect(['class-teacher', 'attendance-list']);

        $currentSchoolId = auth()->user()->school_id;
        $sort = $request->input('sort', 'roll_number');
        $order = $request->input('order', 'ASC');
        $search = $request->input('search');
        $date = date('Y-m-d', strtotime($request->date));
        $sessionYear = $this->cache->getDefaultSessionYear();

        // Get the school_id from the attendances table
        // $attendance = DB::table('attendances')
        //     ->where('date', $date)
        //     ->where('session_year_id', $sessionYear->id)
        //     ->first(['school_id']); // Assuming school_id exists in the attendances table

        // if (!$attendance) {
        //     return response()->json(['error' => 'No attendance records found for the given date and session year.'], 404);
        // }

        $schoolId = Auth::user()->school_id;

        // Get subject IDs associated with the school_id
        // $subjectIds = DB::table('subject_students')
        //     ->distinct()
        //     ->pluck('subject_id');
        $subject_id = $request->subject_id;


        $attendanceQuery = $this->attendance->builder()
            ->with('user.student')
            // ->join('subject_students', 'subject_students.student_id', '=', 'attendances.student_id')
            ->where('attendances.date', $date)
            // ->whereIn('subject_students.subject_id', $subjectIds) // Use whereIn for an array of IDs
            ->where('attendances.session_year_id', $sessionYear->id)
            ->whereHas('user', function ($q) {
                $q->whereNull('deleted_at');
            })
            ->whereHas('user.student', function ($q) use ($sessionYear) {
                $q->where('session_year_id', $sessionYear->id);
            });


        $existingAttendanceData = $attendanceQuery->get();

        $subject_id = $request->subject_id;
        $attendanceData = [];
        $total = 0;


        if ($subject_id) {
            $studentQuery = DB::table('students')
                ->distinct()
                ->select(
                    'students.*',
                    'users.*',
                    DB::raw("CASE 
                    WHEN scs.subject_id = $subject_id THEN scs.subject_id
                    WHEN class_subjects.subject_id = $subject_id AND class_subjects.type = 'Compulsory' THEN class_subjects.subject_id
                    ELSE NULL
                END AS subject_id")
                )
                ->leftJoin('class_sections', 'class_sections.id', '=', 'students.class_section_id')
                ->leftJoin('class_subjects', 'class_subjects.class_id', '=', 'class_sections.class_id')
                ->leftJoin('subjects', 'class_subjects.subject_id', '=', 'subjects.id')
                ->join('users', 'users.id', '=', 'students.user_id')
                // ->leftJoin('subject_students', 'subject_students.student_id', '=', 'students.id')
                ->leftJoin('student_subjects', 'student_subjects.student_id', '=', 'users.id')
                ->leftJoin('class_subjects as scs', 'scs.id', '=', 'student_subjects.class_subject_id')
                ->where('students.session_year_id',$sessionYear->id)
                ->where(function ($query) use ($subject_id,$sessionYear) {
                    $query->where(function($q) use ($subject_id,$sessionYear) {
                        $q->where('scs.subject_id', $subject_id)
                            ->where('student_subjects.session_year_id',$sessionYear->id);
                    })->orWhere(function ($q) use ($subject_id) {
                            $q->where('class_subjects.subject_id', $subject_id)
                                ->where('class_subjects.type', 'Compulsory');
                    });
                })
                ->when($request->class_section_id, function ($query) use ($request) {
                    $query->where('students.class_section_id', $request->class_section_id);
                })
                ->where('students.school_id', $schoolId)
                ->whereNull('students.deleted_at')
                ->whereNull('users.deleted_at')
                ->whereNull('class_subjects.deleted_at')
                ->when($search, function ($query) use ($search) {
                    $query->where('students.id', 'LIKE', "%$search%")
                        ->orWhereHas('user', function ($q) use ($search) {
                            $q->whereRaw("concat(users.first_name, ' ', users.last_name) LIKE ?", ["%{$search}%"])
                                ->where('deleted_at', NULL);
                        });
                });



            $total = $studentQuery->count();
            $studentQuery->orderBy($sort, $order);
            $attendanceData = $studentQuery->get();
        }

        $attendanceLookup = $existingAttendanceData->keyBy('student_id');
        $rows = [];
        $no = 1;

        foreach ($attendanceData as $row) {

            if ($attendanceLookup->has($row->user_id)) {
                $attendance = $attendanceLookup->get($row->user_id);
                $rows[] = [
                    'id'           => $attendance->id,
                    'no'           => $no,
                    'student_id'   => $attendance->student_id,
                    'user_id'      => $attendance->student_id,
                    'admission_no' => $attendance->user->student->admission_no ?? '',
                    'roll_no'      => $attendance->user->student->roll_number ?? '',
                    'name'         => '<input type="hidden" value="' . $attendance->student_id . '" name="attendance_data[' . $no . '][id]"><input type="hidden" value="' . $attendance->student_id . '" name="attendance_data[' . $no . '][student_id]">' . ($attendance->user->first_name ?? '') . ' ' . ($attendance->user->last_name ?? ''),
                    'type'         => $attendance->type,
                ];
            } else {
                $rows[] = [
                    'id'           => null,
                    'no'           => $no,
                    'student_id'   => $row->user_id,
                    'user_id'      => $row->user_id,
                    'admission_no' => $row->admission_no ?? '',
                    'roll_no'      => $row->roll_number ?? '',
                    'name'         => '<input type="hidden" value="' . $row->user_id . '" name="attendance_data[' . $no . '][id]"><input type="hidden" value="' . $row->user_id . '" name="attendance_data[' . $no . '][student_id]">' . ($row->first_name ?? '') . ' ' . ($row->last_name ?? ''),
                    'type'         => null,
                ];
            }

            $deductionMethod = DB::table('school_settings')
                ->select('name', 'data')
                ->where('name', 'deduction_method')
                ->where('school_id', $schoolId)
                ->first();

            $sufficientType = '';

            $userId = DB::table('students as s')
                ->join('users as u', 'u.id', '=', 's.user_id')
                ->select('s.id', DB::raw("CONCAT(u.first_name, ' ', u.last_name) AS full_name"))
                ->where('u.id', $row->user_id)
                ->first();

            if ($deductionMethod) {
                if ($deductionMethod->data === "1") {
                    $query = DB::table('subject_attendances as sa')
                        ->join('users as u', 'sa.user_id', '=', 'u.id')
                        ->join('credit_system as cs', 'sa.user_id', '=', 'cs.user_id')
                        ->select('cs.user_id as credit_user', DB::raw("CONCAT(u.first_name, ' ', u.last_name) AS full_name"), 'cs.class_id', 'cs.balance', 'cs.credit_amount')
                        ->where('sa.status', 1)
                        ->where('cs.user_id', $row->user_id)
                        ->where('cs.school_id', $schoolId)
                        ->orderBy('full_name', 'DESC')
                        ->first();
                    if($query){
                        $balance = DB::table('credit_system')
                        ->where('user_id', $query->credit_user)
                        ->sum('credit_amount');
    
                        $creditStatus = DB::table('students')
                            ->where('user_id', $query->credit_user)
                            ->value('credit_status');

                        if($creditStatus == 1) {
                            $sufficientType = 'Monthly'; 
                            $studentsInsufficientFunds[] = $query->full_name;
                        } else {
                            if ($balance > 0) {
                                $sufficientType = 'Credit';
                                $studentsInsufficientFunds[] = $query->full_name;
                            } else {
                                $sufficientType = 'Outstanding';
                                $studentsInsufficientFunds[] = $query->full_name;
                            }
                        }
                    } else {
                        $sufficientType = 'Outstanding';
                        $studentsInsufficientFunds[] = $userId->full_name;
                    }

                } else if ($deductionMethod->data === "2") {
                    $package = DB::table('purchase_package as pp')
                        ->join('subject_package as sp', 'pp.package_id', '=', 'sp.id')
                        ->select('pp.id as purchase_id')
                        ->where('pp.student_id', $userId->id)
                        ->where('pp.school_id', $schoolId)
                        ->where('pp.status','!=',1)
                        ->whereRaw("FIND_IN_SET(?, sp.subject_id)", [$subject_id])
                        ->first();

                    if ($package) {
                        $packageUsage = DB::table('package_usage as pu')
                            ->where('pu.student_id', $userId->id)
                            ->where('pu.purchase_package_id', $package->purchase_id)
                            ->orderBy('created_at', 'DESC')
                            ->first();
                        if ($packageUsage && $packageUsage->remaining_session > 0) {
                            $studentsInsufficientFunds[] = $userId->full_name;
                            $sufficientType = 'Package';
                        }
                    }
                } else if ($deductionMethod->data === "3") {
                    $package = DB::table('purchase_package as pp')
                        ->join('subject_package as sp', 'pp.package_id', '=', 'sp.id')
                        ->leftJoin(DB::raw('(SELECT 
                            purchase_package_id, 
                            student_id, 
                            remaining_session 
                            FROM package_usage pu1
                            WHERE created_at = (
                                SELECT MAX(created_at) 
                                FROM package_usage pu2 
                                WHERE pu1.purchase_package_id = pu2.purchase_package_id 
                                AND pu1.student_id = pu2.student_id
                            )
                        ) as pu'), function($join) {
                            $join->on('pp.id', '=', 'pu.purchase_package_id')
                                ->on('pu.student_id', '=', 'pp.student_id');
                        })
                        ->select('pp.id as purchase_id')
                        ->where('pp.student_id', $userId->id)
                        ->where('pp.school_id', $schoolId)
                        ->where('pp.status', '!=', 1)
                        ->whereRaw("FIND_IN_SET(?, sp.subject_id)", [$subject_id])
                        ->where(function($query) {
                            $query->where('pu.remaining_session', '>', 0)
                                ->orWhereNull('pu.remaining_session');
                        })
                        ->first();


                    if ($package) {
                        $packageUsage = DB::table('package_usage as pu')
                        ->where('pu.student_id', $userId->id)
                        ->where('pu.purchase_package_id', $package->purchase_id)
                        ->orderBy('created_at', 'DESC')
                        ->first();
                        if ($packageUsage && $packageUsage->remaining_session > 0) {
                            $studentsInsufficientFunds[] = $userId->full_name;
                            $sufficientType = 'Package';
                        }
                    } 
                    $query = DB::table('subject_attendances as sa')
                        ->join('users as u', 'sa.user_id', '=', 'u.id')
                        ->join('credit_system as cs', 'sa.user_id', '=', 'cs.user_id')
                        ->select('cs.user_id as credit_user', DB::raw("CONCAT(u.first_name, ' ', u.last_name) AS full_name"), 'cs.class_id', 'cs.balance', 'cs.credit_amount')
                        ->where('sa.status', 1)
                        ->where('cs.user_id', $row->user_id)
                        ->where('cs.school_id', $schoolId)
                        ->orderBy('full_name', 'DESC')
                        ->first();
                    if(empty($sufficientType)){
                        if($query){
                            $balance = DB::table('credit_system')
                            ->where('user_id', $query->credit_user)
                            ->sum('credit_amount');
                            $creditStatus = DB::table('students')
                            ->where('user_id', $query->credit_user)
                            ->value('credit_status');

                            if($creditStatus == 1) {
                                $sufficientType = 'Monthly'; 
                                $studentsInsufficientFunds[] = $query->full_name;
                            } else if  ($balance > 0) {
                                $sufficientType = 'Credit';
                                $studentsInsufficientFunds[] = $query->full_name;
                            }else{
                                $sufficientType = 'Outstanding';
                                $studentsInsufficientFunds[] = $query->full_name;
                            }
                        } else {
                            $sufficientType = 'Outstanding';
                            $studentsInsufficientFunds[] = $userId->full_name;
                        }
                    }
                }
            }
            if($sufficientType){
                $rows[$no - 1]['status'] = $sufficientType;
            }
            
            $no++;
        }
        $bulkData['total'] = $total;
        $bulkData['rows'] = $rows;

        return response()->json($bulkData);
    }

    public function attendance_show(Request $request)
    {
        ResponseService::noFeatureThenRedirect('Attendance Management');
        ResponseService::noAnyPermissionThenRedirect(['class-teacher', 'attendance-list']);

        $offset = request('offset', 0);
        $limit = 99999; //hardcoded
        $sort = request('sort', 'student_id');
        $order = request('order', 'ASC');
        $search = request('search');
        $attendanceType = request('attendance_type');

        $class_section_id = request('class_section_id');
        $start_date = date('Y-m-d', strtotime(request('start_date')));
        $end_date = date('Y-m-d', strtotime(request('end_date')));

        $validator = Validator::make($request->all(), ['class_section_id' => 'required', 'start_date' => 'required', 'end_date' => 'required',]);
        if ($validator->fails()) {
            ResponseService::errorResponse($validator->errors()->first());
        }

        $sessionYear = $this->cache->getDefaultSessionYear();

        $sql = $this->attendance->builder()->whereDate('date', '>=', $start_date)->whereDate('date', '<=', $end_date)
            ->where(['class_section_id' => $class_section_id])->with('user.student')
            ->where(function ($query) use ($search) {
                $query->when($search, function ($query) use ($search) {
                    $query->where(function ($query) use ($search) {
                        $query->where('id', 'LIKE', "%$search%")
                            ->orwhere('student_id', 'LIKE', "%$search%")
                            ->orWhereHas('user', function ($q) use ($search) {
                                $q->whereRaw("concat(first_name,' ',last_name) LIKE '%" . $search . "%'")
                                    ->orwhere('first_name', 'LIKE', "%$search%")
                                    ->orwhere('last_name', 'LIKE', "%$search%");
                            })->orWhereHas('user.student', function ($q) use ($search) {
                                $q->where('admission_no', 'LIKE', "%$search%")
                                    ->orwhere('id', 'LIKE', "%$search%")
                                    ->orwhere('user_id', 'LIKE', "%$search%")
                                    ->orwhere('roll_number', 'LIKE', "%$search%");
                            });
                    });
                });
            })
            ->when($attendanceType != null, function ($query) use ($attendanceType) {
                $query->where('type', $attendanceType);
            });
        $sql = $sql->whereHas('user.student', function ($q) use ($sessionYear) {
            $q->where('session_year_id', $sessionYear->id);
        });
        $total = $sql->count();

        $sql->orderBy($sort, $order)->skip($offset)->take($limit);
        $res = $sql->get();

        $bulkData = array();
        $bulkData['total'] = $total;
        $rows = array();
        $collect = collect();
        $no = 1;

        foreach ($res as $row) {
            $presents = 0;
            $absents = 0;
            $percentage = 100;
            if ($row['type'] == 3) { //exclude holiday in calculation
                continue;
            }
            if ($row['type'] == 1) {
                $presents = 1;
            } else if ($row['type'] == 0) {
                $absents = 1;
                $percentage = 0;
            }
            if ($collect->isEmpty()) {
                $row['no'] = $no++;
                $row['total_days'] = 1;
                $row['presents'] = $presents;
                $row['absents'] = $absents;
                $row['percentage'] = $percentage;
                $collect->push($row);
            } else {
                $student = $collect->firstWhere('student_id', $row['student_id']);
                if ($student == null) {
                    $row['no'] = $no++;
                    $row['total_days'] = 1;
                    $row['presents'] += $presents;
                    $row['absents'] += $absents;
                    $row['percentage'] = $percentage;
                    $collect->push($row);
                } else {
                    $student['total_days'] += 1;
                    $student['presents'] += $presents;
                    $student['absents'] += $absents;
                    if (($student['total_days'] - $student['absents']) == 0) {
                        $student['percentage'] = 0;
                    } else {
                        $student['percentage'] = round($student['presents'] / $student['total_days'] * 100, 0);
                    }
                }
            }
        }
        // $no = 1;
        // foreach ($collect as $row) {
        //     $tempRow = $row->toArray();
        //     $tempRow['no'] = $no++;
        //     $rows[] = $tempRow;
        // }
        $bulkData['rows'] = $collect->toArray();
        return response()->json($bulkData);
    }

    public function showAttendance(Request $request)
    {
        ResponseService::noFeatureThenRedirect('Attendance Management');
        ResponseService::noAnyPermissionThenRedirect(['class-teacher', 'attendance-list']);
        // Get request parameters
        $offset = $request->input('offset', 0);
        $limit = $request->input('limit', 9999);
        $sort = $request->input('sort', 'id');
        $order = $request->input('order', 'DESC');
        $search = $request->input('search');
        $subject_id = $request->input('subject_id');
        $teacher_id = $request->input('teacher_id');
        $schoolId = Auth::user()->school_id;
        $start_date = Carbon::createFromFormat('d-m-Y', $request->input('start_date'))->startOfDay();
        $end_date = Carbon::createFromFormat('d-m-Y', $request->input('end_date'))->endOfDay();

        // Build the query
        // $query = SubjectAttendance::query();

        // // Join with users table to get student names

        // $query->join('users', 'subject_attendances.user_id', '=', 'users.id')
        //     ->leftJoin('subjects', 'subject_attendances.subjects_id', '=', 'subjects.id')
        //     ->select('subject_attendances.*', 'users.first_name', 'users.last_name', DB::raw('COALESCE(subjects.name, "All Day") as subject_name'))
        //     ->where('subject_attendances.school_id',$schoolId)
        //     ->whereNull('users.deleted_at');
        $query = SubjectAttendance::query()
            ->join('users', 'subject_attendances.user_id', '=', 'users.id') // Join with users table to get student names
            ->join('students', 'students.user_id', '=', 'users.id') // Join with users table to get student names
            ->leftJoin('subjects', 'subject_attendances.subjects_id', '=', 'subjects.id')
            ->leftJoin('staffs', 'subject_attendances.teacher_id', '=', 'staffs.id') // Left join with subjects table
            ->leftJoin('users as t', 'subject_attendances.teacher_id', '=', 't.id') // Join with users table again to get teacher details
            ->select(
                'subject_attendances.*',
                'users.first_name as student_first_name',
                'users.last_name as student_last_name',
                DB::raw('COALESCE(subjects.name, "All Day") as subject_name'),
                DB::raw('COALESCE(CONCAT(t.first_name, " ", t.last_name), "No Teacher Assigned") as teacher_name'), // Teacher name
                't.id as teacher_id'
            )
            ->where('subject_attendances.school_id', $schoolId)
            ->whereNull('users.deleted_at');

        // Apply search filters
        if ($search) {
            $searchTerms = explode(' ', $search);

            $query->where(function ($q) use ($searchTerms) {
                foreach ($searchTerms as $term) {
                    $q->where(function ($q) use ($term) {
                        $q->where('users.first_name', 'LIKE', "%$term%")
                            ->orWhere('users.last_name', 'LIKE', "%$term%")
                            ->orWhere('t.first_name', 'LIKE', "%$term%") // Include teacher name in search
                            ->orWhere('t.last_name', 'LIKE', "%$term%"); // Include teacher name in search
                    });
                }
            });
        }

        if ($subject_id == '0') {
            $query->whereNull('subject_attendances.subjects_id');
        } else if ($subject_id) {
            $query->where('subject_attendances.subjects_id', $subject_id);
        }

        if(Auth::user()->hasRole('Teacher')){
            $query->whereExists(function($subquery) {
                $subquery->select(DB::raw(1))
                    ->from('subject_teachers')
                    ->whereColumn('subject_teachers.subject_id', 'subject_attendances.subjects_id')
                    ->where('subject_teachers.teacher_id', Auth::user()->id);
            });
        }
        // if ($teacher_id == '0') {
        //     $query->whereNull('subject_attendances.teacher_id');
        // } else if ($teacher_id) {
        //     $query->where('subject_attendances.teacher_id', $teacher_id);
        // }

        // Apply date range filter if provided
        if ($start_date && $end_date) {
            $query->whereBetween('subject_attendances.date', [$start_date, $end_date]);
        }

        // Get the total count of records (after applying filters)
        $total = $query->count();

        // Apply sorting, offset, and limit
        $query->orderBy($sort, $order)->skip($offset)->take($limit);

        // Get the records
        $res = $query->get();
    
        // Prepare the response data
        $bulkData = [];
        $bulkData['total'] = $total;
        $rows = [];
        $no = 1;

        foreach ($res as $row) {
            $operate = '';
            $operate .= BootstrapTableService::editButton(route('attendance.updateStudentAttendance', $row->id));
            $operate .= BootstrapTableService::deleteButton(route('attendance.destroy', $row->id));
            $tempRow = $row->toArray();
            $tempRow['no'] = $no++;
            $tempRow['student_name'] = $row->student_first_name . ' ' . $row->student_last_name;
            $tempRow['subject_name'] = $row->subject_name;
            $tempRow['operate'] = $operate;
            $tempRow['clock_in'] = $row->clock_in ? Carbon::parse($row->clock_in)->format('H:i:s') : '-';
            $tempRow['clock_out'] = $row->clock_out ? Carbon::parse($row->clock_out)->format('H:i:s') : '-';
            $tempRow['teacher_id']=$row->teacher_id;
            $tempRow['teacher_name'] = $row->teacher_name;
            $tempRow['in_temperature'] = $tempRow['in_temperature'] ? number_format((float)$tempRow['in_temperature'], 1, '.', '') : '-';
            $tempRow['out_temperature'] = $tempRow['out_temperature'] ? number_format((float)$tempRow['out_temperature'], 1, '.', '') : '-';
            // Determine status based on type (example logic)
            // Determine status based on type (example logic)
            if ($row->status == 1) {
                $tempRow['status'] = 'Present';
            } elseif ($row->status == 2) {
                $tempRow['status'] = 'Late';
            } elseif ($row->status == 3) {
                $tempRow['status'] = 'Replacement';
            } else {
                $tempRow['status'] = 'Absent';
            }
            $rows[] = $tempRow;
        }

        $bulkData['rows'] = $rows;

        return response()->json($bulkData);
    }

    public function updateStudentAttendance(Request $request)
    {
        ResponseService::noFeatureThenRedirect('Attendance Management');
        ResponseService::noAnyPermissionThenRedirect(['class-teacher', 'attendance-list']);

        $request->validate([
            'id'              => 'required|exists:subject_attendances,id',
            'date'            => 'required|date',
            'clock_in'        => 'nullable|date_format:H:i',
            'clock_out'       => 'nullable|date_format:H:i',
            'status'          => 'required',
            'remark_picture'  => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'in_temperature'  => 'nullable',
            'out_temperature' => 'nullable'
        ]);

        try {
            DB::beginTransaction();

            $subjectAttendance = SubjectAttendance::findOrFail($request->id);
            $subjectAttendance->date = $request->date;

            // Handle clock_in
            if (!empty($request->clock_in)) {
                $clockInDateTime = Carbon::createFromFormat('Y-m-d H:i', $request->date . ' ' . $request->clock_in);
                $subjectAttendance->clock_in = $clockInDateTime;
            } else {
                $subjectAttendance->clock_in = null; // or whatever default behavior you need
            }

            // Handle clock_out
            if (!empty($request->clock_out)) {
                $clockOutDateTime = Carbon::createFromFormat('Y-m-d H:i', $request->date . ' ' . $request->clock_out);
                $subjectAttendance->clock_out = $clockOutDateTime;
            } else {
                $subjectAttendance->clock_out = null; // or whatever default behavior you need
            }

            // Handle status
            $subjectAttendance->status = $request->status;

            // Handle student_remark
            $subjectAttendance->student_remark = $request->student_remark;

            // Handle remark_picture
            if ($request->hasFile('remark_picture')) {
                $file = $request->file('remark_picture');
                $path = $file->store('remark_pictures', 'public');
                $subjectAttendance->remark_picture = $path;
            }

            // Calculate total time if both clock_in and clock_out are provided
            if ($subjectAttendance->clock_in && $subjectAttendance->clock_out) {
                $interval = $subjectAttendance->clock_out->diff($subjectAttendance->clock_in);
                $subjectAttendance->total_time = $interval->format('%H:%I:%S');
            } else {
                $subjectAttendance->total_time = null; // or set to default value
            }
            if($request->in_temperature){
                $subjectAttendance->in_temperature = $request->in_temperature;
            }
            if($request->out_temperature){
                $subjectAttendance->out_temperature = $request->out_temperature;
            }
            // dd($request->all());
        
            if($request->teacher_id){
                $subjectAttendance->teacher_id=$request->teacher_id;
            }

            $subjectAttendance->save();
            $oldAttendance = DB::table('attendances')
                             ->where('date',date('Y-m-d', strtotime($request->date)))
                             ->where('student_id',$subjectAttendance->user_id)
                             ->first();
            if ($oldAttendance) {
                $type = $request->status == 0 ? 0 : 1;
                $oldAttendanceData = [
                    'type' => $type,
                    'date' => $request->date,
                    'updated_at' => now(),
                ];
                $result = DB::table('attendances')
                    ->where('id', $oldAttendance->id)
                    ->update($oldAttendanceData);
            }
            DB::commit();
            ResponseService::successResponse('Data Updated Successfully');
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "Leave Controller -> Update Method");
            ResponseService::errorResponse();
        }
    }

    public function destroy($id)
    {
        ResponseService::noFeatureThenRedirect('Attendance Management');
        ResponseService::noAnyPermissionThenRedirect(['class-teacher', 'attendance-list']);
        try {
            DB::beginTransaction();
            $subjectAttendances = DB::table('subject_attendances')->where('id',$id)->first();
            DB::table('subject_attendances')->where('id', $id)->delete();
            if($subjectAttendances && $subjectAttendances->subjects_id == null){
                $oldData = DB::table('attendances')
                ->where('date', $subjectAttendances->date)
                ->where('student_id',$subjectAttendances->user_id)
                ->first();
                if ($oldData) {
                    DB::table('attendances')->where('id', $oldData->id)->delete();
                }
            } else if ($subjectAttendances->status != 0) {
                $deductionMethod = DB::table('school_settings')
                ->select('name', 'data')
                ->where('name', 'deduction_method')
                ->where('school_id', Auth::user()->school_id)
                ->first();
                if($deductionMethod->data != 0){
                    $subject = $this->subject->builder()->where('id',$subjectAttendances->subjects_id)->first();
                    
                    // First check for package refund
                    $userId = DB::table('students')->where('user_id', $subjectAttendances->user_id)->value('id');
                    $package = DB::table('purchase_package as pp')
                        ->join('subject_package as sp', 'pp.package_id', '=', 'sp.id')
                        ->select('pp.id as purchase_id')
                        ->where('pp.student_id', $userId)
                        ->whereIn('pp.status', [0, 2])
                        ->whereRaw("FIND_IN_SET(?, sp.subject_id)", [$subjectAttendances->subjects_id])
                        ->first();
                    
                    $packageRefundProcessed = false;
                    
                    if ($package) {
                        $packageUsage = DB::table('package_usage as pu')
                            ->where('pu.student_id', $userId)
                            ->where('pu.purchase_package_id', $package->purchase_id)
                            ->orderBy('created_at', 'DESC')
                            ->first();
                        
                        if ($packageUsage) {
                            $packageRefundProcessed = true;
                            $remainingSessions = $packageUsage->remaining_session + 1;
                            
                            DB::table('package_usage')->insert([
                                'school_id' => Auth::user()->school_id,
                                'purchase_package_id' => $package->purchase_id,
                                'student_id' => $userId,
                                'deduct' => 1,
                                'remaining_session' => $remainingSessions,
                                'created_at' => Carbon::now(),
                                'updated_at' => Carbon::now(),
                            ]);
                            
                            // Send notification about session refund
                            $userFullName = DB::table('users')
                                ->where('id', $subjectAttendances->user_id)
                                ->value(DB::raw("CONCAT(first_name, ' ', last_name) AS full_name"));
                            
                            $notifyUser = array($subjectAttendances->user_id);
                            $guardianId = DB::table('students')->where('user_id', $subjectAttendances->user_id)->value('guardian_id');
                            $notifyUser[] = $guardianId;
                            
                            $title = 'Session Refund';
                            $type = 'session_refund';
                            $body = "1 session has been refunded for {$subject->name} on " . Carbon::parse($subjectAttendances->date)->format('d/m/Y') . ". You now have {$remainingSessions} sessions remaining.";
                            send_notification($notifyUser, $title, $body, $type);
                        }
                    }
                    
                    // Handle credit system refund only if no package refund was processed
                    if (!$packageRefundProcessed) {
                        $creditSystem = DB::table('credit_system')->where('user_id',$subjectAttendances->user_id)->orderByDesc('created_at')->first();
                        if($creditSystem){
                            $balance = $creditSystem->balance + $subject->commission;
                            $data = [
                                'class_id' => $creditSystem->class_id,
                                'user_id'  => $subjectAttendances->user_id,
                                'credit_amount' => $subject->commission ?? 0,
                                'balance'       => $balance,
                                'detail'        => 'Credit Refund ('.$subject->name.' '.Carbon::parse($subjectAttendances->date)->format('d/m/Y').')',
                                'school_id'     => Auth::user()->school_id,
                                'created_at'    => now(),
                                'updated_at'    => now()
                            ];
                            $result = DB::table('credit_system')->insert($data);
                            $notifyUser = array($subjectAttendances->user_id);
                            if($result){
                                $guardianId = DB::table('students')->where('user_id',$subjectAttendances->user_id)->value('guardian_id');
                                $notifyUser[] = $guardianId;
                                $title = 'Credit Refund';
                                $type = 'Credit Refund'; 
                                $body = "{$subject->name}'s class Attendance on " . Carbon::parse($subjectAttendances->date)->format('d/m/Y') . " has been refunded. New balance: {$balance}.";
                                send_notification($notifyUser, $title, $body, $type); 
                            }
                        }
                    }
                }
            }

            
            DB::commit();
            ResponseService::successResponse('Data Deleted Successfully');
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "Attendance Controller -> Destroy Method");
            ResponseService::errorResponse();
        }
    }

    public function attendanceSummary(Request $request)
    {
        $schoolId = Auth::user()->school_id;
        $searchParams = [];

        $dateFilter = "";
        if ($request->start_date && !$request->end_date) {
            $dateFilter .= " AND created_at >= ? ";
            $searchParams[] = date('Y-m-d', strtotime($request->start_date)) . " 00:00:00";
        } else if (!$request->start_date && $request->end_date) {
            $dateFilter .= " AND created_at <= ? ";
            $searchParams[] = date('Y-m-d', strtotime($request->end_date)) . " 23:59:59";
        } else if ($request->start_date && $request->end_date) {
            $dateFilter .= " AND (created_at BETWEEN ? AND ?) ";
            $searchParams[] = date('Y-m-d', strtotime($request->start_date)) . " 00:00:00";
            $searchParams[] = date('Y-m-d', strtotime($request->end_date)) . " 23:59:59";
        }

        $subjectFilter = "";
        if (isset($request->subject_id)) {
            if ($request->subject_id == 0) {
                $subjectFilter .= " AND subjects_id IS NULL ";
            } elseif (!empty($request->subject_id)) {
                $subjectFilter .= " AND subjects_id = ? ";
                $searchParams[] = $request->subject_id;
            }
        }
        $studentFilter = "";
        if (isset($request->search)) {
            $studentName = '%' . $request->search . '%';
            $studentFilter .= " 
                            AND EXISTS (
                            SELECT 1
                            FROM users
                            WHERE subject_attendances.user_id = users.id
                            AND users.school_id = ?
                            AND (
                                users.first_name LIKE ? 
                                OR users.last_name LIKE ? 
                                OR CONCAT(users.first_name, ' ', users.last_name) LIKE ?
                            )
                        )";
            $searchParams[] = $schoolId;
            $searchParams[] = $studentName;
            $searchParams[] = $studentName;
            $searchParams[] = $studentName;
        }

        $schoolFilter = " 
            AND EXISTS (
            SELECT 1
            FROM users
            WHERE subject_attendances.user_id = users.id
            AND subject_attendances.school_id = ?
            AND users.deleted_at IS NULL
        ) ";
        $searchParams[] = $schoolId;

        $query = "
            SELECT
            COUNT(CASE WHEN status = 1 THEN 1 END) AS total_present,
            COUNT(CASE WHEN status = 0 THEN 1 END) AS total_absent,
            COUNT(CASE WHEN status = 2 THEN 1 END) AS total_late,
            COUNT(CASE WHEN status = 3 THEN 1 END) AS total_replacement
            FROM subject_attendances
            WHERE 1=1
            {$dateFilter}
            {$subjectFilter}
            {$studentFilter}
            {$schoolFilter}
        ";
        $totalResult = DB::select($query, $searchParams);

        $attendance_data = [
            'total_present' => $totalResult[0]->total_present,
            'total_absent' => $totalResult[0]->total_absent,
            'total_late' => $totalResult[0]->total_late,
            'total_replacement' => $totalResult[0]->total_replacement,
        ];

        return response()->json([
            'error'   => false,
            'message' => "Attendance Summary",
            'attendance_data' => $attendance_data
        ]);
    }

    public function loadStudentSubjectAttendance(Request $request){
        \Log::info('Loading student subject attendance', [
            'subject_id' => $request->s,
            'school_id' => Auth::user()->school_id
        ]);

        $subject_id = $request->s;
        $schoolId = Auth::user()->school_id;
        $sessionYear = $this->cache->getDefaultSessionYear();
        $students = DB::table('students as s')
            ->join('users as u', 's.user_id', '=', 'u.id')
            ->where('s.school_id', $schoolId)
            ->whereNull('u.deleted_at')
            ->where('s.session_year_id',$sessionYear->id)
            ->select('u.id', DB::raw("CONCAT(u.first_name, ' ', u.last_name) AS full_name"))
            ->get();
        if(!empty($subject_id)){
            foreach($students as $s){
                $studentCreditStatus = DB::table('students')
                    ->where('user_id', $s->id)
                    ->value('credit_status');

                \Log::info('Credit status check', [
                    'student_id' => $s->id,
                    'credit_status' => $studentCreditStatus
                ]);

                if ($studentCreditStatus == 1) {
                    $sufficientType = 'Monthly'; 
                    $s->full_name .= " (" . $sufficientType . ")";
                    continue;  
                }

                $sufficientType = '';
                $deductionMethod = DB::table('school_settings')
                ->select('name', 'data')
                ->where('name', 'deduction_method')
                ->where('school_id', $schoolId)
                ->first();
                $userId = DB::table('students as s')
                    ->join('users as u', 'u.id', '=', 's.user_id')
                    ->select('s.id', DB::raw("CONCAT(u.first_name, ' ', u.last_name) AS full_name"))
                    ->where('u.id', $s->id)
                    ->first();
        
                if ($deductionMethod) {
                    if ($deductionMethod->data === "1") {
                        \Log::info('Deduction Method 1 Check', [
                            'student_id' => $s->id,
                            'student_name' => $s->full_name
                        ]);

                        $query = DB::table('subject_attendances as sa')
                            ->join('users as u', 'sa.user_id', '=', 'u.id')
                            ->join('credit_system as cs', 'sa.user_id', '=', 'cs.user_id')
                            ->select('cs.user_id as credit_user', DB::raw("CONCAT(u.first_name, ' ', u.last_name) AS full_name"), 'cs.class_id', 'cs.balance', 'cs.credit_amount')
                            ->where('sa.status', 1)
                            ->where('cs.user_id', $s->id)
                            ->where('cs.school_id', $schoolId)
                            ->orderBy('full_name', 'DESC')
                            ->first();

                        if($query){
                            $balance = DB::table('credit_system')
                            ->where('user_id', $query->credit_user)
                            ->sum('credit_amount');
        
                            $creditStatus = DB::table('students')
                                ->where('user_id', $query->credit_user)
                                ->value('credit_status');

                            if($creditStatus == 1) {
                                $sufficientType = 'Monthly';  
                                $studentsInsufficientFunds[] = $query->full_name;
                            } else {
                                if ($balance > 0) {
                                    $sufficientType = 'Credit';
                                    $studentsInsufficientFunds[] = $query->full_name;
                                } else {
                                    $sufficientType = 'Outstanding';
                                    $studentsInsufficientFunds[] = $query->full_name;
                                }
                            }
                        } else {
                            $sufficientType = 'Outstanding';
                            $studentsInsufficientFunds[] = $userId->full_name;
                        }
                    } else if ($deductionMethod->data === "2") {
                        $package = DB::table('purchase_package as pp')
                            ->join('subject_package as sp', 'pp.package_id', '=', 'sp.id')
                            ->select('pp.id as purchase_id')
                            ->where('pp.student_id', $userId->id)
                            ->where('pp.school_id', $schoolId)
                            ->where('pp.status','!=',1)
                            ->whereRaw("FIND_IN_SET(?, sp.subject_id)", [$subject_id])
                            ->first();
        
                        if ($package) {
                            $packageUsage = DB::table('package_usage as pu')
                                ->where('pu.student_id', $userId->id)
                                ->where('pu.purchase_package_id', $package->purchase_id)
                                ->orderBy('created_at', 'DESC')
                                ->first();
                            if ($packageUsage && $packageUsage->remaining_session > 0) {
                                $studentsInsufficientFunds[] = $userId->full_name;
                                $sufficientType = 'Package';
                            }
                        }
                    } else if ($deductionMethod->data === "3") {
                        $package = DB::table('purchase_package as pp')
                            ->join('subject_package as sp', 'pp.package_id', '=', 'sp.id')
                            ->select('pp.id as purchase_id')
                            ->where('pp.student_id', $userId->id)
                            ->where('pp.school_id', $schoolId)
                            ->where('pp.status','!=',1)
                            ->whereRaw("FIND_IN_SET(?, sp.subject_id)", [$subject_id])
                            ->first();
        
                        if ($package) {
                            $packageUsage = DB::table('package_usage as pu')
                            ->where('pu.student_id', $userId->id)
                            ->where('pu.purchase_package_id', $package->purchase_id)
                            ->orderBy('created_at', 'DESC')
                            ->first();
                            if ($packageUsage && $packageUsage->remaining_session > 0) {
                                $studentsInsufficientFunds[] = $userId->full_name;
                                $sufficientType = 'Package';
                            }
                        } 
                        $query = DB::table('credit_system as cs')
                            ->join('users as u', 'cs.user_id', '=', 'u.id')
                            ->select('cs.user_id as credit_user', DB::raw("CONCAT(u.first_name, ' ', u.last_name) AS full_name"), 'cs.class_id', 'cs.balance', 'cs.credit_amount')
                            ->where('cs.user_id', $s->id)
                            ->where('cs.school_id', $schoolId)
                            ->orderBy('full_name', 'DESC')
                            ->first();
                        if(empty($sufficientType)){
                            if($query){
                                $balance = DB::table('credit_system')
                                ->where('user_id', $query->credit_user)
                                ->sum('credit_amount');
                                $creditStatus = DB::table('students')
                                ->where('user_id', $query->credit_user)
                                ->value('credit_status');

                                if($creditStatus == 1) {
                                    $sufficientType = 'Monthly'; 
                                    $studentsInsufficientFunds[] = $query->full_name;
                                }else{
                                    if ($balance > 0) {
                                        $sufficientType = 'Credit';
                                        $studentsInsufficientFunds[] = $query->full_name;
                                    }else{
                                        $sufficientType = 'Outstanding';
                                        $studentsInsufficientFunds[] = $query->full_name;
                                    }
                                }
                                    
                            } else {
                                $sufficientType = 'Outstanding';
                                $studentsInsufficientFunds[] = $userId->full_name;
                            }
                        }
                    }
                }
                if($sufficientType){
                    $s->full_name .= " (" . $sufficientType . ")";  // This is how other statuses work
                }
            }
        }
        return response()->json($students);
    }

    public function storeAddStudent(Request $request)
    {
        // Automatically get the school_id from the logged-in user
        $schoolId = Auth::user()->school_id;
        
        $request->validate([
            'subjects_id' => 'nullable',
            'user_id' => 'required',
            'date' => 'required|date',
            'clock_in' => 'nullable|date_format:H:i',
            'clock_out' => 'nullable|date_format:H:i',
            'teacher_id' => 'nullable',
            'status' => 'required',
        ]);

        $subjectId = $request->subjects_id !== '0' ? $request->subjects_id : null;
        $teacherId = $request->teacher_id !== '0' ? $request->teacher_id : null;
        // $userId = $request->user_id;
        $date = $request->date;
        $clock_in = $request->clock_in;
        $clock_out = $request->clock_out;
        $status = $request->status;

        $subject = DB::table('subjects')
            ->select('name', 'commission', 'commission_month')
            ->where('id', $subjectId)
            ->first();
        $school = DB::table('schools')
            ->select('*')
            ->where('id', $schoolId)
            ->first();
        $teacherGroup = DB::table('user_group_details')
            ->where('subject_id', $subjectId)
            ->where('teacher_id', $teacherId) // Add this condition
            ->first();
        $studentsInsufficientFunds = [];

        foreach($request->user_id as $u){
            $commissionType = null;
            $commissionAmount = null;
            
            if ($teacherGroup) {
                $groupId = $teacherGroup->group_id;
                $userGroup = DB::table('user_groups')->where('id', $groupId)->first();
                if ($userGroup) {
                    $commissionType = $userGroup->commission_type;
                    $commissionAmount = $userGroup->commission_amount;
                }
            }
            $user = DB::table('users')
                ->select('users.id', 'users.first_name', 'users.last_name')
                ->where('users.id', $u)
                ->first();
            if (!$user) {
                return redirect()->back()->with('error','Invalid User');
            }
            
            // Check if one-time attendance is enabled (array 4 in school_settings)
            $school_setting = DB::table('school_settings')
            ->where('school_id', $schoolId)
            ->where('name', 'attendance_setting')
            ->where('data', 'LIKE', '%4%')
            ->exists();

            if ($school_setting) {
            $already_marked = DB::table('subject_attendances')
                ->where('school_id', $schoolId)
                ->where('user_id', $u)
                ->whereDate('created_at', Carbon::parse($date)->toDateString())
                ->exists();

            if ($already_marked) {
                return redirect()->back()->with('error', 'Attendance has already been marked today for '.$user->first_name.' '.$user->last_name);
            }
        }

            DB::beginTransaction();
            $totalTime = null;
            $student = DB::table('students')
            ->where('user_id',$u)
            ->get();

            $class= DB::table('class_sections')
            ->where('id',$student[0]->class_section_id)
            ->select('class_id')
            ->first();
            $classId = $class->class_id;

            $clockInPoints = DB::table('rewards_category')
            ->where('school_id', $school->id)
            ->where('category_name','Clock In')
            ->where('is_default',1)
            ->select('points_amount','mode','id')
            ->orderBy('updated_at', 'desc')
            ->first();

            $clockOutPoints = DB::table('rewards_category')
            ->where('school_id', $school->id)
            ->where('category_name','Clock Out')
            ->where('is_default',1)
            ->select('points_amount','mode','id')
            ->orderBy('updated_at', 'desc')
            ->first();
    

            if($clock_in){
                $clockInTime = Carbon::parse($clock_in);
                if ($clockInPoints && $clockInPoints->points_amount > 0){
                    $points = DB::table('rewards')
                    ->where('school_id',$school->id)
                    ->where('class_id',$classId)
                    ->where('student_id',$student[0]->id)
                    ->select('score_total','reward_point_total')
                    ->orderBy('updated_at', 'desc')
                    ->first();
                

                if ($points) {
                    $scoreTotal = $points->score_total;
                    $rewardPointTotal = $points->reward_point_total;
                } else {
                    $scoreTotal = 0;
                    $rewardPointTotal = 0;
                }

                $updateScoreReward = array(
                    'school_id' => $school->id,
                    'class_id' =>$classId,
                    'student_id' => $student[0]->id,
                    'score_amount' => $clockInPoints->points_amount,
                    'score_total' => $scoreTotal + $clockInPoints->points_amount,
                    'remark' => 'Clock In',
                    'category_id' => $clockInPoints->id
                );

                 // update reward points if mode 1
                 $updateScoreReward['reward_point_amount'] = $clockInPoints->mode == 1 ? $clockInPoints->points_amount : 0;
                 $updateScoreReward['reward_point_total'] = $clockInPoints->mode == 1 ? $rewardPointTotal + $clockInPoints->points_amount : $rewardPointTotal;

                 $result = DB::table('rewards')->insert($updateScoreReward);
            }

            }
            if($clock_out){
                $clockOutTime = Carbon::parse($clock_out);
                            
                if ($clockOutPoints && $clockOutPoints->points_amount > 0){
                    $points = DB::table('rewards')
                    ->where('school_id',$school->id)
                    ->where('class_id',$classId)
                    ->where('student_id',$student[0]->id)
                    ->select('score_total','reward_point_total')
                    ->orderBy('updated_at', 'desc')
                    ->first();

                    if ($points) {
                        $scoreTotal = $points->score_total;
                        $rewardPointTotal = $points->reward_point_total;
                    } else {
                        $scoreTotal = 0;
                        $rewardPointTotal = 0;
                    }

                    $updateScoreReward = array(
                        'school_id' => $school->id,
                        'class_id' =>$classId,
                        'student_id' => $student[0]->id,
                        'score_amount' => $clockOutPoints->points_amount,
                        'score_total' => $scoreTotal + $clockOutPoints->points_amount,
                        'remark' => 'Clock In',
                        'category_id' => $clockOutPoints->id
                    );

                    $updateScoreReward['reward_point_amount'] = $clockOutPoints->mode == 1 ? $clockOutPoints->points_amount : 0;
                    $updateScoreReward['reward_point_total'] = $clockOutPoints->mode == 1 ? $rewardPointTotal + $clockOutPoints->points_amount : $rewardPointTotal;
   
                    $result = DB::table('rewards')->insert($updateScoreReward);

                }
            }
            if ($clock_in && $clock_out) {
                $clockInTime = Carbon::parse($clock_in);
                $clockOutTime = Carbon::parse($clock_out);

                // Check if clock_out is earlier in the day than clock_in
                if ($clockOutTime < $clockInTime) {
                    // For overnight, add 24 hours (1 day) to the clock_out time
                    $adjustedClockOutTime = (clone $clockOutTime)->addDay();
                    $diffInMinutes = $clockInTime->diffInMinutes($adjustedClockOutTime);
                    $hours = floor($diffInMinutes / 60);
                    $minutes = $diffInMinutes % 60;
                    $totalTime = sprintf("%02d:%02d:00", $hours, $minutes);
                } else {
                    // Normal same-day calculation
                    $totalTime = $clockOutTime->diff($clockInTime)->format('%H:%I:%S');
                }

                if ($clockOutPoints && $clockOutPoints->points_amount > 0 || $clockInPoints && $clockInPoints->points_amount > 0){
                    $points = DB::table('rewards')
                    ->where('school_id',$school->id)
                    ->where('class_id',$classId)
                    ->where('student_id',$student[0]->id)
                    ->select('score_total','reward_point_total')
                    ->orderBy('updated_at', 'desc')
                    ->first();

                    if ($points) {
                        $scoreTotal = $points->score_total;
                        $rewardPointTotal = $points->reward_point_total;
                    } else {
                        $scoreTotal = 0;
                        $rewardPointTotal = 0;
                    }

                    $updateScoreRewardClockIn = [
                        'school_id' => $school->id,
                        'class_id' => $classId,
                        'student_id' => $student[0]->id,
                        'score_amount' => $clockInPoints->points_amount,
                        'score_total' => $scoreTotal + $clockInPoints->points_amount,
                        'remark' => 'Clock In',
                        'category_id' => $clockInPoints->id,
                        'reward_point_amount' => $clockInPoints->mode == 1 ? $clockInPoints->points_amount : 0,
                        'reward_point_total' => $clockInPoints->mode == 1 ? $rewardPointTotal + $clockInPoints->points_amount : $rewardPointTotal,
                    ];

                    $scoreTotal += $clockInPoints->points_amount;
                    $rewardPointTotal += $clockInPoints->mode == 1 ? $clockInPoints->points_amount : 0;

                    $updateScoreRewardClockOut = [
                        'school_id' => $school->id,
                        'class_id' => $classId,
                        'student_id' => $student[0]->id,
                        'score_amount' => $clockOutPoints->points_amount,
                        'score_total' => $scoreTotal + $clockOutPoints->points_amount,
                        'remark' => 'Clock Out',
                        'category_id' => $clockOutPoints->id,
                        'reward_point_amount' => $clockOutPoints->mode == 1 ? $clockOutPoints->points_amount : 0,
                        'reward_point_total' => $clockOutPoints->mode == 1 ? $rewardPointTotal + $clockOutPoints->points_amount : $rewardPointTotal,
                    ];

                    DB::table('rewards')->insert($updateScoreRewardClockIn);
                    DB::table('rewards')->insert($updateScoreRewardClockOut);
                }
            }
            // Prepare attendance data
            $attendanceData = [
                'school_id' => $schoolId,
                'subjects_id' => $subjectId,
                'teacher_id' => $teacherId,
                'user_id' => $u,
                'status' => $status,
                'date' => Carbon::parse($date),
                'clock_in' => $clock_in ? $clockInTime : null,
                'clock_out' => $clock_out ? $clockOutTime : null,
                'total_time' => $totalTime ?? null,
                'fees_per_section' => $subject->commission ?? null, 
                'fees_per_month' => $subject->commission_month ?? null,
                'commission_typee' => $commissionType, 
                'commission_amountt' => $status == 0 ? 0 : ($commissionAmount ?? null), 
            ];
            if(!isset($subjectId)){
                $attendanceData['in_temperature'] = $request->in_temperature;
                $attendanceData['out_temperature'] = $request->out_temperature;
            }
            // if($subjectId)
            $id = DB::table('subject_attendances')->insertGetId($attendanceData);
            if (empty($subjectId)) {
                $student = DB::select('SELECT * FROM students JOIN users ON users.id = students.user_id WHERE users.id = ?', [$u]);
                $defaultSession = DB::select('SELECT * FROM session_years WHERE `school_id`= ? AND `default` = 1 LIMIT 1', [$schoolId]);
                if ($student) {
                    $oldAttendanceTable = [
                        'class_section_id' => $student[0]->class_section_id,
                        'student_id' => $u,
                        'session_year_id' => $defaultSession[0]->id,
                        'type' => $status == 0 ? 0 : 1,
                        'date' => Carbon::parse($date)->toDateString(),
                        'school_id' => $schoolId,
                        'subject_attendance_id' => $id,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];
                    $existingOldRecord = DB::table('attendances')
                        ->where('class_section_id', $student[0]->class_section_id)
                        ->where('student_id', $u)
                        ->where('session_year_id', $defaultSession[0]->id)
                        ->where('date', Carbon::parse($date)->toDateString())
                        ->first();
    
                    if ($existingOldRecord) {
                        DB::table('attendances')
                            ->where('id', $existingOldRecord->id)
                            ->update($oldAttendanceTable);
                    } else {
                        DB::table('attendances')->insert($oldAttendanceTable);
                    }
                }
            }
            $deductionMethod = DB::table('school_settings')
            ->select('name', 'data')
            ->where('name', 'deduction_method')
            ->where('school_id', $schoolId)
            ->first();
            $notifyDeduct=0;
            $notifysessionDeduct=0;


            $userFullName = DB::table('users')
                            ->where('id',$u)
                            ->value(DB::raw("CONCAT(first_name, ' ', last_name) AS full_name"));
            $subject = DB::table('subjects')
                        ->where('id',$subjectId)
                        ->first();
            $student = DB::table('students as s')
                        ->join('class_sections as cs','cs.id','=','s.class_section_id')
                        ->where('s.user_id',$u)
                        ->first();
            if ($deductionMethod && $subjectId) {
                $creditStatus = DB::table('students')
                    ->where('user_id', $u)
                    ->value('credit_status');

                if ($creditStatus != 1) {
                    if ($deductionMethod->data === "1" && in_array($status, [1, 2, 3])) {
                        $query = DB::table('subject_attendances as sa')
                            ->join('users as u', 'sa.user_id', '=', 'u.id')
                            ->join('credit_system as cs', 'sa.user_id', '=', 'cs.user_id')
                            ->join('students as s', 's.user_id', '=', 'sa.user_id')
                            ->join('class_sections as cls','cls.id','=','s.class_section_id')
                            ->join('classes as c','c.id','=','cls.class_id')
                            ->join('subjects as sub', 'sa.subjects_id', '=', 'sub.id',)
                            ->select('cs.user_id as credit_user', DB::raw("CONCAT(u.first_name, ' ', u.last_name) AS full_name"), 'c.id as class_id', 'cs.balance', 'cs.credit_amount', 'sub.commission')
                            ->where('sa.date', date('Y-m-d', strtotime($date)))
                            ->where('sa.subjects_id', $subject->id)
                            ->where('sa.status', 1)
                            ->where('cs.school_id', Auth::user()->school_id)
                            ->where('cs.user_id', $u)
                            ->first();

                        if($query){
                            $subjectPrice = -$query->commission;
                            $totalBalance = DB::table('credit_system')
                            ->where('user_id', $query->credit_user)
                            ->sum('credit_amount');
                            
                            $balance = $totalBalance + $subjectPrice;

                            DB::table('credit_system')->insert([
                                'school_id' => Auth::user()->school_id,
                                'class_id' => $query->class_id,
                                'user_id' => $query->credit_user,
                                'credit_amount' => $subjectPrice,
                                'balance' => $balance,
                                'detail' => 'Attended 1 Class' .'('.$subject->name. ')',
                                'created_at' => Carbon::now(),
                                'updated_at' => Carbon::now()
                            ]);

                            if ($balance <= 0) {
                                $studentsInsufficientFunds[] = $query->full_name;

                                if ($query->credit_user !== null){
                                        $guardianId = DB::table('students')
                                        ->where('user_id', $query->credit_user)
                                        ->value('guardian_id');
                    
                                    $notifyUser = array($query->credit_user,$guardianId);  
                                    if ($notifyUser) {
                                        // Check credit_status before sending low credit alert
                                        $creditStatus = DB::table('students')
                                            ->where('user_id', $u)
                                            ->value('credit_status');
    
                                        if ($creditStatus != 1) {
                                            $title = 'Low Credit Alert!';
                                            $type = 'Low Credit Warning'; // Get The Type for Notification
                                            $body = "You have only [". $subjectPrice ."]credits left. Top up now to avoid any interruptions!";
                                            send_notification($notifyUser, $title, $body, $type); // Send Notification
                                        }
                                    }                        
                                }  

                            }
                            $notifyDeduct=$subjectPrice;
                        } else {
                            $subjectPrice = -$subject->commission;
                            $value = [
                                'school_id' => Auth::user()->school_id,
                                'class_id'  => $student->class_id,
                                'user_id'   => $u,
                                'credit_amount' => $subjectPrice,
                                'balance'       => $subjectPrice,
                                'detail' => 'Attended 1 Class' .'('.$subject->name. ')',
                                'created_at' => Carbon::now(),
                                'updated_at' => Carbon::now()
                            ];
                            DB::table('credit_system')->insert($value);
                            $studentsInsufficientFunds[] = $userFullName;
                            $notifyDeduct=$subjectPrice;

                            if ($u !== null){
                                $guardianId = DB::table('students')
                                ->where('user_id', $u)
                                ->value('guardian_id');
                
                                $notifyUser = array($u,$guardianId);  
                                if ($notifyUser) {
                                    // Check credit_status before sending low credit alert
                                    $creditStatus = DB::table('students')
                                        ->where('user_id', $u)
                                        ->value('credit_status');

                                    if ($creditStatus != 1) {
                                        $title = 'Low Credit Alert!';
                                        $type = 'Low Credit Warning'; // Get The Type for Notification
                                        $body = "You have only [". $subjectPrice ."]credits left. Top up now to avoid any interruptions!";
                                        send_notification($notifyUser, $title, $body, $type); // Send Notification
                                    }
                                }                         
                            }  
                        }
                } else if ($deductionMethod->data === "2" && in_array($status, [1, 2, 3])) {
                    $userId = DB::table('students as s')
                        ->join('users as u', 'u.id', '=', 's.user_id')
                        ->select('s.id', DB::raw("CONCAT(u.first_name, ' ', u.last_name) AS full_name"))
                        ->where('u.id', $u)
                        ->first();

                    $package = DB::table('purchase_package as pp')
                        ->join('subject_package as sp', 'pp.package_id', '=', 'sp.id')
                        ->select('pp.id as purchase_id')
                        ->where('pp.student_id', $userId->id)
                        ->whereIn('pp.status', [0, 2])
                        ->whereRaw("FIND_IN_SET(?, sp.subject_id)", [$subject->id])
                        ->first();
                       
                    if ($package) {
                        $packageUsage = DB::table('package_usage as pu')
                            ->where('pu.student_id', $userId->id)
                            ->where('pu.purchase_package_id', $package->purchase_id)
                            ->orderBy('created_at', 'DESC')
                            ->first();
                        if ($packageUsage && $packageUsage->remaining_session > 0) {
                            if($packageUsage->remaining_session != 0){
                                $remain = $packageUsage->remaining_session - 1;
                            }

                            DB::table('package_usage')->insert([
                                'school_id' => Auth::user()->school_id,
                                'purchase_package_id' => $package->purchase_id,
                                'student_id' => $userId->id,
                                'deduct' => -1,
                                'remaining_session' => $remain ?? 0,
                                'created_at' => Carbon::now(),
                                'updated_at' => Carbon::now(),
                            ]);
                            $notifysessionDeduct=1;


                            if (in_array($remain, [1,2,3,4,5,6,7,8,9,10])) {
                                $lowSessionCount[] = ['student_id' => $packageUsage->student_id, 'remaining_session' => $remain];
                            }else if ($remain == 0) {
                                $noSessionCount[] =['student_id' => $packageUsage->student_id, 'remaining_session' => $remain];
                            }
                            if ($packageUsage && $remain == 0) {
                                DB::table('purchase_package as pp')
                                    ->where('pp.id', $package->purchase_id)
                                    ->update([
                                        'status' => 1
                                    ]);
                            }
                        } else {
                            $studentsInsufficientFunds[] = $userId->full_name;
                        }
                    } else {
                      
                        $studentsInsufficientFunds[] = $userFullName;
                    }
                } else if ($deductionMethod->data === "3" && in_array($status, [1, 2, 3])) {
                    $packageUsed = false;
                    $userId = DB::table('students as s')
                        ->join('users as u', 'u.id', '=', 's.user_id')
                        ->select('s.id')
                        ->where('u.id', $u)
                        ->first();

                    $package = DB::table('purchase_package as pp')
                        ->join('subject_package as sp', 'pp.package_id', '=', 'sp.id')
                        ->select('pp.id as purchase_id', 'pp.package_id', 'pp.student_id')
                        ->where('pp.student_id', $userId->id)
                        ->whereIn('pp.status', [0, 2])
                        ->whereRaw("FIND_IN_SET(?, sp.subject_id)", [$subject->id])
                        ->first();

                    if ($package) {
                        $packageUsage = DB::table('package_usage as pu')
                        ->where('pu.student_id', $userId->id)
                        ->where('pu.purchase_package_id', $package->purchase_id)
                        ->orderBy('created_at', 'DESC')
                        ->first();
                        if ($packageUsage && $packageUsage->remaining_session > 0) {
                            if($packageUsage->remaining_session != 0){
                                $remain = $packageUsage->remaining_session - 1;
                                $packageUsed = true;
                            }

                            DB::table('package_usage')->insert([
                                'school_id' => Auth::user()->school_id,
                                'purchase_package_id' => $package->purchase_id,
                                'student_id' => $userId->id,
                                'deduct' => -1,
                                'remaining_session' => $remain ?? 0,
                                'created_at' => Carbon::now(),
                                'updated_at' => Carbon::now(),
                            ]);
                            $notifysessionDeduct=1;


                            if (in_array($remain, [1,2,3,4,5,6,7,8,9,10])) {
                                $lowSessionCount[] = ['student_id' => $packageUsage->student_id, 'remaining_session' => $remain];
                            }else if ($remain == 0) {
                                $noSessionCount[] =['student_id' => $packageUsage->student_id, 'remaining_session' => $remain];
                            }
                            if ($packageUsage && $remain == 0) {
                                DB::table('purchase_package as pp')
                                    ->where('pp.id', $package->purchase_id)
                                    ->update([
                                        'status' => 1
                                    ]);
                            }
                        } 
                    } 
                    
                    $query = DB::table('subject_attendances as sa')
                            ->join('users as u', 'sa.user_id', '=', 'u.id')
                            ->join('credit_system as cs', 'sa.user_id', '=', 'cs.user_id')
                            ->join('students as s', 's.user_id', '=', 'sa.user_id')
                            ->join('class_sections as cls','cls.id','=','s.class_section_id')
                            ->join('classes as c','c.id','=','cls.class_id')
                            ->join('subjects as sub', 'sa.subjects_id', '=', 'sub.id',)
                            ->select('cs.user_id as credit_user', DB::raw("CONCAT(u.first_name, ' ', u.last_name) AS full_name"), 'c.id as class_id', 'cs.balance', 'cs.credit_amount', 'sub.commission')
                            ->where('sa.date', date('Y-m-d', strtotime($date)))
                            ->where('sa.subjects_id', $subject->id)
                            ->where('sa.status', 1)
                            ->where('cs.school_id', Auth::user()->school_id)
                            ->where('cs.user_id', $u)
                            ->first();
                        
                    if(!$packageUsed){
                        if($query){
                            $subjectPrice = -$query->commission;
                            $totalBalance = DB::table('credit_system')
                            ->where('user_id', $query->credit_user)
                            ->sum('credit_amount');
                            
                            $balance = $totalBalance + $subjectPrice;

                            DB::table('credit_system')->insert([
                                'school_id' => Auth::user()->school_id,
                                'class_id' => $query->class_id,
                                'user_id' => $query->credit_user,
                                'credit_amount' => $subjectPrice,
                                'balance' => $balance,
                                'detail' => 'Attended 1 Class' .'('.$subject->name. ')',
                                'created_at' => Carbon::now(),
                                'updated_at' => Carbon::now()
                            ]);

                            if ($balance <= 0) {
                                $studentsInsufficientFunds[] = $query->full_name;  
                                if ($query->credit_user !== null){
                                        $guardianId = DB::table('students')
                                        ->where('user_id', $query->credit_user)
                                        ->value('guardian_id');
                    
                                    $notifyUser = array($query->credit_user,$guardianId);  
                                    if ($notifyUser) {
                                        // Check credit_status before sending low credit alert
                                        $creditStatus = DB::table('students')
                                            ->where('user_id', $u)
                                            ->value('credit_status');
    
                                        if ($creditStatus != 1) {
                                            $title = 'Low Credit Alert!';
                                            $type = 'Low Credit Warning'; // Get The Type for Notification
                                            $body = "You have only [". $subjectPrice ."]credits left. Top up now to avoid any interruptions!";
                                            send_notification($notifyUser, $title, $body, $type); // Send Notification
                                        }
                                    }                      
                                }                      
                            }
                            $notifyDeduct=$subjectPrice;
                        } else {
                            $subjectPrice = -$subject->commission;
                            $value = [
                                'school_id' => Auth::user()->school_id,
                                'class_id'  => $student->class_id,
                                'user_id'   => $u,
                                'credit_amount' => $subjectPrice,
                                'balance'       => $subjectPrice,
                                'detail' => 'Attended 1 Class' .'('.$subject->name. ')',
                                'created_at' => Carbon::now(),
                                'updated_at' => Carbon::now()
                            ];
                            DB::table('credit_system')->insert($value);
                            $studentsInsufficientFunds[] = $userFullName;
                            $notifyDeduct=$subjectPrice;

                            if ($u !== null){
                                $guardianId = DB::table('students')
                                ->where('user_id', $u)
                                ->value('guardian_id');
                
                                $notifyUser = array($u,$guardianId);  
                                if ($notifyUser) {
                                    // Check credit_status before sending low credit alert
                                    $creditStatus = DB::table('students')
                                        ->where('user_id', $u)
                                        ->value('credit_status');

                                    if ($creditStatus != 1) {
                                        $title = 'Low Credit Alert!';
                                        $type = 'Low Credit Warning'; // Get The Type for Notification
                                        $body = "You have only [". $subjectPrice ."]credits left. Top up now to avoid any interruptions!";
                                        send_notification($notifyUser, $title, $body, $type); // Send Notification
                                    }
                                }                         
                            }  
                        }
                    }
                }
            }
        }
            DB::commit();
            $checkUser = DB::select('SELECT * FROM students s, users u WHERE s.user_id = u.id AND u.id = ?', [$u]);
            if (count($checkUser) > 0) {
                $allUser = DB::select('SELECT s.guardian_id, sa.user_id FROM subject_attendances sa JOIN users u ON sa.user_id = u.id JOIN students s ON u.id = s.user_id WHERE sa.user_id = ? AND sa.id = ?', [$u, $id]);
            } else {
                $allUser = DB::select('SELECT u.id FROM subject_attendances sa, users u WHERE sa.user_id=u.id AND sa.user_id = ? AND sa.id = ?', [$u, $id]);
            }
        }
        $notifyUser = [];
        foreach ($allUser as $data) {
            if (isset($data->user_id)) {
                $notifyUser[] = $data->user_id; // student
            }
            if (isset($data->id)) {
                $notifyUser[] = $data->id; // user
            }
            if (isset($data->guardian_id)) {
                $notifyUser[] = $data->guardian_id; // guardian
            }

            if ($notifyUser !== null) {
                $subjectName = null;
                if(!empty($subject)){
                    $subjectName = $subject->name;
                }else{
                    $subjectName = "All Day";
                }
                if ($status == 1) {
                    $title = 'Checked In';
                    $type = 'Attendance'; // Get The Type for Notification
                    $body = "{$school->name}\n{$user->first_name} {$user->last_name} Checked In {$subjectName}, {$date}.";
                } elseif ($status == 0) {
                    $title = 'Absent';
                    $type = 'Attendance'; // Get The Type for Notification
                    $body = "{$school->name}\n{$user->first_name} {$user->last_name} Absent In {$subjectName}, {$date}.";
                } elseif ($status == 2) {
                    $title = 'Checked In Late';
                    $type = 'Attendance';
                    $body = "{$school->name}\n{$user->first_name} {$user->last_name} Late In {$subjectName}, {$date}.";
                } elseif ($status == 3) {
                    $title = 'Checked In Replacement';
                    $type = 'Attendance';
                    $body = "{$school->name}\n{$user->first_name} {$user->last_name} Replacement In {$subjectName}, {$date}.";
                }
                send_notification($notifyUser, $title, $body, $type); // Send Notification

                if($notifyDeduct != 0){
                    $notifyTime=date('h:iA');
                    
                    if($notifyDeduct < 0){
                        $deductMark=$notifyDeduct*-1;
                    } else{
                        $deductMark=$notifyDeduct;
                    }

                    // Check credit_status before sending credit deduction notification
                    $creditStatus = DB::table('students')
                        ->where('user_id', $u)
                        ->value('credit_status');

                    if ($creditStatus != 1) {
                        $title = 'Credit Deduction';
                        $type = 'Credit Deduction Notification'; // Get The Type for Notification
                        $body = "{$deductMark} credits deducted for clocking in to {$subjectName} on {$date},{$notifyTime} .";
                        send_notification($notifyUser, $title, $body, $type); // Send Notification
                    }
                }
              
                if($notifysessionDeduct != 0) {
                    // Get package information and remaining sessions
                    $packageInfo = DB::table('purchase_package as pp')
                        ->join('subject_package as sp', 'pp.package_id', '=', 'sp.id')
                        ->join('students as s', 'pp.student_id', '=', 's.id')
                        ->where('s.user_id', $u)
                        ->where('pp.status', '!=', '1')
                        ->whereRaw("FIND_IN_SET(?, sp.subject_id)", [$subject->id])  // Add this line to filter by subject
                        ->select('pp.date as purchase_date', 'sp.expiry_days', 'pp.id as pp_id')
                        ->first();

                    if ($packageInfo) {
                        $packageUsage = DB::table('package_usage')
                            ->where('purchase_package_id', $packageInfo->pp_id)
                            ->orderBy('created_at', 'DESC')
                            ->first();

                        $remainingSessions = $packageUsage ? $packageUsage->remaining_session : 0;
                        
                        // Calculate expiry date
                        $purchaseDate = Carbon::parse($packageInfo->purchase_date);
                        $expiryDays = $packageInfo->expiry_days;
                        $expirationDate = $purchaseDate->addDays($expiryDays);
                        $formattedExpirationDate = $expirationDate->format('d-m-Y');

                        $title = 'Session Updated';
                        $type = 'session_update';

                        if ($remainingSessions == 0) {
                            $body = "Clock-in successful! Your sessions have finished.";
                        } else {
                            $body = "Clock-in successful! You have {$remainingSessions} sessions left (expires {$formattedExpirationDate})";
                        }    
                    } else {
                        $body = "Clock-in successful!";
                    }
                    send_notification($notifyUser, $title, $body, $type);
                }
            }
        }
        if (!empty($studentsInsufficientFunds)) {
            $namesInsufficient = implode(', ', $studentsInsufficientFunds);
            
            // Check if student has credit_status = 1
            $creditStatus = DB::table('students')
                ->where('user_id', $u)
                ->value('credit_status');
            
            if ($creditStatus == 1) {
                $alertMessage = "Data Updated Successfully";
                return redirect()->back()->with('success', $alertMessage);
            } else {
                return redirect()->back()->with('alertSuccess', $namesInsufficient);
            }
        } else {
            $alertMessage = "Data Updated Successfully";
            return redirect()->back()->with('success', $alertMessage);
        }
    }

    public function dailyAttendanceReportIndex(Request $request)
    {
        ResponseService::noFeatureThenRedirect('Attendance Management');
        ResponseService::noAnyPermissionThenRedirect(['class-teacher', 'attendance-list']);
        $schoolId = Auth::user()->school_id;
        $teacherId = Auth::user()->id;
        $searchParams = [];
        if (Auth::user()->hasRole('School Admin')) {
            $sql = 'SELECT DISTINCT 
                            class_sections.*,
                            classes.name AS class_name,
                            streams.name AS stream_name,
                            sections.name AS section_name,
                            mediums.name AS medium_name
                        FROM 
                            class_sections
                        JOIN 
                            classes ON class_sections.class_id = classes.id
                        LEFT  JOIN 
                            streams ON classes.stream_id = streams.id
                        JOIN 
                            sections ON class_sections.section_id = sections.id
                        JOIN 
                            mediums ON classes.medium_id = mediums.id
                        AND 
                            class_sections.school_id = ?
                        WHERE class_sections.deleted_at IS NULL';
            $searchParams[] = $schoolId;
        } else {
            $sql = "SELECT DISTINCT 
                            class_sections.*,
                            classes.name AS class_name,
                            streams.name AS stream_name,
                            sections.name AS section_name,
                            mediums.name AS medium_name
                        FROM 
                            class_sections
                        JOIN 
                            class_teachers ON class_sections.id = class_teachers.class_section_id
                        JOIN 
                            classes ON class_sections.class_id = classes.id
                        LEFT  JOIN 
                            streams ON classes.stream_id = streams.id
                        JOIN 
                            sections ON class_sections.section_id = sections.id
                        JOIN 
                            mediums ON classes.medium_id = mediums.id
                        WHERE 
                            class_teachers.teacher_id = ?
                        AND 
                            class_sections.school_id = ?
                        AND 
                            class_sections.deleted_at IS NULL
                        ";
            $searchParams[] = $teacherId;
            $searchParams[] = $schoolId;
        }
        $start_date = \Carbon\Carbon::now()->startOfMonth()->format("d-m-Y");
        $end_date = \Carbon\Carbon::now()->endOfMonth()->format("d-m-Y");
        $class_sections = DB::select($sql, $searchParams);
        foreach ($class_sections as $section) {
            $full_name = '';
            if ($section->class_name) {
                $full_name = $section->class_name;
            }
            if ($section->section_name) {
                $full_name .= " " . $section->section_name;
            }
            if ($section->class_name && $section->stream_name) {
                $full_name .= isset($section->stream_name) ? ' ( ' . $section->stream_name . ' ) ' : '';
            }
            if ($section->medium_name) {
                $full_name .= " - " . $section->medium_name;
            }
            $section->full_name = $full_name;
        }

        
        if (Auth::user()->hasRole('Teacher')) {
            $subject_id = DB::table('subject_teachers as st')
                ->join('subjects as s', 'st.subject_id', '=', 's.id')
                ->where('st.teacher_id', Auth::user()->id)
                ->select('s.id', 's.name')
                ->distinct()
                ->get();
        }
        else{
            // $subject_id = DB::table('subjects as s')
            //     ->distinct()
            //     ->select('s.id', 's.name')
            //     ->leftJoin('subject_students as ss', 'ss.subject_id', '=', 's.id')
            //     ->leftJoin('class_subjects as cs', 'cs.subject_id', '=', 's.id')
            //     ->where('s.school_id', $currentSchoolId)
            //     ->whereNull('s.deleted_at')
            //     ->get();
            $subject_id = DB::table('subjects as s')
                ->distinct()
                ->select('s.id', 's.name')
                ->leftJoin('subject_students as ss', 'ss.subject_id', '=', 's.id')
                ->leftJoin('class_subjects as cs', 'cs.subject_id', '=', 's.id')
                ->where('s.school_id', Auth::user()->school_id)
                ->whereNull('s.deleted_at')
                ->get();
        }

       

        return view('attendance.daily_attendance_report', compact('start_date', 'end_date', 'class_sections', 'subject_id'));
    }

    public function dailyAttendanceReportList(Request $request)
    {
        ResponseService::noFeatureThenRedirect('Attendance Management');
        ResponseService::noAnyPermissionThenRedirect(['class-teacher', 'attendance-list']);

        $offset = $request->input('offset', 0);
        $limit = $request->input('limit', 9999);
        $sort = $request->input('sort', 'id');
        $order = $request->input('order', 'ASC');
        $search = $request->input('search');
        $schoolId = Auth::user()->school_id;
        $start_date = Carbon::parse($request->input('start_date'))->format('Y-m-d');
        $end_date = Carbon::parse($request->input('end_date'))->format('Y-m-d');
        $classSectionId = $request->class_section_id;
        $sessionYear = $this->cache->getDefaultSessionYear();
        $type = $request->type;
        $userQuery = [];

        if ($type === "staff") {
            $userQuery = DB::table('staffs')
                ->select('users.id as user_id', 'users.first_name', 'users.last_name', DB::raw('CONCAT("users.first_name"," ","users.last_name") as full_name'))
                ->join('users', 'staffs.user_id', '=', 'users.id')
                ->where('users.school_id', $schoolId)
                ->whereNull('users.deleted_at')
                ->when($search, function ($query) use ($search) {
                    $query->where(function ($query) use ($search) {
                        $query->whereRaw("concat(users.first_name, ' ', users.last_name) LIKE '%" . $search . "%'")
                            ->orWhere('users.first_name', 'LIKE', "%$search%")
                            ->orWhere('users.last_name', 'LIKE', "%$search%");
                    });
                })
                ->get();

            $attendanceQuery = DB::table('users')
                ->select('users.id AS user_id', 'subject_attendances.date', 'subject_attendances.status')
                ->leftJoin('subject_attendances', 'users.id', '=', 'subject_attendances.user_id')
                // ->leftJoin('attendances', 'attendances.date', '=', 'subject_attendances.date')
                ->join('staffs', 'users.id', '=', 'staffs.user_id')
                ->where('users.school_id', $schoolId)
                ->whereNull('subject_attendances.subjects_id')
                ->whereBetween('subject_attendances.date', [$start_date, $end_date])
                ->get();
        } else if ($type === "student") {
            if ($request->class_section_id && !$request->subject_id) {
                $userQuery = DB::table('students')
                    ->join('users', 'students.user_id', '=', 'users.id')
                    ->select('users.id as user_id','students.class_section_id','users.first_name', 'users.last_name', DB::raw('CONCAT(users.first_name," ",users.last_name) as full_name'))
                    ->where('students.class_section_id', $classSectionId)
                    ->where('users.school_id', $schoolId)
                    ->whereNull('users.deleted_at')
                    ->when($search, function ($query) use ($search) {
                        $query->where(function ($query) use ($search) {
                            $query->whereRaw("concat(users.first_name, ' ', users.last_name) LIKE '%" . $search . "%'")
                                ->orWhere('users.first_name', 'LIKE', "%$search%")
                                ->orWhere('users.last_name', 'LIKE', "%$search%");
                        });
                    })->get();
                    

                $attendanceQuery = DB::table('users')
                    ->select('users.id as user_id', 'attendances.date', 'subject_attendances.status', 'attendances.type')
                    ->leftJoin('attendances', 'users.id', '=', 'attendances.student_id')
                    ->leftJoin('subject_attendances', 'subject_attendances.id', '=', 'attendances.subject_attendance_id')
                    ->join('students', 'users.id', '=', 'students.user_id')
                    ->join('class_sections', 'students.class_section_id', '=', 'class_sections.id')
                    ->where('class_sections.id', $classSectionId)
                    ->where('students.school_id', $schoolId)
                    ->whereNull('subject_attendances.subjects_id')
                    ->whereBetween('attendances.date', [$start_date, $end_date])
                    ->get();

                if (Auth::user()->hasRole('Teacher')) {
                    $attendanceQuery = $attendanceQuery->whereExists(function ($query) {
                        $query->select(DB::raw(1))
                            ->from('subject_teachers')
                            ->whereColumn('subject_teachers.subject_id', 'subject_attendances.subjects_id')
                            ->where('subject_teachers.teacher_id', Auth::user()->id);
                    });
                //     $attendanceQuery = $attendanceQuery->where('staffs.user_id', Auth::user()->id);
                }
            } else if ($request->subject_id) {
                // dd($request->subject_id);
                $userQuery =  DB::table('students')
                    ->distinct()
                    ->select(
                        'students.*',
                        'users.*',
                        // 'class_subjects.subject_id AS subject_id'
                    )
                    ->join('class_sections', 'class_sections.id', '=', 'students.class_section_id')
                    ->leftJoin('class_subjects', 'class_subjects.class_id', '=',  'class_sections.class_id')
                    ->leftJoin('subjects', 'class_subjects.subject_id', '=', 'subjects.id')
                    ->join('users', 'users.id', '=', 'students.user_id')
                    ->leftJoin('student_subjects', 'student_subjects.student_id', '=', 'users.id')
                    ->leftJoin('class_subjects as cs', 'cs.id', '=', 'student_subjects.class_subject_id')
                    ->where(function ($query) use ($request,$sessionYear) {
                        $query->where(function($q) use ($request,$sessionYear) {
                            $q->where('cs.subject_id', $request->subject_id)
                                ->where('student_subjects.session_year_id',$sessionYear->id);
                        })->orWhere(function ($q) use ($request) {
                                $q->where('class_subjects.subject_id', $request->subject_id)
                                    ->where('class_subjects.type', 'Compulsory');
                            });
                    })
                    ->where('students.school_id', $schoolId)
                    // ->where('class_subjects.subject_id', $request->subject_id)
                    ->where('students.session_year_id',$sessionYear->id)
                    // ->where('student_subjects.session_year_id',$sessionYear->id)
                    ->whereNull('students.deleted_at')
                    ->whereNull('users.deleted_at')
                    //->where('subjects.id', $request->subject_id)
                    ->whereNull('class_subjects.deleted_at')
                    ->when($request->class_section_id, function ($query) use ($request) {
                        $query->where('students.class_section_id', $request->class_section_id);
                    })
                    ->when($search, function ($query) use ($search) {
                        $query->where('students.id', 'LIKE', "%$search%")
                            ->orWhereHas('user', function ($q) use ($search) {
                                $q->whereRaw("concat(users.first_name, ' ', users.last_name) LIKE ?", ["%{$search}%"])
                                    ->where('deleted_at', NULL);
                            });
                    })->get();
                    // ->when($request->class_section_id, function ($query) use ($request) {
                    //     $query->where('students.class_section_id', $request->class_section_id);
                    //         })->get();

                // $attendanceQuery = DB::table('users')
                //     ->select('users.id as user_id', 'subject_attendances.date', 'subject_attendances.status as type')
                //     ->leftJoin('subject_attendances', 'subject_attendances.user_id', '=', 'users.id')
                //     ->leftJoin('students', 'users.id', '=', 'students.user_id')
                //     ->leftJoin('class_sections', 'students.class_section_id', '=', 'class_sections.id')
                //     ->where('students.school_id', $schoolId)
                //     ->whereBetween('subject_attendances.date', [$start_date, $end_date])
                //     ->where('subject_attendances.subjects_id', $request->subject_id)
                //     ->get();
                
                $attendanceQuery = DB::table('users')
                        ->select('users.id as user_id', 'subject_attendances.date', 'subject_attendances.status',
                                DB::raw('subject_attendances.status as type'))
                        ->leftJoin('subject_attendances', function ($join) use ($start_date, $end_date) {
                            $join->on('users.id', '=', 'subject_attendances.user_id')
                                ->whereBetween('subject_attendances.date', [$start_date, $end_date]);
                        })
                        ->join('students', 'users.id', '=', 'students.user_id')
                        ->where('subject_attendances.subjects_id', $request->subject_id)
                        ->where('students.school_id', $schoolId)
                        ->get();
            } 
        }

        $dateHeaders = [];
        $days = $request->days ?? [];
        $currentDate = Carbon::parse($start_date);
        while ($currentDate->lte($end_date)) {
            if (empty($days) || in_array($currentDate->dayOfWeek, $days)) {
                // dd($currentDate->dayOfWeek);
                $dateHeaders[] = $currentDate->format('Y-m-d');
            }
            $currentDate->addDay();
        }

        $no = 1;
        $users = [];
        foreach ($userQuery as $user) {
            $userData = [
                'no' => $no++,
                'user_id' => $user->user_id,
                'user_name' => $user->first_name . ' ' . $user->last_name,
            ];

            foreach ($dateHeaders as $date) {
                $userData['attendance_' . $date] = null;
            }

            foreach ($attendanceQuery as $attendance) {
                if ($attendance->user_id == $user->user_id && in_array($attendance->date, $dateHeaders)) {
                    $userData['attendance_' . $attendance->date] = !empty($attendance->status) ? $attendance->status : $attendance->type;
                }
            }
            $users[] = $userData;
        }

        $bulkData = [
            'total' => count($users),
            'rows' => $users,
            'date_headers' => $dateHeaders,
        ];

        return response()->json($bulkData);
    }
    public function summaryAttendanceData(Request $request)
    {
        ResponseService::noFeatureThenRedirect('Attendance Management');
        ResponseService::noAnyPermissionThenRedirect(['class-teacher', 'attendance-list']);

        $schoolId = Auth::user()->school_id;
        $classSectionId = $request->input('class_section_id');

        // Add these date filter parameters
        $startDate = $request->filled('start_date')
            ? Carbon::createFromFormat('d-m-Y', $request->start_date)->startOfDay()
            : Carbon::now()->startOfMonth();

        $endDate = $request->filled('end_date')
            ? Carbon::createFromFormat('d-m-Y', $request->end_date)->endOfDay()
            : Carbon::now()->endOfMonth();

        // Format dates for display
        $start_date = $startDate->format('d-m-Y');
        $end_date = $endDate->format('d-m-Y');

        // Class Attendance Query
        $classQuery = DB::table('class_sections as cs')
            ->join('classes as c', 'cs.class_id', '=', 'c.id')
            ->join('sections as s', 'cs.section_id', '=', 's.id')
            ->join('students as st', 'st.class_section_id', '=', 'cs.id')
            ->leftJoin('attendances as a', 'a.student_id', '=', 'st.user_id')
            ->leftJoin('subject_attendances as sa', function ($join) {
                $join->on('sa.id', '=', 'a.subject_attendance_id')
                    ->whereNull('sa.subjects_id');
            })
            ->where('cs.school_id', $schoolId)
            ->whereNull('st.deleted_at');

        if ($classSectionId) {
            $classQuery->where('cs.id', $classSectionId);
        }

        $classAttendanceData = $classQuery
            ->groupBy('cs.id', 'c.name', 's.name')
            ->select(
                DB::raw("CONCAT(c.name, ' - ', s.name) as class_name"),
                DB::raw('COUNT(DISTINCT st.id) as total_students'),
                DB::raw('COUNT(CASE WHEN sa.status = 1 THEN 1 END) as present'),
                DB::raw('COUNT(CASE WHEN sa.status = 0 THEN 1 END) as absent'),
                DB::raw('COUNT(CASE WHEN sa.status = 2 THEN 1 END) as late'),
                DB::raw('COUNT(CASE WHEN sa.status = 3 THEN 1 END) as replacement')
            )
            ->get();

        // Subject Attendance Query
        $subjectQuery = DB::table('subjects as sub')
            ->leftJoin('subject_attendances as sa', 'sa.subjects_id', '=', 'sub.id')
            ->where('sub.school_id', $schoolId)
            ->whereNull('sub.deleted_at')
            ->groupBy('sub.id', 'sub.name')
            ->select(
                'sub.name as subject_name',
                DB::raw('COUNT(DISTINCT sa.user_id) as total_students'),
                DB::raw('COUNT(CASE WHEN sa.status = 1 THEN 1 END) as present'),
                DB::raw('COUNT(CASE WHEN sa.status = 0 THEN 1 END) as absent'),
                DB::raw('COUNT(CASE WHEN sa.status = 2 THEN 1 END) as late'),
                DB::raw('COUNT(CASE WHEN sa.status = 3 THEN 1 END) as replacement')
            )
            ->get();

        // Get class sections with IDs for the chart
        $sections = DB::table('class_sections')
            ->join('classes', 'class_sections.class_id', '=', 'classes.id')
            ->join('sections', 'class_sections.section_id', '=', 'sections.id')
            ->where('class_sections.school_id', $schoolId)
            ->whereNull('class_sections.deleted_at')
            ->select('class_sections.id', DB::raw("CONCAT(classes.name, ' ', sections.name) as name"))
            ->get();

        // Prepare class data for the stacked bar chart
        $classChartData = [
            'sections' => $sections->map(function($section) {
                return [
                    'id' => $section->id,
                    'name' => $section->name
                ];
            })->toArray(),
            'attendance' => $classAttendanceData->map(function($item) {
                return [
                    'present' => (int)$item->present,
                    'absent' => (int)$item->absent,
                    'late' => (int)$item->late,
                    'replacement' => (int)$item->replacement
                ];
            })->toArray()
        ];

        // Get subjects with IDs for the chart
        $subjects = DB::table('subjects')
            ->where('school_id', $schoolId)
            ->whereNull('deleted_at')
            ->select('id', 'name')
            ->get();

        // Prepare subject data for the stacked bar chart
        $subjectChartData = [
            'subjects' => $subjects->map(function($subject) {
                return [
                    'id' => $subject->id,
                    'name' => $subject->name
                ];
            })->toArray(),
            'attendance' => $subjectQuery->map(function($item) {
                return [
                    'present' => (int)$item->present,
                    'absent' => (int)$item->absent,
                    'late' => (int)$item->late,
                    'replacement' => (int)$item->replacement
                ];
            })->toArray()
        ];

        // Calculate class summary
        $classSummary = [
            'total_present' => $classAttendanceData->sum('present'),
            'total_absent' => $classAttendanceData->sum('absent'),
            'total_late' => $classAttendanceData->sum('late'),
            'total_replacement' => $classAttendanceData->sum('replacement')
        ];

        // Calculate subject summary
        $subjectSummary = [
            'total_present' => $subjectQuery->sum('present'),
            'total_absent' => $subjectQuery->sum('absent'),
            'total_late' => $subjectQuery->sum('late'),
            'total_replacement' => $subjectQuery->sum('replacement')
        ];

        if ($request->ajax()) {
            return response()->json([
                'class' => [
                    'summary' => $classSummary,
                    'chartData' => $classChartData
                ],
                'subject' => [
                    'summary' => $subjectSummary,
                    'chartData' => $subjectChartData
                ]
            ]);
        }

        return view('attendance.summary_attendance', [
            // Class data
            'sections' => $classChartData['sections'],
            'attendance' => $classChartData['attendance'],
            'summary' => $classSummary,

            // Subject data
            'subjects' => $subjectChartData['subjects'],
            'subjectAttendance' => $subjectChartData['attendance'],
            'subjectSummary' => $subjectSummary,

            // Date values for display
            'start_date' => $start_date,
            'end_date' => $end_date,
            'startDate' => $startDate,
            'endDate' => $endDate
        ]);
    }

    /**
     * Get summary attendance data for the given date range
     * This is a simplified version that only returns the chart data and summary data
     * for the bar charts and summary boxes
     */
    public function summaryAttendanceDataList(Request $request)
    {
        ResponseService::noFeatureThenRedirect('Attendance Management');
        ResponseService::noAnyPermissionThenRedirect(['class-teacher', 'attendance-list']);

        $schoolId = Auth::user()->school_id;

        // Log the raw request data
        Log::info('Raw request data for summaryAttendanceDataList:', [
            'all' => $request->all()
        ]);

        // Simple date parsing - exactly like daily attendance report
        try {
            // Get date parameters
            $start_date_raw = $request->input('start_date');
            $end_date_raw = $request->input('end_date');

            // Log the raw date inputs
            Log::info('Summary attendance date parameters received:', [
                'start_date' => $start_date_raw,
                'end_date' => $end_date_raw
            ]);

            // Use default dates if not provided
            if (empty($start_date_raw)) {
                $start_date_raw = Carbon::now()->startOfMonth()->format('d-m-Y');
                Log::info('Using default start date: ' . $start_date_raw);
            }

            if (empty($end_date_raw)) {
                $end_date_raw = Carbon::now()->endOfMonth()->format('d-m-Y');
                Log::info('Using default end date: ' . $end_date_raw);
            }

            // Parse dates - try to handle different formats
            try {
                // Try dd-mm-yyyy format first (most likely from the datepicker)
                $start_date = Carbon::createFromFormat('d-m-Y', $start_date_raw)->format('Y-m-d');
                $end_date = Carbon::createFromFormat('d-m-Y', $end_date_raw)->format('Y-m-d');
            } catch (\Exception $e) {
                // If that fails, try generic parsing
                $start_date = Carbon::parse($start_date_raw)->format('Y-m-d');
                $end_date = Carbon::parse($end_date_raw)->format('Y-m-d');
            }

            Log::info('Parsed date parameters for filtering:', [
                'start_date' => $start_date,
                'end_date' => $end_date
            ]);
        } catch (\Exception $e) {
            // Log the error
            Log::error('Error parsing date parameters: ' . $e->getMessage(), [
                'start_date_raw' => $request->input('start_date'),
                'end_date_raw' => $request->input('end_date')
            ]);

            // Use default dates if parsing fails
            $start_date = Carbon::now()->startOfMonth()->format('Y-m-d');
            $end_date = Carbon::now()->endOfMonth()->format('Y-m-d');

            Log::info('Using default dates after error:', [
                'start_date' => $start_date,
                'end_date' => $end_date
            ]);
        }

        // Get additional filters
        $class_section_id = $request->input('class_section_id');
        $subject_id = $request->input('subject_id');

        // For backward compatibility
        if (empty($class_section_id) && $request->has('class')) {
            $class_section_id = $request->input('class');
        }

        if (empty($subject_id) && $request->has('subject')) {
            $subject_id = $request->input('subject');
        }

        // Log the received parameters
        Log::info('Received parameters for summaryAttendanceDataList:', [
            'start_date' => $start_date,
            'end_date' => $end_date,
            'class_section_id' => $class_section_id,
            'subject_id' => $subject_id
        ]);

        // Filter class attendance data
        $classChartData = $this->getClassChartData($start_date, $end_date, $class_section_id);

        // Filter subject attendance data
        $subjectChartData = $this->getSubjectChartData($start_date, $end_date, $subject_id);

        // Filter class summary
        $classSummary = $this->getClassSummary($start_date, $end_date, $class_section_id);

        // Filter subject summary
        $subjectSummary = $this->getSubjectSummary($start_date, $end_date, $subject_id);

        // Prepare the response data - only include what's needed for the charts and summary boxes
        $bulkData = [];

        // Add chart data and summary data to the response - ONLY these three data points
        // Make sure the structure matches exactly what the frontend expects
        $bulkData['chart_data'] = [
            'class' => $classChartData,
            'subject' => $subjectChartData
        ];
        $bulkData['summary'] = $classSummary;
        $bulkData['subject_summary'] = $subjectSummary;

        // Log the final response structure
        Log::info('Final response structure:', [
            'chart_data_keys' => array_keys($bulkData['chart_data']),
            'class_chart_keys' => array_keys($bulkData['chart_data']['class']),
            'subject_chart_keys' => array_keys($bulkData['chart_data']['subject']),
            'summary_keys' => array_keys($bulkData['summary']),
            'subject_summary_keys' => array_keys($bulkData['subject_summary'])
        ]);

        return response()->json($bulkData);
    }
    public function teacherAttendanceReport(Request $request)
    {
        ResponseService::noFeatureThenRedirect('Attendance Management');
        ResponseService::noAnyPermissionThenRedirect(['class-teacher', 'attendance-list']);
        $class_sections = $this->classSection->builder()->ClassTeacher()->with('class', 'class.stream', 'section', 'medium')->get();
        $schoolId = Auth::user()->school_id;
        $subjects = DB::table('subjects')
            ->where('school_id', $schoolId)
            ->get();; // Fetch all subjects
        $students = DB::table('students as s')
            ->join('users as u', 's.user_id', '=', 'u.id')
            ->where('s.school_id', $schoolId)
            ->whereNull('u.deleted_at')
            ->select('u.id', DB::raw("CONCAT(u.first_name, ' ', u.last_name) AS full_name"))
            ->get();

        return view('attendance.teacher-attendance-report', compact('class_sections', 'subjects', 'students'));
    }

    public function showTeacherAttendanceReport(Request $request)
    {
        ResponseService::noFeatureThenRedirect('Attendance Management');
        ResponseService::noAnyPermissionThenRedirect(['class-teacher', 'attendance-list']);
        // Get request parameters
        $offset = $request->input('offset', 0);
        $limit = $request->input('limit', 9999);
        $sort = $request->input('sort', 'id');
        $order = $request->input('order', 'DESC');
        $search = $request->input('search');
        $subject_id = $request->input('subject_id');
        $start_date = Carbon::createFromFormat('d-m-Y', $request->input('start_date'))->startOfDay();
        $end_date = Carbon::createFromFormat('d-m-Y', $request->input('end_date'))->endOfDay();
        $status = $request->status;
        $schoolId = Auth::user()->school_id;

        // Build the query
        $query = TeacherAttendance::query();

        // Join with users table to get student names and staffs table to get staff ID
        $query->join('users', 'teacher_attendance.user_id', '=', 'users.id')
            ->join('staffs', 'users.id', '=', 'staffs.user_id')
            ->leftJoin('subjects', 'teacher_attendance.subject_id', '=', 'subjects.id') // Left join subjects table
            ->select('teacher_attendance.*', 'users.first_name', 'users.last_name', 'staffs.id as staff_id', 'subjects.name as subject_name', DB::raw('COALESCE(subjects.name, "All Day") as subject_name')) // Select subject_name
            ->where('teacher_attendance.school_id', '=', $schoolId)
            ->whereNull('users.deleted_at');

        // Execute the query and fetch results
        $subjectAttendances = $query->get();

        // Apply search filters (scoped within the join)

        if ($search) {
            $searchTerms = explode(' ', $search);

            $query->where(function ($q) use ($searchTerms) {
                foreach ($searchTerms as $term) {
                    $q->where(function ($q) use ($term) {
                        $q->where('users.first_name', 'LIKE', "%$term%")
                            ->orWhere('users.last_name', 'LIKE', "%$term%");
                    });
                }
            });
        }
        // Apply subject filter if provided
        if ($subject_id == '0') {
            $query->where(function ($q) {
                $q->whereNull('teacher_attendance.subject_id')
                  ->orWhere('teacher_attendance.subject_id', '=', 0);
            });
        } else if ($subject_id) {
            $query->where('teacher_attendance.subject_id', $subject_id);
        }

        // Apply date range filter if provided
        if ($start_date && $end_date) {
            $query->whereBetween('teacher_attendance.date', [$start_date, $end_date]);
        }

        if (isset($status)) {
            $query->where('teacher_attendance.status', $status);
        }

        // Get the total count of records (after applying filters)
        $total = $query->count();

        // Apply sorting, offset, and limit
        $query->orderBy($sort, $order)->skip($offset)->take($limit);

        // Get the records
        $res = $query->get();

        // Prepare the response data
        $bulkData = [];
        $bulkData['total'] = $total;
        $rows = [];
        $no = 1;

        foreach ($res as $row) {
            $operate = '';
            $operate .= BootstrapTableService::editButton(route('attendance.updateStudentAttendance', $row->id));
            $operate .= BootstrapTableService::deleteButton(route('attendance.destroy', $row->id));
            $tempRow = $row->toArray();
            $tempRow['no'] = $no++;
            $tempRow['student_name'] = $row->first_name . ' ' . $row->last_name;
            $tempRow['subject_name'] = $row->subject_name;
            $tempRow['operate'] = $operate;
            // Format clock_in and clock_out to display in AM/PM format
            $tempRow['clock_in'] = $row->clock_in ? Carbon::parse($row->clock_in)->format('H:i:s') : '-';
            $tempRow['clock_out'] = $row->clock_out ? Carbon::parse($row->clock_out)->format('H:i:s') : '-';
            // Determine status based on type (example logic)
            if ($row->status == 1) {
                $tempRow['status'] = 'Present';
            } elseif ($row->status == 2) {
                $tempRow['status'] = 'Late';
            } elseif ($row->status == 3) {
                $tempRow['status'] = 'Replacement';
            } else {
                $tempRow['status'] = 'Absent';
            }

            $rows[] = $tempRow;
        }

        $bulkData['rows'] = $rows;

        return response()->json($bulkData);
    }

    public function studentAttendanceReport(Request $request)
    {
        ResponseService::noFeatureThenRedirect('Attendance Management');
        ResponseService::noAnyPermissionThenRedirect(['class-teacher', 'attendance-list']);
        $class_sections = $this->classSection->builder()->ClassTeacher()->with('class', 'class.stream', 'section', 'medium')->get();
        $schoolId = Auth::user()->school_id;
        // $subjects = DB::table('subjects')
        //     ->where('school_id', $schoolId)
        //     ->get();; // Fetch all subjects
        $students = DB::table('students as s')
            ->join('users as u', 's.user_id', '=', 'u.id')
            ->where('s.school_id', $schoolId)
            ->whereNull('u.deleted_at')
            ->select('u.id', DB::raw("CONCAT(u.first_name, ' ', u.last_name) AS full_name"))
            ->get();

        $subjects = [];
        if (Auth::user()->hasRole('Teacher')) {
            $subjects = DB::table('subject_teachers as st')
                ->join('subjects as s', 'st.subject_id', '=', 's.id')
                ->where('st.teacher_id', Auth::user()->id)
                ->select('s.id', 's.name')
                ->distinct()
                ->get();
        }
        else{
            $subjects = DB::table('subjects as s')
                ->distinct()
                ->select('s.id', 's.name')
                ->leftJoin('subject_students as ss', 'ss.subject_id', '=', 's.id')
                ->leftJoin('class_subjects as cs', 'cs.subject_id', '=', 's.id')
                ->where('s.school_id', $schoolId)
                ->whereNull('s.deleted_at')
                ->get();
        }

        return view('attendance.student-attendance-report', compact('class_sections', 'subjects', 'students'));
    }

    public function showStudentAttendanceReport(Request $request)
    {
        ResponseService::noFeatureThenRedirect('Attendance Management');
        ResponseService::noAnyPermissionThenRedirect(['class-teacher', 'attendance-list']);
        // Get request parameters
        $offset = $request->input('offset', 0);
        $limit = $request->input('limit', 9999);
        $sort = $request->input('sort', 'id');
        $order = $request->input('order', 'DESC');
        $search = $request->input('search');
        $subject_id = $request->input('subject_id');
        $start_date = Carbon::createFromFormat('d-m-Y', $request->input('start_date'))->startOfDay();
        $end_date = Carbon::createFromFormat('d-m-Y', $request->input('end_date'))->endOfDay();
        $status = $request->status;
        $schoolId = Auth::user()->school_id;

        // Build the query
        $query = SubjectAttendance::query();

        // Join with users table to get student names and students table to get student ID
        $query->join('users AS user', 'subject_attendances.user_id', '=', 'user.id')
            ->leftJoin('users AS teacher', 'subject_attendances.teacher_id', '=', 'teacher.id')
            ->join('students', 'user.id', '=', 'students.user_id')
            ->leftJoin('subjects', 'subject_attendances.subjects_id', '=', 'subjects.id') // Left join subjects table
            ->select(
                'subject_attendances.*',
                'user.first_name',
                'user.last_name',
                'students.id as student_id',
                'subjects.name as subject_name', // Direct subject name from subjects table
                DB::raw('COALESCE(subjects.name, "All Day") as subject_name'),
                DB::raw('CONCAT(teacher.first_name, " ", teacher.last_name) as teacher_name')
            )
            ->whereNull('user.deleted_at')
            ->where('subject_attendances.school_id', '=', $schoolId);

            if (Auth::user()->hasRole('Teacher')) {
                $query->where('subject_attendances.teacher_id', Auth::user()->id);
            }
        // Execute the query and fetch results
        //$subjectAttendances = $query->get();

        // Apply search filters (scoped within the join)

        if ($search) {
            $searchTerms = explode(' ', $search);

            $query->where(function ($q) use ($searchTerms) {
                foreach ($searchTerms as $term) {
                    $q->where(function ($q) use ($term) {
                        $q->where('user.first_name', 'LIKE', "%$term%")
                            ->orWhere('user.last_name', 'LIKE', "%$term%");
                    });
                }
            });
        }
        // Apply subject filter if provided
        if ($subject_id == '0') {
            $query->whereNull('subject_attendances.subjects_id');
        } else if ($subject_id) {
            $query->where('subject_attendances.subjects_id', $subject_id);
        }

        // Apply date range filter if provided
        if ($start_date && $end_date) {
            $query->whereBetween('subject_attendances.date', [$start_date, $end_date]);
        }

        if (isset($status)) {
            $query->where('subject_attendances.status', $status);
        }

        // Get the total count of records (after applying filters)
        $total = $query->count();

        // Apply sorting, offset, and limit
        $query->orderBy($sort, $order)->skip($offset)->take($limit);

        // Get the records
        $res = $query->get();

        // Prepare the response data
        $bulkData = [];
        $bulkData['total'] = $total;
        $rows = [];
        $no = 1;


        foreach ($res as $row) {
            $operate = '';
            $operate .= BootstrapTableService::editButton(route('attendance.updateStudentAttendance', $row->id));
            $operate .= BootstrapTableService::deleteButton(route('attendance.destroy', $row->id));
            $tempRow = $row->toArray();
            $tempRow['no'] = $no++;
            $tempRow['student_name'] = $row->first_name . ' ' . $row->last_name;
            $tempRow['subject_name'] = $row->subject_name;
            $tempRow['teacher_id'] = $row->teacher_name;
            $tempRow['fees_per_section'] = $row->status == 0 ? 0 : $row->fees_per_section;
            $tempRow['operate'] = $operate;
            // Format clock_in and clock_out to display in AM/PM format
            $tempRow['clock_in'] = $row->clock_in ? Carbon::parse($row->clock_in)->format('H:i:s') : '-';
            $tempRow['clock_out'] = $row->clock_out ? Carbon::parse($row->clock_out)->format('H:i:s') : '-';
            // Determine status based on type (example logic)
            if ($row->status == 1) {
                $tempRow['status'] = 'Present';
            } elseif ($row->status == 2) {
                $tempRow['status'] = 'Late';
            } elseif ($row->status == 3) {
                $tempRow['status'] = 'Replacement';
            } else {
                $tempRow['status'] = 'Absent';
            }

            $rows[] = $tempRow;
        }

        $bulkData['rows'] = $rows;
        Log::info('Date parameters for chart data:', [
            'start_date' => $start_date,
            'end_date' => $end_date,
            'request_start_date' => $request->input('start_date'),
            'request_end_date' => $request->input('end_date')
        ]);

        // Get the chart data and summary data for the filtered date range
        $classChartData = $this->getClassChartData($start_date, $end_date);
        $subjectChartData = $this->getSubjectChartData($start_date, $end_date);
        $classSummary = $this->getClassSummary($start_date, $end_date);
        $subjectSummary = $this->getSubjectSummary($start_date, $end_date);

        // Add chart data and summary data to the response
        $bulkData['chart_data'] = [
            'class' => $classChartData,
            'subject' => $subjectChartData
        ];
        $bulkData['summary'] = $classSummary;
        $bulkData['subject_summary'] = $subjectSummary;

        return response()->json($bulkData);
    }
    
     //* Get class chart data for the given date range and optional class_section_id
     
    private function getClassChartData($start_date, $end_date, $class_section_id = null)
    {
        $schoolId = Auth::user()->school_id;

        // Log the input parameters
        Log::info('getClassChartData called with:', [
            'start_date' => $start_date,
            'end_date' => $end_date,
            'class_section_id' => $class_section_id,
            'schoolId' => $schoolId
        ]);

        // Get class sections for the chart
        $query = DB::table('class_sections')
            ->join('classes', 'class_sections.class_id', '=', 'classes.id')
            ->join('sections', 'class_sections.section_id', '=', 'sections.id')
            ->where('class_sections.school_id', $schoolId)
            ->whereNull('class_sections.deleted_at')
            ->select('class_sections.id', DB::raw("CONCAT(classes.name, ' ', sections.name) as name"));

        // Filter by class_section_id if provided
        if (!empty($class_section_id)) {
            Log::info('Filtering by class_section_id:', ['class_section_id' => $class_section_id]);
            $query->where('class_sections.id', $class_section_id);
        }

        $sections = $query->get();

        // Log the sections found
        Log::info('Sections found for chart:', [
            'count' => $sections->count(),
            'sections' => $sections->toArray()
        ]);

        // We'll return the full section objects (with id and name) for better display and filtering

        $classAttendanceData = [];

        // Get actual attendance data for each section
        foreach ($sections as $section) {
            // Log the current section being processed
            Log::info('Processing section:', [
                'section_id' => $section->id,
                'section_name' => $section->name
            ]);

            // Apply both class and date filters
            $present = DB::table('attendances')
                ->where('class_section_id', $section->id)
                ->whereBetween('date', [$start_date, $end_date])
                ->where('type', 1) // Present
                ->count();

            $absent = DB::table('attendances')
                ->where('class_section_id', $section->id)
                ->whereBetween('date', [$start_date, $end_date])
                ->where('type', 0) // Absent
                ->count();

            $late = DB::table('attendances')
                ->where('class_section_id', $section->id)
                ->whereBetween('date', [$start_date, $end_date])
                ->where('type', 2) // Late
                ->count();

            $replacement = DB::table('attendances')
                ->where('class_section_id', $section->id)
                ->whereBetween('date', [$start_date, $end_date])
                ->where('type', 3) // Replacement
                ->count();

            $classAttendanceData[] = [
                'present' => $present,
                'absent' => $absent,
                'late' => $late,
                'replacement' => $replacement
            ];
        }

        // Log the results
        Log::info('getClassChartData results:', [
            'sections_count' => count($sections),
            'attendance_data_count' => count($classAttendanceData),
            'first_section' => $sections->first() ? $sections->first()->name : 'none',
            'first_attendance' => $classAttendanceData[0] ?? 'none'
        ]);

        // Create an array of objects with both id and name for each section
        $sectionsData = [];
        foreach ($sections as $section) {
            $sectionsData[] = [
                'id' => $section->id,
                'name' => $section->name
            ];
        }

        return [
            'sections' => $sectionsData,
            'attendance' => $classAttendanceData
        ];
    }

    private function getSubjectChartData($start_date, $end_date, $subject_id = null)
    {
        $schoolId = Auth::user()->school_id;

        // Log the input parameters
        Log::info('getSubjectChartData called with:', [
            'start_date' => $start_date,
            'end_date' => $end_date,
            'subject_id' => $subject_id,
            'schoolId' => $schoolId
        ]);

        // Get subjects for the chart
        $query = DB::table('subjects')
            ->where('school_id', $schoolId)
            ->whereNull('deleted_at')
            ->select('id', 'name');

        // Filter by subject_id if provided
        if (!empty($subject_id)) {
            Log::info('Filtering by subject_id:', ['subject_id' => $subject_id]);
            $query->where('id', $subject_id);
        }

        $subjects = $query->get();

        // Log the subjects found
        Log::info('Subjects found:', [
            'count' => $subjects->count(),
            'subjects' => $subjects->toArray()
        ]);

        // We'll use the full subject objects instead of just names
        $subjectAttendanceData = [];

        // Get actual attendance data for each subject
        foreach ($subjects as $subject) {
            // Get attendance counts for this subject in the date range
            $present = DB::table('subject_attendances')
                ->where('subjects_id', $subject->id)
                ->whereBetween('date', [$start_date, $end_date])
                ->where('status', 1) // Present
                ->count();

            $absent = DB::table('subject_attendances')
                ->where('subjects_id', $subject->id)
                ->whereBetween('date', [$start_date, $end_date])
                ->where('status', 0) // Absent
                ->count();

            $late = DB::table('subject_attendances')
                ->where('subjects_id', $subject->id)
                ->whereBetween('date', [$start_date, $end_date])
                ->where('status', 2) // Late
                ->count();

            $replacement = DB::table('subject_attendances')
                ->where('subjects_id', $subject->id)
                ->whereBetween('date', [$start_date, $end_date])
                ->where('status', 3) // Replacement
                ->count();

            $subjectAttendanceData[] = [
                'present' => $present,
                'absent' => $absent,
                'late' => $late,
                'replacement' => $replacement
            ];
        }

        // Create an array of objects with both id and name for each subject
        $subjectsData = [];
        foreach ($subjects as $subject) {
            $subjectsData[] = [
                'id' => $subject->id,
                'name' => $subject->name
            ];
        }

        return [
            'subjects' => $subjectsData,
            'attendance' => $subjectAttendanceData
        ];
    }

    private function getClassSummary($start_date, $end_date, $class_section_id = null)
{
    $schoolId = Auth::user()->school_id;

    // Log the input parameters
    Log::info('getClassSummary called with:', [
        'start_date' => $start_date,
        'end_date' => $end_date,
        'class_section_id' => $class_section_id,
        'schoolId' => $schoolId
    ]);

    // Helper function to build a filtered query
    $buildQuery = function ($type) use ($schoolId, $start_date, $end_date, $class_section_id) {
        $query = DB::table('attendances')
            ->join('class_sections', 'attendances.class_section_id', '=', 'class_sections.id')
            ->where('class_sections.school_id', $schoolId)
            ->whereBetween('attendances.date', [$start_date, $end_date])
            ->where('attendances.type', $type);

        if (!empty($class_section_id)) {
            Log::info('Filtering by class_section_id in getClassSummary:', ['class_section_id' => $class_section_id]);
            $query->where('attendances.class_section_id', $class_section_id);
        }

        return $query->count();
    };

    return [
        'total_present' => $buildQuery(1),
        'total_absent' => $buildQuery(0),
        'total_late' => $buildQuery(2),
        'total_replacement' => $buildQuery(3)
    ];
}


    private function getSubjectSummary($start_date, $end_date, $subject_id = null)
    {
        $schoolId = Auth::user()->school_id;

        // Log the input parameters
        Log::info('getSubjectSummary called with:', [
            'start_date' => $start_date,
            'end_date' => $end_date,
            'subject_id' => $subject_id,
            'schoolId' => $schoolId
        ]);

        // Helper function to build a filtered query
        $buildQuery = function ($status) use ($schoolId, $start_date, $end_date, $subject_id) {
            $query = DB::table('subject_attendances')
                ->join('subjects', 'subject_attendances.subjects_id', '=', 'subjects.id')
                ->where('subjects.school_id', $schoolId)
                ->whereBetween('subject_attendances.date', [$start_date, $end_date])
                ->where('subject_attendances.status', $status);

            if (!empty($subject_id)) {
                Log::info('Filtering by subject_id in getSubjectSummary:', ['subject_id' => $subject_id]);
                $query->where('subject_attendances.subjects_id', $subject_id);
            }

            return $query->count();
        };

        $present = $buildQuery(1); // Present
        $absent = $buildQuery(0); // Absent

        $late = $buildQuery(2); // Late
        $replacement = $buildQuery(3); // Replacement

        $summary = [
            'total_present' => $present,
            'total_absent' => $absent,
            'total_late' => $late,
            'total_replacement' => $replacement
        ];

        // Log the summary
        Log::info('Subject summary results:', [
            'summary' => $summary,
            'subject_id' => $subject_id
        ]);

        return $summary;
    }

    /**
     * Get detailed attendance data by date for summary attendance page
     * This endpoint returns daily attendance data for a class or subject
     */
    public function getDetailedAttendance(Request $request)
    {
        ResponseService::noFeatureThenRedirect('Attendance Management');
        ResponseService::noAnyPermissionThenRedirect(['class-teacher', 'attendance-list']);

        $schoolId = Auth::user()->school_id;
        $type = $request->input('type'); // 'class' or 'subject'
        $classSectionId = $request->input('class_section_id');
        $subjectId = $request->input('subject_id');

        // Parse dates
        $startDate = $request->filled('start_date')
            ? Carbon::createFromFormat('d-m-Y', $request->start_date)->startOfDay()
            : Carbon::now()->startOfMonth();

        $endDate = $request->filled('end_date')
            ? Carbon::createFromFormat('d-m-Y', $request->end_date)->endOfDay()
            : Carbon::now()->endOfMonth();

        // Generate all dates in the range
        $dateRange = [];
        $currentDate = clone $startDate;
        while ($currentDate <= $endDate) {
            $dateRange[] = $currentDate->format('Y-m-d');
            $currentDate->addDay();
        }

        $dailyData = [];

        if ($type === 'class') {
            // For class attendance
            foreach ($dateRange as $date) {
                $query = DB::table('attendances')
                    ->where('school_id', $schoolId)
                    ->where('date', $date);

                if ($classSectionId) {
                    $query->where('class_section_id', $classSectionId);
                }

                $present = (clone $query)->where('type', 1)->count();
                $absent = (clone $query)->where('type', 0)->count();
                $late = (clone $query)->where('type', 2)->count();
                $replacement = (clone $query)->where('type', 3)->count();

                $dailyData[] = [
                    'date' => Carbon::parse($date)->format('d-m-Y'),
                    'present' => $present,
                    'absent' => $absent,
                    'late' => $late,
                    'replacement' => $replacement
                ];
            }
        } else {
            // For subject attendance
            foreach ($dateRange as $date) {
                $query = DB::table('subject_attendances')
                    ->where('school_id', $schoolId)
                    ->where('date', $date);

                if ($subjectId) {
                    $query->where('subjects_id', $subjectId);
                }

                $present = (clone $query)->where('status', 1)->count();
                $absent = (clone $query)->where('status', 0)->count();
                $late = (clone $query)->where('status', 2)->count();
                $replacement = (clone $query)->where('status', 3)->count();

                $dailyData[] = [
                    'date' => Carbon::parse($date)->format('d-m-Y'),
                    'present' => $present,
                    'absent' => $absent,
                    'late' => $late,
                    'replacement' => $replacement
                ];
            }
        }

        return response()->json([
            'daily_data' => $dailyData,
            'date_range' => [
                'start' => $startDate->format('d-m-Y'),
                'end' => $endDate->format('d-m-Y')
            ]
        ]);
    }

    /**
     * Get students by attendance status for summary attendance page
     * This endpoint returns a list of student names based on the attendance status
     */
    public function getStudentsByStatus(Request $request)
    {
        ResponseService::noFeatureThenRedirect('Attendance Management');
        ResponseService::noAnyPermissionThenRedirect(['class-teacher', 'attendance-list']);

        $schoolId = Auth::user()->school_id;
        $status = $request->input('status');
        $isSubject = $request->input('is_subject', 0);
        $classSectionId = $request->input('class_section_id');
        $subjectId = $request->input('subject_id');

        // Parse dates
        $startDate = $request->filled('start_date')
            ? Carbon::createFromFormat('d-m-Y', $request->start_date)->startOfDay()
            : Carbon::now()->startOfMonth();

        $endDate = $request->filled('end_date')
            ? Carbon::createFromFormat('d-m-Y', $request->end_date)->endOfDay()
            : Carbon::now()->endOfMonth();

        // Log the request parameters
        Log::info('getStudentsByStatus called with:', [
            'status' => $status,
            'is_subject' => $isSubject,
            'class_section_id' => $classSectionId,
            'subject_id' => $subjectId,
            'start_date' => $startDate->format('Y-m-d'),
            'end_date' => $endDate->format('Y-m-d'),
            'schoolId' => $schoolId
        ]);

        // First, get the count from the summary to ensure we match the displayed number
        $count = 0;
        $allStudents = [];

        if ($isSubject) {
            // Get the subject summary count first
            $subjectSummary = $this->getSubjectSummary($startDate, $endDate, $subjectId);

            // Use the appropriate count based on status
            switch ($status) {
                case 1: $count = $subjectSummary['total_present']; break;
                case 0: $count = $subjectSummary['total_absent']; break;
                case 2: $count = $subjectSummary['total_late']; break;
                case 3: $count = $subjectSummary['total_replacement']; break;
            }

            // Subject attendance query - get all attendance records
            $query = DB::table('subject_attendances as sa')
                ->join('users as u', 'sa.user_id', '=', 'u.id')
                ->where('sa.school_id', $schoolId)
                ->where('sa.status', $status)
                ->whereBetween('sa.date', [$startDate, $endDate])
                ->select(
                    'sa.id as attendance_id',
                    'u.id',
                    DB::raw("CONCAT(u.first_name, ' ', u.last_name) as name"),
                    'sa.date'
                );

            if ($subjectId) {
                $query->where('sa.subjects_id', $subjectId);
            }

            // Get all attendance records
            $allAttendances = $query->get();

            // Get the total count of attendance records (this should match the summary count)
            $totalAttendanceCount = $allAttendances->count();

            // Log the raw attendance records for debugging
            Log::info('Raw subject attendance records:', [
                'count' => $totalAttendanceCount,
                'first_few_records' => $allAttendances->take(5)->toArray()
            ]);

            // Group by student ID to get unique students
            $studentGroups = $allAttendances->groupBy('id');

            // Create a collection of unique students with their attendance dates
            foreach ($studentGroups as $studentId => $attendances) {
                // Get the first record for the student name
                $firstRecord = $attendances->first();

                // Count the number of days this student has this attendance status
                $daysCount = $attendances->unique('date')->count();

                // Add to the array
                $allStudents[] = [
                    'id' => $studentId,
                    'name' => $firstRecord->name,
                    'days_count' => $daysCount
                ];
            }
        } else {
            // Get the class summary count first
            $classSummary = $this->getClassSummary($startDate, $endDate, $classSectionId);

            // Use the appropriate count based on status
            switch ($status) {
                case 1: $count = $classSummary['total_present']; break;
                case 0: $count = $classSummary['total_absent']; break;
                case 2: $count = $classSummary['total_late']; break;
                case 3: $count = $classSummary['total_replacement']; break;
            }

            // Regular attendance query - get all attendance records
            $query = DB::table('attendances as a')
                ->join('users as u', 'a.student_id', '=', 'u.id')
                ->where('a.school_id', $schoolId)
                ->where('a.type', $status)
                ->whereBetween('a.date', [$startDate, $endDate])
                ->select(
                    'a.id as attendance_id',
                    'u.id',
                    DB::raw("CONCAT(u.first_name, ' ', u.last_name) as name"),
                    'a.date'
                );

            if ($classSectionId) {
                $query->where('a.class_section_id', $classSectionId);
            }

            // Get all attendance records
            $allAttendances = $query->get();

            // Get the total count of attendance records (this should match the summary count)
            $totalAttendanceCount = $allAttendances->count();

            // Log the raw attendance records for debugging
            Log::info('Raw class attendance records:', [
                'count' => $totalAttendanceCount,
                'first_few_records' => $allAttendances->take(5)->toArray()
            ]);

            // Group by student ID to get unique students
            $studentGroups = $allAttendances->groupBy('id');

            // Create a collection of unique students with their attendance dates
            foreach ($studentGroups as $studentId => $attendances) {
                // Get the first record for the student name
                $firstRecord = $attendances->first();

                // Count the number of days this student has this attendance status
                $daysCount = $attendances->unique('date')->count();

                // Add to the array
                $allStudents[] = [
                    'id' => $studentId,
                    'name' => $firstRecord->name,
                    'days_count' => $daysCount
                ];
            }
        }

        // If we still don't have enough students to match the count, we need to try a different approach
        if (count($allStudents) < $count) {
            Log::warning('Not enough students found in initial query. Attempting to get all attendance records.', [
                'current_count' => count($allStudents),
                'expected_count' => $count
            ]);

            // Try a different approach - directly query for all students with this attendance status
            if ($isSubject) {
                // For subject attendance, we need to get all students with this status
                $directQuery = DB::table('subject_attendances as sa')
                    ->join('users as u', 'sa.user_id', '=', 'u.id')
                    ->where('sa.school_id', $schoolId)
                    ->where('sa.status', $status)
                    ->whereBetween('sa.date', [$startDate, $endDate])
                    ->select(
                        'u.id',
                        DB::raw("CONCAT(u.first_name, ' ', u.last_name) as name"),
                        DB::raw('COUNT(DISTINCT sa.date) as days_count')
                    )
                    ->groupBy('u.id', 'u.first_name', 'u.last_name');

                if ($subjectId) {
                    $directQuery->where('sa.subjects_id', $subjectId);
                }
            } else {
                // For regular attendance, we need to get all students with this type
                $directQuery = DB::table('attendances as a')
                    ->join('users as u', 'a.student_id', '=', 'u.id')
                    ->where('a.school_id', $schoolId)
                    ->where('a.type', $status)
                    ->whereBetween('a.date', [$startDate, $endDate])
                    ->select(
                        'u.id',
                        DB::raw("CONCAT(u.first_name, ' ', u.last_name) as name"),
                        DB::raw('COUNT(DISTINCT a.date) as days_count')
                    )
                    ->groupBy('u.id', 'u.first_name', 'u.last_name');

                if ($classSectionId) {
                    $directQuery->where('a.class_section_id', $classSectionId);
                }
            }

            // Get the direct query results
            $directResults = $directQuery->get();

            Log::info('Direct query results:', [
                'count' => $directResults->count(),
                'first_few_records' => $directResults->take(5)->toArray()
            ]);

            // If we got results, use them instead
            if ($directResults->count() > 0) {
                $allStudents = [];
                foreach ($directResults as $record) {
                    $allStudents[] = [
                        'id' => $record->id,
                        'name' => $record->name,
                        'days_count' => $record->days_count
                    ];
                }
            } else {
                // If we still don't have results, try one more approach - get all attendance records
                // This is a last resort fallback
                Log::warning('Direct query returned no results. Trying one more approach.');

                // Get all attendance records for the given status, regardless of date
                if ($isSubject) {
                    $allRecordsQuery = DB::table('subject_attendances as sa')
                        ->join('users as u', 'sa.user_id', '=', 'u.id')
                        ->where('sa.school_id', $schoolId)
                        ->where('sa.status', $status)
                        ->select(
                            'u.id',
                            DB::raw("CONCAT(u.first_name, ' ', u.last_name) as name")
                        )
                        ->distinct();

                    if ($subjectId) {
                        $allRecordsQuery->where('sa.subjects_id', $subjectId);
                    }
                } else {
                    $allRecordsQuery = DB::table('attendances as a')
                        ->join('users as u', 'a.student_id', '=', 'u.id')
                        ->where('a.school_id', $schoolId)
                        ->where('a.type', $status)
                        ->select(
                            'u.id',
                            DB::raw("CONCAT(u.first_name, ' ', u.last_name) as name")
                        )
                        ->distinct();

                    if ($classSectionId) {
                        $allRecordsQuery->where('a.class_section_id', $classSectionId);
                    }
                }

                $allRecords = $allRecordsQuery->get();

                Log::info('Fallback query results:', [
                    'count' => $allRecords->count(),
                    'first_few_records' => $allRecords->take(5)->toArray()
                ]);

                // Create a map of existing student IDs for quick lookup
                $existingStudentIds = collect($allStudents)->pluck('id')->toArray();

                // Add any missing students to our list
                foreach ($allRecords as $record) {
                    if (!in_array($record->id, $existingStudentIds)) {
                        $allStudents[] = [
                            'id' => $record->id,
                            'name' => $record->name,
                            'days_count' => 1 // Default to 1 day since we don't have detailed info
                        ];
                        $existingStudentIds[] = $record->id;
                    }
                }
            }
        }

        // Convert to collection for consistency
        $students = collect($allStudents);

        // Log the count discrepancy if any
        if ($count != $students->count()) {
            Log::info('Count discrepancy in getStudentsByStatus:', [
                'summary_count' => $count,
                'actual_students_count' => $students->count(),
                'params' => [
                    'status' => $status,
                    'is_subject' => $isSubject,
                    'class_section_id' => $classSectionId,
                    'subject_id' => $subjectId,
                    'date_range' => [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')]
                ]
            ]);
        }

        // Log the results
        Log::info('getStudentsByStatus results:', [
            'count' => count($students),
            'summary_count' => $count
        ]);

        return response()->json([
            'success' => true,
            'students' => $students,
            'total_count' => $count,
            'date_range' => [
                'start' => $startDate->format('d-m-Y'),
                'end' => $endDate->format('d-m-Y')
            ]
        ]);
    }
}
