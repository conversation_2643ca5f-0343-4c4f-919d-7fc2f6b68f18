<?php

namespace App\Imports;

use App\Models\StudentFeeType;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Throwable;
use Maatwebsite\Excel\Concerns\WithCustomValueBinder;
use PhpOffice\PhpSpreadsheet\Cell\DefaultValueBinder;
use Maatwebsite\Excel\Concerns\WithMappedCells;

class StudentFeeTypesImport extends DefaultValueBinder implements ToCollection, WithHeadingRow, WithCustomValueBinder
{
    private $class_id;
    private $school_id;
    private $session_year_id;

    public function __construct(int $class_id, int $school_id, int $session_year_id)
    {
        $this->class_id = $class_id;
        $this->school_id = $school_id;
        $this->session_year_id = $session_year_id;
    }

    /**
    * @param Collection $collection
    */
    public function collection(Collection $collection)
    {
        try {
            DB::beginTransaction();
            
            foreach($collection as $row) {
                if (empty($row['item_code'])) {
                    continue;
                }

                // Check if record exists
                $exists = StudentFeeType::where([
                    'class_id' => $this->class_id,
                    'school_id' => $this->school_id,
                    'item_code' => $row['item_code']
                ])->exists();

                if (!$exists) {
                    throw new \Exception("Item code {$row['item_code']} does not exist. New records cannot be created through import.");
                }

                StudentFeeType::where([
                    'class_id' => $this->class_id,
                    'school_id' => $this->school_id,
                    'item_code' => $row['item_code']
                ])->update([
                    'classification_code' => $row['classification_code'],
                    'fees_type_name' => $row['fees_type'],
                    'unit' => $row['uom'],
                    'quantity' => $row['quantity'],
                    'discount' => $row['discount'] ?? 0,
                    'tax' => $row['tax'] ?? 0,
                    'unit_price' => $this->cleanPrice($row['unit_price']),
                    'remarks' => $row['remarks'] ?? null
                ]);
            }
            
            DB::commit();
            return "Import Successful";
        } catch (Throwable $e) {
            DB::rollback();
            throw $e;
        }
    }

    private function cleanPrice($price)
    {
        return (float) str_replace(['RM ', ','], '', $price);
    }
}
