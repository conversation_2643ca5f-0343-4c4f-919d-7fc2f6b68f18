<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class TeacherAttendance extends Model
{
    protected $table = 'teacher_attendance';
    
    protected $fillable = [
        "id",
        "school_id",
        "status",
        "date",
        "clock_in",
        "clock_out",
        "total_time",
        "user_id",
        "student_remark",
        "remark_picture",
        "subject_id",
        "in_temperature",
        "out_temperature",
    ];

    // Relationship with User model for students
    public function student()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }
}
