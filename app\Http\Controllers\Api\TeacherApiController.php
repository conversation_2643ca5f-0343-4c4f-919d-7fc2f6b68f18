<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Attendance;
use App\Models\ClassSection;
use App\Models\File;
use App\Models\SubjectAttendance;
use App\Models\UserNotifications;
use App\Repositories\Reward\RewardInterface;
use App\Repositories\Announcement\AnnouncementInterface;
use App\Repositories\AnnouncementClass\AnnouncementClassInterface;
use App\Repositories\Assignment\AssignmentInterface;
use App\Repositories\AssignmentSubmission\AssignmentSubmissionInterface;
use App\Repositories\Attendance\AttendanceInterface;
use App\Repositories\ClassSection\ClassSectionInterface;
use App\Repositories\ClassSubject\ClassSubjectInterface;
use App\Repositories\ClassTeachers\ClassTeachersInterface;
use App\Repositories\Exam\ExamInterface;
use App\Repositories\ExamMarks\ExamMarksInterface;
use App\Repositories\ExamResult\ExamResultInterface;
use App\Repositories\ExamTimetable\ExamTimetableInterface;
use App\Repositories\Files\FilesInterface;
use App\Repositories\Grades\GradesInterface;
use App\Repositories\Holiday\HolidayInterface;
use App\Repositories\Gallery\GalleryInterface;
use App\Repositories\Lessons\LessonsInterface;
use App\Repositories\Student\StudentInterface;
use App\Repositories\StudentSubject\StudentSubjectInterface;
use App\Repositories\SubjectTeacher\SubjectTeacherInterface;
use App\Repositories\Timetable\TimetableInterface;
use App\Repositories\Topics\TopicsInterface;
use App\Repositories\User\UserInterface;
use App\Rules\uniqueLessonInClass;
use App\Rules\uniqueTopicInLesson;
use App\Services\CachingService;
use App\Services\ResponseService;
use App\Services\FileSizeLimitService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use JetBrains\PhpStorm\NoReturn;
use Throwable;
use App\Rules\YouTubeUrl;
use App\Rules\DynamicMimes;
use PDF;
use Str;

//use App\Models\Parents;

class TeacherApiController extends Controller
{

    private StudentInterface $student;
    private AttendanceInterface $attendance;
    private TimetableInterface $timetable;
    private AssignmentInterface $assignment;
    private AssignmentSubmissionInterface $assignmentSubmission;
    private CachingService $cache;
    private ClassSubjectInterface $classSubject;
    private FilesInterface $files;
    private LessonsInterface $lesson;
    private TopicsInterface $topic;
    private AnnouncementInterface $announcement;
    private AnnouncementClassInterface $announcementClass;
    private SubjectTeacherInterface $subjectTeacher;
    private StudentSubjectInterface $studentSubject;
    private HolidayInterface $holiday;
    private ExamInterface $exam;
    private ExamTimetableInterface $examTimetable;
    private ExamMarksInterface $examMarks;
    private UserInterface $user;
    private ClassSectionInterface $classSection;
    private ClassTeachersInterface $classTeacher;
    private GalleryInterface $gallery;


    public function __construct(StudentInterface $student, AttendanceInterface $attendance, TimetableInterface $timetable, AssignmentInterface $assignment, AssignmentSubmissionInterface $assignmentSubmission, CachingService $cache, ClassSubjectInterface $classSubject, FilesInterface $files, LessonsInterface $lesson, TopicsInterface $topic, AnnouncementInterface $announcement, AnnouncementClassInterface $announcementClass, SubjectTeacherInterface $subjectTeacher, StudentSubjectInterface $studentSubject, HolidayInterface $holiday, ExamInterface $exam, ExamTimetableInterface $examTimetable, ExamMarksInterface $examMarks, UserInterface $user, ClassSectionInterface $classSection, ClassTeachersInterface $classTeacher, GalleryInterface $gallery)
    {
        $this->student = $student;
        $this->attendance = $attendance;
        $this->timetable = $timetable;
        $this->assignment = $assignment;
        $this->assignmentSubmission = $assignmentSubmission;
        $this->cache = $cache;
        $this->classSubject = $classSubject;
        $this->files = $files;
        $this->lesson = $lesson;
        $this->topic = $topic;
        $this->announcement = $announcement;
        $this->announcementClass = $announcementClass;
        $this->subjectTeacher = $subjectTeacher;
        $this->studentSubject = $studentSubject;
        $this->holiday = $holiday;
        $this->exam = $exam;
        $this->examTimetable = $examTimetable;
        $this->examMarks = $examMarks;
        $this->user = $user;
        $this->classSection = $classSection;
        $this->classTeacher = $classTeacher;
        $this->gallery = $gallery;
    }

    public function getCommission(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'year' => 'nullable|numeric',
            'type' => 'required|in:daily,month'
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }

        try {
            $year = $request->year ?? date('Y');
            $type = $request->type;

            if ($type === 'daily') {
                $commissions = DB::table('subject_attendances as sa')
                    ->select(
                        DB::raw('ROUND(SUM(CASE WHEN sa.commission_typee = 0 THEN (sa.fees_per_section * (sa.commission_amountt / 100)) ELSE sa.commission_amountt END), 2) as total_commission'),
                        DB::raw('COUNT(sa.id) as total_attendance'),
                        DB::raw('MONTH(sa.date) as month')
                    )
                    ->join('subjects as s', 's.id', '=', 'sa.subjects_id')
                    ->join('users as u', 'u.id', '=', 'sa.user_id')
                    ->where('sa.school_id', Auth::user()->school_id)
                    ->where('sa.teacher_id', Auth::user()->id)
                    ->whereYear('sa.date', $year)
                    ->groupBy(DB::raw('MONTH(sa.date)'))
                    ->orderByDesc('month')
                    ->get();
            } else {
                $commissions = DB::table('subject_attendances')
                    ->select(
                        DB::raw('ROUND(SUM(DISTINCT CASE WHEN sa.commission_typee = 0 THEN (sa.fees_per_month * (sa.commission_amountt / 100)) ELSE sa.commission_amountt END), 2) as total_commission'),
                        DB::raw('COUNT(DISTINCT subjects_id) as total_attendance'),
                        DB::raw('MONTH(date) as month')
                    )
                    ->where('school_id', Auth::user()->school_id)
                    ->where('teacher_id', Auth::user()->id)
                    ->whereYear('date', $year)
                    ->groupBy(DB::raw('MONTH(date)'))
                    ->orderByDesc('month')
                    ->get();
            }

            ResponseService::successResponse('Commission data fetched successfully', $commissions);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function getCommissionDetail(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'year' => 'nullable|numeric',
            'month' => 'required|numeric|between:1,12',
            'type' => 'required|in:daily,month'
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }

        try {
            $year = $request->year ?? date('Y');
            $month = $request->month;
            $type = $request->type;

            $query = DB::table('subject_attendances as sa')
                ->select(
                    'u.first_name as student_name',
                    's.name as subject_name',
                    DB::raw('DATE(sa.date) as date'),
                    DB::raw('ROUND(' . ($type === 'daily' ? 'CASE WHEN sa.commission_typee = 0 THEN (sa.fees_per_section * (sa.commission_amountt / 100)) ELSE sa.commission_amountt END' : 'CASE WHEN sa.commission_typee = 0 THEN (sa.feess_per_month * (sa.commission_amountt / 100)) ELSE sa.commission_amountt END') . ', 2) as commission')
                )
                ->join('subjects as s', 's.id', '=', 'sa.subjects_id')
                ->join('users as u', 'u.id', '=', 'sa.user_id')
                ->where('sa.school_id', Auth::user()->school_id)
                ->where('sa.teacher_id', Auth::user()->id)
                ->whereYear('sa.date', $year)
                ->whereMonth('sa.date', $month)
                ->orderBy('sa.date');

            $commissions = $query->get();

            ResponseService::successResponse('Commission details fetched successfully', $commissions);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    #[NoReturn] public function login(Request $request)
    {
        if (Auth::attempt([
            'email'    => $request->email,
            'password' => $request->password
        ])) {

            $auth = Auth::user();
            // $permission = $auth;
            if ($request->fcm_id) {
                $auth->fcm_id = $request->fcm_id;
                $auth->save();
            }
            // Check school status is activated or not
            if ($auth->school->status == 0 || $auth->status == 0) {
                $auth->fcm_id = '';
                $auth->save();
                ResponseService::errorResponse(trans('your_account_has_been_deactivated_please_contact_admin'), null, config('constants.RESPONSE_CODE.INVALID_LOGIN'));
            }

            $token = $auth->createToken($auth->first_name)->plainTextToken;
            if (Auth::user()->hasRole('Teacher')) {
                $user = $auth->load(['teacher','teacher.staffSalary.payrollSetting']);
            } else {
                $user = $auth->load(['staff','staff.staffSalary.payrollSetting']);
            }
            ResponseService::successResponse('User logged-in!', $user, ['token' => $token], config('constants.RESPONSE_CODE.LOGIN_SUCCESS'));
        }

        ResponseService::errorResponse('Invalid Login Credentials', null, config('constants.RESPONSE_CODE.INVALID_LOGIN'));
    }

    public function subjects(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'class_section_id' => 'nullable|numeric',
            'subject_id'       => 'nullable|numeric'
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            $user = $request->user();
            $teacher = $user->teacher;
            $subjects = $teacher->subjects()
                ->whereHas('subject', function($q) {
                    $q->whereNull('deleted_at');
                })
                ->whereHas('class_section', function($q) {
                    $q->whereNull('deleted_at');
                })
                ->whereHas('class_subject', function($q) {
                    $q->whereNull('deleted_at');
                });
            
            if ($request->class_section_id) {
                $subjects = $subjects->where('class_section_id', $request->class_section_id);
            }
            if ($request->subject_id) {
                $subjects = $subjects->where('subject_id', $request->subject_id);
            }
            $subjects = $subjects->with(['subject' => function($q) {
                    $q->whereNull('deleted_at');
                },
                'class_subject' => function($q) {
                    $q->whereNull('deleted_at');
                }
            ])->groupBy('subject_id')->get();
            ResponseService::successResponse('Teacher Subject Fetched Successfully.', $subjects);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function getAssignment(Request $request)
    {
        ResponseService::noFeatureThenSendJson('Assignment Management');
        ResponseService::noPermissionThenSendJson('assignment-list');
        $validator = Validator::make($request->all(), [
            'class_section_id' => 'nullable|numeric',
            'class_subject_id'       => 'nullable|numeric'
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            $sql = $this->assignment->builder()->with('class_section.class.stream', 'file', 'class_subject', 'class_section.medium');
            if ($request->class_section_id) {
                $sql = $sql->where('class_section_id', $request->class_section_id);
            }

            if ($request->class_subject_id) {
                $sql = $sql->where('class_subject_id', $request->class_subject_id);
            }



            $data = $sql->orderBy('id', 'DESC')->paginate();
            ResponseService::successResponse('Assignment Fetched Successfully.', $data);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function createAssignment(Request $request)
    {
        ResponseService::noFeatureThenSendJson('Assignment Management');
        ResponseService::noPermissionThenSendJson('assignment-create');
        $validator = Validator::make($request->all(), [
            "class_section_id"            => 'required|numeric',
            "class_subject_id"            => 'required|numeric',
            "name"                        => 'required',
            "instructions"                => 'nullable',
            "due_date"                    => 'required|date',
            "points"                      => 'nullable',
            "resubmission"                => 'nullable|boolean',
            "extra_days_for_resubmission" => 'nullable|numeric'
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            DB::beginTransaction();
            $sessionYear = $this->cache->getDefaultSessionYear();

            $assignmentData = array(
                ...$request->all(),
                'due_date'                    => date('Y-m-d H:i', strtotime($request->due_date)),
                'resubmission'                => $request->resubmission ? 1 : 0,
                'extra_days_for_resubmission' => $request->resubmission ? $request->extra_days_for_resubmission : null,
                'session_year_id'             => $sessionYear->id,
                'created_by'                  => Auth::user()->id,
            );
            $assignment = $this->assignment->create($assignmentData);

            $subject_name = $this->classSubject->findById($request->class_subject_id, ['id', 'subject_id'], ['subject'])->subject_with_name;
            $title = 'New assignment added in ' . $subject_name;
            $body = $request->name;
            $type = "assignment";

            $user = $this->student->builder()->select('user_id')->where('class_section_id', $request->class_section_id)->get()->pluck('user_id');

            send_notification($user, $title, $body, $type);

            if ($request->hasFile('file')) {
                $assignmentModelAssociate = $this->files->model()->modal()->associate($assignment);
                foreach ($request->file as $file_upload) {
                    $tempFileData = array(
                        'modal_type' => $assignmentModelAssociate->modal_type,
                        'modal_id'   => $assignmentModelAssociate->modal_id,
                        'file_name'  => $file_upload->getClientOriginalName(),
                        'type'       => 1,
                        'file_url'   => $file_upload
                    );
                    $fileData[] = $tempFileData; // Store Temp File Data in Multi-Dimensional File Data Array
                }
                $this->files->createBulk($fileData); // Store File Data
            }
            DB::commit();
            ResponseService::successResponse('Data Stored Successfully');
        } catch (Throwable $e) {
            if (Str::contains($e->getMessage(), [
                'does not exist','file_get_contents'
            ])) {
                DB::commit();
                ResponseService::warningResponse("Data Stored successfully. But App push notification not send.");
            } else {
                ResponseService::logErrorResponse($e);
                ResponseService::errorResponse();
            }
        }
    }

    public function updateAssignment(Request $request)
    {
        ResponseService::noFeatureThenSendJson('Assignment Management');
        ResponseService::noPermissionThenSendJson('assignment-edit');
        $validator = Validator::make($request->all(), [
            "assignment_id"               => 'required|numeric',
            "class_section_id"            => 'required|numeric',
            "class_subject_id"                  => 'required|numeric',
            "name"                        => 'required',
            "instructions"                => 'nullable',
            "due_date"                    => 'required|date',
            "points"                      => 'nullable',
            "resubmission"                => 'nullable|boolean',
            "extra_days_for_resubmission" => 'nullable|numeric'
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            DB::beginTransaction();
            $sessionYear = $this->cache->getDefaultSessionYear();

            $sessionYear = $this->cache->getDefaultSessionYear();

            $assignmentData = array(
                ...$request->all(),
                'due_date'                    => date('Y-m-d H:i', strtotime($request->due_date)),
                'resubmission'                => $request->resubmission ? 1 : 0,
                'extra_days_for_resubmission' => $request->resubmission ? $request->extra_days_for_resubmission : null,
                'session_year_id'             => $sessionYear->id,
                'created_by'                  => Auth::user()->id,
            );
            $assignment = $this->assignment->update($request->assignment_id, $assignmentData);

            $subject_name = $this->classSubject->findById($request->class_subject_id, ['id', 'subject_id'], ['subject'])->subject_with_name;
            $title = 'New assignment added in ' . $subject_name;
            $body = $request->name;
            $type = "assignment";

            $user = $this->student->builder()->select('user_id')->where('class_section_id', $request->class_section_id)->get()->pluck('user_id');

            send_notification($user, $title, $body, $type);

            if ($request->hasFile('file')) {
                $assignmentModelAssociate = $this->files->model()->modal()->associate($assignment);
                foreach ($request->file as $file_upload) {
                    $tempFileData = array(
                        'modal_type' => $assignmentModelAssociate->modal_type,
                        'modal_id'   => $assignmentModelAssociate->modal_id,
                        'file_name'  => $file_upload->getClientOriginalName(),
                        'type'       => 1,
                        'file_url'   => $file_upload
                    );
                    $fileData[] = $tempFileData; // Store Temp File Data in Multi-Dimensional File Data Array
                }
                $this->files->createBulk($fileData); // Store File Data
            }
            DB::commit();
            ResponseService::successResponse('Data Updated Successfully');
        } catch (Throwable $e) {
            if (Str::contains($e->getMessage(), [
                'does not exist','file_get_contents'
            ])) {
                DB::commit();
                ResponseService::warningResponse("Data Stored successfully. But App push notification not send.");
            } else {
                ResponseService::logErrorResponse($e);
                ResponseService::errorResponse();
            }
        }
    }

    public function deleteAssignment(Request $request)
    {
        ResponseService::noFeatureThenSendJson('Assignment Management');
        ResponseService::noPermissionThenSendJson('assignment-delete');
        try {
            DB::beginTransaction();
            $this->assignment->deleteById($request->assignment_id);
            DB::commit();
            ResponseService::successResponse('Data Deleted Successfully');
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function getAssignmentSubmission(Request $request)
    {
        ResponseService::noFeatureThenSendJson('Assignment Management');
        ResponseService::noPermissionThenSendJson('assignment-submission');
        $validator = Validator::make($request->all(), ['assignment_id' => 'required|nullable|numeric']);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            $data = $this->assignmentSubmission->builder()->with('assignment.class_subject.subject:id,name,type', 'file', 'student:id,first_name,last_name,image')->where('assignment_id', $request->assignment_id)->get();

            ResponseService::successResponse('Assignment Fetched Successfully', $data);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function updateAssignmentSubmission(Request $request)
    {
        ResponseService::noFeatureThenSendJson('Assignment Management');
        ResponseService::noPermissionThenSendJson('assignment-submission');
        $validator = Validator::make($request->all(), [
            'assignment_submission_id' => 'required|numeric',
            'status'                   => 'required|numeric|in:1,2',
            'points'                   => 'nullable|numeric',
            'feedback'                 => 'nullable'
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }

        try {
            DB::beginTransaction();
            $updateAssignmentSubmissionData = array(
                'feedback' => $request->feedback,
                'points'   => $request->status == 1 ? $request->points : NULL,
                'status'   => $request->status,
            );
            $assignmentSubmission = $this->assignmentSubmission->update($request->assignment_submission_id, $updateAssignmentSubmissionData);

            $assignmentData = $this->assignment->builder()->where('id', $assignmentSubmission->assignment_id)->with('class_subject.subject')->first();
            if ($request->status == 1) {
                $title = "Assignment accepted";
                $body = $assignmentData->name . " accepted in " . $assignmentData->class_subject->subject->name_with_type . " subject";
            } else {
                $title = "Assignment rejected";
                $body = $assignmentData->name . " rejected in " . $assignmentData->class_subject->subject->name_with_type . " subject";
            }

            $type = "assignment";
            $user = $this->student->builder()->select('user_id')->where('id', $assignmentSubmission->student_id)->get()->pluck('user_id');
            send_notification($user, $title, $body, $type);
            DB::commit();
            ResponseService::successResponse('Data Updated Successfully');
        } catch (Throwable $e) {
            if (Str::contains($e->getMessage(), [
                'does not exist','file_get_contents'
            ])) {
                DB::commit();
                ResponseService::warningResponse("Data Stored successfully. But App push notification not send.");
            } else {
                ResponseService::logErrorResponse($e);
                ResponseService::errorResponse();
            }
        }
    }

    public function getLesson(Request $request)
    {
        ResponseService::noFeatureThenSendJson('Lesson Management');
        ResponseService::noPermissionThenSendJson('lesson-list');
        $validator = Validator::make($request->all(), [
            'lesson_id'        => 'nullable|numeric',
            'class_section_id' => 'nullable|numeric',
            'class_subject_id'       => 'nullable|numeric'
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            $sql = $this->lesson->builder()->with('file')->withCount('topic');

            if ($request->lesson_id) {
                $sql = $sql->where('id', $request->lesson_id);
            }

            if ($request->class_section_id) {
                $sql = $sql->where('class_section_id', $request->class_section_id);
            }

            if ($request->class_subject_id) {
                $sql = $sql->where('class_subject_id', $request->class_subject_id);
            }
            $data = $sql->orderBy('id', 'DESC')->get();
            ResponseService::successResponse('Lesson Fetched Successfully', $data);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function createLesson(Request $request)
    {
        ResponseService::noFeatureThenSendJson('Lesson Management');
        ResponseService::noPermissionThenSendJson('lesson-create');
        $validator = Validator::make(
            $request->all(),
            [
                'name'                  => ['required', new uniqueLessonInClass($request->class_section_id, $request->class_subject_id)],
                'description'           => 'required',
                'class_section_id'      => 'required|numeric',
                'class_subject_id'      => 'required|numeric',
                'file'             => 'nullable|array',
                'file.*.type'      => 'required|in:file_upload,youtube_link,video_upload',
                'file.*.name'      => 'required_with:file.*.type',
                'file.*.thumbnail' => 'required_if:file.*.type,youtube_link,video_upload',
                'file.*.link'      => ['nullable', 'required_if:file.*.type,youtube_link', new YouTubeUrl], //Regex for YouTube Link
                'file.*.file'      => ['nullable', 'required_if:file.*.type,file_upload,video_upload', new DynamicMimes],
            ],
            [
                'name.unique' => trans('lesson_already_exists')
            ]
        );

        if ($validator->fails()) {
            ResponseService::errorResponse($validator->errors()->first());
        }
        try {
            DB::beginTransaction();
            $lessonData = array(
                ...$request->all(),
            );

            // Store Lesson Data
            $lesson = $this->lesson->create($lessonData);

            if (!empty($request->file)) {
                // Initialize the Empty Array
                $lessonFileData = array();

                // Create A File Model Instance
                $lessonFile = $this->files->model();

                // Get the Association Values of File with Lesson
                $lessonModelAssociate = $lessonFile->modal()->associate($lesson);

                // Loop to the File Array from Request
                foreach ($request->file as $file) {

                    // Initialize of Empty Array
                    //                    $tempFileData = array();

                    // Check the File type Exists
                    if ($file['type']) {

                        // Make custom Array for storing the data in TempFileData
                        $tempFileData = array(
                            'modal_type' => $lessonModelAssociate->modal_type,
                            'modal_id'   => $lessonModelAssociate->modal_id,
                            'file_name'  => $file['name'],
                        );

                        // If File Upload
                        if ($file['type'] == "file_upload") {

                            // Add Type And File Url to TempDataArray and make Thumbnail data null
                            $tempFileData['type'] = 1;
                            $tempFileData['file_thumbnail'] = null;
                            $tempFileData['file_url'] = $file['file'];
                        } elseif ($file['type'] == "youtube_link") {

                            // Add Type , Thumbnail and Link to TempDataArray
                            $tempFileData['type'] = 2;
                            $tempFileData['file_thumbnail'] = $file['thumbnail'];
                            $tempFileData['file_url'] = $file['link'];
                        } elseif ($file['type'] == "video_upload") {

                            // Add Type , File Thumbnail and File URL to TempDataArray
                            $tempFileData['type'] = 3;
                            $tempFileData['file_thumbnail'] = $file['thumbnail'];
                            $tempFileData['file_url'] = $file['file'];
                        } elseif ($file['type'] == "other_link") {

                            // Add Type , File Thumbnail and File URL to TempDataArray
                            $tempFileData['type'] = 4;
                            $tempFileData['file_thumbnail'] = $file['thumbnail'];
                            $tempFileData['file_url'] = $file['link'];
                        }

                        // Store to Multi Dimensional LessonFileData Array
                        $lessonFileData[] = $tempFileData;
                    }
                }
                // Store Bulk Data of Files
                $this->files->createBulk($lessonFileData);
            }
            DB::commit();
            ResponseService::successResponse('Data Stored Successfully');
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function updateLesson(Request $request)
    {
        ResponseService::noFeatureThenSendJson('Lesson Management');
        ResponseService::noPermissionThenSendJson('lesson-edit');
        $validator = Validator::make($request->all(), [
            'lesson_id'        => 'required|numeric',
            'name'             => 'required',
            'description'      => 'required',
            'class_section_id' => 'required|numeric',
            'class_subject_id'       => 'required|numeric',
            'file'             => 'nullable|array',
            'file.*.type'      => 'nullable|in:file_upload,youtube_link,video_upload',
            'file.*.name'      => 'required_with:file.*.type',
            'file.*.thumbnail' => 'required_if:file.*.type,youtube_link,video_upload',
            'file.*.file'      => 'required_if:file.*.type,file_upload,video_upload',
            'file.*.link'      => 'required_if:file.*.type,youtube_link',
        ]);
        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }

        $validator2 = Validator::make($request->all(), [
            'name' => [
                'required',
                new uniqueLessonInClass($request->class_section_id, $request->lesson_id)
            ]
        ]);
        if ($validator2->fails()) {
            ResponseService::errorResponse($validator2->errors()->first(), null, config('constants.RESPONSE_CODE.NOT_UNIQUE_IN_CLASS'));
        }
        try {
            DB::beginTransaction();
            $lesson = $this->lesson->update($request->lesson_id, $request->all());

            //Add the new Files
            if ($request->file) {
                // Initialize the Empty Array
                //                $lessonFileData = array();

                foreach ($request->file as $file) {
                    if ($file['type']) {

                        // Create A File Model Instance
                        $lessonFile = $this->files->model();

                        // Get the Association Values of File with Lesson
                        $lessonModelAssociate = $lessonFile->modal()->associate($lesson);

                        // Make custom Array for storing the data in TempFileData
                        $tempFileData = array(
                            'id'         => $file['id'] ?? null,
                            'modal_type' => $lessonModelAssociate->modal_type,
                            'modal_id'   => $lessonModelAssociate->modal_id,
                            'file_name'  => $file['name'],
                        );

                        // If File Upload
                        if ($file['type'] == "file_upload") {

                            // Add Type And File Url to TempDataArray and make Thumbnail data null
                            $tempFileData['type'] = 1;
                            $tempFileData['file_thumbnail'] = null;
                            if (!empty($file['file'])) {
                                $tempFileData['file_url'] = $file['file'];
                            }
                        } elseif ($file['type'] == "youtube_link") {

                            // Add Type , Thumbnail and Link to TempDataArray
                            $tempFileData['type'] = 2;
                            if (!empty($file['thumbnail'])) {
                                $tempFileData['file_thumbnail'] = $file['thumbnail'];
                            }
                            $tempFileData['file_url'] = $file['link'];
                        } elseif ($file['type'] == "video_upload") {

                            // Add Type , File Thumbnail and File URL to TempDataArray
                            $tempFileData['type'] = 3;
                            if (!empty($file['thumbnail'])) {
                                $tempFileData['file_thumbnail'] = $file['thumbnail'];
                            }
                            if (!empty($file['file'])) {
                                $tempFileData['file_url'] = $file['file'];
                            }
                        } elseif ($file['type'] == "other_link") {

                            // Add Type , File Thumbnail and File URL to TempDataArray
                            $tempFileData['type'] = 4;
                            if ($file['thumbnail']) {
                                $tempFileData['file_thumbnail'] = $file['thumbnail'];
                            }
                            $tempFileData['file_url'] = $file['link'];
                        }
                        $tempFileData['created_at'] = date('Y-m-d H:i:s');
                        $tempFileData['updated_at'] = date('Y-m-d H:i:s');

                        $this->files->updateOrCreate(['id' => $file['id'] ?? null], $tempFileData);
                    }
                }
            }
            DB::commit();
            ResponseService::successResponse('Data Updated Successfully');
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function deleteLesson(Request $request)
    {
        ResponseService::noFeatureThenSendJson('Lesson Management');
        ResponseService::noPermissionThenSendJson('lesson-delete');
        $validator = Validator::make($request->all(), ['lesson_id' => 'required|numeric',]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            DB::beginTransaction();
            $this->lesson->deleteById($request->lesson_id);
            DB::commit();
            ResponseService::successResponse('Data Deleted Successfully');
        } catch (Throwable) {
            ResponseService::errorResponse();
        }
    }

    public function getTopic(Request $request)
    {
        ResponseService::noFeatureThenSendJson('Lesson Management');
        ResponseService::noPermissionThenSendJson('topic-list');
        $validator = Validator::make($request->all(), ['lesson_id' => 'required|numeric',]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            $sql = $this->topic->builder()->with('lesson.class_section', 'lesson.class_subject.subject', 'file');
            $data = $sql->where('lesson_id', $request->lesson_id)->orderBy('id', 'DESC')->get();
            ResponseService::successResponse('Topic Fetched Successfully', $data);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function createTopic(Request $request)
    {
        ResponseService::noFeatureThenSendJson('Lesson Management');
        ResponseService::noPermissionThenSendJson('topic-create');
        $validator = Validator::make($request->all(), [
            'lesson_id'             => 'required|numeric',
            'name'                  => ['required', new uniqueTopicInLesson($request->lesson_id)],
            'description'           => 'required',
            'file'             => 'nullable|array',
            'file.*.type'      => 'required|in:file_upload,youtube_link,video_upload,other_link',
            'file.*.name'      => 'required_with:file.*.type',
            'file.*.thumbnail' => 'required_if:file.*.type,youtube_link,video_upload,other_link',
            'file.*.link'      => ['nullable', 'required_if:file.*.type,youtube_link', new YouTubeUrl], //Regex for YouTube Link
            'file.*.file'      => ['nullable', 'required_if:file.*.type,file_upload,video_upload', new DynamicMimes],

        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }

        try {
            DB::beginTransaction();
            $topic = $this->topic->create($request->all());

            if (!empty($request->file)) {
                // Initialize the Empty Array
                $topicFileData = array();

                // Create A File Model Instance
                $topicFile = $this->files->model();

                // Get the Association Values of File with Topic
                $topicModelAssociate = $topicFile->modal()->associate($topic);

                // Loop to the File Array from Request
                foreach ($request->file as $file) {

                    // Initialize of Empty Array
                    //                    $tempFileData = array();

                    // Check the File type Exists
                    if ($file['type']) {

                        // Make custom Array for storing the data in TempFileData
                        $tempFileData = array(
                            'modal_type' => $topicModelAssociate->modal_type,
                            'modal_id'   => $topicModelAssociate->modal_id,
                            'file_name'  => $file['name'],
                        );

                        // If File Upload
                        if ($file['type'] == "file_upload") {

                            // Add Type And File Url to TempDataArray and make Thumbnail data null
                            $tempFileData['type'] = 1;
                            $tempFileData['file_thumbnail'] = null;
                            $tempFileData['file_url'] = $file['file'];
                        } elseif ($file['type'] == "youtube_link") {

                            // Add Type , Thumbnail and Link to TempDataArray
                            $tempFileData['type'] = 2;
                            $tempFileData['file_thumbnail'] = $file['thumbnail'];
                            $tempFileData['file_url'] = $file['link'];
                        } elseif ($file['type'] == "video_upload") {

                            // Add Type , File Thumbnail and File URL to TempDataArray
                            $tempFileData['type'] = 3;
                            $tempFileData['file_thumbnail'] = $file['thumbnail'];
                            $tempFileData['file_url'] = $file['file'];
                        } elseif ($file['type'] == "other_link") {

                            // Add Type , File Thumbnail and File URL to TempDataArray
                            $tempFileData['type'] = 4;
                            $tempFileData['file_thumbnail'] = $file['thumbnail'];
                            $tempFileData['file_url'] = $file['link'];
                        }

                        // Store to Multi Dimensional topicFileData Array
                        $topicFileData[] = $tempFileData;
                    }
                }
                // Store Bulk Data of Files
                $this->files->createBulk($topicFileData);
            }

            DB::commit();

            ResponseService::successResponse('Data Stored Successfully');
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function updateTopic(Request $request)
    {
        ResponseService::noFeatureThenSendJson('Lesson Management');
        ResponseService::noPermissionThenSendJson('topic-edit');
        $validator = Validator::make($request->all(), [
            'topic_id'         => 'required|numeric',
            'name'             => 'required',
            'description'      => 'required',
            'file'             => 'nullable|array',
            'file.*.type'      => 'nullable|in:file_upload,youtube_link,video_upload,other_link',
            'file.*.name'      => 'required_with:file.*.type',
            'file.*.thumbnail' => 'required_if:file.*.type,youtube_link,video_upload,other_link',
            'file.*.file'      => 'required_if:file.*.type,file_upload,video_upload',
            'file.*.link'      => 'required_if:file.*.type,youtube_link,other_link',
        ]);
        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        $validator2 = Validator::make($request->all(), [
            'name' => [
                'required',
                new uniqueTopicInLesson($request->lesson_id, $request->topic_id)
            ],
        ]);
        if ($validator2->fails()) {
            ResponseService::errorResponse($validator2->errors()->first(), null, config('constants.RESPONSE_CODE.NOT_UNIQUE_IN_CLASS'));
        }
        try {
            DB::beginTransaction();
            $topic = $this->topic->update($request->topic_id, $request->all());

            //Add the new Files
            if ($request->file) {

                foreach ($request->file as $file) {
                    if ($file['type']) {

                        // Create A File Model Instance
                        $topicFile = $this->files->model();

                        // Get the Association Values of File with Topic
                        $topicModelAssociate = $topicFile->modal()->associate($topic);

                        // Make custom Array for storing the data in fileData
                        $fileData = array(
                            'id'         => $file['id'] ?? null,
                            'modal_type' => $topicModelAssociate->modal_type,
                            'modal_id'   => $topicModelAssociate->modal_id,
                            'file_name'  => $file['name'],
                        );

                        // If File Upload
                        if ($file['type'] == "file_upload") {

                            // Add Type And File Url to TempDataArray and make Thumbnail data null
                            $fileData['type'] = 1;
                            $fileData['file_thumbnail'] = null;
                            if (!empty($file['file'])) {
                                $fileData['file_url'] = $file['file'];
                            }
                        } elseif ($file['type'] == "youtube_link") {

                            // Add Type , Thumbnail and Link to TempDataArray
                            $fileData['type'] = 2;
                            if (!empty($file['thumbnail'])) {
                                $fileData['file_thumbnail'] = $file['thumbnail'];
                            }
                            $fileData['file_url'] = $file['link'];
                        } elseif ($file['type'] == "video_upload") {

                            // Add Type , File Thumbnail and File URL to TempDataArray
                            $fileData['type'] = 3;
                            if (!empty($file['thumbnail'])) {
                                $fileData['file_thumbnail'] = $file['thumbnail'];
                            }
                            if (!empty($file['file'])) {
                                $fileData['file_url'] = $file['file'];
                            }
                        } elseif ($file['type'] == "other_link") {

                            // Add Type , File Thumbnail and File URL to TempDataArray
                            $fileData['type'] = 4;
                            if ($file['thumbnail']) {
                                $fileData['file_thumbnail'] = $file['thumbnail'];
                            }
                            $fileData['file_url'] = $file['link'];
                        }
                        $fileData['created_at'] = date('Y-m-d H:i:s');
                        $fileData['updated_at'] = date('Y-m-d H:i:s');

                        $this->files->updateOrCreate(['id' => $file['id'] ?? null], $fileData);
                    }
                }
            }

            DB::commit();

            ResponseService::successResponse('Data Updated Successfully');
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function deleteTopic(Request $request)
    {
        ResponseService::noFeatureThenSendJson('Lesson Management');
        ResponseService::noPermissionThenSendJson('topic-delete');
        try {
            DB::beginTransaction();
            $this->topic->deleteById($request->topic_id);
            DB::commit();
            ResponseService::successResponse('Data Deleted Successfully');
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function updateFile(Request $request)
    {
        $validator = Validator::make($request->all(), ['file_id' => 'required|numeric',]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            $file = File::find($request->file_id);
            $file->file_name = $request->name;


            if ($file->type == "1") {
                // Type File :- File Upload

                if (!empty($request->file)) {
                    if (Storage::disk('public')->exists($file->getRawOriginal('file_url'))) {
                        Storage::disk('public')->delete($file->getRawOriginal('file_url'));
                    }

                    if ($file->modal_type == "App\Models\Lesson") {

                        $file->file_url = $request->file->store('lessons', 'public');
                    } else if ($file->modal_type == "App\Models\LessonTopic") {

                        $file->file_url = $request->file->store('topics', 'public');
                    } else {

                        $file->file_url = $request->file->store('other', 'public');
                    }
                }
            } elseif ($file->type == "2") {
                // Type File :- YouTube Link Upload

                if (!empty($request->thumbnail)) {
                    if (Storage::disk('public')->exists($file->getRawOriginal('file_url'))) {
                        Storage::disk('public')->delete($file->getRawOriginal('file_url'));
                    }

                    if ($file->modal_type == "App\Models\Lesson") {

                        $file->file_thumbnail = $request->thumbnail->store('lessons', 'public');
                    } else if ($file->modal_type == "App\Models\LessonTopic") {

                        $file->file_thumbnail = $request->thumbnail->store('topics', 'public');
                    } else {

                        $file->file_thumbnail = $request->thumbnail->store('other', 'public');
                    }
                }
                $file->file_url = $request->link;
            } elseif ($file->type == "3") {
                // Type File :- Video Upload

                if (!empty($request->file)) {
                    if (Storage::disk('public')->exists($file->getRawOriginal('file_url'))) {
                        Storage::disk('public')->delete($file->getRawOriginal('file_url'));
                    }

                    if ($file->modal_type == "App\Models\Lesson") {

                        $file->file_url = $request->file->store('lessons', 'public');
                    } else if ($file->modal_type == "App\Models\LessonTopic") {

                        $file->file_url = $request->file->store('topics', 'public');
                    } else {

                        $file->file_url = $request->file->store('other', 'public');
                    }
                }

                if (!empty($request->thumbnail)) {
                    if (Storage::disk('public')->exists($file->getRawOriginal('file_url'))) {
                        Storage::disk('public')->delete($file->getRawOriginal('file_url'));
                    }
                    if ($file->modal_type == "App\Models\Lesson") {

                        $file->file_thumbnail = $request->thumbnail->store('lessons', 'public');
                    } else if ($file->modal_type == "App\Models\LessonTopic") {

                        $file->file_thumbnail = $request->thumbnail->store('topics', 'public');
                    } else {

                        $file->file_thumbnail = $request->thumbnail->store('other', 'public');
                    }
                }
            }
            $file->save();

            ResponseService::successResponse('Data Stored Successfully', $file);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function deleteFile(Request $request)
    {
        $validator = Validator::make($request->all(), ['file_id' => 'required|numeric',]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            DB::beginTransaction();
            $this->files->deleteById($request->file_id);
            DB::commit();
            ResponseService::successResponse('Data Deleted Successfully');
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function getAnnouncement(Request $request)
    {
        ResponseService::noFeatureThenSendJson('Announcement Management');
        ResponseService::noPermissionThenSendJson('announcement-list');
        $validator = Validator::make($request->all(), [
            'class_section_id' => 'nullable|numeric',
            'subject_id'       => 'nullable|numeric',
        ]);
        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            $sql = $this->announcement->builder()->select('id', 'title', 'description')->with('file', 'announcement_class.class_section.class.stream', 'announcement_class.class_section.section', 'announcement_class.class_section.medium');
            if ($request->class_section_id) {
                $sql = $sql->whereHas('announcement_class', function ($q) use ($request) {
                    $q->where('class_section_id', $request->class_section_id);
                });
            }
            if ($request->class_subject_id) {
                $sql = $sql->whereHas('announcement_class', function ($q) use ($request) {
                    $q->where('class_subject_id', $request->class_subject_id);
                });
            }

            $data = $sql->orderBy('id', 'DESC')->paginate();
            ResponseService::successResponse('Announcement Fetched Successfully.', $data);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function sendAnnouncement(Request $request)
    {
        ResponseService::noFeatureThenSendJson('Announcement Management');
        ResponseService::noPermissionThenSendJson('announcement-create');
        $validator = Validator::make($request->all(), [
            'class_section_id' => 'required|numeric',
            'class_subject_id'       => 'required|numeric',
            'title'            => 'required',
            'description'      => 'nullable',
            'file'             => 'nullable'
        ]);
        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            DB::beginTransaction();
            $sessionYear = $this->cache->getDefaultSessionYear(); // Get Current Session Year
            // Custom Announcement Array to Store Data
            $announcementData = array(
                'title'           => $request->title,
                'description'     => $request->description,
                'session_year_id' => $sessionYear->id,
            );

            $announcement = $this->announcement->create($announcementData); // Store Data
            $announcementClassData = array();

            if ($request->class_subject_id) {

                // When Subject is passed then Store the data according to Subject Teacher
                $teacherId = Auth::user()->id; // Teacher ID
                $subjectTeacherData = $this->subjectTeacher->builder()->where('class_section_id', $request->class_section_id)->where(['teacher_id' => $teacherId, 'class_subject_id' => $request->class_subject_id])->with('subject')->first(); // Get the Subject Teacher Data
                $subjectName = $subjectTeacherData->subject_with_name ?? ''; // Subject Name

                if (empty($subjectName)) {
                    $subjectId = DB::table('class_subjects')->where('id', $request->class_subject_id)->value('subject_id');
                    $subjectName = DB::table('subjects')->where('id', $subjectId)->value('name');
                }

                // Check the Subject Type and Select Students According to it for Notification
                $getClassSubjectType = $this->classSubject->findById($request->class_subject_id, ['type']);
                if ($getClassSubjectType == 'Elective') {
                    $getStudentId = $this->studentSubject->builder()->select('student_id')->where('class_section_id', $request->class_section_id)->where(['class_subject_id' => $request->class_subject_id])->get()->pluck('student_id'); // Get the Student's ID According to Class Subject
                    $notifyUser = $this->student->builder()->select('user_id')->whereIn('id', $getStudentId)->get()->pluck('user_id'); // Get the Student's User ID
                } else {
                    $notifyUser = $this->student->builder()->select('user_id')->where('class_section_id', $request->class_section_id)->get()->pluck('user_id'); // Get the All Student's User ID In Specified Class
                }
                
                $guardianIds = collect();
                foreach ($notifyUser as $user){
                    $guardianId = DB::table('students')
                    ->where('user_id', $user)
                    ->pluck('guardian_id');

                    $guardianIds = $guardianIds->merge($guardianId);
                }
                $notifyUser = $notifyUser->merge($guardianIds);

                // Set class section with subject
                $announcementClassData = [
                    'announcement_id'  => $announcement->id,
                    'class_section_id' => $request->class_section_id,
                    'class_subject_id' => $request->class_subject_id
                ];
                $title = trans('New announcement in ') . $subjectName; // Title for Notification
                $this->announcementClass->create($announcementClassData);
            }

            // If File Exists
            if ($request->hasFile('file')) {
                $fileData = array(); // Empty FileData Array
                $fileInstance = $this->files->model(); // Create A File Model Instance
                $announcementModelAssociate = $fileInstance->modal()->associate($announcement); // Get the Association Values of File with Announcement
                foreach ($request->file as $file_upload) {
                    // Create Temp File Data Array
                    $tempFileData = array(
                        'modal_type' => $announcementModelAssociate->modal_type,
                        'modal_id'   => $announcementModelAssociate->modal_id,
                        'file_name'  => $file_upload->getClientOriginalName(),
                        'type'       => 1,
                        'file_url'   => $file_upload
                    );
                    $fileData[] = $tempFileData; // Store Temp File Data in Multi-Dimensional File Data Array
                }
                $this->files->createBulk($fileData); // Store File Data
            }

            if ($notifyUser !== null && !empty($title)) {
                $type = 'Class Section'; // Get The Type for Notification
                $body = $request->title; // Get The Body for Notification
                send_notification($notifyUser, $title, $body, $type); // Send Notification
            }

            DB::commit();
            ResponseService::successResponse('Data Stored Successfully');
        } catch (Throwable $e) {
            if (Str::contains($e->getMessage(), [
                'does not exist','file_get_contents'
            ])) {
                DB::commit();
                ResponseService::warningResponse("Data Stored successfully. But App push notification not send.");
            } else {
                ResponseService::logErrorResponse($e);
                ResponseService::errorResponse();
            }
        }
    }

    public function updateAnnouncement(Request $request)
    {
        ResponseService::noFeatureThenSendJson('Announcement Management');
        ResponseService::noPermissionThenSendJson('announcement-edit');
        $validator = Validator::make($request->all(), [
            'announcement_id'  => 'required|numeric',
            'class_section_id' => 'required|numeric',
            'class_subject_id'       => 'required|numeric',
            'title'            => 'required'
        ]);
        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            DB::beginTransaction();
            $sessionYear = $this->cache->getDefaultSessionYear(); // Get Current Session Year

            // Custom Announcement Array to Store Data
            $announcementData = array(
                'title'           => $request->title,
                'description'     => $request->description,
                'session_year_id' => $sessionYear->id,
            );

            $announcement = $this->announcement->update($request->announcement_id, $announcementData); // Store Data
            $announcementClassData = array();
            // $this->announcement->findById($request->announcement_id)->announcement_class->pluck('class_section_id')->toArray();
            $this->announcementClass->builder()->where('announcement_id', $request->announcement_id)->delete();

            //Check the Assign Data
            if ($request->class_subject_id) {

                // When Subject is passed then Store the data according to Subject Teacher
                // $teacherId = Auth::user()->teacher->id; // Teacher ID
                $teacherId = Auth::user()->id; // Teacher ID foreign key directly assign to user table

                $subjectTeacherData = $this->subjectTeacher->builder()->where('class_section_id', $request->class_section_id)->where(['teacher_id' => $teacherId, 'class_subject_id' => $request->class_subject_id])->first(); // Get the Subject Teacher Data
                $subjectName = $subjectTeacherData->subject->name; // Subject Name

                // Check the Subject Type and Select Students According to it for Notification
                $getClassSubjectType = $this->classSubject->builder()->where('id', $request->class_subject_id)->pluck('type')->first();
                if ($getClassSubjectType == 'Elective') {
                    $getStudentId = $this->studentSubject->builder()->select('student_id')->where('class_section_id', $request->class_section_id)->where(['class_subject_id' => $request->class_subject_id])->get()->pluck('student_id'); // Get the Student's ID According to Class Subject
                    $notifyUser = $this->student->builder()->select('user_id')->whereIn('id', $getStudentId)->get()->pluck('user_id'); // Get the Student's User ID
                } else {
                    $notifyUser = $this->student->builder()->select('user_id')->where('class_section_id', $request->class_section_id)->get()->pluck('user_id'); // Get the All Student's User ID In Specified Class
                }

                // Set class sections with subject
                $announcementClassData = [
                    'announcement_id'   => $announcement->id,
                    'class_section_id'  => $request->class_section_id,
                    'class_subject_id'  => $request->class_subject_id
                ];

                $title = trans('Updated announcement in') . $subjectName; // Title for Notification


            }

            $this->announcementClass->create($announcementClassData);

            // Delete announcement class sections
            // $this->announcementClass->builder()->where('announcement_id', $request->announcement_id)->where('class_section_id', $oldClassSection)->delete();


            // If File Exists
            if ($request->hasFile('file')) {
                $fileData = array(); // Empty FileData Array
                $fileInstance = $this->files->model(); // Create A File Model Instance
                $announcementModelAssociate = $fileInstance->modal()->associate($announcement); // Get the Association Values of File with Announcement
                foreach ($request->file as $file_upload) {
                    // Create Temp File Data Array
                    $tempFileData = array(
                        'modal_type' => $announcementModelAssociate->modal_type,
                        'modal_id'   => $announcementModelAssociate->modal_id,
                        'file_name'  => $file_upload->getClientOriginalName(),
                        'type'       => 1,
                        'file_url'   => $file_upload
                    );
                    $fileData[] = $tempFileData; // Store Temp File Data in Multi-Dimensional File Data Array
                }
                $this->files->createBulk($fileData); // Store File Data
            }

            if ($notifyUser !== null && !empty($title)) {
                $type = $request->aissgn_to; // Get The Type for Notification
                $body = $request->title; // Get The Body for Notification
                // send_notification($notifyUser, $title, $body, $type); // Send Notification
            }

            DB::commit();
            ResponseService::successResponse('Data Updated Successfully');
        } catch (Throwable $e) {
            if (Str::contains($e->getMessage(), [
                'does not exist','file_get_contents'
            ])) {
                DB::commit();
                ResponseService::warningResponse("Data Stored successfully. But App push notification not send.");
            } else {
                ResponseService::logErrorResponse($e);
                ResponseService::errorResponse();
            }
        }
    }

    public function deleteAnnouncement(Request $request)
    {
        ResponseService::noFeatureThenSendJson('Announcement Management');
        ResponseService::noPermissionThenSendJson('announcement-delete');
        $validator = Validator::make($request->all(), ['announcement_id' => 'required|numeric',]);
        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            DB::beginTransaction();
            $this->announcement->deleteById($request->announcement_id);
            DB::commit();
            ResponseService::successResponse('Data Deleted Successfully');
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function getAttendance(Request $request)
    {   
        ResponseService::noFeatureThenSendJson('Attendance Management');
        ResponseService::noPermissionThenSendJson('attendance-list');
        $class_section_id = $request->class_section_id;
        $attendance_type = $request->type;
        $date = date('Y-m-d', strtotime($request->date));

        $validator = Validator::make($request->all(), [
            'class_section_id' => 'required',
            'date'             => 'required|date',
            'type'             => 'in:0,1',
        ]);
        if ($validator->fails()) {
            ResponseService::validationError();
        }
        try {
            $sql = $this->attendance->builder()->with('user:id,first_name,last_name,image', 'user.student:id,user_id,roll_number')->where('class_section_id', $class_section_id)->where('date', $date);
            if (isset($attendance_type) && $attendance_type != '') {
                $sql->where('attendances.type', $attendance_type);
            }
            $data = $sql->get();
            $holiday = $this->holiday->builder()->where('date', $date)->get();
            if ($holiday->count()) {
                ResponseService::successResponse("Data Fetched Successfully", $data, [
                    'is_holiday' => true,
                    'holiday'    => $holiday,
                ]);
            } else if ($data->count()) {
                ResponseService::successResponse("Data Fetched Successfully", $data, ['is_holiday' => false]);
            } else {
                ResponseService::successResponse("Attendance not recorded", $data, ['is_holiday' => false]);
            }
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function getStudentSubjectAttendance(Request $request)
    {
        ResponseService::noFeatureThenSendJson('Attendance Management');
        ResponseService::noPermissionThenSendJson('attendance-list');
        $subject_id = $request->subject_id;
        // $date = date('Y-m-d', strtotime($request->date));
        $sessionYear = $this->cache->getDefaultSessionYear();


        $validator = Validator::make($request->all(), [
            'subject_id' => 'required',
            // 'date'             => 'required|date',
        ]);
        if ($validator->fails()) {
            ResponseService::validationError();
        }
        try {
            $sql = DB::table('students')
            ->distinct()
            ->select(
                'students.*',
                'users.*',
                DB::raw('CONCAT(users.first_name," ",users.last_name) as full_name'),
                DB::raw("CASE 
                WHEN subject_students.subject_id = $subject_id THEN subject_students.subject_id            
                WHEN scs.subject_id = $subject_id THEN scs.subject_id
                WHEN class_subjects.subject_id = $subject_id AND class_subjects.type = 'Compulsory' THEN class_subjects.subject_id
                ELSE NULL
            END AS subject_id")
            )
            ->leftJoin('class_sections', 'class_sections.id', '=', 'students.class_section_id')
            ->leftJoin('class_subjects', 'class_subjects.class_id', '=', 'class_sections.class_id')
            ->leftJoin('subjects', 'class_subjects.subject_id', '=', 'subjects.id')
            ->join('users', 'users.id', '=', 'students.user_id')
            ->leftJoin('subject_students', 'subject_students.student_id', '=', 'students.id')
            ->leftJoin('student_subjects','student_subjects.student_id','=','users.id')
            ->leftJoin('class_subjects as scs','scs.id','=','student_subjects.class_subject_id')
            ->where('students.session_year_id',$sessionYear->id)
            ->where(function ($query) use ($subject_id) {
                $query->where('subject_students.subject_id', $subject_id)
                    ->orWhere('scs.subject_id',$subject_id)
                    ->orWhere(function ($q) use ($subject_id) {
                        $q->where('class_subjects.subject_id', $subject_id)
                            ->where('class_subjects.type', 'Compulsory');
                    });
            })
            ->when($request->class_section_id,function($query) use ($request){
                $query->where('students.class_section_id',$request->class_section_id);
            })
            ->where('students.school_id', Auth::user()->school_id)
            ->whereNull('students.deleted_at')
            ->whereNull('users.deleted_at')
            ->whereNull('class_subjects.deleted_at');

            $data = $sql->get();
            ResponseService::successResponse("Data Fetched Successfully", $data);  
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function submitSubjectAttendance(Request $request) {
        ResponseService::noFeatureThenSendJson('Attendance Management');
    
        $validator = Validator::make($request->all(), [
            'subject_id'        => 'required',
            'attendance.*.user_id' => 'required',
            'attendance.*.status'       => 'required|in:0,1',
            'date'                    => 'required|date',
        ]);
        if ($validator->fails()) {
            ResponseService::validationError();
        }
    
        try {
            DB::beginTransaction();
    
            $subject_id = $request->subject_id;
            $student_ids_absent = [];
            $student_ids_present = [];
            $teacher_id = Auth::user()->id ?? null;
    
            $fees_per_section = DB::table('subjects')->where('id', $subject_id)->value('commission');
            $fees_per_month = DB::table('subjects')->where('id', $subject_id)->value('commission_month');
    
            $group_id = DB::table('user_group_details')
                ->where('subject_id', $request->subject_id)
                ->where('teacher_id', $teacher_id) 
                ->value('group_id');
    
            $commission_typee = DB::table('user_groups')->where('id', $group_id)->value('commission_type');
            $commission_amountt = DB::table('user_groups')->where('id', $group_id)->value('commission_amount');
    
            $studentsInsufficientFunds = [];
            $alreadyTakenStudents = [];
            foreach ($request->attendance as $value) {
                $data = (object)$value;
                $scanAttendanceData = [
                    'school_id' => Auth::user()->school_id,
                    'user_id' => $data->user_id,
                    'teacher_id' => $teacher_id,
                    'subjects_id' => $subject_id,
                    'status' => $data->status,
                    'date' => date('Y-m-d', strtotime($request->date)),
                    'fees_per_section' => $fees_per_section,
                    'fees_per_month' => $fees_per_month,
                    'commission_typee' => $commission_typee,
                    'commission_amountt' => $data->status == 0 ? 0 : $commission_amountt, // Set to 0 if absent
                    'created_at' => now(),
                    'updated_at' => now()
                ];
                $isOneTimeAttendanceSetting= DB::table('school_settings')
                    ->where('school_id',Auth::user()->school_id)
                    ->where('name','attendance_setting')
                    ->where('data','LIKE','%4%')
                    ->first();
                if($isOneTimeAttendanceSetting){
                    $isSubjectAttendanceExist=DB::table('subject_attendances')
                                    ->where('user_id',$data->user_id)
                                    ->where('subjects_id',$subject_id)
                                    ->where('date',date('Y-m-d',strtotime($request->date)))
                                    ->first();
                    if($isSubjectAttendanceExist){
                        $studentName = DB::table('users')
                            ->where('id', $data->user_id)
                            ->value(DB::raw("CONCAT(first_name, ' ', last_name) AS full_name"));
                        $alreadyTakenStudents[] = $studentName;
                        continue;
                    } 
                }
                DB::table('subject_attendances')->insertGetId($scanAttendanceData); 
            
                $deductionMethod = DB::table('school_settings')
                        ->select('name', 'data')
                        ->where('name', 'deduction_method')
                        ->where('school_id', Auth::user()->school_id)
                        ->first();

                $userFullName = DB::table('users')
                                ->where('id',$data->user_id)
                                ->value(DB::raw("CONCAT(first_name, ' ', last_name) AS full_name"));
                $subject = DB::table('subjects')
                            ->where('id',$subject_id)
                            ->first();
                $student = DB::table('students as s')
                            ->join('class_sections as cs','cs.id','=','s.class_section_id')
                            ->where('s.user_id',$data->user_id)
                            ->first();
                // Add credit status check here
                $creditStatus = DB::table('students')
                    ->where('user_id', $data->user_id)
                    ->value('credit_status');

                // Only proceed with deductions if credit_status is 0
                if ($creditStatus == 0 && $deductionMethod) {
                    if ($deductionMethod->data === "1" && in_array($data->status, [1, 2, 3])) {
                        $query = DB::table('subject_attendances as sa')
                            ->join('users as u', 'sa.user_id', '=', 'u.id')
                            ->join('credit_system as cs', 'sa.user_id', '=', 'cs.user_id')
                            ->join('students as s', 's.user_id', '=', 'sa.user_id')
                            ->join('class_sections as cls','cls.id','=','s.class_section_id')
                            ->join('classes as c','c.id','=','cls.class_id')
                            ->join('subjects as sub', 'sa.subjects_id', '=', 'sub.id',)
                            ->select('cs.user_id as credit_user', DB::raw("CONCAT(u.first_name, ' ', u.last_name) AS full_name"), 'c.id as class_id', 'cs.balance', 'cs.credit_amount', 'sub.commission')
                            ->where('sa.date', date('Y-m-d', strtotime($request->date)))
                            ->where('sa.subjects_id', $subject_id)
                            ->where('sa.status', 1)
                            ->where('cs.school_id', Auth::user()->school_id)
                            ->where('cs.user_id', $data->user_id)
                            ->first();
                        if($query){
                            $subjectPrice = -$query->commission;
                            $totalBalance = DB::table('credit_system')
                            ->where('user_id', $query->credit_user)
                            ->sum('credit_amount');
                            
                            $balance = $totalBalance + $subjectPrice;

                            DB::table('credit_system')->insert([
                                'school_id' => Auth::user()->school_id,
                                'class_id' => $query->class_id,
                                'user_id' => $query->credit_user,
                                'credit_amount' => $subjectPrice,
                                'balance' => $balance,
                                'detail' => 'Attended 1 Class' . '('.$subject->name.')',
                                'created_at' => Carbon::now(),
                                'updated_at' => Carbon::now()
                            ]);

                            if ($balance <= 0) {
                                $studentsInsufficientFunds[] = $query->full_name;
                            }
                        } else {
                            $subjectPrice = -$subject->commission;
                            $value = [
                                'school_id' => Auth::user()->school_id,
                                'class_id'  => $student->class_id,
                                'user_id'   => $data->user_id,
                                'credit_amount' => $subjectPrice,
                                'balance'       => $subjectPrice,
                                'detail' => 'Attended 1 Class' . '('.$subject->name.')',
                                'created_at' => Carbon::now(),
                                'updated_at' => Carbon::now()
                            ];
                            DB::table('credit_system')->insert($value);
                            $studentsInsufficientFunds[] = $userFullName;
                        }
                    } else if ($deductionMethod->data === "2" && in_array($data->status, [1, 2, 3])) {
                        $userId = DB::table('students as s')
                            ->join('users as u', 'u.id', '=', 's.user_id')
                            ->select('s.id', DB::raw("CONCAT(u.first_name, ' ', u.last_name) AS full_name"))
                            ->where('u.id', $data->user_id)
                            ->first();

                        $package = DB::table('purchase_package as pp')
                            ->join('subject_package as sp', 'pp.package_id', '=', 'sp.id')
                            ->select('pp.id as purchase_id')
                            ->where('pp.student_id', $userId->id)
                            ->whereIn('pp.status', [0, 2])
                            ->whereRaw("FIND_IN_SET(?, sp.subject_id)", [$subject_id])
                            ->first();
                        if ($package) {
                            $packageUsage = DB::table('package_usage as pu')
                                ->where('pu.student_id', $userId->id)
                                ->where('pu.purchase_package_id', $package->purchase_id)
                                ->orderBy('created_at', 'DESC')
                                ->first();
                            if ($packageUsage && $packageUsage->remaining_session > 0) {
                                if($packageUsage->remaining_session != 0){
                                    $remain = $packageUsage->remaining_session - 1;
                                }

                                DB::table('package_usage')->insert([
                                    'school_id' => Auth::user()->school_id,
                                    'purchase_package_id' => $package->purchase_id,
                                    'student_id' => $userId->id,
                                    'deduct' => -1,
                                    'remaining_session' => $remain ?? 0,
                                    'created_at' => Carbon::now(),
                                    'updated_at' => Carbon::now(),
                                ]);


                                if (in_array($remain, [1,2,3,4,5,6,7,8,9,10])) {
                                    $lowSessionCount[] = ['student_id' => $packageUsage->student_id, 'remaining_session' => $remain];
                                }else if ($remain == 0) {
                                    $noSessionCount[] =['student_id' => $packageUsage->student_id, 'remaining_session' => $remain];
                                }
                                if ($packageUsage && $remain == 0) {
                                    DB::table('purchase_package as pp')
                                        ->where('pp.id', $package->purchase_id)
                                        ->update([
                                            'status' => 1
                                        ]);
                                }
                            } else {
                                $studentsInsufficientFunds[] = $userId->full_name;
                            }
                        } else {
                            $studentsInsufficientFunds[] = $userFullName;
                        }
                    } else if ($deductionMethod->data === "3" && in_array($data->status, [1, 2, 3])) {
                        $packageUsed = false;
                        $userId = DB::table('students as s')
                            ->join('users as u', 'u.id', '=', 's.user_id')
                            ->select('s.id')
                            ->where('u.id', $data->user_id)
                            ->first();

                        $package = DB::table('purchase_package as pp')
                            ->join('subject_package as sp', 'pp.package_id', '=', 'sp.id')
                            ->select('pp.id as purchase_id', 'pp.package_id', 'pp.student_id')
                            ->where('pp.student_id', $userId->id)
                            ->whereIn('pp.status', [0, 2])
                            ->whereRaw("FIND_IN_SET(?, sp.subject_id)", [$subject_id])
                            ->first();

                        if ($package) {
                            $packageUsage = DB::table('package_usage as pu')
                            ->where('pu.student_id', $userId->id)
                            ->where('pu.purchase_package_id', $package->purchase_id)
                            ->orderBy('created_at', 'DESC')
                            ->first();
                            if ($packageUsage && $packageUsage->remaining_session > 0) {
                                if($packageUsage->remaining_session != 0){
                                    $remain = $packageUsage->remaining_session - 1;
                                    $packageUsed = true;
                                }

                                DB::table('package_usage')->insert([
                                    'school_id' => Auth::user()->school_id,
                                    'purchase_package_id' => $package->purchase_id,
                                    'student_id' => $userId->id,
                                    'deduct' => -1,
                                    'remaining_session' => $remain ?? 0,
                                    'created_at' => Carbon::now(),
                                    'updated_at' => Carbon::now(),
                                ]);


                                if (in_array($remain, [1,2,3,4,5,6,7,8,9,10])) {
                                    $lowSessionCount[] = ['student_id' => $packageUsage->student_id, 'remaining_session' => $remain];
                                }else if ($remain == 0) {
                                    $noSessionCount[] =['student_id' => $packageUsage->student_id, 'remaining_session' => $remain];
                                }
                                if ($packageUsage && $remain == 0) {
                                    DB::table('purchase_package as pp')
                                        ->where('pp.id', $package->purchase_id)
                                        ->update([
                                            'status' => 1
                                        ]);
                                }
                            } 
                        } 
                            
                        $query = DB::table('subject_attendances as sa')
                            ->join('users as u', 'sa.user_id', '=', 'u.id')
                            ->join('credit_system as cs', 'sa.user_id', '=', 'cs.user_id')
                            ->join('students as s', 's.user_id', '=', 'sa.user_id')
                            ->join('class_sections as cls','cls.id','=','s.class_section_id')
                            ->join('classes as c','c.id','=','cls.class_id')
                            ->join('subjects as sub', 'sa.subjects_id', '=', 'sub.id',)
                            ->select('cs.user_id as credit_user', DB::raw("CONCAT(u.first_name, ' ', u.last_name) AS full_name"), 'c.id as class_id', 'cs.balance', 'cs.credit_amount', 'sub.commission')
                            ->where('sa.date', date('Y-m-d', strtotime($request->date)))
                            ->where('sa.subjects_id', $subject_id)
                            ->where('sa.status', 1)
                            ->where('cs.school_id', Auth::user()->school_id)
                            ->where('cs.user_id', $data->user_id)
                            ->first();
                        if(!$packageUsed){
                            if($query){
                                $subjectPrice = -$query->commission;
                                $totalBalance = DB::table('credit_system')
                                ->where('user_id', $query->credit_user)
                                ->sum('credit_amount');
                                
                                $balance = $totalBalance + $subjectPrice;

                                DB::table('credit_system')->insert([
                                    'school_id' => Auth::user()->school_id,
                                    'class_id' => $query->class_id,
                                    'user_id' => $query->credit_user,
                                    'credit_amount' => $subjectPrice,
                                    'balance' => $balance,
                                    'detail' => 'Attended 1 Class' . '('.$subject->name.')',
                                    'created_at' => Carbon::now(),
                                    'updated_at' => Carbon::now()
                                ]);

                                if ($balance <= 0) {
                                    $studentsInsufficientFunds[] = $query->full_name;
                                }
                            } else {
                                $subjectPrice = -$subject->commission;
                                $value = [
                                    'school_id' => Auth::user()->school_id,
                                    'class_id'  => $student->class_id,
                                    'user_id'   => $data->user_id,
                                    'credit_amount' => $subjectPrice,
                                    'balance'       => $subjectPrice,
                                    'detail' => 'Attended 1 Class' . '('.$subject->name.')',
                                    'created_at' => Carbon::now(),
                                    'updated_at' => Carbon::now()
                                ];
                                DB::table('credit_system')->insert($value);
                                $studentsInsufficientFunds[] = $userFullName;
                            }
                        }
                    }
                } 
                
                if ($data->status == 0) {
                    $student_ids_absent[] = $data->user_id;
                } elseif ($data->status == 1) {
                    $student_ids_present[] = $data->user_id;
                }
            }
    
            $date = Carbon::parse(date('Y-m-d', strtotime($request->date)))->format('F jS, Y');
            $type = "attendance";
    
            
            if (!empty($student_ids_absent)) {
                $user_absent = $this->student->builder()->whereIn('user_id', $student_ids_absent)->pluck('guardian_id');
                $title = 'Absent';
                $body = 'Your child is absent on ' . $date;
                send_notification($user_absent, $title, $body, $type);
            }
    
            if (!empty($student_ids_present)) {
                $user_present = $this->student->builder()->whereIn('user_id', $student_ids_present)->pluck('guardian_id');
                $title = 'Present';
                $body = 'Your child is present on ' . $date;
                send_notification($user_present, $title, $body, $type);
            }

            if (!empty($lowSessionCount)) {
                $studentIds = array_column($lowSessionCount, 'student_id');
                $lowSessionId = DB::table('users as u')
                ->join('students as s', 'u.id', '=', 's.user_id')
                ->select('u.id', 's.guardian_id', 'u.first_name', 'u.last_name')
                ->whereIn('s.id', $studentIds)
                ->get();

                foreach ($lowSessionId as $user) {
                    // Get package info to calculate expiry date
                    $packageInfo = DB::table('purchase_package as pp')
                        ->join('subject_package as sp', 'pp.package_id', '=', 'sp.id')
                        ->join('students as s', 'pp.student_id', '=', 's.id')
                        ->where('s.id', $lowSessionCount[0]['student_id'])  // Use the student_id from lowSessionCount
                        ->whereRaw("FIND_IN_SET(?, sp.subject_id)", [$subject_id])
                        ->select('pp.date as purchase_date', 'sp.expiry_days')
                        ->orderBy('pp.id', 'DESC')
                        ->first();

                    // Calculate expiry date
                    $formattedExpirationDate = "";
                    if ($packageInfo) {
                        $purchaseDate = Carbon::parse($packageInfo->purchase_date);
                        $expiryDays = $packageInfo->expiry_days;
                        $expirationDate = $purchaseDate->addDays($expiryDays);
                        $formattedExpirationDate = $expirationDate->format('d-m-Y');
                    }

                    $notifyUser = array();
                    $notifyUser[] = $user->guardian_id;
                    $notifyUser[] = $user->id;
                    $userName = $user->first_name . " " . $user->last_name;
                    $title = "Session Updated";
                    $type = "session_update";
                    $body = "Clock-in successful! You have ".$lowSessionCount[0]['remaining_session']. " sessions left (expires ".$formattedExpirationDate.")";
                    send_notification($notifyUser, $title, $body, $type);
                }
            } 

            if (!empty($noSessionCount)) {
                $studentIds = array_column($noSessionCount, 'student_id');
                $lowSessionId = DB::table('users as u')
                ->join('students as s', 'u.id', '=', 's.user_id')
                ->select('u.id', 's.guardian_id' , 'u.first_name', 'u.last_name')
                ->whereIn('s.id', $studentIds)
                ->get();

                foreach ($lowSessionId as $user) {
                    $notifyUser = array();
                    $notifyUser[] = $user->guardian_id;
                    $notifyUser[] = $user->id;
                    $userName = $user->first_name . " " . $user->last_name;
                    $title = "Session Updated";
                    $type = "session_update";
                    $body = "Clock-in successful! Your sessions have finished.";
                    send_notification($notifyUser, $title, $body, $type);
                }
            }

    
            DB::commit();
            if (!empty($studentsInsufficientFunds) || !empty($alreadyTakenStudents)) {
                $response = [
                    "message" => "success",
                    'date' => $request->date
                ];

                if (!empty($studentsInsufficientFunds)) {
                    $namesInsufficient = implode(', ', $studentsInsufficientFunds);
                    $response['namesInsufficient'] = $namesInsufficient;
                }

                if (!empty($alreadyTakenStudents)) {
                    $response['namesInsufficient'] = 'Attendance already marked for: ' . implode(', ', $alreadyTakenStudents);
                }

                return response()->json($response);
            } else {
                ResponseService::successResponse('Data Stored Successfully');
            }
        } catch (Throwable $e) {
            if (Str::contains($e->getMessage(), ['does not exist', 'file_get_contents'])) {
                DB::commit();
                ResponseService::warningResponse("Data Stored successfully. But App push notification not sent.");
            } else {
                DB::rollback();
                ResponseService::logErrorResponse($e, "Attendance Controller -> Store method");
                ResponseService::errorResponse();
            }
        }
    }

    public function submitAttendance(Request $request)
    {
        ResponseService::noFeatureThenSendJson('Attendance Management');
        // ResponseService::noAnyPermissionThenSendJson([
        //     'attendance-create',
        //     'attendance-edit'
        // ]);
        $validator = Validator::make($request->all(), [
            'class_section_id'        => 'required',
            'attendance.*.student_id' => 'required',
            'attendance.*.type'       => 'required|in:0,1',
            'date'                    => 'required|date',
        ]);
        if ($validator->fails()) {
            ResponseService::validationError();
        }
        try {
            DB::beginTransaction();
            $sessionYear = $this->cache->getDefaultSessionYear();
            $date = date('Y-m-d', strtotime($request->date));
            $student_ids = array();
            $present_student_ids = array();
            for ($i = 0, $iMax = count($request->attendance); $i < $iMax; $i++) {

                $attendanceData = [
                    'class_section_id' => $request->class_section_id,
                    'student_id'       => $request->attendance[$i]['student_id'],
                    'session_year_id'  => $sessionYear->id,
                    //'type'             => $request->holiday ?? $request->attendance[$i]['type'],
                    'type'             => $request->attendance[$i]['type'],
                    'date'             => date('Y-m-d', strtotime($request->date)),
                ];

                if ($request->attendance[$i]['type'] == 0) {
                    $student_ids[] = $request->attendance[$i]['student_id'];
                } else if ($request->attendance[$i]['type'] == 1) {
                    $present_student_ids[] = $request->attendance[$i]['student_id'];
                }


                $attendance = $this->attendance->builder()->where('class_section_id', $request->class_section_id)->where('student_id', $request->attendance[$i]['student_id'])->whereDate('date', $date)->first();
                if ($attendance) {
                    $this->attendance->update($attendance->id, $attendanceData);
                } else {
                    $this->attendance->create($attendanceData);
                }

                $subjectAttendance = SubjectAttendance::where('user_id',$request->attendance[$i]['student_id'])
                                    ->where('date',date('Y-m-d', strtotime($request->date)))
                                    ->whereNull('subjects_id')
                                    ->first();
                if($subjectAttendance){
                    $subjectAttendance->update(['status' => $request->attendance[$i]['type']]);
                } else {
                    $array = [
                        'school_id' => Auth::user()->school_id,
                        'user_id'   => $request->attendance[$i]['student_id'],
                        'status'    => $request->attendance[$i]['type'],
                        'date'      =>  date('Y-m-d', strtotime($request->date)),
                    ];
                    SubjectAttendance::create($array);
                }
            }

            $date = Carbon::parse(date('Y-m-d', strtotime($request->date)))->format('F jS, Y');
            $type = "attendance";

            if ($request->absent_notification) {
                $user = $this->student->builder()->whereIn('user_id', $student_ids)->pluck('guardian_id');
                $title = 'Absent';
                $body = 'Your child is absent on ' . $date;
                send_notification($user, $title, $body, $type);
            }

            if (!empty($present_student_ids)) {
                $user = $this->student->builder()->whereIn('user_id', $present_student_ids)->pluck('guardian_id');
                $title = 'Present';
                $body = 'Your child is present on ' . $date;
                send_notification($user, $title, $body, $type);
            }
            
            DB::commit();
            ResponseService::successResponse('Data Stored Successfully');
        } catch (Throwable $e) {
            if (Str::contains($e->getMessage(), [
                'does not exist','file_get_contents'
            ])) {
                DB::commit();
                ResponseService::warningResponse("Data Stored successfully. But App push notification not send.");
            } else {
                ResponseService::logErrorResponse($e);
                ResponseService::errorResponse();
            }
        }
    }

    public function submitScanAttendance(Request $request){
        $schoolId = Auth::user()->school_id;
        $validator = Validator::make($request->all(), [
            'subject_id'      => 'required',
            'user_id'         => 'required_without:user_ids',
            'user_ids'        => 'required_without:user_id',
            'date'            => 'required|date',
            'clock_in'        => 'nullable',
            'clock_out'       => 'nullable',
            'in_temperature'  => 'nullable',
            'out_temperature' => 'nullable',
            'status'          => 'required',
            'remark'          => 'nullable',
            'file'            => 'nullable'
            
        ]);
        if ($validator->fails()) {
            ResponseService::validationError();
        }

        $subjectId = $request->subject_id;
        $teacherId = Auth::user()->id;
        $userIds = $request->user_ids ? explode(',', $request->user_ids) : [$request->user_id];
        $date = $request->date;
        $clock_in = $request->clock_in;
        $clock_out = $request->clock_out;
        $status = $request->status;
        $isOneTimeAttendanceSetting= DB::table('school_settings')
                                    ->where('school_id',$schoolId)
                                    ->where('name','attendance_setting')
                                    ->where('data','LIKE','%4%')
                                    ->first();
        $users = [];
        foreach($userIds as $userId) {
            $user = DB::table('users')
                ->select('users.id', 'users.first_name', 'users.last_name')
                ->where('users.id', $userId)
                ->first();
            if (!$user) {
                return redirect()->back()->with('error', 'One or more students do not belong to valid users.');
            }
            $users[] = $user;

            if($isOneTimeAttendanceSetting){
                $isSubjectAttendanceExist=DB::table('subject_attendances')
                                        ->where('user_id',$userId)
                                        ->where('subjects_id',$subjectId)
                                        ->where('date',date('Y-m-d',strtotime($date)))
                                        ->first();
                if($isSubjectAttendanceExist){
                    return response()->json([
                        "message" => "success",
                        'namesInsufficient' => 'Attendance already marked for students '.$user->first_name.' '.$user->last_name,
                    ]);
                }
            }
        }

        $school = DB::table('schools')
            ->select('*')
            ->where('id', $schoolId)
            ->first();

        $subject = DB::table('subjects')
            ->select('name', 'commission')
            ->where('id', $subjectId)
            ->first();

        $teacherGroup = DB::table('user_group_details')
            ->where('subject_id', $subjectId)
            ->where('teacher_id', $teacherId)
            ->first();

        $commissionType = null;
        $commissionAmount = null;

        if ($teacherGroup) {
            $groupId = $teacherGroup->group_id;
            $userGroup = DB::table('user_groups')->where('id', $groupId)->first();
            if ($userGroup) {
                $commissionType = $userGroup->commission_type;
                $commissionAmount = $userGroup->commission_amount;
            }
        }

        

        DB::beginTransaction();
        $totalTime = null;
        $clockInTime = !empty($clock_in) ? Carbon::parse($clock_in) : null;
        $clockOutTime = !empty($clock_out) ? Carbon::parse($clock_out) : null;
        if ($clockInTime && $clockOutTime) {
            $totalTime = $clockOutTime->diff($clockInTime)->format('%H:%I:%S');
        }

        if ($request->hasFile('file')) {
            $file = $request->file('file');
            $path = $file->store('remark_pictures', 'public');
            $fileName = $path;
        }

        foreach($users as $user) {
            $attendanceData = [
                'school_id' => $schoolId,
                'subjects_id' => $subjectId,
                'teacher_id' => $teacherId,
                'user_id' => $user->id,
                'status' => $status,
                'date' => Carbon::parse($date),
                'clock_in' => $clockInTime ?? null,
                'clock_out' => $clockOutTime ?? null,
                'in_temperature' => $request->in_temperature,
                'out_temperature' => $request->out_temperature,
                'total_time' => $totalTime,
                'student_remark' => $request->remark,
                'remark_picture' => $fileName ?? null,
                'fees_per_section' => $subject->commission ?? null, 
                'fees_per_month' => $subject->commission_month ?? null, 
                'commission_typee' => $commissionType,
                'commission_amountt' => $status == 0 ? 0 : ($commissionAmount ?? null),
            ];

            $id = DB::table('subject_attendances')->insertGetId($attendanceData);

            $deductionMethod = DB::table('school_settings')
            ->select('name', 'data')
            ->where('name', 'deduction_method')
            ->where('school_id', $schoolId)
            ->first();

            $userFullName = DB::table('users')
                            ->where('id',$user->id)
                            ->value(DB::raw("CONCAT(first_name, ' ', last_name) AS full_name"));
            $subject = DB::table('subjects')
                        ->where('id',$subjectId)
                        ->first();
            $student = DB::table('students as s')
                        ->join('class_sections as cs','cs.id','=','s.class_section_id')
                        ->where('s.user_id',$user->id)
                        ->first();
            $skipPackageCredit = DB::table('students')
                ->where('user_id', $user->id)
                ->value('credit_status');

            // Only proceed with deductions if credit_status is 0
            if ($skipPackageCredit == 0 && $deductionMethod) {
                if ($deductionMethod->data === "1" && in_array($status, [1, 2, 3])) {
                        $query = DB::table('subject_attendances as sa')
                        ->join('users as u', 'sa.user_id', '=', 'u.id')
                        ->join('credit_system as cs', 'sa.user_id', '=', 'cs.user_id')
                        ->join('students as s', 's.user_id', '=', 'sa.user_id')
                        ->join('class_sections as cls','cls.id','=','s.class_section_id')
                        ->join('classes as c','c.id','=','cls.class_id')
                        ->join('subjects as sub', 'sa.subjects_id', '=', 'sub.id',)
                        ->select('cs.user_id as credit_user', DB::raw("CONCAT(u.first_name, ' ', u.last_name) AS full_name"), 'c.id as class_id', 'cs.balance', 'cs.credit_amount', 'sub.commission')
                        ->where('sa.date', date('Y-m-d', strtotime($request->date)))
                        ->where('sa.subjects_id', $subjectId)
                        ->where('sa.status', 1)
                        ->where('cs.school_id', Auth::user()->school_id)
                        ->where('cs.user_id',$user->id)
                        ->first();
                    if($query){
                        $subjectPrice = -$query->commission;
                        $totalBalance = DB::table('credit_system')
                        ->where('user_id', $query->credit_user)
                        ->sum('credit_amount');
                        
                        $balance = $totalBalance + $subjectPrice;

                        DB::table('credit_system')->insert([
                            'school_id' => Auth::user()->school_id,
                            'class_id' => $query->class_id,
                            'user_id' => $query->credit_user,
                            'credit_amount' => $subjectPrice,
                            'balance' => $balance,
                            'detail' => 'Attended 1 Class' . '('.$subject->name.')',
                            'created_at' => Carbon::now(),
                            'updated_at' => Carbon::now()
                        ]);

                        if ($balance <= 0) {
                            $studentsInsufficientFunds[] = $query->full_name;
                        }
                    } else {
                        $subjectPrice = -$subject->commission;
                        $value = [
                            'school_id' => Auth::user()->school_id,
                            'class_id'  => $student->class_id,
                            'user_id'   => $user->id,
                            'credit_amount' => $subjectPrice,
                            'balance'       => $subjectPrice,
                            'detail' => 'Attended 1 Class' . '('.$subject->name.')',
                            'created_at' => Carbon::now(),
                            'updated_at' => Carbon::now()
                        ];
                        DB::table('credit_system')->insert($value);
                        $studentsInsufficientFunds[] = $userFullName;
                    }
                } else if ($deductionMethod->data === "2" && in_array($status, [1, 2, 3])) {
                    $userId = DB::table('students as s')
                        ->join('users as u', 'u.id', '=', 's.user_id')
                        ->select('s.id', DB::raw("CONCAT(u.first_name, ' ', u.last_name) AS full_name"))
                        ->where('u.id', $user->id)
                        ->first();

                    $package = DB::table('purchase_package as pp')
                        ->join('subject_package as sp', 'pp.package_id', '=', 'sp.id')
                        ->select('pp.id as purchase_id')
                        ->where('pp.student_id', $userId->id)
                        ->whereIn('pp.status', [0, 2])
                        ->whereRaw("FIND_IN_SET(?, sp.subject_id)", [$subjectId])
                        ->first();
                    if ($package) {
                        $packageUsage = DB::table('package_usage as pu')
                            ->where('pu.student_id', $userId->id)
                            ->where('pu.purchase_package_id', $package->purchase_id)
                            ->orderBy('created_at', 'DESC')
                            ->first();
                        if ($packageUsage && $packageUsage->remaining_session > 0) {
                            if($packageUsage->remaining_session != 0){
                                $remain = $packageUsage->remaining_session - 1;
                            }

                            DB::table('package_usage')->insert([
                                'school_id' => Auth::user()->school_id,
                                'purchase_package_id' => $package->purchase_id,
                                'student_id' => $userId->id,
                                'deduct' => -1,
                                'remaining_session' => $remain ?? 0,
                                'created_at' => Carbon::now(),
                                'updated_at' => Carbon::now(),
                            ]);


                            if (in_array($remain, [1,2,3,4,5,6,7,8,9,10])) {
                                $lowSessionCount[] = ['student_id' => $packageUsage->student_id, 'remaining_session' => $remain];
                            }else if ($remain == 0) {
                                $noSessionCount[] =['student_id' => $packageUsage->student_id, 'remaining_session' => $remain];
                            }
                            if ($packageUsage && $remain == 0) {
                                DB::table('purchase_package as pp')
                                    ->where('pp.id', $package->purchase_id)
                                    ->update([
                                        'status' => 1
                                    ]);
                            }
                        } else {
                            $studentsInsufficientFunds[] = $userId->full_name;
                        }
                    } else {
                        $studentsInsufficientFunds[] = $userFullName;
                    }
                } else if ($deductionMethod->data === "3" && in_array($status, [1, 2, 3])) {
                    $packageUsed = false;
                    $userId = DB::table('students as s')
                        ->join('users as u', 'u.id', '=', 's.user_id')
                        ->select('s.id')
                        ->where('u.id', $user->id)
                        ->first();

                    $package = DB::table('purchase_package as pp')
                        ->join('subject_package as sp', 'pp.package_id', '=', 'sp.id')
                        ->select('pp.id as purchase_id', 'pp.package_id', 'pp.student_id')
                        ->where('pp.student_id', $userId->id)
                        ->whereIn('pp.status', [0, 2])
                        ->whereRaw("FIND_IN_SET(?, sp.subject_id)", [$subjectId])
                        ->first();

                    if ($package) {
                        $packageUsage = DB::table('package_usage as pu')
                        ->where('pu.student_id', $userId->id)
                        ->where('pu.purchase_package_id', $package->purchase_id)
                        ->orderBy('created_at', 'DESC')
                        ->first();
                        if ($packageUsage && $packageUsage->remaining_session > 0) {
                            if($packageUsage->remaining_session != 0){
                                $remain = $packageUsage->remaining_session - 1;
                                $packageUsed = true;
                            }

                            DB::table('package_usage')->insert([
                                'school_id' => Auth::user()->school_id,
                                'purchase_package_id' => $package->purchase_id,
                                'student_id' => $userId->id,
                                'deduct' => -1,
                                'remaining_session' => $remain ?? 0,
                                'created_at' => Carbon::now(),
                                'updated_at' => Carbon::now(),
                            ]);


                            if (in_array($remain, [1,2,3,4,5,6,7,8,9,10])) {
                                $lowSessionCount[] = ['student_id' => $packageUsage->student_id, 'remaining_session' => $remain];
                            }else if ($remain == 0) {
                                $noSessionCount[] =['student_id' => $packageUsage->student_id, 'remaining_session' => $remain];
                            }
                            if ($packageUsage && $remain == 0) {
                                DB::table('purchase_package as pp')
                                    ->where('pp.id', $package->purchase_id)
                                    ->update([
                                        'status' => 1
                                    ]);
                            }
                        } 
                    } 
                        
                    $query = DB::table('subject_attendances as sa')
                        ->join('users as u', 'sa.user_id', '=', 'u.id')
                        ->join('credit_system as cs', 'sa.user_id', '=', 'cs.user_id')
                        ->join('students as s', 's.user_id', '=', 'sa.user_id')
                        ->join('class_sections as cls','cls.id','=','s.class_section_id')
                        ->join('classes as c','c.id','=','cls.class_id')
                        ->join('subjects as sub', 'sa.subjects_id', '=', 'sub.id',)
                        ->select('cs.user_id as credit_user', DB::raw("CONCAT(u.first_name, ' ', u.last_name) AS full_name"), 'c.id as class_id', 'cs.balance', 'cs.credit_amount', 'sub.commission')
                        ->where('sa.date', date('Y-m-d', strtotime($request->date)))
                        ->where('sa.subjects_id', $subjectId)
                        ->where('sa.status', 1)
                        ->where('cs.school_id', Auth::user()->school_id)
                        ->where('cs.user_id',$user->id)
                        ->first();
                    if(!$packageUsed){
                        if($query){
                            $subjectPrice = -$query->commission;
                            $totalBalance = DB::table('credit_system')
                            ->where('user_id', $query->credit_user)
                            ->sum('credit_amount');
                            
                            $balance = $totalBalance + $subjectPrice;

                            DB::table('credit_system')->insert([
                                'school_id' => Auth::user()->school_id,
                                'class_id' => $query->class_id,
                                'user_id' => $query->credit_user,
                                'credit_amount' => $subjectPrice,
                                'balance' => $balance,
                                'detail' => 'Attended 1 Class' . '('.$subject->name.')',
                                'created_at' => Carbon::now(),
                                'updated_at' => Carbon::now()
                            ]);

                            if ($balance <= 0) {
                                $studentsInsufficientFunds[] = $query->full_name;
                            }
                        } else {
                            $subjectPrice = -$subject->commission;
                            $value = [
                                'school_id' => Auth::user()->school_id,
                                'class_id'  => $student->class_id,
                                'user_id'   => $user->id,
                                'credit_amount' => $subjectPrice,
                                'balance'       => $subjectPrice,
                                'detail' => 'Attended 1 Class' . '('.$subject->name.')',
                                'created_at' => Carbon::now(),
                                'updated_at' => Carbon::now()
                            ];
                            DB::table('credit_system')->insert($value);
                            $studentsInsufficientFunds[] = $userFullName;
                        }
                    }
                }
            } 

            DB::commit();
            $notifyUser = [];
            if($id){
                $guardian_id = DB::table('students')->where('user_id',$user->id)->pluck('guardian_id')->first();
                if($guardian_id){
                    $notifyUser[] = $guardian_id;
                    $notifyUser[] = $user->id;
                    if($status == 0){
                        $title = 'Absent';
                    } else if ($status == 2){
                        $title = 'Late';
                    } else if ($status == 3){
                        $title = 'Replacement';
                    }else{
                        $title = 'Present';
                    }
                    $body = $user->first_name.' '.$user->last_name.' is '.strtolower($title).' on ' . $date;
                    $type = 'Attendance';
                    send_notification($notifyUser, $title, $body, $type);
                }
            }

            if (!empty($lowSessionCount)) {
                $studentIds = array_column($lowSessionCount, 'student_id');
                $lowSessionId = DB::table('users as u')
                ->join('students as s', 'u.id', '=', 's.user_id')
                ->select('u.id', 's.guardian_id', 'u.first_name', 'u.last_name')
                ->whereIn('s.id', $studentIds)
                ->get();

                foreach ($lowSessionId as $user) {
                    // Get package info to calculate expiry date
                    $packageInfo = DB::table('purchase_package as pp')
                        ->join('subject_package as sp', 'pp.package_id', '=', 'sp.id')
                        ->join('students as s', 'pp.student_id', '=', 's.id')
                        ->where('s.id', $lowSessionCount[0]['student_id'])  // Use the student_id from lowSessionCount
                        ->whereRaw("FIND_IN_SET(?, sp.subject_id)", [$subject->id])
                        ->select('pp.date as purchase_date', 'sp.expiry_days')
                        ->orderBy('pp.id', 'DESC')
                        ->first();

                    // Calculate expiry date
                    $formattedExpirationDate = "";
                    if ($packageInfo) {
                        $purchaseDate = Carbon::parse($packageInfo->purchase_date);
                        $expiryDays = $packageInfo->expiry_days;
                        $expirationDate = $purchaseDate->addDays($expiryDays);
                        $formattedExpirationDate = $expirationDate->format('d-m-Y');
                    }

                    $notifyUser = array();
                    $notifyUser[] = $user->guardian_id;
                    $notifyUser[] = $user->id;
                    $userName = $user->first_name . " " . $user->last_name;
                    $title = "Session Updated";
                    $type = "session_update";
                    $body = "Clock-in successful! You have ".$lowSessionCount[0]['remaining_session']. " sessions left (expires ".$formattedExpirationDate.")";
                    send_notification($notifyUser, $title, $body, $type);
                }
            } 
            if (!empty($noSessionCount)) {
                $studentIds = array_column($noSessionCount, 'student_id');
                $lowSessionId = DB::table('users as u')
                ->join('students as s', 'u.id', '=', 's.user_id')
                ->select('u.id', 's.guardian_id' , 'u.first_name', 'u.last_name')
                ->whereIn('s.id', $studentIds)
                ->get();

                foreach ($lowSessionId as $user) {
                    $notifyUser = array();
                    $notifyUser[] = $user->guardian_id;
                    $notifyUser[] = $user->id;
                    $userName = $user->first_name . " " . $user->last_name;
                    $title = "Session Updated";
                    $type = "session_update";
                    $body = "Clock-in successful! Your sessions have finished.";
                    send_notification($notifyUser, $title, $body, $type);
                }
            }
        }
        
        if (!empty($studentsInsufficientFunds) || !empty($alreadyTakenStudents)) {
            $response = [
                "message" => "success",
                'date' => $request->date
            ];

            if (!empty($studentsInsufficientFunds)) {
                $namesInsufficient = implode(', ', $studentsInsufficientFunds);
                $response['namesInsufficient'] = $namesInsufficient . ' has insufficient funds';
            }

            if (!empty($alreadyTakenStudents)) {
                $response['namesInsufficient'] = 'Attendance already marked for: ' . implode(', ', $alreadyTakenStudents);
            }

            return response()->json($response);
        } else {
            ResponseService::successResponse('Data Stored Successfully');
        }
    }

    public function getAttendancesForSubject(Request $request){
        ResponseService::noFeatureThenSendJson('Attendance Management');
        $validator = Validator::make($request->all(), [
            // 'subject_id' => 'required|numeric',
            'date'       => 'required|date',
        ]);
        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        $subject_id = $request->subject_id;
        $query = SubjectAttendance::query();

        $query->join('users AS user', 'subject_attendances.user_id', '=', 'user.id')
            ->leftJoin('users AS teacher', 'subject_attendances.teacher_id', '=', 'teacher.id')
            ->join('students', 'user.id', '=', 'students.user_id')
            ->leftJoin('subjects', 'subject_attendances.subjects_id', '=', 'subjects.id')
            ->select(
                'subject_attendances.*',
                'user.first_name',
                'user.last_name',
                'students.id as student_id',
                DB::raw("CONCAT(user.first_name,' ',user.last_name) as student_full_name"),
                DB::raw("CASE 
                            WHEN subject_attendances.status = 0 THEN 0 
                            ELSE 1 
                        END AS status")
            )
            ->whereNull('user.deleted_at')
            //->where('subject_attendances.teacher_id',Auth::user()->id)
            ->where('subject_attendances.school_id', '=', Auth::user()->school_id)
            ->whereExists(function($query) {
                $query->select(DB::raw(1))
                    ->from('subject_teachers')
                    ->whereColumn('subject_teachers.subject_id', 'subjects.id')
                    ->where('subject_teachers.teacher_id', Auth::user()->id);
            });
        if ($subject_id) {
            $query->where('subject_attendances.subjects_id', $subject_id);
        }
        if ($request->date) {
            $query->where('subject_attendances.date', $request->date);
        }
        if (isset($request->status)) {
            $query->where('subject_attendances.status', $request->status);
        }
        if (isset($request->student_id)) {
            $query->where('subject_attendances.user_id', $request->student_id);
        }
        $data = $query->orderByDesc('created_at')->paginate(100);
        ResponseService::successResponse("Data Fetched Successfully", $data);  
    }

    public function getStudentList(Request $request)
    {
        $validator = Validator::make($request->all(), ['class_section_id' => 'required|numeric',]);
        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            if ($request->student_id) {
                $sql = $this->user->builder()->whereHas('student', function ($q) use ($request) {
                    $q->where('class_section_id', $request->class_section_id);
                })->with('student.guardian')->first();
            } else {
                $sql = $this->user->builder()->whereHas('student', function ($q) use ($request) {
                    $q->where('class_section_id', $request->class_section_id);
                })->with('student.guardian')->has('student')->role('Student');

                if ($request->status == 2) {
                    $sql->onlyTrashed();
                }

                if ($request->search) {
                    $sql->where(function ($q) use ($request) {
                        $q->when($request->search, function ($q) use ($request) {
                            $q->where('first_name', 'LIKE', "%$request->search%")
                                ->orwhere('last_name', 'LIKE', "%$request->search%")
                                ->orwhere('mobile', 'LIKE', "%$request->search%")
                                ->orwhere('email', 'LIKE', "%$request->search%")
                                ->orwhere('gender', 'LIKE', "%$request->search%")
                                ->orWhereRaw('concat(first_name," ",last_name) like ?', "%$request->search%")
                                ->where(function ($q) use ($request) {
                                    $q->when($request->session_year_id, function ($q) use ($request) {
                                        $q->whereHas('student', function ($q) use ($request) {
                                            $q->where('session_year_id', $request->session_year_id);
                                        });
                                    });
                                });
                        });
                    });
                }

                if ($request->session_year_id) {
                    $sql = $sql->whereHas('student', function ($q) use ($request) {
                        $q->where('session_year_id', $request->session_year_id);
                    });
                }

                if (($request->paginate || $request->paginate != 0 || $request->paginate == null)) {
                    $sql = $sql->has('student')->orderBy('id')->paginate(500);
                } else {
                    $sql = $sql->has('student')->orderBy('id')->get();
                }  

                // 
                if ($request->exam_id) {
                 
                    $validator = Validator::make($request->all(), ['class_subject_id' => 'required']);
                    if ($validator->fails()) {
                        ResponseService::validationError($validator->errors()->first());
                    }

                    $exam = $this->exam->builder()->with('timetable:id,date,exam_id,start_time,end_time')->where('id', $request->exam_id)->first();

                    // Get Student ids according to Subject is elective or compulsory
                    $classSubject = $this->classSubject->findById($request->class_subject_id);
                    if ($classSubject->type == "Elective") {
                        $studentIds = $this->studentSubject->builder()->where(['class_section_id' => $request->class_section_id, 'class_subject_id' => $classSubject->id])->pluck('student_id');
                    } else {
                        $studentIds = $this->user->builder()->role('student')->whereHas('student', function ($query) use ($request) {
                            $query->where('class_section_id', $request->class_section_id);
                        })->pluck('id');
                    }

                    // Get Timetable Data
                    $timetable = $exam->timetable()->where('class_subject_id', $request->class_subject_id)->first();

                    // return $timetable;

                    // IF Timetable is empty then show error message
                    if (!$timetable) {
                        return response()->json(['error' => true, 'message' => trans(  'Exam Timetable Does not Exists')]);
                    }

                    // IF Exam status is not 3 that is exam not completed then show error message
                    if ($exam->exam_status != 3 && $exam->exam_status != 2) {
                        ResponseService::errorResponse('Exam not completed yet');
                    }

                    $sessionYear = $this->cache->getDefaultSessionYear(); // Get Students Data on the basis of Student ids

                    $sql = $this->user->builder()->select('id', 'first_name', 'last_name', 'image')->role('Student')->whereIn('id', $studentIds)->with(['marks' => function ($query) use ($timetable) {
                        $query->where('exam_timetable_id', $timetable->id)->select('id', 'exam_timetable_id', 'student_id', 'obtained_marks');
                    }])
                        ->whereHas('student', function ($q) use ($sessionYear) {
                            $q->where('session_year_id', $sessionYear->id);
                        })->get();
                }
            }

            for($i = 0; $i < COUNT($sql); $i++){
                $sql[$i]->mobile = "**********";
                if(isset($sql[$i]->student)){
                    if(isset($sql[$i]->student->guardian)){
                        $sql[$i]->student->guardian->mobile = '**********';
                        $sql[$i]->student->guardian->email = '**********';
                    }
                }
            }

            ResponseService::successResponse("Student Details Fetched Successfully", $sql);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function getStudentDetails(Request $request)
    {
        $validator = Validator::make($request->all(), ['student_id' => 'required|numeric',]);
        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            $student_data = $this->student->findById($request->student_id, ['user_id', 'class_section_id', 'guardian_id'], ['user', 'guardian']);

            $student_total_present = $this->attendance->builder()->where('student_id', $student_data->user_id)->where('type', 1)->count();
            $student_total_absent = $this->attendance->builder()->where('student_id', $student_data->user_id)->where('type', 0)->count();

            $today_date_string = Carbon::now();
            $today_date_string->toDateTimeString();
            $today_date = date('Y-m-d', strtotime($today_date_string));

            $student_today_attendance = $this->attendance->builder()->where('student_id', $student_data->user_id)->where('date', $today_date)->first();

            if ($student_today_attendance) {
                if ($student_today_attendance->type == 1) {
                    $today_attendance = 'Present';
                } else {
                    $today_attendance = 'Absent';
                }
            } else {
                $today_attendance = 'Not Taken';
            }
            ResponseService::successResponse("Student Details Fetched Successfully", null, [
                'data'             => $student_data,
                'total_present'    => $student_total_present,
                'total_absent'     => $student_total_absent,
                'today_attendance' => $today_attendance ?? ''
            ]);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function getTeacherTimetable(Request $request)
    {
        ResponseService::noFeatureThenSendJson('Timetable Management');
        try {
            $timetable = $this->timetable->builder()->whereHas('subject_teacher', function ($q) {
                $q->where('teacher_id', Auth::user()->id);
            })->whereHas('class_section', function ($q) {
                $q->whereNull('deleted_at');
            })->with('class_section.class.stream', 'class_section.section', 'subject')->orderBy('start_time', 'ASC')->get();


            ResponseService::successResponse("Timetable Fetched Successfully", $timetable);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function submitExamMarksBySubjects(Request $request)
    {
        ResponseService::noFeatureThenSendJson('Exam Management');
        $validator = Validator::make($request->all(), [
            'exam_id'    => 'required|numeric',
            'class_subject_id' => 'required|numeric',
        ]);
        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }

        try {
            DB::beginTransaction();
            $exam_published = $this->exam->builder()->where('id', $request->exam_id)->first();
            if (isset($exam_published) && $exam_published->publish == 1) {
                ResponseService::errorResponse('exam_published', null, config('constants.RESPONSE_CODE.EXAM_ALREADY_PUBLISHED'));
            }

            $currentTime = Carbon::now();
            $current_date = date($currentTime->toDateString());
            if ($current_date >= $exam_published->start_date && $current_date <= $exam_published->end_date) {
                $exam_status = "1"; // Upcoming = 0 , On Going = 1 , Completed = 2
            } elseif ($current_date < $exam_published->start_date) {
                $exam_status = "0"; // Upcoming = 0 , On Going = 1 , Completed = 2
            } else {
                $exam_status = "2"; // Upcoming = 0 , On Going = 1 , Completed = 2
            }
            if ($exam_status != 2) {
                ResponseService::errorResponse('exam_not_completed_yet', null, config('constants.RESPONSE_CODE.EXAM_ALREADY_PUBLISHED'));
            } else {

                $exam_timetable = $this->examTimetable->builder()->where('exam_id', $request->exam_id)->where('class_subject_id', $request->class_subject_id)->firstOrFail();

                foreach ($request->marks_data as $marks) {
                    if ($marks['obtained_marks'] > $exam_timetable['total_marks']) {
                        ResponseService::errorResponse('The obtained marks that did not exceed the total marks');
                    }
                    $passing_marks = $exam_timetable->passing_marks;
                    if ($marks['obtained_marks'] >= $passing_marks) {
                        $status = 1;
                    } else {
                        $status = 0;
                    }
                    $marks_percentage = ($marks['obtained_marks'] / $exam_timetable['total_marks']) * 100;

                    $exam_grade = findExamGrade($marks_percentage);
                    if ($exam_grade == null) {
                        ResponseService::errorResponse('grades_data_does_not_exists', null, config('constants.RESPONSE_CODE.GRADES_NOT_FOUND'));
                    }

                    $exam_marks = $this->examMarks->builder()->where('exam_timetable_id', $exam_timetable->id)->where('class_subject_id', $request->class_subject_id)->where('student_id', $marks['student_id'])->first();
                    if ($exam_marks) {
                        $exam_data = [
                            'obtained_marks' => $marks['obtained_marks'],
                            'passing_status' => $status,
                            'grade' => $exam_grade
                        ];
                        $this->examMarks->update($exam_marks->id, $exam_data);
                    } else {
                        $exam_result_marks[] = array(
                            'exam_timetable_id' => $exam_timetable->id,
                            'student_id'        => $marks['student_id'],
                            'class_subject_id'        => $request->class_subject_id,
                            'obtained_marks'    => $marks['obtained_marks'],
                            'passing_status'    => $status,
                            'session_year_id'   => $exam_timetable->session_year_id,
                            'grade'             => $exam_grade,
                        );
                    }
                }
                if (isset($exam_result_marks)) {
                    $this->examMarks->createBulk($exam_result_marks);
                }
                DB::commit();
                ResponseService::successResponse('Data Stored Successfully');
            }
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }


    public function submitExamMarksByStudent(Request $request)
    {
        ResponseService::noFeatureThenSendJson('Exam Management');
        $validator = Validator::make($request->all(), [
            'exam_id'    => 'required|numeric',
            'student_id' => 'required|numeric',
        ]);
        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            DB::beginTransaction();
            $exam_published = $this->exam->findById($request->exam_id);
            if (isset($exam_published) && $exam_published->publish == 1) {

                ResponseService::errorResponse('exam_published', null, config('constants.RESPONSE_CODE.EXAM_ALREADY_PUBLISHED'));
            }

            $currentTime = Carbon::now();
            $current_date = date($currentTime->toDateString());
            if ($current_date >= $exam_published->start_date && $current_date <= $exam_published->end_date) {
                $exam_status = "1"; // Upcoming = 0 , On Going = 1 , Completed = 2
            } elseif ($current_date < $exam_published->start_date) {
                $exam_status = "0"; // Upcoming = 0 , On Going = 1 , Completed = 2
            } else {
                $exam_status = "2"; // Upcoming = 0 , On Going = 1 , Completed = 2
            }

            if ($exam_status != 2) {
                ResponseService::errorResponse('exam_published', null, config('constants.RESPONSE_CODE.EXAM_NOT_COMPLETED'));
            } else {

                foreach ($request->marks_data as $marks) {
                    $exam_timetable = $this->examTimetable->builder()->where('exam_id', $request->exam_id)->where('class_subject_id', $marks['class_subject_id'])->firstOrFail();

                    if ($marks['obtained_marks'] > $exam_timetable['total_marks']) {
                        ResponseService::errorResponse('The obtained marks that did not exceed the total marks');
                    }
                    $passing_marks = $exam_timetable->passing_marks;
                    if ($marks['obtained_marks'] >= $passing_marks) {
                        $status = 1;
                    } else {
                        $status = 0;
                    }
                    $marks_percentage = ($marks['obtained_marks'] / $exam_timetable->total_marks) * 100;

                    $exam_grade = findExamGrade($marks_percentage);
                    if ($exam_grade == null) {
                        ResponseService::errorResponse('grades_data_does_not_exists', null, config('constants.RESPONSE_CODE.GRADES_NOT_FOUND'));
                    }
                    $exam_marks = $this->examMarks->builder()->where('exam_timetable_id', $exam_timetable->id)->where('class_subject_id', $marks['class_subject_id'])->where('student_id', $request->student_id)->first();
                    if ($exam_marks) {
                        $exam_data = [
                            'obtained_marks' => $marks['obtained_marks'],
                            'passing_status' => $status,
                            'grade' => $exam_grade
                        ];
                        $this->examMarks->update($exam_marks->id, $exam_data);
                    } else {
                        $exam_result_marks[] = array(
                            'exam_timetable_id' => $exam_timetable->id,
                            'student_id'        => $request->student_id,
                            'class_subject_id'        => $marks['class_subject_id'],
                            'obtained_marks'    => $marks['obtained_marks'],
                            'passing_status'    => $status,
                            'session_year_id'   => $exam_timetable->session_year_id,
                            'grade'             => $exam_grade,
                        );
                    }
                }
                if (isset($exam_result_marks)) {
                    $this->examMarks->createBulk($exam_result_marks);
                }

                DB::commit();
                ResponseService::successResponse('Data Stored Successfully');
            }
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }


    public function GetStudentExamResult(Request $request)
    {
        ResponseService::noFeatureThenSendJson('Exam Management');
        $validator = Validator::make($request->all(), ['student_id' => 'required|nullable']);
        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            $exam_marks_db = $this->exam->builder()->with(['timetable.exam_marks' => function ($q) use ($request) {
                $q->where('student_id', $request->student_id);
            }])->has('timetable.exam_marks')->with('timetable.class_subject.subject')->has('results')->with(['results' => function ($q) use ($request) {
                $q->where('student_id', $request->student_id)->with('class_section.class.stream', 'class_section.section', 'class_section.medium');
            }])->get();

            if (count($exam_marks_db)) {
                foreach ($exam_marks_db as $data_db) {
                    $currentTime = Carbon::now();
                    $current_date = date($currentTime->toDateString());
                    if ($current_date >= $data_db->start_date && $current_date <= $data_db->end_date) {
                        $exam_status = "1"; // Upcoming = 0 , On Going = 1 , Completed = 2
                    } elseif ($current_date < $data_db->start_date) {
                        $exam_status = "0"; // Upcoming = 0 , On Going = 1 , Completed = 2
                    } else {
                        $exam_status = "2"; // Upcoming = 0 , On Going = 1 , Completed = 2
                    }

                    // check whether exam is completed or not
                    if ($exam_status == 2) {
                        $marks_array = array();

                        // check whether timetable exists or not
                        if (count($data_db->timetable)) {
                            foreach ($data_db->timetable as $timetable_db) {
                                $total_marks = $timetable_db->total_marks;
                                $exam_marks = array();
                                if (count($timetable_db->exam_marks)) {
                                    foreach ($timetable_db->exam_marks as $marks_data) {
                                        $exam_marks = array(
                                            'marks_id'       => $marks_data->id,
                                            'subject_name'   => $marks_data->class_subject->subject->name,
                                            'subject_type'   => $marks_data->class_subject->subject->type,
                                            'total_marks'    => $total_marks,
                                            'obtained_marks' => $marks_data->obtained_marks,
                                            'grade'          => $marks_data->grade,
                                        );
                                    }
                                } else {
                                    $exam_marks = (object)[];
                                }

                                $marks_array[] = array(
                                    'subject_id'   => $timetable_db->class_subject->subject_id,
                                    'subject_name' => $timetable_db->class_subject->subject->name,
                                    'subject_type' => $timetable_db->class_subject->subject->type,
                                    'total_marks'  => $total_marks,
                                    'subject_code' => $timetable_db->class_subject->subject->code,
                                    'marks'        => $exam_marks
                                );
                            }
                            $exam_result = array();
                            if (count($data_db->results)) {
                                foreach ($data_db->results as $result_data) {
                                    $exam_result = array(
                                        'result_id'      => $result_data->id,
                                        'exam_id'        => $result_data->exam_id,
                                        'exam_name'      => $data_db->name,
                                        'class_name'     => $result_data->class_section->full_name,
                                        'student_name'   => $result_data->user->first_name . ' ' . $result_data->user->last_name,
                                        'exam_date'      => $data_db->start_date,
                                        'total_marks'    => $result_data->total_marks,
                                        'obtained_marks' => $result_data->obtained_marks,
                                        'percentage'     => $result_data->percentage,
                                        'grade'          => $result_data->grade,
                                        'session_year'   => $result_data->session_year->name,
                                    );
                                }
                            } else {
                                $exam_result = (object)[];
                            }
                            $data[] = array(
                                'exam_id'    => $data_db->id,
                                'exam_name'  => $data_db->name,
                                'exam_date'  => $data_db->start_date,
                                'marks_data' => $marks_array,
                                'result'     => $exam_result
                            );
                        }
                    }
                }
                ResponseService::successResponse("Exam Marks Fetched Successfully", $data ?? []);
            } else {
                ResponseService::successResponse("Exam Marks Fetched Successfully", []);
            }
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function GetStudentExamMarks(Request $request)
    {
        ResponseService::noFeatureThenSendJson('Exam Management');
        $validator = Validator::make($request->all(), ['student_id' => 'required|nullable']);
        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            $sessionYear = $this->cache->getDefaultSessionYear();
            $exam_marks_db = $this->exam->builder()->with(['timetable.exam_marks' => function ($q) use ($request) {
                $q->where('student_id', $request->student_id);
            }])->has('timetable.exam_marks')->with('timetable.class_subject')->where('session_year_id', $sessionYear->id)->get();


            if (count($exam_marks_db)) {
                foreach ($exam_marks_db as $data_db) {
                    $marks_array = array();
                    foreach ($data_db->timetable as $marks_db) {
                        $exam_marks = array();
                        if (count($marks_db->exam_marks)) {
                            foreach ($marks_db->exam_marks as $marks_data) {
                                $exam_marks = array(
                                    'marks_id'       => $marks_data->id,
                                    'subject_name'   => $marks_data->class_subject->subject->name,
                                    'subject_type'   => $marks_data->class_subject->subject->type,
                                    'total_marks'    => $marks_data->timetable->total_marks,
                                    'obtained_marks' => $marks_data->obtained_marks,
                                    'grade'          => $marks_data->grade,
                                );
                            }
                        } else {
                            $exam_marks = [];
                        }

                        $marks_array[] = array(
                            'subject_id'   => $marks_db->class_subject->subject_id,
                            'subject_name' => $marks_db->subject_with_name,
                            'marks'        => $exam_marks
                        );
                    }
                    $data[] = array(
                        'exam_id'    => $data_db->id,
                        'exam_name'  => $marks_db->exam->name ?? '',
                        'marks_data' => $marks_array
                    );
                }
                ResponseService::successResponse("Exam Marks Fetched Successfully", $data ?? '');
            } else {
                ResponseService::successResponse("Exam Marks Fetched Successfully", []);
            }
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function getExamList(Request $request)
    {
        ResponseService::noFeatureThenSendJson('Exam Management');
        $validator = Validator::make($request->all(), [
            'status'  => 'in:0,1,2,3',
            'publish' => 'nullable|in:0,1',
        ]);
        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            $sessionYear = $this->cache->getDefaultSessionYear();

            $sql = $this->exam->builder()->with('session_year', 'class')->select('id', 'name', 'description', 'class_id', 'start_date', 'end_date', 'session_year_id', 'publish')
                ->with(['timetable.class_subject' => function ($q) {
                    $q->withTrashed();
                }]);


            if (isset($request->publish)) {
                $sql = $sql->where('publish', $request->publish);
            }
            if ($request->session_year_id) {
                $sql = $sql->where('session_year_id', $request->session_year_id);
            } else {
                $sql = $sql->where('session_year_id', $sessionYear->id);
            }

            // if ($request->class_section_id) {
            //     $sql = $sql->whereHas('class.class_sections', function ($q) use ($request) {
            //         $q->where('id', $request->class_section_id);
            //     });
            // }

            // if ($request->medium_id) {
            //     $sql = $sql->whereHas('class', function ($q) use ($request) {
            //         $q->where('medium_id', $request->medium_id);
            //     });
            // }

            $exam_data = array();
            $exam_data_db = $sql->orderBy('id', 'DESC')->get();
            foreach ($exam_data_db as $data) {
                $currentTime = Carbon::now();
                $current_date = date($currentTime->toDateString());
                $current_time = Carbon::now();
                //  0- Upcoming, 1-On Going, 2-Completed, 3-All Details
                $exam_status = "3";
                if ($current_date == $data->start_date && $current_date == $data->end_date) {
                    if (count($data->timetable)) {
                        $exam_end_time = Carbon::parse($data->timetable->first()->end_time);
                        $exam_start_time = Carbon::parse($data->timetable->first()->start_time);
                        // if ($exam_start_time->gt($current_time)) {
                        //     $exam_status = "1";
                        // } else if ($exam_end_time->lt($current_time)){
                        //     $exam_status = "3";
                        // } else {
                        //     $exam_status = "2";
                        // }

                        if ($current_time->lt($exam_start_time)) {
                            $exam_status = "0";
                        } elseif ($current_time->gt($exam_end_time)) {
                            $exam_status = "2";
                        } else {
                            $exam_status = "1";
                        }
                    }
                } else {
                    if ($current_date >= $data->start_date && $current_date <= $data->end_date) {
                        $exam_status = "1";
                    } else if ($current_date < $data->start_date) {
                        $exam_status = "0";
                    } else if ($current_date >= $data->end_date) {
                        $exam_status = "2";
                    } else {
                        $exam_status = null;
                    }
                }

                $timetable_data = array();
                if (count($data->timetable)) {
                    foreach ($data->timetable as $key => $timetable) {
                        $subject = [
                            'id' => $timetable->class_subject->subject->id,
                            'name' => $timetable->class_subject->subject->name,
                            'type' => $timetable->class_subject->subject->type,
                        ];
                        $class_subject = [
                            'id' => $timetable->class_subject->id,
                            'class_id' => $timetable->class_subject->id,
                            'subject_id' => $timetable->class_subject->id,
                            'subject' => $subject
                        ];
                        $timetable_data[] = [
                            'id' => $timetable->id,
                            'total_marks' => $timetable->total_marks,
                            'passing_marks' => $timetable->passing_marks,
                            'date' => $timetable->date,
                            'start_time' => $timetable->start_time,
                            'end_time' => $timetable->end_time,
                            'subject_name' => $timetable->subject_with_name,
                            'class_subject' => $class_subject
                        ];
                    }
                }

                if (isset($request->status) && $request->status != 3) {
                    if ($request->status == 0 && $exam_status == 0) {
                        $exam_data[] = array(
                            'id'                 => $data->id,
                            'name'               => $data->name,
                            'description'        => $data->description,
                            'publish'            => $data->publish,
                            'session_year'       => $data->session_year->name,
                            'exam_starting_date' => $data->start_date,
                            'exam_ending_date'   => $data->end_date,
                            'exam_status'        => $exam_status,
                            'class_name'        => $data->class_name,
                            'timetable'         => $timetable_data,
                        );
                    } else if ($request->status == 1) {
                        if ($exam_status == 1) {
                            $exam_data[] = array(
                                'id'                 => $data->id,
                                'name'               => $data->name,
                                'description'        => $data->description,
                                'publish'            => $data->publish,
                                'session_year'       => $data->session_year->name,
                                'exam_starting_date' => $data->start_date,
                                'exam_ending_date'   => $data->end_date,
                                'exam_status'        => $exam_status,
                                'class_name'        => $data->class_name,
                                'timetable'         => $timetable_data,
                            );
                        }
                    } else if ($exam_status == 2 && $request->status == 2) {
                        $exam_data[] = array(
                            'id'                 => $data->id,
                            'name'               => $data->name,
                            'description'        => $data->description,
                            'publish'            => $data->publish,
                            'session_year'       => $data->session_year->name,
                            'exam_starting_date' => $data->start_date,
                            'exam_ending_date'   => $data->end_date,
                            'exam_status'        => $exam_status,
                            'class_name'        => $data->class_name,
                            'timetable'         => $timetable_data,
                        );
                    } else if ($request->status == 3 && count($data->timetable) && $data->exam_status == 3) {
                        $exam_data[] = array(
                            'id'                 => $data->id,
                            'name'               => $data->name,
                            'description'        => $data->description,
                            'publish'            => $data->publish,
                            'session_year'       => $data->session_year->name,
                            'exam_starting_date' => $data->start_date,
                            'exam_ending_date'   => $data->end_date,
                            'exam_status'        => $exam_status,
                            'class_name'        => $data->class_name,
                            'timetable'         => $timetable_data,
                        );
                    }
                } else {
                    $exam_data[] = array(
                        'id'                 => $data->id,
                        'name'               => $data->name,
                        'description'        => $data->description,
                        'publish'            => $data->publish,
                        'session_year'       => $data->session_year->name,
                        'exam_starting_date' => $data->start_date,
                        'exam_ending_date'   => $data->end_date,
                        'exam_status'        => $exam_status,
                        'class_name'        => $data->class_name,
                        'timetable'         => $timetable_data,
                    );
                }


                // $exam_data['timetable'] = $timetable_data;
            }

            ResponseService::successResponse('Data Fetched Successfully', $exam_data);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function getExamDetails(Request $request)
    {
        ResponseService::noFeatureThenSendJson('Exam Management');
        $validator = Validator::make($request->all(), ['exam_id' => 'required|nullable',]);
        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }
        try {
            $exam_data = $this->exam->builder()->select('id', 'name', 'description', 'session_year_id', 'publish')->with('timetable.class_subject.subject')->where('id', $request->exam_id)->first();

            ResponseService::successResponse('Data Fetched Successfully', $exam_data);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function getClassDetail(Request $request)
    {
        try {

            $sql = $this->classSection->builder()->with(['class.stream', 'medium', 'section', 'class_teachers.teacher:id,first_name,last_name', 'subject_teachers' => function($q) {
                $q->whereHas('subject', function($q) {
                    $q->whereNull('deleted_at');
                })
                ->with('teacher:id,first_name,last_name')
                ->with(['subject' => function($q) {
                    $q->select('id','name','code','type');
                }]);
            }]);
            if ($request->class_id) {
                $sql = $sql->where('class_id', $request->class_id);
            }

            // $sql = $this->classSection->builder()->with(['class.stream', 'medium', 'section', 'class_teachers.teacher:id,first_name,last_name',  'subject_teachers'=> function ($q) {
            //     $q->with('teacher:id,first_name,last_name')
            //     ->has('class_subject')->with(['class_subject' => function($q) {
            //         $q->whereNull('deleted_at')->with('semester');
            //     }])
            //     ->with('subject')->owner();
            // }]);

            // if ($request->class_id) {
            //     $sql = $sql->where('class_id', $request->class_id);
            // }


            $sql = $sql->orderBy('id', 'DESC')->get();
            ResponseService::successResponse('Data Fetched Successfully', $sql);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function userNotifications(Request $request){
        $userId = Auth::user()->id;
        $notifications = UserNotifications::where('user_id', '=', $userId)->orderBy('created_at', 'DESC')->get();
        for($i = 0; $i < COUNT($notifications); $i++){
            $notifications[$i]->image = str_replace('https://schola.one/storage/https://schola.one/storage/','', $notifications[$i]->image ?? '');
        }
        ResponseService::successResponse("Notifications Fetched Successfully", $notifications);
    }
    public function getClassSection(Request $request){
        // $classSections = DB::table('class_sections')
        // ->leftJoin('classes', 'class_sections.class_id', '=', 'classes.id')
        // ->leftJoin('sections', 'class_sections.section_id', '=', 'sections.id')
        // ->leftJoin('mediums', 'class_sections.medium_id', '=', 'mediums.id')
        // ->join('subject_teachers as st','st.class_section_id','=','class_sections.id')
        // ->select('class_sections.id',DB::raw("CONCAT(classes.name, ' ', IFNULL(sections.name, ''), ' ', IFNULL(mediums.name, '')) AS name"))
        // ->where('class_sections.school_id',Auth::user()->school_id)
        // ->where('st.teacher_id', Auth::user()->id)
        // ->whereNull('class_sections.deleted_at')
        // ->distinct()
        // ->get();
        $data = array();
        $classSections = $this->classSection->builder()->with('class', 'class.stream', 'section', 'medium')->get();
        foreach($classSections as $item){
            $data[] = [
                'id'    => $item->id,
                'name'  => $item->full_name
            ];
        }
        ResponseService::successResponse("ClassSection Fetched Successfully", $data);
    }
    public function getSubjectByCLassSection(Request $request){


        // $subjectClassSection= DB::table('subject_teachers as st')
        //     ->join('class_subjects as cs', 'cs.id', '=', 'st.class_subject_id')
        //     ->join('class_sections as css', 'css.class_id', '=', 'cs.class_id')
        //     ->join('subjects as s','s.id','=','cs.subject_id')
        //     ->select('cs.id', DB::raw("CONCAT(s.name, '-', s.type) AS name") )
        //     ->where('st.teacher_id', Auth::user()->id)
        //     ->where('css.school_id',Auth::user()->school_id)
        //     ->whereNull('cs.deleted_at')
        //     ->where('css.id', $request->id)
        //     ->get();
        $subjectClassSection = array();
        $subjectTeachers = $this->subjectTeacher->builder()->with('subject:id,name,type')->where('class_section_id',$request->id)->where('teacher_id',Auth::user()->id)->get();
        foreach($subjectTeachers as $item){
            $data = [
                'id'    => $item->class_subject_id,
                'name'  => $item->subject_with_name
            ];
            $subjectClassSection[] = $data;
        }


        ResponseService::successResponse("ClassSection Fetched Successfully", $subjectClassSection);
    }

    public function getClassSubjectSessionYear(){
        
        $classSubject = DB::table('class_subjects as cs')
                        ->leftJoin('classes as c','c.id','=','cs.class_id')
                        ->leftJoin('subjects as s','s.id','=','cs.subject_id')
                        ->leftJoin('class_sections as css','css.class_id','=','c.id')
                        ->leftJoin('sections as ss','ss.id','=','css.section_id')
                        ->select(DB::raw("CONCAT(cs.id,':',css.id) AS id"),DB::raw("CONCAT(IFNULL(c.name, ''),' - ', IFNULL(s.name, ''),' - ',IFNULL(ss.name, '')) AS name"))
                        ->where('cs.school_id',Auth::user()->school_id)
                        ->whereNull('cs.deleted_at')
                        ->whereNull('s.deleted_at')
                        ->whereNull('c.deleted_at')
                        ->get();

        $sessionYear = DB::table('session_years')->select('id','name')->where('school_id',Auth::user()->school_id)->whereNull('deleted_at')->get();

        $data = [
            'classSubject' => $classSubject,
            'sessionYear' => $sessionYear
        ];
        ResponseService::successResponse("Data Fetched Successfully", $data);
    }

    public function getGalleryList(Request $request){
        ResponseService::noFeatureThenSendJson("School Gallery Management");

        $class_subject_id = null;
        $class_section_id = null;
        if($request->class_subject_section_id){
            $classSubjectSectionArray = explode(':',$request->class_subject_section_id);
            $class_subject_id = $classSubjectSectionArray[0];
            $class_section_id = $classSubjectSectionArray[1];
        }
        $sql =  $this->gallery->builder()->with('file')
        ->when($request->session_year_id, function ($query) use ($request) {
                $query->where('session_year_id',$request->session_year_id);
        })->when($class_subject_id, function ($query) use ($class_subject_id) {
                $query->where('class_subject_id',$class_subject_id);
        })->when($class_section_id, function ($query) use ($class_section_id) {
                $query->where('class_section_id',$class_section_id);
        })
        ->orderby("created_at","desc")
        ->paginate(10);
        ResponseService::successResponse("Data Fetched Successfully", $sql);
    }

    public function deleteGalleryFile($id){
        try {
            DB::beginTransaction();

            // Find the Data by FindByID
            $file = $this->files->findById($id);
            if (Storage::disk('public')->exists($file->getRawOriginal('file_url'))) {
                Storage::disk('public')->delete($file->getRawOriginal('file_url'));
            }

            $file->delete();

            DB::commit();
            ResponseService::successResponse('Data Deleted Successfully');
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "Gallery Controller -> deleteFile Method");
            ResponseService::errorResponse();
        }
    }
    
    public function deletestudentprogressFile(Request $request)
    {
        try {
            DB::beginTransaction();

            $studentProgress = DB::table('student_progress')->find($request->id);

            if (!$studentProgress) {
                return response()->json(['message' => 'Record not found'], 404);
            }

            $file = $studentProgress->files;

            if (Storage::disk('public')->exists($file)) {
                Storage::disk('public')->delete($file);
            } else {
                return response()->json(['message' => 'File not found in storage'], 404);
            }

            DB::table('student_progress')->where('id', $request->id)->update(['files' => null]);

            DB::commit();

            return response()->json(['message' => 'File and record deleted successfully']);
        } catch (Throwable $e) {
            DB::rollBack();

            ResponseService::logErrorResponse($e, "Student Progress Controller -> deleteFile Method");

            return response()->json(['message' => 'An error occurred while deleting the file'], 500);
        }
    }

    public function getGalleryStudentTag(Request $request){
        $classSubjectSectionId = $request->class_subject_section_id;
        if($classSubjectSectionId){
            $classSectionId = explode(':',$classSubjectSectionId)[1];
            $school_id = Auth::user()->school_id;
            $students = DB::select("SELECT students.id, CONCAT(users.first_name,' ',users.last_name,' ',users.email) as name FROM students JOIN users ON users.id = students.user_id WHERE students.school_id = ? AND students.class_section_id = ? AND users.deleted_at IS NULL",[$school_id,$classSectionId]);
    
            ResponseService::successResponse('Data Retrieve Successfully',$students);
        }
    }

    public function storeGallery(Request $request){
        // ResponseService::noFeatureThenRedirect("School Gallery Management");
        // ResponseService::noPermissionThenSendJson('gallery-create');
        
        $validator = Validator::make($request->all(), [
            'title' => 'required',
            'thumbnail' => 'required',
        ]);
        if ($validator->fails()) {
            ResponseService::errorResponse($validator->errors()->first());
        }

        try {
            DB::beginTransaction();
 
            $classSubjectName = 'All';
            $subjectId = '';
            $sectionId = '';
            $schoolId = Auth::user()->school_id;

            if($request->class_subject_id != ''){
                $subjectSectionIdArray = explode(":",$request->class_subject_id);
                $subjectId = $subjectSectionIdArray[0];
                $sectionId = $subjectSectionIdArray[1];
                $classSubjectSections = DB::select("SELECT class_subjects.id, class_subjects.class_id, (SELECT name FROM classes WHERE id = class_subjects.class_id) AS class_name,subject_id,(SELECT name FROM subjects WHERE id = class_subjects.subject_id) AS subject_name,class_sections.id AS section_id,(SELECT name FROM sections WHERE id = class_sections.section_id AND school_id = ".$schoolId.") AS section_name FROM class_subjects, class_sections WHERE class_subjects.school_id = ".$schoolId." AND class_sections.class_id = class_subjects.class_id AND class_subjects.id = ".$subjectId);
                if(COUNT($classSubjectSections) > 0){
                    $classSubjectName = $classSubjectSections[0]->class_name.' - '.$classSubjectSections[0]->subject_name.' - '.$classSubjectSections[0]->section_name;
                }

                // if($request->student_tag){
                //     $student_tagged = implode(',', $request->student_tag);
                // }
            }
           
            if ($request->hasFile('thumbnail')) {
                if (FileSizeLimitService::checkFileSizeLimit($schoolId)) {
                    return ResponseService::errorResponse('storage capacity not enough');
                }
                $thumbnailFile = $request->file('thumbnail');
                $thumbnailSize = $thumbnailFile->getSize();
                $thumbnailSizeKB = round($thumbnailSize / 1024, 2); 

                $thumbnailPath = $thumbnailFile->store('gallery', 'public');

                $data = [
                    'title' => $request->title,
                    'description' => $request->description,
                    'thumbnail' => $request->file('thumbnail')->store('gallery','public'),
                    'session_year_id' => $request->session_year_id,
                    'class_subject_id' => $subjectId,
                    'class_section_id' => $sectionId,
                    'class_subject_section_name' => $classSubjectName,
                    'session_year_id' => $request->session_year_id,
                    'student_tag' => $request->student_tag ?? null,
                    'file_size'=> $thumbnailSizeKB,
                ];
            }

            $gallery = $this->gallery->create($data);

            $galleryFileData = array();

            $galleryFile = $this->files->model();

            $galleryModelAssociate = $galleryFile->modal()->associate($gallery);

            if (!empty($request->images)) {
                foreach ($request->images as $key => $image) {
                    $fileSize = $image->getSize();
                    $fileSizeKB = round($fileSize / 1024, 2);
                    $totalFileSizeKB= $fileSizeKB;

                    $tempFileData = array(
                        'modal_type' => $galleryModelAssociate->modal_type,
                        'modal_id'   => $galleryModelAssociate->modal_id,
                        'file_name'  => basename($image->getClientOriginalName(), '.'.$image->getClientOriginalExtension()),
                        'file_size'  => $totalFileSizeKB,
                    );

                    $tempFileData['type'] = 1;
                    $tempFileData['file_thumbnail'] = null;
                    $tempFileData['file_url'] = $image;

                    $galleryFileData[] = $tempFileData;
                }
            }

                    //Video
                    if ($request->hasFile('videos')) {
                        foreach ($request->file('videos') as $video) {
                            $fileSize = $video->getSize();
                            $fileSizeKB = round($fileSize / 1024, 2);
                    
                            $tempFileData = [
                                'modal_type' => $galleryModelAssociate->modal_type,
                                'modal_id'   => $galleryModelAssociate->modal_id,
                                'file_name'  => basename($video->getClientOriginalName(), '.' . $video->getClientOriginalExtension()),
                                'file_size'  => $fileSizeKB,
                                'type'       => 3,
                                'file_url'   => $video->store('files', 'public'),
                            ];
                    
                            $galleryFileData[] = $tempFileData;
                        }
                    
                        $this->files->createBulk($galleryFileData);
                    }
                    
            if ($request->youtube_links) {
                $links = explode(",", $request->youtube_links);

                foreach ($links as $key => $link) {
                    $tempFileData = array(
                        'modal_type' => $galleryModelAssociate->modal_type,
                        'modal_id'   => $galleryModelAssociate->modal_id,
                        'file_name'  => 'YouTube Link',
                        'file_size'  => 0.0
                    );

                    $tempFileData['type'] = 2;
                    $tempFileData['file_thumbnail'] = null;
                    $tempFileData['file_url'] = $link;

                    $galleryFileData[] = $tempFileData;
                }
            }
            if (!empty($request->images) || $request->youtube_links) {
                $this->files->createBulk($galleryFileData);
            }

            if ($classSubjectName == 'All') {
                $student_ids = DB::table('students')->where('school_id', Auth::user()->school_id)->pluck('user_id')->toArray();
            } else {
                if (!empty($request->student_tag)) {
                    $gallery_ids = explode(',', $request->student_tag);
  
                    if (!empty($gallery_ids)) {
                        $student_ids = DB::table('students')
                            ->join('users', 'students.user_id', '=', 'users.id')
                            ->whereIn('students.id', $gallery_ids)
                            ->pluck('students.user_id')
                            ->toArray();
                    } else {
                        $student_ids = [];
                    }
                } else {
                    if ($sectionId) {
                        $student_ids = DB::table('students')
                            ->where('class_section_id', $sectionId)
                            ->where('school_id', Auth::user()->school_id)
                            ->pluck('user_id')
                            ->toArray();
                    } else {
                        $student_ids = [];
                    }
                }
                
            }

            if($student_ids){   
                $student_ids = collect($student_ids);
                $students = DB::table('students')
                ->join('users', 'students.user_id', '=', 'users.id')
                ->whereIn('students.user_id', $student_ids)
                ->whereNull('users.deleted_at')
                ->get(['students.user_id', 'students.guardian_id', 'users.first_name', 'users.last_name']);
                foreach ($students as $student) {
                    $notifyUser = [];
                    $studentName = $student->first_name . ' ' . $student->last_name;
                    $notifyUser[] = $student->user_id;

                    if ($student->guardian_id) {
                        $notifyUser[] = $student->guardian_id;
                    }
                    $title = 'Gallery Update ';
                    $body = $studentName . ' has added a new learning memory!';
                    $type = "gallery";
                    send_notification($notifyUser, $title, $body, $type);
                }
            }
            DB::commit();
            ResponseService::successResponse('Data Stored Successfully');
        } catch (\Throwable $th) {
            DB::rollBack();
            ResponseService::logErrorResponse($th, "Gallery Controller -> Store Method");
            ResponseService::errorResponse();
        }
    }

    public function deleteGallery($id) {
        try {
            DB::beginTransaction();

            $gallery = $this->gallery->findById($id);
            if (Storage::disk('public')->exists($gallery->getRawOriginal('thumbnail'))) {
                Storage::disk('public')->delete($gallery->getRawOriginal('thumbnail'));
            }

            $gallery->delete();

            DB::commit();
            ResponseService::successResponse('Data Deleted Successfully');
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "Gallery Controller -> destroy Method");
            ResponseService::errorResponse();
        }
    }

    public function updateGallery(Request $request){
        // ResponseService::noFeatureThenRedirect("School Gallery Management");
        // ResponseService::noPermissionThenSendJson('gallery-edit');
        
        $validator = Validator::make($request->all(), [
            'title' => 'required',
            // 'thumbnail' => 'required'
        ]);
        if ($validator->fails()) {
            ResponseService::errorResponse($validator->errors()->first());
        }

        try {
            DB::beginTransaction();
            $schoolId = Auth::user()->school_id;

            if ($request->youtube_links) {
                $links = explode(",", $request->youtube_links);
            }
            $gallery = $this->gallery->findById($request->id);
            $classSubjectName = 'All';            
            $subjectId = '';
            $sectionId = '';
            if($request->class_subject_id != ''){
                $schoolId = Auth::user()->school_id;

                $subjectSectionIdArray = explode(":",$request->class_subject_id);
                $subjectId = $subjectSectionIdArray[0];
                $sectionId = $subjectSectionIdArray[1];
                $classSubjectSections = DB::select("SELECT class_subjects.id, class_subjects.class_id, (SELECT name FROM classes WHERE id = class_subjects.class_id) AS class_name,subject_id,(SELECT name FROM subjects WHERE id = class_subjects.subject_id) AS subject_name,class_sections.id AS section_id,(SELECT name FROM sections WHERE id = class_sections.section_id AND school_id = ".$schoolId.") AS section_name FROM class_subjects, class_sections WHERE class_subjects.school_id = ".$schoolId." AND class_sections.class_id = class_subjects.class_id AND class_subjects.id = ".$subjectId);
                if(COUNT($classSubjectSections) > 0){
                    $classSubjectName = $classSubjectSections[0]->class_name.' - '.$classSubjectSections[0]->subject_name.' - '.$classSubjectSections[0]->section_name;
                }
            }
            if ($request->hasFile('thumbnail')) {
                if($gallery && $gallery->thumbnail){
                    Storage::disk('public')->delete($gallery->thumbnail);
                }
                $thumbnailFile = $request->file('thumbnail');
                $thumbnailSize = $thumbnailFile->getSize();
                $thumbnailSizeKB = round($thumbnailSize / 1024, 2); // Convert size to KB
                $thumbnailPath = $thumbnailFile->store('gallery', 'public');
            }
            
            $data = [
                'title' => $request->title,
                'description' => $request->description,
                'class_subject_id' => $subjectId,
                'class_section_id' => $sectionId,
                'class_subject_section_name' => $classSubjectName,
                'session_year_id' => $request->session_year_id,
                'student_tag' => $student_tagged ?? null,
            ];

            if(isset($thumbnailSizeKB)){
                $data['file_size'] = $thumbnailSizeKB;
            }


            if ($request->hasFile('thumbnail')) {
                if (FileSizeLimitService::checkFileSizeLimit($schoolId)) {
                    return ResponseService::errorResponse('storage capacity not enough');
                    if (Storage::disk('public')->exists($gallery->getRawOriginal('thumbnail'))) {
                        Storage::disk('public')->delete($gallery->getRawOriginal('thumbnail'));
                    }
                }
                $data['thumbnail'] = $request->file('thumbnail')->store('gallery','public');
            }
    
            $this->gallery->update($request->id,$data);

            // Initialize the Empty Array
            $galleryFileData = array();

            // Create A File Model Instance
            $galleryFile = $this->files->model();

            // Get the Association Values of File with gallery
            $galleryModelAssociate = $galleryFile->modal()->associate($gallery);
            if (!empty($request->images)) {
                foreach ($request->images as $key => $image) {
                    if (FileSizeLimitService::checkFileSizeLimit($schoolId)) {
                        return ResponseService::errorResponse('storage capacity not enough');}
                    $fileSize = $image->getSize();
                    $fileSizeKB = round($fileSize / 1024, 2);
                    
                    $tempFileData = array(
                        'modal_type' => $galleryModelAssociate->modal_type,
                        'modal_id'   => $galleryModelAssociate->modal_id,
                        'file_name'  => basename($image->getClientOriginalName(), '.'.$image->getClientOriginalExtension()),
                        'file_size'  => $fileSizeKB,
                    );

                    $tempFileData['type'] = 1;
                    $tempFileData['file_thumbnail'] = null;
                    $tempFileData['file_url'] = $image;

                    $galleryFileData[] = $tempFileData;

                }
            }

            if ($request->youtube_links) {
                
                $links = explode(",", $request->youtube_links);
                foreach ($links as $key => $link) {
                    $tempFileData = array(
                        'modal_type' => $galleryModelAssociate->modal_type,
                        'modal_id'   => $galleryModelAssociate->modal_id,
                        'file_name'  => 'YouTube Link',
                        'file_size'  => 0.0,
                    );

                    $tempFileData['type'] = 2;
                    $tempFileData['file_thumbnail'] = null;
                    $tempFileData['file_url'] = $link;

                    $galleryFileData[] = $tempFileData;
                }
            }
            if (!empty($request->images) || $request->youtube_links) {
                $this->files->createBulk($galleryFileData);
            }

            DB::commit();
            ResponseService::successResponse('Data Updated Successfully');
        } catch (\Throwable $th) {
            DB::rollBack();
            ResponseService::logErrorResponse($th, "Gallery Controller -> Update Method");
            ResponseService::errorResponse();
        }
    }

    public function getStudentProgressList(Request $request)
    {
        ResponseService::noFeatureThenSendJson('Student Progress');

        $schoolId = Auth::user()->school_id;
        $class_section_id = $request->input('class_section_id');
        $subject_id = $request->input('subject_id');
        // $sort = $request->input('sort', 'created_at');
        // $order = $request->input('order', 'desc');
        // $offset = $request->input('offset', 0);
        // $limit = $request->input('limit', 10);
    
        $query = DB::table('student_progress AS sp')
            ->leftJoin('students AS s', 's.id', '=', 'sp.student_id')
            ->leftJoin('users AS u', 'u.id', '=', 's.user_id')
            ->leftJoin('class_subjects AS cs', 'cs.id', '=', 'sp.class_subject_id')
            ->leftJoin('subjects AS sub', 'sub.id', '=', 'cs.subject_id')
            ->leftJoin('users AS t', 't.id', '=', 'sp.teacher_id')
            ->select(
                'sp.id',
                'sp.title',
                'sp.description',
                'sp.created_on',
                'sp.files',
                'sp.class_section_id',
                'cs.id as subject_id' ,
                'sp.school_id',
                'u.first_name AS student_first_name',
                'u.last_name AS student_last_name',
                'sub.name AS subject_name',
                'sub.type as type',
                't.first_name AS teacher_first_name',
                't.last_name AS teacher_last_name',
                'sp.status',
                'sp.remark'
            )
            ->whereNull('u.deleted_at')
            ->where('s.school_id', $schoolId);
    
        if ($class_section_id) {
            $query->where('s.class_section_id', $class_section_id);
        }
    
        if ($subject_id) {
            $query->where('cs.id', $subject_id);
        }
    
        $query->orderBy('sp.created_on', 'DESC');
    
        // Get the total count of records
        $total = $query->count();
    
        // Apply sorting, offset, and limit
        // $query->orderBy($sort, $order)->skip($offset)->take($limit);
    
        // Get the records
        $res = $query->get();
    
        // Prepare the response data
        $bulkData = [];
        $bulkData['total'] = $total;
        $rows = [];
    
        foreach ($res as $row) {
            $tempRow['id'] = $row->id;
            $tempRow['student_name'] = $row->student_first_name . " " . $row->student_last_name;
            $tempRow['subject_name'] = $row->subject_name;
            $tempRow['teacher_name'] = $row->teacher_first_name . " " . $row->teacher_last_name;
            $tempRow['title'] = $row->title;
            $tempRow['description'] = $row->description;
            $tempRow['created_on'] = $row->created_on;
            $tempRow['files'] = $row->files;
            $tempRow['class_section_id'] = $row->class_section_id;
            $tempRow['school_id'] = $row->school_id;
            $tempRow['type'] = $row->type;
            // $tempRow['subject_id'] = $row->subjects_id;
            $tempRow['class_subject_id'] = $row->subject_id;
            $tempRow['status'] = $row->status;
            $tempRow['remark'] = $row->remark;

            $rows[] = $tempRow;
        }
    
        $bulkData['rows'] = $rows;
    
        return response()->json($bulkData);
    }

    public function updateStudentProgress(Request $request)
    {
        ResponseService::noFeatureThenRedirect('Student Progress');

        $request->validate([
            'title'             => 'nullable',
            'description'       => 'nullable',
            'files'             => 'nullable',
        ]);

        try {
            DB::beginTransaction();

            // Retrieve existing record from the database
            $studentProgress = DB::table('student_progress')->where('id', $request->id)->first();

            if (!$studentProgress) {
                throw new Exception('Student progress record not found.');
            }

            // Check permissions
            $isAdmin = Auth::user()->hasRole('School Admin');
            if (!$isAdmin) {
                // For teachers: can only edit their own entries that are pending or auto-approve
                if (!($studentProgress->teacher_id == Auth::id() && 
                    ($studentProgress->status == "auto-approve" || $studentProgress->status == "pending"))
                ) {
                    return ResponseService::errorResponse('Cannot edit this progress');
                }
            }

            // Prepare the data to update
            $data = [
                'title'             => $request->input('title'),
                'description'       => $request->input('description'),
                // 'file_size'         => $fileSizeKB,
            ];
            $schoolId = Auth::user()->school_id;

            // Check if a new file is uploaded
            if ($request->hasFile('files')) {
                $fileSize = $request->file('files')->getSize();
                $fileSizeKB = round($fileSize / 1024, 2);
                    if (FileSizeLimitService::checkFileSizeLimit($schoolId)) {
                        return ResponseService::errorResponse('storage capacity not enough');}
                if ($studentProgress->files) {
                    $oldFilePath = storage_path('app/public/' . $studentProgress->files);
                    if (file_exists($oldFilePath)) {
                        unlink($oldFilePath);
                    }
                }

                // Store the new file
                $file = $request->file('files');
                $path = $file->store('images', 'public'); 
                $data['files'] = $path; 
                $data['file_size'] =$fileSizeKB;
            }

            // Update the record in the database
            DB::table('student_progress')->where('id', $request->id)->update($data);

            DB::commit();
            ResponseService::successResponse('Data Updated Successfully');
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "StudentProgress Controller -> Update Method");
            ResponseService::errorResponse();
        }
    }
        
    public function studentProgressStore(Request $request)
    {
        $sessionYear = $this->cache->getDefaultSessionYear();
        $teacherId = Auth::user()->id;
        // Validate the request data
        $request->validate([
            'student_id'        => 'required',
            'class_subject_id'  => 'required',
            'teacher_id'        => 'nullable',
            'title'             => 'nullable',
            'description'       => 'nullable',
            'files'             => 'nullable',
            'created_on'        => 'required|date',
        ]);
    
        $schoolId = Auth::user()->school_id;

        $settings = $this->cache->getSchoolSettings();
        $requireApproval = isset($settings['student_progress_approval']) && $settings['student_progress_approval'] == 1;
        $isAdmin = Auth::user()->hasRole('School Admin');
        $status = $isAdmin ? 'auto-approve' : ($requireApproval ? 'pending' : 'auto-approve');
        
            // Handle file upload if applicable
            $imageName = '';
            if ($request->hasFile('files')) {
                $image = $request->file('files');
                if (FileSizeLimitService::checkFileSizeLimit($schoolId)) {
                    return ResponseService::errorResponse('storage capacity not enough');}
                $imageName = time() . '_' . $image->getClientOriginalName();
                $imageName = $image->store('images', 'public');
                $fileSize = $image->getSize();
                $fileSizeKB = round($fileSize / 1024, 2);
            }
    
        // Insert data into the database using DB facade
        DB::table('student_progress')->insert([
            'school_id' => Auth::user()->school_id,
            'class_section_id' => $request->class_section_id,
            'student_id' => $request->student_id,
            'class_subject_id' => $request->class_subject_id,
            'teacher_id' => $teacherId,
            'title' => $request->title,
            'description' => $request->description,
            'files' => $imageName ?? '',
            'session_year_id' => $sessionYear->id,
            'created_on' => $request->created_on,
            'file_size'  => $fileSizeKB ?? '',
            'status' => $status,
            'remark' => $request->remark ?? ''
        ]);

        $notifyUser = DB::table('students')
            ->where('id', $request->student_id)
            ->select('user_id', 'guardian_id')
            ->get();

        $studentName = DB::table('users')
            ->join('students', 'students.user_id', '=', 'users.id')
            ->where('students.id', $request->student_id)
            ->select(DB::raw('concat(first_name, " ", last_name) as name'))
            ->first();

        if (!$studentName) {
            return response()->json(['error' => 'Student not found'], 404);
        }

        $name = $studentName->name;

        $studentDetails = DB::table('students')->join('users','users.id','=','students.user_id')->where('students.id',$request->student_id)->first();
        
        if ($isAdmin || $status === 'auto-approve') {
            $recipients = [];
            $recipients[] = $studentDetails->user_id;
            
            $guardian_id = DB::table('students')
                ->where('id', $request->student_id)
                ->whereNotNull('guardian_id')
                ->value('guardian_id');
                
            if ($guardian_id) {
                $recipients[] = $guardian_id;
            }

            $subjectDetails = DB::table('class_subjects AS cs')
                ->join('subjects AS s', 's.id', '=', 'cs.subject_id')
                ->where('cs.id', $request->class_subject_id)
                ->select('s.name AS subject_name', 's.type AS subject_type')
                ->first();

            $title = 'New Progress Added';
            $body = 'The progress for student ' . $studentDetails->first_name . ' ' . $studentDetails->last_name . 
            ' in ' . $subjectDetails->subject_name . ' - ' . $subjectDetails->subject_type . 
            ' has been added';
            
            if ($status === 'auto-approve') {
                $body .= ' by ' . Auth::user()->first_name . ' ' . Auth::user()->last_name;
            }
            
            $type = 'Student Progress';
            
            send_notification($recipients, $title, $body, $type);
        }

        ResponseService::successResponse('Data Updated Successfully');
    }
    
    public function studentProgressListDestroy($id)
    {
        try {
            DB::beginTransaction(); 
            $studentProgress = DB::table('student_progress')->where('id', $id)->first();
            
            if (!$studentProgress) {
                throw new Exception('Student progress record not found.');
            }

            // Check permissions
            $isAdmin = Auth::user()->hasRole('School Admin');
            if (!$isAdmin) {
                // For teachers: can only delete their own entries that are pending or auto-approve
                if (!($studentProgress->teacher_id == Auth::id() && 
                    ($studentProgress->status == "auto-approve" || $studentProgress->status == "pending"))
                ) {
                    return ResponseService::errorResponse('Cannot delete this progress');
                }
            }

            if ($studentProgress->files) {
                $filePath = public_path('storage/' . $studentProgress->files);
                if (file_exists($filePath)) {
                    unlink($filePath); // Delete the file from the storage
                }
            }

            DB::table('student_progress')->where('id',$id)->delete();
            DB::commit();
            ResponseService::successResponse('Data Deleted Successfully');
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "Student Controller -> Destroy Method");
            ResponseService::errorResponse();
        }
    }

    public function getstudentbyclasssection(Request $request){


        $schoolId = Auth::user()->school_id;

        $students = DB::table('students as s')
            ->join('users as u', 's.user_id', '=', 'u.id')
            ->where('s.class_section_id', '=', $request->id) 
            ->where('s.school_id',$schoolId)
            ->whereNull('u.deleted_at')
            ->whereNull('s.deleted_at')
            ->select('s.id', DB::raw("CONCAT(u.first_name, ' ', u.last_name) AS name"))
            ->get();

        ResponseService::successResponse("ClassSection Fetched Successfully", $students);
    }
        
    public function getTeacherBookingSlot(Request $request)
    {
        ResponseService::noFeatureThenSendJson('Timetable Management');
        try {
            $bookingSlot = [];
            $allSlot = DB::table("booking_slot")
            ->where("teacher_id", Auth::user()->id)
            ->whereNull("deleted_at") 
            ->orderBy('date', 'desc')
            ->get();

            $allSlot->each(function ($slot) {
                if ($slot->booking_category_id !== null) {
                    $categoryTitle = DB::table('booking_category')
                        ->select('title')
                        ->where('id', $slot->booking_category_id)
                        ->first();
            
                    $slot->title = $categoryTitle->title ?? null;
                }
            });
            foreach ($allSlot as $slot) {         
                   // Get the number of bookings for the current slot
                $bookedCount = DB::table('booking')
                ->where('slot_id', $slot->id)
                ->whereNull("deleted_at")
                ->distinct('student_id') 
                ->count();

                $slot->bookedCapacity = $bookedCount;

                  
                    $studentsInSlots = DB::table("booking as b")
                    ->join("students as s", "s.id", "=", "b.student_id")
                    ->join("users as u", "u.id", "=", "s.user_id")
                    ->join("booking_slot as bs", "bs.id", "=", "b.slot_id")
                    ->select(DB::raw("CONCAT(u.first_name, ' ', u.last_name) as fullname"), "u.image")
                    ->where("b.slot_id", $slot->id)
                    ->whereNull("b.deleted_at") 
                    ->get();

                    foreach ($studentsInSlots as $student) {
                        $slot->students[] = [
                            'full_name' => $student->fullname,
                            'image' => $student->image
                        ];
                    }

                    $bookingSlot[] = $slot;

          
        }


            ResponseService::successResponse("Timetable Fetched Successfully", $bookingSlot);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function getStudentBookingList(Request $request)
    {
        ResponseService::noFeatureThenSendJson('Timetable Management');
        try {
            $studentsInSlots = DB::table("booking as b")
            ->join("students as s", "s.id", "=", "b.student_id")
            ->join("users as u", "u.id", "=", "s.user_id")
            ->join("booking_slot as bs", "bs.id", "=", "b.slot_id")
            ->select(DB::raw("CONCAT(u.first_name, ' ', u.last_name) as fullname"), "u.image","b.deleted_at",'b.slot_id','b.student_id')
            ->where("b.slot_id", $request->slotId)
            ->distinct()
            ->get();


            ResponseService::successResponse("Student Booking List Fetched Successfully", $studentsInSlots);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function getTeacherBookingTitle(Request $request){
        try {
            $school_id = Auth::user()->school_id;
            $bookingTitle = DB::table('booking_category')
            ->where('school_id',$school_id)
            ->wherenull('deleted_at')
            ->get();
           

            ResponseService::successResponse("Booking Title Fetched Successfully", $bookingTitle);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function createBookingSlot(Request $request){
        try{
            
            DB::beginTransaction();
            $schoolId = Auth::user()->school_id;


            $subject_id = $request->subject_id;
            //$class_section_id = $request->class_section_id;
         
            $teacherId = $request->teacher_id;
            $date = $request->date;
            $start_time = $request->start_time;
            $end_time = $request->end_time;
            $capacity = $request->capacity;
            $status = "pending";
            $student_id = $request->student_id;
            
            if($request->title) {
                $title = $request->title;
                $bookingSlotId = DB::table('booking_slot')->insertGetId([
                    'school_id' => $schoolId,
                    'title' => $title,
                    'subject_id' => $subject_id,
                    //'class_section_id' => $class_section_id,
                    'teacher_id' => $teacherId,
                    'date' => $date,
                    'start_time' => $start_time,
                    'end_time' => $end_time,
                    'capacity' => $capacity,
                    'status' => $status,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }

            if($request->booking_category_id) {
                $bookingCategoryId = $request->booking_category_id;
                $bookingSlotId = DB::table('booking_slot')->insertGetId([
                    'school_id' => $schoolId,
                    'booking_category_id' => $bookingCategoryId,
                    'subject_id' => $subject_id,
                    //'class_section_id' => $class_section_id,
                    'teacher_id' => $teacherId,
                    'date' => $date,
                    'start_time' => $start_time,
                    'end_time' => $end_time,
                    'capacity' => $capacity,
                    'status' => $status,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }  

            if (is_array($student_id)) {
                foreach ($student_id as $id) {
                    DB::table('booking_slot_students')->insert([
                        'booking_slot_id' => $bookingSlotId,
                        'student_id' => $id,
                        'created_at' => now(),
                        'updated_at' => now()
                    ]);
                }
            } else {
                // Handle single student ID
                DB::table('booking_slot_students')->insert([
                    'booking_slot_id' => $bookingSlotId,
                    'student_id' => $student_id,
                    'created_at' => now(),
                    'updated_at' => now()
                ]);
            }

            DB::table('admission_notification')->insert([
                'user_id'       =>$teacherId,
                'school_id'     =>Auth::user()->school_id,
                'date'          =>date('Y-m-d'),
                'status'        =>0,
                'type'          =>0
            ]);
            DB::commit();
            
            return response()->json(['message' => 'Booking slot created successfully'], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['message' => 'Error: ' . $e->getMessage()], 500);
        }
    }

    // public function getStudentRewardInfo(Request $request){

    //     try{
    //         $studentRewardInfo= DB::table('rewards as r1')
    //         ->join('students', 'students.id', '=', 'r1.student_id')
    //         ->join('users', 'users.id', '=', 'students.user_id')
    //         ->join(
    //             DB::raw('(SELECT student_id, MAX(updated_at) as latest_update FROM rewards GROUP BY student_id) as r2'),
    //             function ($join) {
    //                 $join->on('r1.student_id', '=', 'r2.student_id')
    //                      ->on('r1.updated_at', '=', 'r2.latest_update');
    //             }
    //         )
    //         ->where('r1.class_id', $request->classId)
    //         ->where('r1.school_id', Auth::user()->school_id)
    //         ->whereNull('r1.deleted_at')
    //         ->select(
    //             'r1.*',
    //             DB::raw("CONCAT(users.first_name, ' ', users.last_name) as studentName"),
    //             'users.image as imgUrl'
    //         )
    //         ->orderBy('r1.student_id','asc')
    //         ->get();

            
    //         ResponseService::successResponse("Booking Title Fetched Successfully", $studentRewardInfo);
    //     }catch(\Exception $e){

    //         ResponseService::logErrorResponse($e);
    //         ResponseService::errorResponse();

    //     }
        
    // }
    
    public function getRewardCategory(){
        try{
            $rewardCategory =DB::table("rewards_category")
                            ->where("school_id",Auth::user()->school_id)
                            ->whereNull("deleted_at")
                            ->get();
                            
            ResponseService::successResponse("Reward Category Fetched Successfully", $rewardCategory);


        }catch(\Exception $e){
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }
    public function updateReward(Request $request){
        try{
            DB::beginTransaction();

            $rewardCategory=DB::table("rewards_category")
                            ->where("id",$request->categoryId)
                            ->whereNull("deleted_at")
                            ->first();
            
            
            foreach($request->students as $student){
                $studentId=DB::table("students")
                            ->where("students.user_id",$student)
                            ->whereNull("deleted_at")
                            ->value('students.id');
                $studentReward=DB::table("rewards")
                            ->where("student_id",$studentId)
                            ->orderBy('updated_at', 'desc')
                            ->whereNull('deleted_at')
                            ->first();
                if(!$studentReward){
                    $totalScore=0;
                    $rewardPointTotal=0;
                } else {
                    $totalScore=$studentReward->score_total;
                    $rewardPointTotal=$studentReward->reward_point_total;
                }
                if($rewardCategory->mode == 1) {
                    $score_total = $totalScore + $rewardCategory->points_amount;
                    $reward_point_total = $rewardPointTotal + $rewardCategory->points_amount;
                } else {
                    $score_total = $totalScore + $rewardCategory->points_amount;
                    $reward_point_total =$studentReward->reward_point_total;
                } 
                
                DB::table("rewards")->insert([
                    "school_id"=>Auth::user()->school_id,
                    "class_id"=>$request->classId,
                    "student_id"=>$studentId,
                    "score_amount"=>$rewardCategory->points_amount,
                    "score_total"=>$score_total,
                    "reward_point_amount"=>$rewardCategory->mode == 1 ? $rewardCategory->points_amount : 0,
                    "reward_point_total"=>$reward_point_total,
                    "remark"=>$request->remark,
                    "category_id"=>$request->categoryId

                ]);

            }
            $studentsUserId=collect($request->students);
            $students= DB::table('students')
                            ->join('users', 'students.user_id', '=', 'users.id')
                            ->whereIn('students.user_id', $studentsUserId)
                            ->whereNull('users.deleted_at')
                            ->get(['students.user_id', 'students.guardian_id','users.first_name', 'users.last_name']);
            if($students){
                $type = "Reward Notification";
                foreach($students as $student) {
                    $notifyUser=[];
                    $notifyUser[]=$student->user_id;
                    $studentName = $student->first_name . ' ' . $student->last_name;
                    if ($student->guardian_id) {
                        $guardian=[];
                        $guardian[] = $student->guardian_id;
                    }
                    if($rewardCategory->points_amount>0){
                        $title = 'Points Added!';
                        if($rewardCategory->remark){
                            $body ="Great job! You've earned ". $rewardCategory->points_amount . "points for" . $rewardCategory->remark . ". Keep up the good work!";
                            if($guardian !==null){
                                $guardianBody = "Congratulations! Your child, " . $studentName . ", has earned " . $rewardCategory->points_amount . " points for " . $rewardCategory->remark . ".";
                            }
                        }else{
                            $body ="Great job! You've earned ". $rewardCategory->points_amount . " points. Keep up the good work!";
                            if($guardian!==null){
                                $guardianBody = "Congratulations! Your child, " . $studentName . ", has earned " . $rewardCategory->points_amount . " points.";
                            }
                        }
                    }else{
                        $title = 'Points Deducted';
                        if ($rewardCategory->remark) {
                            $body = "Unfortunately, you've lost " . $rewardCategory->points_amount . " points due to " . $rewardCategory->remark . ". Let's work on improving this!";
                            if ($guardian!==null) {
                                $guardianBody = "Unfortunately, your child, " . $studentName . ", has lost " . $rewardCategory->points_amount . " points due to " . $rewardCategory->remark . ".";
                            }
                        } else {
                            $body = "Unfortunately, you've lost " . $rewardCategory->points_amount . " points. Let's work on improving this!";
                            if ($guardian!==null) {
                                $guardianBody = "Unfortunately, your child, " . $studentName . ", has lost " . $rewardCategory->points_amount . " points.";
                            }
                        }
                    }
                    send_notification($notifyUser, $title, $body, $type);
                    if ($guardian!==null) {
                        send_notification($guardian, $title, $guardianBody, $type);
                    }
                }
                                       
            }
                
            DB::commit();
            
            return response()->json(['message' => 'Score and reward point updated successfully'], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['message' => 'Error: ' . $e->getMessage()], 500);
        }
    }

    public function getStudentBySubject(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'subject_id' => 'required|numeric',
        ]);

        if ($validator->fails()) {
            ResponseService::validationError($validator->errors()->first());
        }

        try {
            $teacherId = Auth::user()->id;
            $subjectId = $request->subject_id;
    
            $students = DB::table('students')
                ->join('class_sections', 'class_sections.id', '=', 'students.class_section_id')
                ->join('users', 'students.user_id', '=', 'users.id')
                ->leftJoin('class_subjects', 'class_subjects.class_id', '=', 'class_sections.class_id')
                ->leftJoin('subjects', 'class_subjects.subject_id', '=', 'subjects.id')
                ->leftJoin('subject_teachers', 'class_subjects.id', '=', 'subject_teachers.class_subject_id')
                ->leftJoin('student_subjects', function($join) {
                    $join->on('student_subjects.student_id', '=', 'users.id')
                         ->on('student_subjects.class_subject_id', '=', 'class_subjects.id');
                })
                ->select(
                    'students.id',
                    'users.first_name',
                    'users.last_name',
                    'class_subjects.id as class_subject_id',
                    'subjects.id as subject_id',
                    'subjects.name as subject_name',
                    'class_subjects.type as class_subject_type',
                    'student_subjects.id as student_subject_id'
                )
                ->where('subject_teachers.teacher_id', $teacherId)
                ->where('class_subjects.subject_id', $subjectId)
                ->where('users.school_id', Auth::user()->school_id)
                ->whereNull('users.deleted_at')
                ->get();
    
            // Process results into core/elective arrays
            $rows = [];
            foreach ($students as $student) {
                if (!isset($rows[$student->id])) {
                    $rows[$student->id] = [
                        'id' => $student->id,
                        'fullname' => $student->first_name.' '.$student->last_name,
                        'core_subjects' => [],
                        'elective_subjects' => []
                    ];
                }
    
                if ($student->class_subject_type === 'Compulsory') {
                    $rows[$student->id]['core_subjects'][] = [
                        'subject_id' => $student->subject_id,
                        'subject_name' => $student->subject_name
                    ];
                } elseif ($student->class_subject_type === 'Elective' && $student->student_subject_id) {
                    $rows[$student->id]['elective_subjects'][] = [
                        'subject_id' => $student->subject_id,
                        'subject_name' => $student->subject_name
                    ];
                }
            }
    
            // Apply subject filter
            if ($request->subject_id) {
                $rows = array_filter($rows, function($student) use ($subjectId) {
                    foreach ($student['core_subjects'] as $subject) {
                        if ($subject['subject_id'] == $subjectId) return true;
                    }
                    foreach ($student['elective_subjects'] as $subject) {
                        if ($subject['subject_id'] == $subjectId) return true;
                    }
                    return false;
                });
            }
    
            ResponseService::successResponse('Students fetched successfully', array_values($rows));
    
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }
}
