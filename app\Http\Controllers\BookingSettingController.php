<?php

namespace App\Http\Controllers;
use App\Services\ResponseService;


use App\Repositories\ClassSchool\ClassSchoolInterface;
use App\Repositories\ClassSection\ClassSectionInterface;
use App\Repositories\SubjectTeacher\SubjectTeacherInterface;
use App\Repositories\Subject\SubjectInterface;
use App\Repositories\FeesType\FeesTypeInterface; 
use App\Services\CachingService;
use App\Repositories\Student\StudentInterface;
use App\Services\BootstrapTableService;
use \DateTime;


use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Auth;
use Throwable;
use Exception;

class BookingSettingController extends Controller
{
    private StudentInterface $student;
    private SubjectTeacherInterface $subjectTeacher;
    private ClassSchoolInterface $class;
    private CachingService $cache;
    private ClassSectionInterface $classSection;
    private SubjectInterface $subject;

    public function __construct(
        CachingService $cache,
        ClassSchoolInterface $classSchool,
        StudentInterface $student,
        ClassSectionInterface $classSection,
        SubjectTeacherInterface $subjectTeacher,
        SubjectInterface $subject
    ) {

        $this->cache = $cache;
        $this->class = $classSchool;
        $this->student = $student;
        $this->classSection = $classSection;
        $this->subjectTeacher=$subjectTeacher;
        $this->subject=$subject;
        }
    

    public function index() {
        ResponseService::noFeatureThenRedirect('Booking Management');
        $teacher="";
        $classes = $this->classSection->all(['*'], ['class', 'class.stream', 'section', 'medium']);
        $simpleClassDropdown = $this->class->builder()->pluck('name','id');
        $subject=DB::table('subjects')
        ->where('school_id',Auth::user()->school_id)
        ->whereNull('deleted_at')
        ->get();
    
        if(Auth::user()->hasRole('Teacher')) {   
            $teacher=DB::table('users')->where('id',Auth::user()->id)->select('id',DB::raw('concat(first_name," ",last_name) as name'))->first();                      
            
        } 
        
        return view('booking.booking_setting', compact('simpleClassDropdown','classes','teacher','subject'));

    }

    public function store(Request $request) {
       
        
        // $request->validate([
        //     'points_amount' => 'required|numeric',
        //     'category_name' => 'required|string',
        // ]);

        try{

            $school_id = Auth::user()->school_id; 
            $mode=$request->mode;

            if(!$mode){
                $mode=0;
            }
           
            $points_amount=(int)$request->points_amount;
            $category_name=$request->category_name;
            $remark=$request->remark;

            $type = $request->type;

            if($type==='debit'){
                $points_amount=$points_amount*-1;
            }

           
            DB::beginTransaction();

            $data=[
                'title'     =>$request->title,
                'school_id' =>Auth::user()->school_id
                  
            ];

            DB::table('booking_category')->insert($data);
            DB::commit();
            ResponseService::successResponse('Data Stored Successfully');
        }catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "RewardCategory Controller -> Store Method");
            ResponseService::errorResponse();
        }

    }

    public function show() {
        $offset = request('offset', 0);
        $limit = request('limit', 10);
        $sort = request('sort', 'id'); 
        $order = request('order', 'ASC');
        $search = request('search');
        $showDeleted = request('show_deleted');
        $school_id = Auth::user()->school_id; 
        
        $query = DB::table('booking_category')
            ->when($search, function ($query) use ($search) {
                $query->where('title', 'LIKE', "%$search%");
            })
            ->when(!empty($showDeleted), function ($query) {
                $query->whereNotNull('deleted_at');
            }, function ($query) {
                $query->whereNull('deleted_at');
            });
    
        $total = $query->where('school_id', '=', $school_id)->count();
    
        $data = $query->orderBy($sort, $order)
                    ->skip($offset)
                    ->take($limit)
                    ->get();
    
        $bulkData = [];
        $bulkData['total'] = $total;
        $rows = [];
        $no = 1;
    
        foreach ($data as $row) {
            $operate = '';
    
            // Check if the record is deleted or active
            if (empty($row->deleted_at)) {
                // Active record - show edit and delete buttons
                $operate .= BootstrapTableService::editButton(route('bookingsetting.update', $row->id));
                $operate .= BootstrapTableService::deleteButton(route('bookingsetting.destroy', $row->id));
            } else {
                // Deleted record - show restore and trash buttons
                $operate .= BootstrapTableService::restoreButton(route('bookingsetting.restore', $row->id));
                $operate .= BootstrapTableService::trashButton(route('bookingsetting.trash', $row->id));
            }
    
            // Extract date and time from created_at
            $date = null;
            $time = null;
            if (!empty($row->created_at)) {
                $dateTime = new DateTime($row->created_at);
                $date = $dateTime->format('Y-m-d');
                $time = $dateTime->format('H:i:s');
            }
    
            $tempRow = (array) $row; 
            $tempRow['no'] = $no++;
            $tempRow['date'] = $date;
            $tempRow['time'] = $time;
            $tempRow['operate'] = $operate;
            $rows[] = $tempRow;
        }
    
        $bulkData['rows'] = $rows;
        return response()->json($bulkData);
    }

    public function update(Request $request, $id) {
        try {
            DB::beginTransaction();
    
            // Prepare the data for the update
            $data = [
                'title' => $request->title,
            ];
    
            // Update the booking category title
            DB::table('booking_category')->where('id', $id)->update($data);
    
            DB::commit();
            ResponseService::successResponse('Data Updated Successfully');
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "Booking Controller -> update Method");
            ResponseService::errorResponse();
        }
    }
    public function trash($id) {
        try {
            DB::beginTransaction();
            DB::table('booking_category')->where('id', $id)->delete();
            
            DB::commit();
            ResponseService::successResponse('Data Deleted Successfully');
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "Booking Controller -> Trash Method");
            ResponseService::errorResponse();
        }
    }
    public function destroy($id)
    {
        try {
            DB::beginTransaction();
            DB::table('booking_category')->where('id', $id)->update(['deleted_at' => now()]);
            
            DB::commit();
            ResponseService::successResponse('Data Delete Successfully');
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "Booking Controller -> Trash Method");
            ResponseService::errorResponse();
        }
    }
    public function restore($id) {

        try {
            DB::beginTransaction();
            DB::table('booking_category')->where('id', $id)->update(['deleted_at' => null]);
            
            DB::commit();
            ResponseService::successResponse('Data Restored Successfully');
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "Booking Controller -> restore Method");
            ResponseService::errorResponse();
        }

    }
    
    
    
    }
    
    
                    
    




