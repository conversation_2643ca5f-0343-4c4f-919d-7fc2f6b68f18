<?php

namespace App\Console\Commands;

use DateTime;
use Carbon\Carbon;
use App\Models\StudentFee;
use App\Models\StudentFeesDetail;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use App\Repositories\StudentFeesDetail\StudentFeesDetailInterface;

class overdueCron extends Command
{
    private StudentFeesDetailInterface $studentFeesDetail;
    public function __construct(StudentFeesDetailInterface $studentFeesDetail)
    {
        parent::__construct();
        $this->studentFeesDetail = $studentFeesDetail;   
    }
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'overdue:cron';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            //$this->recurringCronJob();
            $packageStatus = DB::table('purchase_package')->get();
            foreach ($packageStatus as $row) {
                $pp_id = $row->id;
    
                $remaining_days = DB::table('purchase_package as pp')
                    ->join('subject_package as sp', 'pp.package_id', '=', 'sp.id')
                    ->select(
                        'pp.student_id',
                        'sp.name',
                        'sp.expiry_days',
                        'pp.date as purchase_date'
                    )
                    ->where('pp.id', $pp_id)
                    ->first();
    
                if ($remaining_days) {
                    $purchaseDate = Carbon::parse($remaining_days->purchase_date);
                    $expiryDays = $remaining_days->expiry_days;
    
                    $expirationDate = $purchaseDate->addDays($expiryDays);
                    $formattedExpirationDate = $expirationDate->format('Y-m-d');
    
                    $remainingDays = $expirationDate->diffInDays(Carbon::now());
    
                    if (Carbon::now() >= $expirationDate) {
                        $expirationDate->diffInDays(Carbon::now(), false);
    
                        DB::table('purchase_package')
                            ->where('id', $pp_id)
                            ->update(['status' => 1]);
                    } else if ($remainingDays <= 30) {
                        DB::table('purchase_package')
                            ->where('id', $pp_id)
                            ->update(['status' => 2]);
                    } else {
                        DB::table('purchase_package')
                            ->where('id', $pp_id)
                            ->update(['status' => 0]);
                    }
                }
            }
            $this->overdueStudents();
            $this->handleTomorrowDueDates();
            // $result = DB::table('cronjob_testing')->insert(['name' => 'laravel cronjob runned']);
            Log::info("Overdue Cron Executed!");
        } catch (\Exception $e) {
            // Log the error
            Log::error('Error in overdue cron job: ' . $e->getMessage(), [
                'exception' => $e,
                'stack_trace' => $e->getTraceAsString(),
            ]);
        }
    }

    public function overdueStudents()
    {
        $overdueStudents = DB::table('students')
        ->join('student_fees', 'students.id', '=', 'student_fees.student_id')
        ->leftJoin('student_fees_paids', 'student_fees.id', '=', 'student_fees_paids.student_fees_id')
        ->where('student_fees.due_date', '<', date('Y-m-d')) 
        ->whereNull('student_fees.deleted_at')
        ->where('student_fees.status','=','published')
        ->whereNull('student_fees_paids.is_fully_paid')
        ->select('students.*', 'student_fees.due_date','student_fees.id as student_fee_id','student_fees.uid as student_fee_uid') 
        ->get();
        $sevenDaysAgo = date('Y-m-d', strtotime('-7 days'));
        foreach($overdueStudents as $student){
            $invoiceNumber = "INV" . sprintf('%08d', $student->student_fee_uid ?? '');
            $recent = DB::table('duefees_reminder')->where('student_id',$student->id)->where('student_fee_id',$student->student_fee_id)->where('reminder_date','>',$sevenDaysAgo)->where('type','=',2)->exists();
            if(!$recent){
                $guardian = DB::select('SELECT s.guardian_id, CONCAT(u.first_name, " ", u.last_name) AS full_name FROM students s JOIN users u ON u.id = s.user_id WHERE s.id ='. $student->id);
                if(!empty($guardian)){
                    $title = 'Reminder';
                    $body = $guardian[0]->full_name."'s school fees (Invoice #" . $invoiceNumber . ") are overdue. Please make the payment at your earliest convenience";
                    $type = "Fees Overdue";
                    send_notification([$guardian[0]->guardian_id], $title, $body, $type);

                    DB::table('duefees_reminder')->insert(['student_id' => $student->id, 'student_fee_id' => $student->student_fee_id, 'type' => 2, 'reminder_date' => date('Y-m-d')]);
                }
            }
        }
    }

    public function handleTomorrowDueDates()
    {
        $tomorrow = date('Y-m-d', strtotime('+1 day'));

        $studentsWithDueDateTomorrow = DB::table('students')
            ->join('student_fees', 'students.id', '=', 'student_fees.student_id')
            ->leftJoin('student_fees_paids', 'student_fees.id', '=', 'student_fees_paids.student_fees_id')
            ->where('student_fees.due_date', '=', $tomorrow)
            ->whereNull('student_fees.deleted_at')
            ->where('student_fees.status','=','published')
            ->whereNull('student_fees_paids.is_fully_paid')
            ->select('students.id', 'student_fees.id as student_fee_id','student_fees.uid as student_fee_uid')
            ->get();

        foreach ($studentsWithDueDateTomorrow as $student) {
            $invoiceNumber = "INV" . sprintf('%08d', $student->student_fee_uid ?? '');
            $guardian = DB::select('SELECT s.guardian_id, CONCAT(u.first_name, " ", u.last_name) AS student_full_name FROM students s JOIN users u ON u.id = s.user_id WHERE s.id ='. $student->id);
            if(!empty($guardian)){
                $title = 'Reminder';
                $body = $guardian[0]->student_full_name."'s school fees (Invoice #" . $invoiceNumber . ") are due tomorrow. Please make payment before the due date to avoid due charges.";
                $type = "Fees Due Tomorrow";
                send_notification([$guardian[0]->guardian_id], $title, $body, $type);
                
                DB::table('duefees_reminder')->insert(['student_id' => $student->id,'student_fee_id' => $student->student_fee_id,'type'=>1,'reminder_date' => date('Y-m-d')]);
            }
         
        }
    }

    public function recurringCronJob()
    {
        try {
            $studentFees = StudentFee::whereNotNull('recurring_invoice')->where('status', 'published')->get();
            foreach ($studentFees as $fee) {
                if (isset($fee->total_cycles) && $fee->current_cycle < $fee->total_cycles) {
                    $recurringDate = $this->calculateNextRecurringDate($fee->created_at, $fee->current_cycle, $fee->recurring_invoice);
                    //YYYY-MM-DD
                    $sessionYear = DB::select("SELECT end_date FROM session_years WHERE school_id = ? AND default = 1", [$fee->school_id]);
                    if (COUNT($sessionYear) == 0) {
                        continue;
                    } else {
                        $sessionEndDate = $sessionYear[0]->end_date;
                        $sessionEndDate = Carbon::parse($sessionEndDate)->format('Y-m-d');
                        if (Carbon::parse($recurringDate)->gt(Carbon::parse($sessionEndDate))) {
                            continue;
                        }
                    }

                    $earlyDate = $this->calculateEarlyDate($fee->early_date, $fee->current_cycle, $fee->recurring_invoice);
                    $dueDate = $this->calculateDueDate($fee->due_date, $fee->current_cycle, $fee->recurring_invoice);
    
                    if (Carbon::parse($recurringDate)->lte(Carbon::now())) {
                        
                        $fee->current_cycle += 1;
                        $currentCycle = $fee->current_cycle;
                        $fee->save();
                        $newFee = StudentFee::create([
                            'name'               => 'Auto-generated',
                            'due_date'           => $dueDate,
                            'due_charges'        => $fee->due_charges ?? 0,
                            'due_charges_amount' => $fee->due_charges_amount ?? 0,
                            'early_date'         => $earlyDate ? Carbon::parse($earlyDate)->format('Y-m-d') : null,
                            'early_offer'        => $fee->early_offer ?? 0,
                            'early_offer_amount' => $fee->early_offer_amount ?? 0,
                            'class_id'           => $fee->class_id,
                            'school_id'          => $fee->school_id,
                            'session_year_id'    => $fee->session_year_id,
                            'student_id'         => $fee->student_id,
                            'status'             => 'published',
                            'uid'                => $this->generateNextUid($fee->school_id),
                            'current_cycle'      => $currentCycle,
                            'total_cycles'        => $fee->total_cycles,
                            'recurring_reference' => $fee->id,
                            'created_at'         => now(),
                            'created_at_draft'   => now(),
                            'updated_at'         => now(),
                        ]);
    
                        $feeTypes = DB::table('student_fees_details')->where('student_fees_id',$fee->id)->whereNotIn('fees_type_name', ['Overdue Fees', 'Early Discount'])->get();
                        // $this->studentFeeType->builder()->where("student_id", $fee->student_id)->get();
    
                        $feeDetails = [];
                        foreach ($feeTypes as $feeType) {
                            $feeDetails[] = [
                                "student_fees_id"   => $newFee->id,
                                "fees_type_name"    => $feeType->fees_type_name,
                                "fees_type_amount"  => $feeType->fees_type_amount,
                                "classification_code" => $feeType->classification_code,
                                "quantity"          => $feeType->quantity ?? 1,
                                "unit"              => $feeType->unit,
                                "discount"          => $feeType->discount ?? null,
                                "tax"               => $feeType->tax ?? null,
                                "optional"          => 0,
                                "school_id"         => $fee->school_id,
                                "created_at"        => now(),   
                                "updated_at"        => now(),
                            ];
                        }
                        if (count($feeDetails) > 0) {
                            // Log::info('Fee Details: ' . json_encode($feeDetails));
                            StudentFeesDetail::insert($feeDetails);
                            // $this->studentFeesDetail->upsert($feeDetails, ['student_fees_id','school_id'], ['fees_type_name', 'fees_type_amount', 'classification_code', 'unit', 'quantity', 'optional']);
                        }
                    }
                }
            }
        } catch (\Exception $e) {
            Log::error('Error in overdue cron job: ' . $e->getMessage(), [
                'exception' => $e,
                'stack_trace' => $e->getTraceAsString(),
            ]);
        }
    }
    public function addMonth($date, $count) {
        $start = new DateTime($date);
        $checkDate = new DateTime($date);
        $checkDate->modify('last day of +'.$count.' month');
        if ($start->format('Y-m-d') === $start->format('Y-m-t')) {
            $start->modify('last day of +'.$count.' month');
        } else {
            if (($start->format('d') > $checkDate->format('d'))) {
                $start = $checkDate;
            } else {
                $start->modify('+'.$count.' month');
            }   
        }
        return $start;
    }
    private function calculateNextRecurringDate($created_at, $current_cycle, $recurring_invoice)
    {
        $createdAt = Carbon::parse($created_at);

        $nextRecurringDate = $this->addMonth($createdAt,($current_cycle + 1) * $recurring_invoice);
        return $nextRecurringDate->format('Y-m-d');
    }
    private function calculateDueDate($due_date, $current_cycle, $recurring_invoice)
    {
        $createdAt = Carbon::parse($due_date);
        $nextRecurringDate = $this->addMonth($createdAt,($current_cycle + 1) * $recurring_invoice);

        return $nextRecurringDate->format('Y-m-d');
    }
    private function calculateEarlyDate($early_date, $current_cycle, $recurring_invoice)
    {
        if (!$early_date) {
            return null;
        }
        $createdAt = Carbon::parse($early_date);

        $nextRecurringDate = $this->addMonth($createdAt,($current_cycle + 1) * $recurring_invoice);

        return $nextRecurringDate->format('Y-m-d');
    }

    public function generateNextUid($schoolId)
    {
        $latestUID = DB::table('student_fees')->where('school_id', $schoolId)->where('status', 'published')->whereNull('deleted_at')->select('uid')->orderBy('uid', 'desc')->value('uid');
        $uid = $latestUID ? $latestUID + 1 : 1;

        while (DB::table('student_fees')
            ->where('uid', $uid)
            ->where('school_id', $schoolId)
            ->where('status', 'published')
            ->exists()
        ) {
            $uid++;
        } 

        return $uid;
    }
}
