<?php

namespace App\Http\Controllers;
use Carbon\Carbon;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Barryvdh\DomPDF\Facade\Pdf;

class StatementController extends Controller
{
        public function index()
    {
        $request = request();
        $user = auth()->user();
        $school = $user->school;

        $start_date = $request->query('filter_start_date', Carbon::now()->startOfYear()->format("d-m-Y"));
        $end_date = $request->query('filter_end_date', Carbon::now()->endOfYear()->format("d-m-Y"));

        $student_name = $request->query('filter_student_name', '');

        $balance = 0.00;
        $sumPayment =0.00;
        $sumInvoice=0.00;

        $invoice = DB::table('student_fees')
        ->Join('students','student_fees.student_id','=','students.id')
        ->Join('student_fees_details','student_fees.id','=','student_fees_details.student_fees_id')
        ->Join('users','students.user_id','=','users.id')
        ->select(
            DB::raw('DATE(student_fees.invoice_date) as note_date'), 
            DB::raw('SUM(
                ((student_fees_details.fees_type_amount - 
                  (student_fees_details.fees_type_amount *COALESCE(student_fees_details.discount, 0) / 100)) + 
                 (student_fees_details.fees_type_amount * COALESCE(student_fees_details.tax, 0) / 100)) * 
                student_fees_details.quantity
            ) as amount'),
            'student_fees.uid as student_fees_id',
            'student_fees.id',
            'student_fees.due_date',
            'student_fees.early_offer',
            'student_fees.early_offer_amount',
            'student_fees.due_charges',
            'student_fees.due_charges_amount',
            DB::raw('CONCAT(COALESCE(users.first_name, ""), " ", COALESCE(users.last_name, "")) as full_name'),
            DB::raw('"invoice" as type')
        )
        ->groupBy(DB::raw('DATE_FORMAT(student_fees.invoice_date, "%Y-%m-%d %H:%i:%s")'), 'student_fees_details.student_fees_id') 
        ->whereNotNull('student_fees.uid') 
        ->where('student_fees.status','published')
        ->where('student_fees.school_id', '=', auth()->user()->school_id)
        ->whereNull('student_fees.deleted_at') 
        ->whereBetween(DB::raw('DATE(student_fees.invoice_date)'),[
            Carbon::createFromFormat('d-m-Y', $start_date)->startOfDay(),
            Carbon::createFromFormat('d-m-Y', $end_date)->endOfDay()
        ])
        ->get();

        $payment = DB::table('student_fees')
        ->Join('students','student_fees.student_id','=','students.id')
        ->Join('student_fees_paids','student_fees.id','=','student_fees_paids.student_fees_id')
        ->Join('users','students.user_id','=','users.id')
        ->select(
            'student_fees_paids.date as note_date', 
                'student_fees_paids.amount',
                'student_fees_paids.student_fees_id as student_fee_id',
                'student_fees_paids.id',
                'student_fees.due_date',
                'student_fees.uid as student_fees_id',
                DB::raw('CONCAT(COALESCE(users.first_name, ""), " ", COALESCE(users.last_name, "")) as full_name'),
                DB::raw('"payment" as type')
            )
        ->where('student_fees.school_id', '=', auth()->user()->school_id)
        ->whereBetween('student_fees_paids.date', [
            Carbon::createFromFormat('d-m-Y', $start_date)->startOfDay(),
            Carbon::createFromFormat('d-m-Y', $end_date)->endOfDay()
        ])
        ->where('student_fees_paids.is_fully_paid', '=', '1')
        ->get();

        $creditStatements = DB::table('students as s')
        ->join('users as u', 's.user_id', '=', 'u.id')
        ->join('credit_note as cn', 's.id', '=', 'cn.student_id')
        ->join('credit_note_details as cnd', 'cn.id', '=', 'cnd.credit_note_id')
        ->join('student_fees as sf', 'sf.id', '=', 'cn.student_fee_id')
        ->select(
            'cn.date as note_date',
            DB::raw('(cnd.credit_note_amount * cnd.tax / 100 + cnd.credit_note_amount) * cnd.quantity as amount'),
            'cn.student_fee_id',
            'cn.id',
            'sf.uid as student_fees_id',
            'cn.uid',
            DB::raw('CONCAT(COALESCE(u.first_name, ""), " ", COALESCE(u.last_name, "")) as full_name'),
            DB::raw('"credit" as type')
        )
        ->where('s.school_id', '=', auth()->user()->school_id)
        ->whereNull('cn.deleted_at')
        ->whereBetween('cn.date', [
            Carbon::createFromFormat('d-m-Y', $start_date)->startOfDay(),
            Carbon::createFromFormat('d-m-Y', $end_date)->endOfDay()
        ])
        ->get();

        $debitStatements = DB::table('students as s')
        ->join('users as u', 's.user_id', '=', 'u.id')
        ->join('debit_note as dn', 's.id', '=', 'dn.student_id')
        ->join('debit_note_details as dnd', 'dn.id', '=', 'dnd.debit_note_id')
        ->join('student_fees as sf', 'sf.id', '=', 'dn.student_fee_id')
        ->select(
            'dn.date as note_date',
            DB::raw('(dnd.debit_note_amount * dnd.tax / 100 + dnd.debit_note_amount) * dnd.quantity as amount'),
            'dn.student_fee_id',
            'dn.id',
            'sf.uid as student_fees_id',
            'dn.uid',
            DB::raw('CONCAT(COALESCE(u.first_name, ""), " ", COALESCE(u.last_name, "")) as full_name'),
            DB::raw('"debit" as type')
        )
        ->where('s.school_id', '=', auth()->user()->school_id)
        ->whereNull('dn.deleted_at')
        ->whereBetween('dn.date', [
            Carbon::createFromFormat('d-m-Y', $start_date)->startOfDay(),
            Carbon::createFromFormat('d-m-Y', $end_date)->endOfDay()
        ])
        ->get();

        $refundStatements = DB::table('students as s')
        ->join('users as u', 's.user_id', '=', 'u.id')
        ->join('refund_note as rn', 's.id', '=', 'rn.student_id')
        ->join('refund_note_details as rnd', 'rn.id', '=', 'rnd.refund_note_id')
        ->join('student_fees as sf', 'sf.id', '=', 'rn.student_fee_id')
        ->select(
            'rn.date as note_date',
            DB::raw('(rnd.refund_note_amount * rnd.tax / 100 + rnd.refund_note_amount) * rnd.quantity as amount'),
            'rn.student_fee_id',
            'rn.id',
            'sf.uid as student_fees_id',
            'rn.uid',
            DB::raw('CONCAT(COALESCE(u.first_name, ""), " ", COALESCE(u.last_name, "")) as full_name'),
            DB::raw('"refund" as type')
        )
        ->where('s.school_id', '=', auth()->user()->school_id)
        ->whereNull('rn.deleted_at')
        ->whereBetween('rn.date', [
            Carbon::createFromFormat('d-m-Y', $start_date)->startOfDay(),
            Carbon::createFromFormat('d-m-Y', $end_date)->endOfDay()
        ])
        ->get();

        $allStatements = $creditStatements->merge($debitStatements)
        ->merge($refundStatements)
        ->merge($invoice)
        ->merge($payment)
        ->sortBy('note_date'); 

        if ($request->ajax()) {
            return response()->json([
                'start_date' => Carbon::parse($start_date)->format('d-m-Y'), 
                'end_date' => Carbon::parse($end_date)->format('d-m-Y'),   
                'allStatements' => $allStatements ,
                'sumInvoice' => $sumInvoice, 
                'sumPayment' => $sumPayment, 
                'balance' => $balance        
                ]);
            }
        
         // Calculate balance dynamically for each row
        foreach ($allStatements as $statement) {
            if ($statement->type == 'invoice' || $statement->type == 'debit') {
                $statement->balance = $balance += $statement->amount; 
                $sumInvoice += $statement->amount;
            } elseif ($statement->type == 'payment' || $statement->type == 'refund' || $statement->type == 'credit') {
                $statement->balance = $balance -= $statement->amount; 
                $sumPayment += $statement->amount;
            }
        }

        return view('statement.index', compact('school', 'start_date', 'end_date','allStatements','sumPayment','sumInvoice','user'));
    }

    public function showPdf(Request $request)
    {
        $user = auth()->user();
        $school = $user->school;
        $studentId = null;

        if (request()->has('start_date') && request()->has('end_date')) {
        $start_date = Carbon::createFromFormat('d-m-Y', request()->get('start_date'))->startOfDay();
        $end_date = Carbon::createFromFormat('d-m-Y', request()->get('end_date'))->endOfDay();
        }


        $student_name = request()->get('student_name');

        $studentDetails = DB::table('students')
        ->Join('users as user', 'students.user_id', '=', 'user.id')
        ->Join('users as guardian', 'students.guardian_id', '=', 'guardian.id')
        ->Join('class_sections','students.class_section_id','=','class_sections.id')
        ->Join('classes','class_sections.class_id','=','classes.id')
        ->select(  
            DB::raw('CONCAT(COALESCE(user.first_name, ""), " ", COALESCE(user.last_name, "")) as full_name'), 
                    DB::raw('CONCAT(COALESCE(guardian.first_name, ""), " ", COALESCE(guardian.last_name, "")) as guardian_name'),
                    'classes.name as class_name',
                    'students.id as student_id'
        )
        ->where('students.school_id', '=', auth()->user()->school_id)
        ->whereNull('students.deleted_at') 
        ->groupBy('full_name')
        ->having('full_name', '=', $student_name) 
        ->get();

        if ($studentDetails->isNotEmpty()) {
            $studentId = $studentDetails->first()->student_id;
        }
       

        $invoiceQuery = DB::table('student_fees')
        ->Join('students','student_fees.student_id','=','students.id')
        ->Join('student_fees_details','student_fees.id','=','student_fees_details.student_fees_id')
        ->Join('users','students.user_id','=','users.id')
        ->select(
            DB::raw('DATE(student_fees.invoice_date) as note_date'), 
            DB::raw('SUM(
                ((student_fees_details.fees_type_amount - 
                  (student_fees_details.fees_type_amount *COALESCE(student_fees_details.discount, 0) / 100)) + 
                 (student_fees_details.fees_type_amount * COALESCE(student_fees_details.tax, 0) / 100)) * 
                student_fees_details.quantity
            ) as amount'),
            'student_fees.uid as student_fee_id',
            'student_fees.id',
            'student_fees.due_date',
            'student_fees.early_offer',
            'student_fees.early_offer_amount',
            'student_fees.due_charges',
            'student_fees.due_charges_amount',
            DB::raw('CONCAT(COALESCE(users.first_name, ""), " ", COALESCE(users.last_name, "")) as full_name'),
            DB::raw('"invoice" as type')
        )
        ->groupBy(DB::raw('DATE_FORMAT(student_fees.invoice_date, "%Y-%m-%d %H:%i:%s")'), 'student_fees_details.student_fees_id')  
        ->where('student_fees.school_id', '=', auth()->user()->school_id)
        ->whereNull('student_fees.deleted_at') 
        ->whereBetween('student_fees.invoice_date', [$start_date, $end_date]);
        if ($studentId) {
            $invoiceQuery->where('student_fees.student_id', '=', $studentId);
        }
        $invoice = $invoiceQuery->get();

        $paymentQuery = DB::table('student_fees')
        ->Join('students','student_fees.student_id','=','students.id')
        ->Join('student_fees_paids','student_fees.id','=','student_fees_paids.student_fees_id')
        ->Join('users','students.user_id','=','users.id')
        ->select(
            'student_fees_paids.date as note_date', 
                'student_fees_paids.amount',
                'student_fees_paids.student_fees_id as student_fee_id',
                'student_fees_paids.id',
                'student_fees.due_date',
                'student_fees.uid as student_fees_id',
                DB::raw('CONCAT(COALESCE(users.first_name, ""), " ", COALESCE(users.last_name, "")) as full_name'),
                DB::raw('"payment" as type')
            )
        ->where('student_fees.school_id', '=', auth()->user()->school_id)
        ->whereBetween('student_fees_paids.date', [$start_date, $end_date])
        ->where('student_fees_paids.is_fully_paid', '=', '1');
        if ($studentId) {
            $paymentQuery->where('student_fees.student_id', '=', $studentId);
        }
        $payment = $paymentQuery->get();


        $creditQuery = DB::table('students')
        ->Join('users','students.user_id','=','users.id')
        ->Join('credit_note', 'students.id', '=', 'credit_note.student_id')
        ->Join('credit_note_details', 'credit_note.id', '=', 'credit_note_details.credit_note_id')
        ->select(
            'credit_note.date as note_date',
            'credit_note_details.credit_note_amount as amount',
            'credit_note.student_fee_id as student_fee_id',
            'credit_note.id',
            'credit_note.uid',
            DB::raw('CONCAT(COALESCE(users.first_name, ""), " ", COALESCE(users.last_name, "")) as full_name'),
            DB::raw('"credit" as type')
        )
        ->where('students.school_id', '=', auth()->user()->school_id)
        ->whereNull('credit_note.deleted_at')
        ->whereBetween('credit_note.date', [$start_date, $end_date]);
        if ($studentId) {
            $creditQuery->where('students.id', '=', $studentId);
        }
        $creditStatements = $creditQuery->get();


        $debitQuery = DB::table('students')
        ->Join('users','students.user_id','=','users.id')
        ->Join('debit_note', 'students.id', '=', 'debit_note.student_id')
        ->Join('debit_note_details', 'debit_note.id', '=', 'debit_note_details.debit_note_id')
        ->select(
          'debit_note.date as note_date',
            'debit_note_details.debit_note_amount as amount',
            'debit_note.student_fee_id as student_fee_id',
            'debit_note.id',
            'debit_note.uid',
            DB::raw('CONCAT(COALESCE(users.first_name, ""), " ", COALESCE(users.last_name, "")) as full_name'),
            DB::raw('"debit" as type')
        )
        ->where('students.school_id', '=', auth()->user()->school_id)
        ->whereNull('debit_note.deleted_at')
        ->whereBetween('debit_note.date', [$start_date, $end_date]);
        if ($studentId) {
            $debitQuery->where('students.id', '=', $studentId);
        }
        $debitStatements = $debitQuery->get();

        $refundQuery =  DB::table('students as s')
        ->join('users as u', 's.user_id', '=', 'u.id')
        ->join('refund_note as rn', 's.id', '=', 'rn.student_id')
        ->join('refund_note_details as rnd', 'rn.id', '=', 'rnd.refund_note_id')
        ->join('student_fees as sf', 'sf.id', '=', 'rn.student_fee_id')
        ->select(
            'rn.date as note_date',
            DB::raw('(rnd.refund_note_amount * rnd.tax / 100 + rnd.refund_note_amount) * rnd.quantity as amount'),
            'rn.student_fee_id',
            'rn.id',
            'sf.uid as student_fees_id',
            'rn.uid',
            DB::raw('CONCAT(COALESCE(u.first_name, ""), " ", COALESCE(u.last_name, "")) as full_name'),
            DB::raw('"refund" as type')
        )
        ->where('s.school_id', '=', auth()->user()->school_id)
        ->whereNull('rn.deleted_at')
         ->whereBetween('rn.date', [$start_date, $end_date]);
         if ($studentId) {
            $refundQuery->where('s.id', '=', $studentId);
        }
        $refundStatements = $refundQuery->get();


        // Merge and sort all statements
        $allStatements = $creditStatements->merge($debitStatements)
            ->merge($refundStatements)
            ->merge($invoice)
            ->merge($payment)
            ->sortBy('note_date'); 

        // Initialize sums (calculate balance)
        $balance = 0.00;
        $sumInvoice = 0.00;
        $sumPayment = 0.00;

        foreach($allStatements as $statement){
            if (in_array($statement->type, ['invoice', 'debit'])) {
                $balance += $statement->amount;
                $sumInvoice += $statement->amount;
            } elseif (in_array($statement->type, ['payment', 'refund', 'credit'])) {
                $balance -= $statement->amount;
                $sumPayment += $statement->amount;
            }
        }
        
        // Generate PDF from Blade view
        $pdf = Pdf::loadView('statement.statement_receipt', compact(
            'school', 
            'start_date', 
            'end_date', 
            'allStatements',
            'studentDetails',
            'balance',
            'sumInvoice',
            'sumPayment'
        ));

        // For debugging, return the view instead of generating the PDF
        return $pdf->stream('statement-receipt.pdf');
        }
}