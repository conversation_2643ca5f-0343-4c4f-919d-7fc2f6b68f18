<?php

namespace App\Http\Controllers;

use Throwable;
use App\Models\SelfBilling;
use Illuminate\Http\Request;
use App\Services\CachingService;
use App\Services\ResponseService;
use Illuminate\Support\Facades\DB;
use App\Models\SelfBillingSupplier;
use Illuminate\Support\Facades\Auth;
use App\Services\BootstrapTableService;

class SupplierController extends Controller
{
    private CachingService $cache;

    public function __construct(CachingService $cachingService){
        $this->cache = $cachingService;
    }
    public function index(){
        return view('self-billing.supplier.index');
    }

    //tin,icno,businessregno
    public function store(Request $request){
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:self_billing_supplier,name',
            'id_type'=>'required',
            'id_type_no'=>'required',
            'tin_number' => 'required',
            'msic_code' => 'required',
            'sst_registration_num' => 'required|string|max:50',
            'tourism_tax_num' => 'required|string|max:50',
            'email' => 'nullable|email|max:200',
            'phone_number' => 'required|string|max:20',
            'addressLine1' => 'required|string|max:255',
            'addressLine2' => 'nullable|string|max:255',
            'addressLine3' => 'nullable|string|max:255',
            'postcode' => 'nullable|string|max:20',
            'city' => 'required|string|max:100',
            'country' => 'required',
            'state' => 'required',
        ]);
        if($validated['id_type'] == 'NRIC'){
            $regex = '/^[0-9]{6}[0-9]{2}[0-9]{4}$/';
            if (!preg_match($regex, $validated['id_type_no'])) {
                ResponseService::errorResponse("Invalid IC Number format. Use format: XXXXXXYYZZZZ", null, null);
            }
        }
        if($validated['tin_number']){
            $isTINError = false;
            if(strlen($validated['tin_number']) >= 11 && strlen($validated['tin_number']) <= 13){
            }
            else{
                $isTINError = true;
            }
            if($isTINError) {            
                ResponseService::errorResponse("Invalid TIN format.\nIndividual TIN: IGXXXXXXXXX\n11 to 13 characters", null, null);
            }
        }

        try {
            $supplier = SelfBillingSupplier::create($validated);
            if($supplier) {
                ResponseService::successResponse('Data Stored Successfully');
            }

        } catch (\Throwable $th) {
            ResponseService::logErrorResponse($th, "Guardian Controller -> Update method");
            ResponseService::errorResponse();
        }
    }

    public function show(Request $request)
    {
        $offset = request('offset', 0);
        $limit = request('limit', 10);
        $sort = request('sort', 'id');
        $order = request('order', 'ASC');
        $showDeleted = request('show_deleted');
        try {
            $sql = SelfBillingSupplier::query();
            if (!empty($showDeleted)) {
                $sql->onlyTrashed();
            } 

            $total = $sql->count(); 
            $sql->orderBy($sort, $order)->skip($offset)->take($limit);
            $res = $sql->get();
            $bulkData = [];
            $bulkData['total'] = $total;

            $rows = [];
            foreach ($res as $key => $data) {
                $operate = '';
                $temp = $data->toArray();
                $temp['no'] = $key + 1;
                if(!empty($showDeleted)){
                    $operate .= BootstrapTableService::restoreButton(route('supplier.restore', $data->id));
                    $operate .= BootstrapTableService::trashButton(route('supplier.trash', $data->id));
                } else{
                    $operate .= BootstrapTableService::editButton(route('supplier.update', $data->id));
                    $operate .= BootstrapTableService::deleteButton(route('supplier.destroy', $data->id));
                    if($data->status != 1){
                        $operate .= BootstrapTableService::button('fa fa-solid fa-file-lines', route('supplier.validate-einvoice', $data->id), ['btn-gradient-warning','validate-einvoice'], ['title' => __('Validate E-invoice')]);
                    }
                }

                $temp['operate'] = $operate;
                array_push($rows, $temp);
            }
            $bulkData['rows'] = $rows;

            return response()->json($bulkData);

        } catch (\Throwable $th) {
            ResponseService::logErrorResponse($th, "Supplier Controller -> Show method");
            return ResponseService::errorResponse();
        }
    }

    public function update(Request $request,$id){
        $validated = $request->validate([
            'name' => 'required',
            'id_type'=>'required',
            'id_type_no'=>'required',
            'tin_number' => 'required',
            'msic_code' => 'required',
            'sst_registration_num' => 'required|string|max:50',
            'tourism_tax_num' => 'required|string|max:50',
            'email' => 'nullable|email|max:200',
            'phone_number' => 'required|string|max:20',
            'addressLine1' => 'required|string|max:255',
            'addressLine2' => 'nullable|string|max:255',
            'addressLine3' => 'nullable|string|max:255',
            'postcode' => 'nullable|string|max:20',
            'city' => 'required|string|max:100',
            'country' => 'required',
            'state' => 'required',
        ]);
        if($validated['id_type'] == 'NRIC'){
            $regex = '/^[0-9]{6}[0-9]{2}[0-9]{4}$/';
            if (!preg_match($regex, $validated['id_type_no'])) {
                ResponseService::errorResponse("Invalid IC Number format. Use format: XXXXXXYYZZZZ", null, null);
            }
        }
        if($validated['tin_number']){
            $isTINError = false;
            if(strlen($validated['tin_number']) >= 11 && strlen($validated['tin_number']) <= 13){
            }
            else{
                $isTINError = true;
            }
            if($isTINError) {            
                ResponseService::errorResponse("Invalid TIN format.\nIndividual TIN: IGXXXXXXXXX\n11 to 13 characters", null, null);
            }
        }

        //Check id_type_no if changed
        $checkSupplier = SelfBillingSupplier::find($id);
        if($checkSupplier->status == 1 && !isset($request->confirmed)){
            if (trim($checkSupplier->id_type_no) !== trim($validated['id_type_no']) || trim($checkSupplier->tin_number !== trim($validated['tin_number']))) {
                $selfBilling = SelfBilling::where('supplier_id',$id)->exists();
                // if($selfBilling){
                //     ResponseService::errorResponse("Supplier associated with self-billing cannot be changed");
                // }
                return response()->json([
                    'code' => 422,
                    'message' => 'Changing the business registration number, IC number, or TIN number will require revalidating the e-invoice. Are you sure you want to proceed?'
                ]);
            }
        } 

        try {
            $data = [
                'name' => $validated['name'],
                'id_type' => $validated['id_type'],
                'id_type_no' => $validated['id_type_no'],
                'tin_number' => $validated['tin_number'],
                'msic_code' => $validated['msic_code'],
                'sst_registration_num' => $validated['sst_registration_num'],
                'tourism_tax_num' => $validated['tourism_tax_num'],
                'email' => $validated['email'],
                'phone_number' => $validated['phone_number'],
                'addressLine1' => $validated['addressLine1'],
                'addressLine2' => $validated['addressLine2'],
                'addressLine3' => $validated['addressLine3'],
                'postcode' => $validated['postcode'],
                'city' => $validated['city'],
                'country' => $validated['country'],
                'state' => $validated['state']
            ];

            //Change status to re-validate
            if(isset($request->confirmed)){
                $data['status'] = 0;
            }

            $supplier = SelfBillingSupplier::where('id', $id)->update($data);
            if($supplier) {
                return ResponseService::successResponse('Data Updated Successfully');
            }
        } catch (\Throwable $th) {
            ResponseService::logErrorResponse($th, "Supplier Controller -> Update method");
            return ResponseService::errorResponse();
        }
    }
    
    public function destroy($id) {
        try {
            DB::beginTransaction();
            $supplier = SelfBillingSupplier::where('id',$id)->update(['deleted_at' => now()]);
            DB::commit();
            ResponseService::successResponse("Data Deleted Successfully");
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "Supplier Controller -> Update method");
            ResponseService::errorResponse();
        }
    }

    public function restore($id){
        try {
            DB::beginTransaction();
            $supplier = SelfBillingSupplier::withTrashed()->where('id',$id)->update(['deleted_at' => null]);
            DB::commit();
            ResponseService::successResponse("Data Restored Successfully");
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "Supplier Controller -> Update method");
            ResponseService::errorResponse();
        }
    }

    public function trash($id) {
        try {
            DB::beginTransaction();
            $supplier = SelfBillingSupplier::withTrashed()->where('id', $id)->forceDelete();
            DB::commit();
            ResponseService::successResponse("Data Deleted Permanently");
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "Supplier Controller -> Update method");
            ResponseService::errorResponse();
        }
    }

    public function validateSupplierEInvoice($id) {
        try {
            $schoolEInvoice = DB::table('e_invoice')
                                ->where('school_id', Auth::user()->school_id)
                                ->first();
                                
            // Check if school invoice is not validated
            if (!$schoolEInvoice || $schoolEInvoice->status != 1) {
                ResponseService::errorResponse("School's TIN number not validated");
                return;
            }

            $supplier = SelfBillingSupplier::where('id',$id)->first();
    
            $accessToken = null;
    
            // Validate Guardian e-invoice address 
            if ($schoolEInvoice && $supplier) {       
            
                if (!$supplier || empty($supplier->tin_number)) {
                    ResponseService::errorResponse("No TIN number found");
                    return;
                }
    
                $schoolSettings = $this->cache->getSchoolSettings();

                if ($schoolSettings) {
                    $accessToken = '';
                    $clientId = $schoolSettings['client_id'] ?? '';
                    $client_secret = array_filter([$schoolSettings['client_secret_1'] ?? null, $schoolSettings['client_secret_2'] ?? null]);
                    if (!empty($clientId) && count($client_secret) > 0) {
                        $accessTokenExist = DB::table('self_billing_access_token')->where('supplier_id', $supplier->id)->orderBy('id', 'desc')->first();
                        if ($accessTokenExist && $accessTokenExist->time_expired > now()) {
                            $accessToken = $accessTokenExist->access_token;
                        } else {
                            foreach ($client_secret as $secret) {
                                $url = 'https://api.myinvois.hasil.gov.my/connect/token';
                                $headers = ['onbehalfof:' . $supplier->tin_number];
                                $fields = [
                                    'client_id' => $clientId,
                                    'client_secret' => $secret,
                                    'grant_type' => 'client_credentials',
                                    'scope' => 'InvoicingAPI',
                                ];
                                $encodedFields = http_build_query($fields);
                                $ch = curl_init();
                                curl_setopt($ch, CURLOPT_URL, $url);
                                curl_setopt($ch, CURLOPT_POST, true);
                                curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
                                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                                curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
                                curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
                                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                                curl_setopt($ch, CURLOPT_POSTFIELDS, $encodedFields);
                                $result = curl_exec($ch);
                                $httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                                
                                if ($result === false) {
                                    $error = curl_error($ch);
                                    ResponseService::errorResponse("Unable to connect to validate TIN number");
                                    continue;
                                }

                                $data = json_decode($result);
                                if (json_last_error() !== JSON_ERROR_NONE) {
                                    ResponseService::errorResponse("Invalid response format from server");
                                    continue;
                                }

                                if (isset($data->error)) {
                                    ResponseService::errorResponse($data->error);
                                    continue;
                                }

                                if (!empty($data->access_token)) {
                                    $accessToken = $data->access_token;
                                    $expiresIn = isset($data->expires_in) ? now()->addSeconds($data->expires_in) : now()->addHours(1);
                                    
                                    DB::table('self_billing_access_token')->insert([
                                        'access_token' => $accessToken,
                                        'supplier_id' => $supplier->id,
                                        'time_expired' => $expiresIn
                                    ]);
                                    break;
                                }

                                continue;
                                curl_close($ch);
                            }
                        }
    
                        if ($accessToken) {
                            if (!empty($supplier->tin_number) && !empty($supplier->id_type_no)) {
                                $idType = $supplier->id_type == 'NRIC' ? 'NRIC' : 'BRN';
                                $url = 'https://api.myinvois.hasil.gov.my/api/v1.0/taxpayer/validate/' . $supplier->tin_number . '?idType=' . $idType . '&idValue=' . $supplier->id_type_no;
                                $headers = [
                                    'authorization: Bearer ' . $accessToken,
                                    'Content-Type: application/json',
                                    'Accept: application/json',
                                    'Accept-Language: en'
                                ];
                                $ch = curl_init();
                                curl_setopt($ch, CURLOPT_URL, $url);
                                curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
                                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                                curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
                                curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
                                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                                $result = curl_exec($ch);
                                $httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                                curl_close($ch);
                                if ($httpcode == 200) {
                                    SelfBillingSupplier::where('id',$id)->update(['status' => 1]);
                                    ResponseService::successResponse("TIN number has been validated");
                                } else {
                                    ResponseService::errorResponse("Unable to validate TIN number");
                                }
                            }
                        }
                    }
                }
            } else {
                // If the conditions for address and other fields are not met, return null
                return null;
            }
        } catch (\Throwable $e) {
            DB::rollBack();
            ResponseService::errorResponse("Error Occured");
        }
    }
}
