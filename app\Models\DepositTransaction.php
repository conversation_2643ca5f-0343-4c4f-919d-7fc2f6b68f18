<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class DepositTransaction extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'student_deposit_id',
        'transaction_type',
        'amount',
        'remark',
        'created_by',
        'updated_by'
    ];

    protected $casts = [
        'amount' => 'decimal:2'
    ];

    public function studentDeposit()
    {
        return $this->belongsTo(StudentDeposit::class);
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function getTransactionTypeLabel()
    {
        return ucfirst($this->transaction_type);
    }

    public function scopePayments($query)
    {
        return $query->where('transaction_type', 'payment');
    }

    public function scopeRefunds($query)
    {
        return $query->whereIn('transaction_type', ['refund', 'return']);
    }
}