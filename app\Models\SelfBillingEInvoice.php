<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

class SelfBillingEInvoice extends Model
{
    use HasFactory,SoftDeletes;
    protected $table = 'self_billing_einvoice';
    
    protected $fillable = [
        'self_billing_id',
        'signature_value',
        'submission_uid',
        'long_id',
        'internal_id',
        'uuid',
        'invoice_code_number',
        'rejected_documents',
        'document_summary',
        'status',
        'reject_reason',
        'cancel_reason',
        'created_at',
        'updated_at',
        'deleted_at',
    ];
}
?>