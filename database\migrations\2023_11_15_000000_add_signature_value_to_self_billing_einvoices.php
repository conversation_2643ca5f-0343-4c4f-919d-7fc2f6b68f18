<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddSignatureValueToSelfBillingEinvoices extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('self_billing_einvoice', function (Blueprint $table) {
            $table->text('signature_value')->nullable()->after('self_billing_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('self_billing_einvoice', function (Blueprint $table) {
            $table->dropColumn('signature_value');
        });
    }
}