<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class StudentDeposit extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'student_id',
        'deposit_type_id',
        'amount',
        'status',
        'remark',
        'created_by',
        'updated_by'
    ];

    protected $casts = [
        'amount' => 'decimal:2'
    ];

    public function student()
    {
        return $this->belongsTo(Students::class);
    }

    public function depositType()
    {
        return $this->belongsTo(DepositType::class);
    }

    public function transactions()
    {
        return $this->hasMany(DepositTransaction::class);
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    protected $appends = ['remaining_amount'];

    public function getRemainingAmountAttribute()
    {
        if ($this->relationLoaded('transactions')) {
            $refunds = $this->transactions
                ->whereIn('transaction_type', ['refund', 'return'])
                ->sum('amount');
            return $this->amount - $refunds;
        }

        return $this->transactions()
            ->whereIn('transaction_type', ['refund', 'return'])
            ->sum('amount');
    }

    public function canBeRefunded()
    {
        return $this->status === 'active' && $this->getRemainingAmount() > 0;
    }

    public function processRefund($amount, $remark, $userId)
    {
        if (!$this->canBeRefunded() || $amount > $this->getRemainingAmount()) {
            return false;
        }

        $this->transactions()->create([
            'transaction_type' => 'refund',
            'amount' => $amount,
            'remark' => $remark,
            'created_by' => $userId
        ]);

        if ($amount >= $this->getRemainingAmount()) {
            $this->update(['status' => 'refunded']);
        }

        return true;
    }
}