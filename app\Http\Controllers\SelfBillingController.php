<?php

namespace App\Http\Controllers;

use PDO;
use DOMXPath;
use DOMDocument;
use Carbon\Carbon;
use App\Models\SelfBilling;
use Illuminate\Http\Request;
use chillerlan\QRCode\QRCode;
use GuzzleHttp\Psr7\Response;
use App\Helpers\EInvoiceHelper;
use Barryvdh\DomPDF\Facade\Pdf;
use App\Services\CachingService;
use App\Services\ResponseService;
use App\Models\SelfBillingDetails;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\DB;
use App\Models\SelfBillingEInvoice;
use App\Models\SelfBillingShipping;
use App\Models\SelfBillingSupplier;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use App\Models\SelfBillingImportInfo;
use App\Models\SelfBillingTaxDetails;
use App\Models\SelfBillingBillingInfo;
use App\Services\BootstrapTableService;
use Illuminate\Support\Facades\Validator;
use App\Services\SelfBillingFormatService;
use Klsheng\Myinvois\Helper\MyInvoisHelper;
use App\Repositories\SessionYear\SessionYearInterface;

class SelfBillingController extends Controller
{
    private CachingService $cache;
    private SessionYearInterface $sessionYear;
    private SelfBillingFormatService $selfBillingFormat;

    public function __construct(CachingService $cachingService, SessionYearInterface $sessionYear,SelfBillingFormatService $selfBillingFormat)
    {
        $this->cache = $cachingService;
        $this->sessionYear = $sessionYear;
        $this->selfBillingFormat = $selfBillingFormat;
    }
    public function index(){
        ResponseService::noFeatureThenRedirect('E-Invoicing');
        ResponseService::noAnyRoleThenRedirect(['School Admin']);
        $supplier = SelfBillingSupplier::where('status',1)->get();
        $sessionYears = $this->sessionYear->all();
        $selfBillingReference = SelfBilling::where('type','=','INV')
            ->select('id', 'uid', 'type')
            ->get()
            ->map(function($item) {
                $item->invoice_no = $this->invoiceNoGenerator($item->type, $item->uid);
                return $item;
            });
        
        return view('self-billing.index', compact('supplier','sessionYears', 'selfBillingReference'));
    }

    public function store(Request $request){
        ResponseService::noFeatureThenRedirect('E-Invoicing');
        ResponseService::noAnyRoleThenRedirect(['School Admin']);
        $validator = Validator::make($request->all(), [
            'supplier' => 'required',
            'issue_date_time' => 'required',
            'self_billing_type' => 'required',
            'line_items' => 'required|json'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }
        // dd($request->all());

        $lineItems = json_decode($request->line_items, true);
        $errors = [];

        if($request->shipping_id_type_no != null && $request->shipping_id_type == null){
            $errors['shipping_id_type'] = ['The shipping id type field is required.'];
        }

        if($request->shipping_id_type == 'NRIC'){
            $regex = '/^[0-9]{6}[0-9]{2}[0-9]{4}$/';
            if (!preg_match($regex, $request->shipping_id_type_no)) {
                $errors['shipping_id_type_no'] = ['Invalid IC Number format. Use format: XXXXXXYYZZZZ'];
            }
        }

        if($request->shipping_tin_number){
            if(!(strlen($request->shipping_tin_number) >= 11 && strlen($request->shipping_tin_number) <= 13)){
                $errors['shipping_tin_number'] = ["Invalid TIN format.\nIndividual TIN: IGXXXXXXXXX\n11 to 13 characters"];
            }
        }

        foreach($lineItems as $key => $lineItem){
            if(isset($lineItem['tax_exempted']) && $lineItem['tax_exempted'] > 0){
                if($lineItem['tax_exempted_details'] == null || $lineItem['tax_exempted_details'] == ''){
                    $errors['line_items.'.$key.'.tax_exempted_details'] = ['The tax exempted details field is required.'];
                }
            }
        }

        if($request->self_billing_type != 'INV' && $request->invoice_reference == null){
            $errors['invoice_reference'] = ['The invoice reference field is required.'];
        }

        if (!empty($errors)) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $errors
            ], 422);
        }

        try {
            DB::beginTransaction();
            $sessionYear = $this->cache->getDefaultSessionYear();
            // Create main self-billing record
            $selfBilling = new SelfBilling();
            $selfBilling->supplier_id = $request->supplier;
            $selfBilling->issue_date_time = $request->issue_date_time;
            $selfBilling->type = $request->self_billing_type;
            $selfBilling->session_year_id = $sessionYear->id;
            $selfBilling->uid = $this->getUID(Auth::user()->school_id, $request->self_billing_type);
            $selfBilling->total_tax_amount       = $request->total_tax_amount_summary;
            $selfBilling->total_net_amount       = $request->total_net_amount_summary;
            $selfBilling->invoice_discount_value = $request->invoice_discount_value;
            $selfBilling->invoice_discount_description = $request->invoice_discount_description;
            $selfBilling->invoice_charge_value   = $request->invoice_charge_value;
            $selfBilling->invoice_charge_description = $request->invoice_charge_description;
            $selfBilling->total_excluding_tax    = $request->total_excluding_tax_summary;
            $selfBilling->total_including_tax    = $request->total_including_tax_summary;
            $selfBilling->total_rounding_amount  = $request->total_rounding_amount;
            $selfBilling->total_payable_amount   = $request->total_payable_amount;

            if(!empty($request->invoice_reference)){
                $selfBilling->self_billing_reference = $request->invoice_reference;
            }

            $selfBilling->save();

            // Process line items
            foreach ($lineItems as $item) {
                //Get Country Of Origin Code
                if(isset($item['country_of_origin'])){
                    $countryOfOrigin = array_map('trim', explode("-", $item['country_of_origin']));
                    $countryCode = $countryOfOrigin[0];
                }
                // Create line item record
                $lineItem = new SelfBillingDetails();
                $lineItem->self_billing_id = $selfBilling->id;
                $lineItem->classification_code = $item['classification_code'];
                $lineItem->description = $item['description'];
                $lineItem->tariff_code = $item['tariff_code'] ?? null;
                $lineItem->unit_price = $item['unit_price'];
                $lineItem->quantity = $item['quantity'] ?? 1;
                $lineItem->measurement = $item['measurement'] ?? null;
                $lineItem->total_amount = $item['total_amount'];
                $lineItem->discount_rate = $item['discount_rate'] ?? null;
                $lineItem->total_discount_amount = $item['total_discount_amount'] ?? 0;
                $lineItem->discount_description = $item['discount_description'] ?? null;
                $lineItem->charge_rate = $item['charge_rate'] ?? null;
                $lineItem->total_charge_rate = $item['total_charge_rate'] ?? 0;
                $lineItem->charge_description = $item['charge_description'] ?? null;
                $lineItem->total_excluding_tax = $item['total_excluding_tax'];
                $lineItem->country_of_origin = $countryCode ?? null;
                $lineItem->tax_exempted = $item['tax_exempted'] ?? 0;
                $lineItem->total_exempted = $item['total_exempted'] ?? 0;
                $lineItem->tax_exempted_details = $item['tax_exempted_details'] ?? null;
                $lineItem->save();

                // Process tax details for this line item
                if (isset($item['tax_details']) && is_array($item['tax_details'])) {
                    foreach ($item['tax_details'] as $taxDetail) {
                        if (!empty($taxDetail['tax_type'])) {
                            $tax = new SelfBillingTaxDetails();
                            $tax->self_billing_details_id = $lineItem->id;
                            $tax->tax_type = $taxDetail['tax_type'];
                            $tax->tax_percentage = $taxDetail['tax_percentage'] ?? null;
                            $tax->tax_amount = $taxDetail['total_tax'] ?? 0;
                            $tax->save();
                        }
                    }
                }
            }

            // Process billing information if provided
            if ($request->filled('frequency_billing') || $request->filled('billing_start_date') || $request->filled('billing_end_date') ||
                $request->filled('payment_mode') || $request->filled('supplier_bank_acc') || $request->filled('payment_terms') ||
                $request->filled('prepayment_amount') || $request->filled('prepayment_datetime') || $request->filled('prepayment_reference_num') ||
                $request->filled('bill_reference_num')) {
                
                $billingInfo = new SelfBillingBillingInfo();
                $billingInfo->self_billing_id = $selfBilling->id;
                $billingInfo->frequency_billing = $request->frequency_billing;
                $billingInfo->billing_start_date = $request->billing_start_date;
                $billingInfo->billing_end_date = $request->billing_end_date;
                $billingInfo->payment_mode = $request->payment_mode;
                $billingInfo->supplier_bank_acc = $request->supplier_bank_acc;
                $billingInfo->payment_terms = $request->payment_terms;
                $billingInfo->prepayment_amount = $request->prepayment_amount;
                $billingInfo->prepayment_datetime = $request->prepayment_datetime;
                $billingInfo->prepayment_reference_num = $request->prepayment_reference_num;
                $billingInfo->bill_reference_num = $request->bill_reference_num;
                $billingInfo->save();
            }

            // Process shipping information if provided
            if ($request->filled('shipping_recipient_name') || $request->filled('shipping_address_line_1') || $request->filled('shipping_address_line_2') || 
                $request->filled('shipping_postal_code') || $request->filled('shipping_address_line_3') || $request->filled('shipping_city') || 
                $request->filled('shipping_country') || $request->filled('shipping_state') || $request->filled('shipping_id_type') || 
                $request->filled('shipping_id_type_no') || $request->filled('shipping_tin_number')) {
                
                $shippingInfo = new SelfBillingShipping();
                $shippingInfo->self_billing_id = $selfBilling->id;
                $shippingInfo->recipient_name = $request->shipping_recipient_name;
                $shippingInfo->address_line1 = $request->shipping_address_line_1;
                $shippingInfo->address_line2 = $request->shipping_address_line_2;
                $shippingInfo->address_line3 = $request->shipping_address_line_3;
                $shippingInfo->postal_code = $request->shipping_postal_code;
                $shippingInfo->city = $request->shipping_city;
                $shippingInfo->country = $request->shipping_country;
                $shippingInfo->state = $request->shipping_state;
                $shippingInfo->id_type = $request->shipping_id_type;
                $shippingInfo->id_type_no = $request->shipping_id_type_no;
                $shippingInfo->tin_number = $request->shipping_tin_number;
                $shippingInfo->save();
            }

            // Process import/export information if provided
            if ($request->filled('reference_num_form1_9') || $request->filled('incoterms') || $request->filled('fta_information') ||
                $request->filled('authorization_number') || $request->filled('reference_num_form2') || $request->filled('details_other_charges') ||
                $request->filled('details_other_charges_description')) {
                
                $importInfo = new SelfBillingImportInfo();
                $importInfo->self_billing_id = $selfBilling->id;
                $importInfo->reference_num_form1_9 = $request->reference_num_form1_9;
                $importInfo->incoterms = $request->incoterms;
                $importInfo->fta_information = $request->fta_information;
                $importInfo->authorization_number = $request->authorization_number;
                $importInfo->reference_num_form2 = $request->reference_num_form2;
                $importInfo->details_other_charges = $request->details_other_charges;
                $importInfo->details_other_charges_description = $request->details_other_charges_description;
                $importInfo->save();
            }

            // Commit transaction
            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Self Billing Created Successfully',
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Self-billing creation error: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while creating the self-billing: ' . $e->getMessage()
            ], 500);
        }
    }

    public function getUID($schoolId, $selfBillinType){
        $uid = null;
        $isUnique = false;
        
        while (!$isUnique) {
            $latestUID = DB::table('self_billing')
                        ->where('school_id', $schoolId)
                        ->where('type', $selfBillinType)
                        ->orderBy('uid', 'desc')
                        ->value('uid');

            $uid = $latestUID ? $latestUID + 1 : 1;
            
            // Check if this UID already exists (in case of race conditions)
            $exists = DB::table('self_billing')
                    ->where('school_id', $schoolId)
                    ->where('type', $selfBillinType)
                    ->where('uid', $uid)
                    ->exists();
            
            $isUnique = !$exists;
        }
        
        return $uid;
    }

    public function show(){
        ResponseService::noFeatureThenRedirect('E-Invoicing');
        ResponseService::noAnyRoleThenRedirect(['School Admin']);
        $offset = request('offset', 0);
        $limit = request('limit', 10);
        $sort = request('sort', 'id');
        $order = request('order', 'ASC');
        $showDeleted = request('show_deleted');
        $sessionYearId = request('session_year_id');
        $selfBillingType = request('self_billing_type');
        $search = request('search');

        if($sort == 'invoice_no'){
            $sort = 'uid';
        }

        try {
            $sql = SelfBilling::with(['details', 'details.taxDetails','importInfo', 'billingInfo', 'shipping', 'supplier']);

            if (!empty($sessionYearId)) {
                $sql->where('session_year_id', $sessionYearId);
            }

            if (!empty($selfBillingType)) {
                $sql->where('type', $selfBillingType);
            }

            if (!empty($search)) {
                $sql->where(function($query) use ($search, $selfBillingType) {
                    $query->whereRaw("CONCAT(?, LPAD(uid, 6, '0'), 'SB') LIKE ?", [$selfBillingType, "%{$search}%"])
                    ->orWhereHas('supplier', function($q) use ($search) {
                              $q->where('name', 'LIKE', "%{$search}%");
                          });
                });
            }

            if (!empty($showDeleted)) {
                $sql->onlyTrashed();
            } 
            $total = $sql->count();

            $sql->orderBy($sort, $order)->skip($offset)->take($limit);
            $res = $sql->get();

            $bulkData = [];
            $bulkData['total'] = $total;
            $rows = [];

            foreach ($res as $key => $data) {
                $operate = '';
                $temp = $data->toArray();
                
                $temp['no'] = $key + 1;
                $temp['invoice_no'] = $this->invoiceNoGenerator($data->type, $data->uid);
                $temp['issue_date_time'] = Carbon::parse($data->issue_date_time)->format('d-m-Y h:iA');
                $temp['total_net_amount']        = number_format($data->total_net_amount, 2);
                $temp['invoice_charge_value']    = number_format($data->invoice_charge_value, 2);
                $temp['invoice_discount_value']  = number_format($data->invoice_discount_value, 2);
                $temp['total_excluding_tax']     = number_format($data->total_excluding_tax, 2);
                $temp['total_tax_amount']        = number_format($data->total_tax_amount, 2);
                $temp['total_including_tax']     = number_format($data->total_including_tax, 2);
                $temp['total_rounding_amount']   = number_format($data->total_rounding_amount, 2);
                $temp['total_payable_amount']   = number_format($data->total_payable_amount, 2);


                
                $operate .= BootstrapTableService::menuButton("Edit",route('self-billing.update', $data->id),["edit-data"],['data-id' => $data->id]);
                $operate .= BootstrapTableService::menuButton('invoice', route('self-billing.invoice', ['id' => $data->id,'type' => $data->full_name_type]), ['generate-invoice-pdf'],['target' => "_blank"]);


                if(isset($data->shipping) && $data->shipping->status != 1){
                    $operate .= BootstrapTableService::menuButton('Validate Shipping', route('self-billing.shipping-validate', $data->shipping->id), ['validate-einvoice']);
                }

                $eInvoiceValid = SelfBillingEInvoice::withTrashed()->where('self_billing_id', $data->id)->orderBy('id','DESC')->first();
                //IF Einvoice is Validated then show cancel e-invoice button
                if(isset($eInvoiceValid)){
                    $temp['submitted_e_invoice'] = $eInvoiceValid->status;
                    if($eInvoiceValid->status == 1){
                        $temp['e_invoice_status'] = true;
                        $operate.= BootstrapTableService::menuButton('cancel e-invoice', route('self-billing.cancel-einvoice',['id' => $data->id]), ['cancel-einvoice']);
                    }
                }

                $temp['operate'] = BootstrapTableService::menuItem($operate);
                array_push($rows, $temp);
            }
            $bulkData['rows'] = $rows;

            return response()->json($bulkData);

        } catch (\Throwable $th) {
            ResponseService::logErrorResponse($th, "Self Billing Controller -> Show method");
            return ResponseService::errorResponse();
        }
    }

    public function invoiceNoGenerator($selfBillingType, $uid){
        
        if ($selfBillingType && $uid) {
            $formattedUid = str_pad($uid, 6, '0', STR_PAD_LEFT);
            return $selfBillingType. $formattedUid . 'SB';
        }
        return null;
    }

    public function update(Request $request, $id){
        ResponseService::noFeatureThenRedirect('E-Invoicing');
        ResponseService::noAnyRoleThenRedirect(['School Admin']);
        $validator = Validator::make($request->all(), [
            // 'supplier' => 'required',
            'issue_date_time' => 'required',
            // 'self_billing_type' => 'required',
            'line_items' => 'required|json'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $lineItems = json_decode($request->line_items, true);
        $errors = [];


        if($request->shipping_id_type_no != null && $request->shipping_id_type == null){
            $errors['shipping_id_type'] = ['The shipping id type field is required.'];
        }

        if($request->shipping_id_type == 'NRIC'){
            $regex = '/^[0-9]{6}[0-9]{2}[0-9]{4}$/';
            if (!preg_match($regex, $request->shipping_id_type_no)) {
                $errors['shipping_id_type_no'] = ['Invalid IC Number format. Use format: XXXXXXYYZZZZ'];
            }
        }        

        if($request->shipping_tin_number){
            if(!(strlen($request->shipping_tin_number) >= 11 && strlen($request->shipping_tin_number) <= 13)){
                $errors['shipping_tin_number'] = ["Invalid TIN format.\nIndividual TIN: IGXXXXXXXXX\n11 to 13 characters"];
            }
        }

        foreach($lineItems as $key => $lineItem){
            if(isset($lineItem['tax_exempted']) && $lineItem['tax_exempted'] > 0){
                if($lineItem['tax_exempted_details'] == null || $lineItem['tax_exempted_details'] == ''){
                    $errors['line_items.'.$key.'.tax_exempted_details'] = ['The tax exempted details field is required.'];
                }
            }
        }

        if (!empty($errors)) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $errors
            ], 422);
        }

        if($request->shipping_id){
            $checkShipping = SelfBillingShipping::find($request->shipping_id);
            if($checkShipping->status == 1 && !isset($request->confirmed)){
                if (trim($checkShipping->id_type_no) !== trim($request->shipping_id_type_no) || trim($checkShipping->tin_number !== trim($request->shipping_tin_number))) {
                    return response()->json([
                        'supplier_validation_error' => true,
                        'message' => 'Changing the business registration number, IC number, or TIN number will require revalidating the e-invoice. Are you sure you want to proceed?'
                    ]);
                }
            } 
        }

        try {
            DB::beginTransaction();
            // Update main self-billing record
            $selfBilling = SelfBilling::findOrFail($id);
            // $selfBilling->supplier_id = $request->supplier;
            $selfBilling->issue_date_time = $request->issue_date_time;
            // $selfBilling->type = $request->self_billing_type;
            $selfBilling->total_tax_amount       = $request->total_tax_amount_summary;
            $selfBilling->total_net_amount       = $request->total_net_amount_summary;
            $selfBilling->invoice_discount_value = $request->invoice_discount_value;
            $selfBilling->invoice_discount_description = $request->invoice_discount_description;
            $selfBilling->invoice_charge_value   = $request->invoice_charge_value;
            $selfBilling->invoice_charge_description = $request->invoice_charge_description;            
            $selfBilling->total_excluding_tax    = $request->total_excluding_tax_summary;
            $selfBilling->total_including_tax    = $request->total_including_tax_summary;
            $selfBilling->total_rounding_amount  = $request->total_rounding_amount;
            $selfBilling->total_payable_amount   = $request->total_payable_amount;
            $selfBilling->save();

            // Process line items
            foreach ($lineItems as $item) {
                //Get Country Of Origin Code
                if(isset($item['country_of_origin'])){
                    $countryOfOrigin = array_map('trim', explode("-", $item['country_of_origin']));
                    $countryCode = $countryOfOrigin[0];
                }

                // Update or Create
                if(isset($item['line_item_id'])){
                    $lineItem = SelfBillingDetails::findOrFail($item['line_item_id']);
                } else {
                    $lineItem = new SelfBillingDetails();
                    $lineItem->self_billing_id = $selfBilling->id;
                }

                $lineItem->classification_code = $item['classification_code'];
                $lineItem->description = $item['description'];
                $lineItem->tariff_code = $item['tariff_code'] ?? null;
                $lineItem->unit_price = $item['unit_price'];
                $lineItem->quantity = $item['quantity'] ?? 1;
                $lineItem->measurement = $item['measurement'] ?? null;
                $lineItem->total_amount = $item['total_amount'];
                $lineItem->discount_rate = $item['discount_rate'] ?? null;
                $lineItem->total_discount_amount = $item['total_discount_amount'] ?? 0;
                $lineItem->discount_description = $item['discount_description'] ?? null;
                $lineItem->charge_rate = $item['charge_rate'] ?? null;
                $lineItem->total_charge_rate = $item['total_charge_rate'] ?? 0;
                $lineItem->charge_description = $item['charge_description'] ?? null;
                $lineItem->total_excluding_tax = $item['total_excluding_tax'];
                $lineItem->country_of_origin = $countryCode ?? null;
                $lineItem->tax_exempted = $item['tax_exempted'] ?? 0;
                $lineItem->total_exempted = $item['total_exempted'] ?? 0;
                $lineItem->tax_exempted_details = $item['tax_exempted_details'] ?? null;
                $lineItem->save();

                // Process tax details for this line item
                if (isset($item['tax_details']) && is_array($item['tax_details'])) {
                    foreach ($item['tax_details'] as $taxDetail) {
                        if (!empty($taxDetail['tax_type'])) {

                            // Update or Create
                            if(isset($taxDetail['tax_id'])){
                                $tax = SelfBillingTaxDetails::findOrFail($taxDetail['tax_id']); 
                            } else {
                                $tax = new SelfBillingTaxDetails();
                                $tax->self_billing_details_id = $lineItem->id;
                            }
                                
                            $tax->tax_type = $taxDetail['tax_type'];
                            $tax->tax_percentage = $taxDetail['tax_percentage'] ?? null;
                            $tax->tax_amount = $taxDetail['total_tax'] ?? 0;
                            $tax->save();
                        }
                    }
                }
            }

            // Process billing information if provided
            if ($request->filled('frequency_billing') || $request->filled('billing_start_date') || $request->filled('billing_end_date') ||
                $request->filled('payment_mode') || $request->filled('supplier_bank_acc') || $request->filled('payment_terms') ||
                $request->filled('prepayment_amount') || $request->filled('prepayment_datetime') || $request->filled('prepayment_reference_num') ||
                $request->filled('bill_reference_num')) {

                // Update or Create
                if(isset($request->billing_id)){
                    $billingInfo = SelfBillingBillingInfo::findOrFail($request->billing_id); 
                } else {
                    $billingInfo = new SelfBillingBillingInfo();
                    $billingInfo->self_billing_id = $selfBilling->id;
                }

                $billingInfo->frequency_billing = $request->frequency_billing;
                $billingInfo->billing_start_date = $request->billing_start_date;
                $billingInfo->billing_end_date = $request->billing_end_date;
                $billingInfo->payment_mode = $request->payment_mode;
                $billingInfo->supplier_bank_acc = $request->supplier_bank_acc;
                $billingInfo->payment_terms = $request->payment_terms;
                $billingInfo->prepayment_amount = $request->prepayment_amount;
                $billingInfo->prepayment_datetime = $request->prepayment_datetime;
                $billingInfo->prepayment_reference_num = $request->prepayment_reference_num;
                $billingInfo->bill_reference_num = $request->bill_reference_num;
                $billingInfo->save();
            }

            // Process shipping information if provided
            if ($request->filled('shipping_recipient_name') || $request->filled('shipping_address_line_1') || $request->filled('shipping_address_line_2') || 
                $request->filled('shipping_postal_code') || $request->filled('shipping_address_line_3') || $request->filled('shipping_city') || 
                $request->filled('shipping_country') || $request->filled('shipping_state') || $request->filled('shipping_id_type') || 
                $request->filled('shipping_id_type_no') || $request->filled('shipping_tin_number')) {
                
                // Update or Create
                if(isset($request->shipping_id)){
                    $shippingInfo = SelfBillingShipping::findOrFail($request->shipping_id);  
                } else {
                    $shippingInfo = new SelfBillingShipping();
                    $shippingInfo->self_billing_id = $selfBilling->id;
                }

                $shippingInfo->recipient_name = $request->shipping_recipient_name;
                $shippingInfo->address_line1 = $request->shipping_address_line_1;
                $shippingInfo->address_line2 = $request->shipping_address_line_2;
                $shippingInfo->address_line3 = $request->shipping_address_line_3;
                $shippingInfo->postal_code = $request->shipping_postal_code;
                $shippingInfo->city = $request->shipping_city;
                $shippingInfo->country = $request->shipping_country;
                $shippingInfo->state = $request->shipping_state;
                $shippingInfo->id_type = $request->shipping_id_type;
                $shippingInfo->id_type_no = $request->shipping_id_type_no;
                $shippingInfo->tin_number = $request->shipping_tin_number;
                if(isset($request->confirmed)){
                    $shippingInfo->status = 0;
                }
                $shippingInfo->save();
            }

            // Process import/export information if provided
            if ($request->filled('reference_num_form1_9') || $request->filled('incoterms') || $request->filled('fta_information') ||
                $request->filled('authorization_number') || $request->filled('reference_num_form2') || $request->filled('details_other_charges') ||
                $request->filled('details_other_charges_description')) {
                
                // Update or Create
                if(isset($request->import_id)){
                    $importInfo = SelfBillingImportInfo::findOrFail($request->import_id);
                } else {
                    $importInfo = new SelfBillingImportInfo();
                    $importInfo->self_billing_id = $selfBilling->id;
                }

                $importInfo->reference_num_form1_9 = $request->reference_num_form1_9;
                $importInfo->incoterms = $request->incoterms;
                $importInfo->fta_information = $request->fta_information;
                $importInfo->authorization_number = $request->authorization_number;
                $importInfo->reference_num_form2 = $request->reference_num_form2;
                $importInfo->details_other_charges = $request->details_other_charges;
                $importInfo->details_other_charges_description = $request->details_other_charges_description;
                $importInfo->save();
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Self Billing Update Successfully',
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Self-billing creation error: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while creating the self-billing: ' . $e->getMessage()
            ], 500);
        }
    }

    public function trash(Request $request){
        ResponseService::noFeatureThenRedirect('E-Invoicing');
        ResponseService::noAnyRoleThenRedirect(['School Admin']);
        try {
            DB::beginTransaction();
            $ids = $request->ids;

            // Ensure $ids is an array
            if (!is_array($ids)) {
                $ids = [$ids];
            }
            
            $undeleteableRecords = [];
            
            foreach ($ids as $id) {
                $selfBilling = SelfBilling::find($id);
                
                if (!$selfBilling) {
                    continue;
                }
                
                // Check if there's an invoice reference
                if ($selfBilling->self_billing_reference) {
                    // Check if the referenced invoice is deleted
                    $referencedInvoice = SelfBilling::withTrashed()
                        ->where('id', $selfBilling->self_billing_reference)
                        ->first();
                        
                    if ($referencedInvoice && $referencedInvoice->deleted_at === null) {
                        $undeleteableRecords[] = $this->invoiceNoGenerator($selfBilling->type, $selfBilling->uid);
                        continue;
                    }
                }

                $selfBilling->delete();
            }
            
            DB::commit();
            if(count($undeleteableRecords) > 0){
                $response = [
                    'warning' => true,
                    'message' => 'Some records could not be deleted due to existing references. Please check below for more details.',
                    'undeleteable_records' => $undeleteableRecords
                ];
            } else {
                $response = [
                    'success' => true,
                    'message' => 'Self Billing Trashed Successfully'
                ];
            }
            
            return response()->json($response);
            
        } catch (\Exception $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "Self Billing Controller -> Trash method");
            ResponseService::errorResponse();
        }
    }

    public function restore(Request $request){
        ResponseService::noFeatureThenRedirect('E-Invoicing');
        ResponseService::noAnyRoleThenRedirect(['School Admin']);
        try {
            DB::beginTransaction();
            $ids = $request->ids;
            // Ensure $ids is an array
            if (!is_array($ids)) {
                $ids = [$ids];
            }
                        
            foreach ($ids as $id) {
                $selfBilling = SelfBilling::withTrashed()->where('id', $id)->first();
                
                if (!$selfBilling) {
                    continue;
                }
                
                $selfBilling->restore();
            }
            
            DB::commit();
            return response()->json([
               'success' => true,
               'message' => 'Self Billing Restore Successfully'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "Self Billing Controller -> Restore method");
            ResponseService::errorResponse();
        }
    }

    public function destroy(Request $request){
        ResponseService::noFeatureThenRedirect('E-Invoicing');
        ResponseService::noAnyRoleThenRedirect(['School Admin']);
        try {
            DB::beginTransaction();
            $ids = $request->ids;

            // Ensure $ids is an array
            if (!is_array($ids)) {
                $ids = [$ids];
            }
            
            $undeleteableRecords = [];
            
            foreach ($ids as $id) {
                $selfBilling = SelfBilling::withTrashed()->where('id', $id)->first();
                
                if (!$selfBilling) {
                    continue;
                }
                
                // Check if there's an invoice reference
                if ($selfBilling->self_billing_reference) {
                    // Check if the referenced invoice is deleted
                    $referencedInvoice = SelfBilling::withTrashed()
                        ->where('id', $selfBilling->self_billing_reference)
                        ->first();
                        
                    if ($referencedInvoice && $referencedInvoice->deleted_at === null) {
                        $undeleteableRecords[] = $this->invoiceNoGenerator($selfBilling->type, $selfBilling->uid);
                        continue;
                    }
                }
                
                $selfBilling->forceDelete();
            }
            
            DB::commit();
            if(count($undeleteableRecords) > 0){
                $response = [
                    'warning' => true,
                    'message' => 'Some records could not be deleted due to existing references. Please check below for more details.',
                    'undeleteable_records' => $undeleteableRecords
                ];
            } else {
                $response = [
                    'success' => true,
                    'message' => 'Self Billing Deleted Successfully'
                ];
            }
            
            return response()->json($response);
            
        } catch (\Exception $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "Self Billing Controller -> Destroy method");
            ResponseService::errorResponse();
        }
    }

    public function deleteLineItem($id){
        ResponseService::noFeatureThenRedirect('E-Invoicing');
        ResponseService::noAnyRoleThenRedirect(['School Admin']);
        try {
            DB::beginTransaction();
            $lineItem = SelfBillingDetails::find($id);
            if(!$lineItem){
                return response()->json([
                    'success' => false,
                    'message' => 'Line Item Not Found'
                ]);
            }
            $lineItem->delete();
            DB::commit();
            return response()->json([
               'success' => true,
               'message' => 'Line Item Deleted Successfully'
            ]);
            
        } catch (\Exception $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "Self Billing Controller -> Trash method");
            ResponseService::errorResponse();
        }
    }

    public function deleteTaxItem($id){
        ResponseService::noFeatureThenRedirect('E-Invoicing');
        ResponseService::noAnyRoleThenRedirect(['School Admin']);
        try {
            DB::beginTransaction();
            $tax = SelfBillingTaxDetails::find($id);
            if(!$tax){
                return response()->json([
                    'success' => false,
                    'message' => 'Tax Not Found'
                ]);
            }
            $tax->delete();
            DB::commit();
            return response()->json([
               'success' => true,
               'message' => 'Tax Item Deleted Successfully'
            ]);
            
        } catch (\Exception $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "Self Billing Controller -> Trash method");
            ResponseService::errorResponse();
        }
    }

    public function validateShippingEInvoice($id){
        ResponseService::noFeatureThenRedirect('E-Invoicing');
        ResponseService::noAnyRoleThenRedirect(['School Admin']);
        try {
            $schoolEInvoice = DB::table('e_invoice')
                                ->where('school_id', Auth::user()->school_id)
                                ->first();
                                
            // Check if school invoice is not validated
            if (!$schoolEInvoice || $schoolEInvoice->status != 1) {
                ResponseService::errorResponse("School's TIN number not validated");
                return;
            }

            $shipping = SelfBillingShipping::where('id',$id)->first();
    
            $accessToken = null;
    
            // Validate Guardian e-invoice address 
            if ($schoolEInvoice && $shipping) {       
            
                if (!$shipping || empty($shipping->tin_number) || empty($shipping->id_type_no)) {
                    ResponseService::errorResponse("No TIN number found");
                    return;
                }
    
                $schoolSettings = $this->cache->getSchoolSettings();

                if ($schoolSettings) {
                    $accessToken = '';
                    $clientId = $schoolSettings['client_id'] ?? '';
                    $client_secret = array_filter([$schoolSettings['client_secret_1'] ?? null, $schoolSettings['client_secret_2'] ?? null]);
                    if (!empty($clientId) && count($client_secret) > 0) {  
                            foreach ($client_secret as $secret) {
                                $url = 'https://api.myinvois.hasil.gov.my/connect/token';
                                $headers = ['onbehalfof:' . $shipping->tin_number];
                                $fields = [
                                    'client_id' => $clientId,
                                    'client_secret' => $secret,
                                    'grant_type' => 'client_credentials',
                                    'scope' => 'InvoicingAPI',
                                ];
                                $encodedFields = http_build_query($fields);
                                $ch = curl_init();
                                curl_setopt($ch, CURLOPT_URL, $url);
                                curl_setopt($ch, CURLOPT_POST, true);
                                curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
                                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                                curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
                                curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
                                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                                curl_setopt($ch, CURLOPT_POSTFIELDS, $encodedFields);
                                $result = curl_exec($ch);
                                $httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                                
                                if ($result === false) {
                                    ResponseService::errorResponse("Unable to connect to validate TIN number");
                                    die('Curl failed: ' . curl_error($ch));
                                } else {
                                    $data = json_decode($result);
                                    if (!isset($data->error) && !empty($data->access_token)) {
                                        $accessToken = $data->access_token;
                                        break;
                                    }
                                }
                                curl_close($ch);
                            }
    
                        if ($accessToken) {
                            if (!empty($shipping->tin_number) && !empty($shipping->id_type_no)) {
                                $url = 'https://api.myinvois.hasil.gov.my/api/v1.0/taxpayer/validate/' . $shipping->tin_number . '?idType=' . $shipping->id_type . '&idValue=' . $shipping->id_type_no;
                                $headers = [
                                    'authorization: Bearer ' . $accessToken,
                                    'Content-Type: application/json',
                                    'Accept: application/json',
                                    'Accept-Language: en'
                                ];
                                $ch = curl_init();
                                curl_setopt($ch, CURLOPT_URL, $url);
                                curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
                                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                                curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
                                curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
                                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                                $result = curl_exec($ch);
                                $httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                                curl_close($ch);
    
                                if ($httpcode == 200) {
                                    SelfBillingShipping::where('id',$id)->update(['status' => 1]);
                                    ResponseService::successResponse("TIN number has been validated");
                                } else {
                                    ResponseService::errorResponse("Unable to validate TIN number");
                                }
                            }
                        }
                    }
                }
            } else {
                return null;
            }
        } catch (\Throwable $e) {
            DB::rollBack();
            ResponseService::errorResponse("Address Details Not Complete");
        }
    }

    public function submitEInvoice(Request $request){
        ResponseService::noFeatureThenRedirect('E-Invoicing');
        ResponseService::noAnyRoleThenRedirect(['School Admin']);
        // dd($request->all());
        $validator = Validator::make($request->all(), [
            'self_billing_ids'  => 'required|array',
            'self_billing_type' => 'required'
        ]);
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $selfBillings = $request->self_billing_ids;
        $selfBillingType = $request->self_billing_type;
        $documents = array();
        
        //Check submitted successfully
        $eInvoiceIds = SelfBillingEInvoice::whereIn('self_billing_id', $request->self_billing_ids)
            ->whereNull('deleted_at')
            ->where('status',1)
            ->pluck('self_billing_id')
            ->toArray();

        if(!empty($eInvoiceIds)){
            $invoiceNumbers = SelfBilling::whereIn('id', $eInvoiceIds)
                ->pluck('uid')
                ->map(function($uid) use ($selfBillingType) {
                    return $this->invoiceNoGenerator($selfBillingType, $uid);
                })->implode(', '); 

            return response()->json([
            'success' => false,
            'message' => 'Some e-invoices are already submitted successfully: '. $invoiceNumbers,
            ], 422);
        }

        if($selfBillingType != 'INV'){
            // Get referenced self billing records
            $referencedBillings = SelfBilling::whereIn('id', $request->self_billing_ids)
                ->whereNotNull('self_billing_reference')
                ->get();
                
            // Get all reference IDs
            $referenceIds = $referencedBillings->pluck('self_billing_reference')->filter()->values();
            
            // First check if e-invoice records exist for the reference IDs
            $latestEInvoices = SelfBillingEInvoice::whereIn('self_billing_id', $referenceIds)
                ->whereNull('deleted_at')
                ->orderBy('id', 'desc')
                ->get()
                ->groupBy('self_billing_id')
                ->map(function ($group) {
                    return $group->first();
                });
            
            if ($latestEInvoices->isEmpty()) {
                return $this->eInvoiceValidationError();
            }

            // Check if the latest e-invoice for each reference has status 1
            foreach($referencedBillings as $referencedBilling) {
                $latestEInvoice = $latestEInvoices->get($referencedBilling->self_billing_reference);
                if (!$latestEInvoice || $latestEInvoice->status != 1) {
                    return $this->eInvoiceValidationError();
                }
            }
        }

        $errors = [];
        foreach($selfBillings as $id){
            $invoice = null;
            
            $selfBilling = SelfBilling::with(['details', 'details.taxDetails','importInfo', 'billingInfo', 'shipping', 'supplier'])->find($id);
            $schoolEInvoice = DB::table('e_invoice')->where('school_id',Auth::user()->school_id)->first();
            $invoiceNo = $this->invoiceNoGenerator($selfBilling->type, $selfBilling->uid);
            $schoolSettings = $this->cache->getSchoolSettings();

            if(!$selfBilling){
                return ResponseService::errorResponse('Self-billing record not found');
            }
            
            //Supplier
            if(!$selfBilling->supplier){
                $errors[] = "[$invoiceNo] : Supplier not found for this self billing record";
                continue;
            }

            //Customer
            if(!$schoolEInvoice){
                $errors[] = "[$invoiceNo] : School e-invoice record not found";
                continue;
            }

            try {
                
                // Use EInvoiceHelper to validate and convert invoice type code
                $invoiceCode = $this->getInvoiceCode($selfBillingType);
                $invoiceType = EInvoiceHelper::mapInvoiceTypeCode($invoiceCode);
                $data = $this->formatEInvoiceData($selfBilling,$schoolEInvoice,$schoolSettings,$invoiceType);
                // //Generate the invoice xml
                // dd(json_encode($data));
                $invoice = EInvoiceHelper::createXmlDocument($invoiceType, $data);
                $documents = [];
                $document = MyInvoisHelper::getSubmitDocument($id, $invoice);
                $documents[] = $document;

                //This is how you extract the signatue value from the invoice xml
                $signatureValue = '';
                $dom = new DOMDocument();
                $dom->loadXML($invoice);
                $xpath = new DOMXPath($dom);
                $xpath->registerNamespace('ds', 'http://www.w3.org/2000/09/xmldsig#');
                $xpath->registerNamespace('cac', 'urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2');
                $xpath->registerNamespace('ext', 'urn:oasis:names:specification:ubl:schema:xsd:CommonExtensionComponents-2');
                $signatureValueNodes = $xpath->query('//ds:SignatureValue');
                if ($signatureValueNodes->length > 0) {
                    $signatureValue = $signatureValueNodes->item(0)->nodeValue;
                }
                
                $result = EInvoiceHelper::submitDocument(Auth::user()->school_id, $documents);
                
                if (!$result['success']) {
                    if (isset($result['errors']) && is_object($result['errors'])) {
                        $error = $result['errors']->error;
                        if (
                            isset($error->code) && $error->code === 'ValidationError' &&
                            isset($error->details[0]->code) && $error->details[0]->code === 'submission'
                        ) {
                            $errors[] = "[$invoiceNo] :  {$error->details[0]->message}";
                            continue;
                        }
                    }
                    $errors[] = "[$invoiceNo] : " . ($result['message'] ?? 'Error submitting document');
                    continue;
                }
                
                $data = array();
                $responseData = $result['data'] ?? [];
                
                if (isset($responseData['submissionUid'])) {
                    $data['submission_uid'] = $responseData['submissionUid'];
                }

                if (!empty($responseData['rejectedDocuments'])) {
                    $data['rejected_documents'] = json_encode($responseData['rejectedDocuments']);
                    $data['deleted_at'] = now();
                }

                if (!empty($responseData['acceptedDocuments'])) {
                    foreach ($responseData['acceptedDocuments'] as $document) {
                        if (isset($document['uuid'])) {
                            $data['uuid'] = $document['uuid'];
                        }
                        if (isset($document['invoiceCodeNumber'])) {
                            $data['invoice_code_number'] = $document['invoiceCodeNumber'];
                        }
                    }
                }

                if(!empty($signatureValue)){
                    $data['signature_value'] = $signatureValue;
                }

                if(count($data) > 0){
                    $data['self_billing_id'] = $id;
                    SelfBillingEInvoice::create($data);
                }   
                    
            } catch (\Exception $exception) {
                $errorMessage = $exception->getMessage();
                $submitDocument = json_decode($errorMessage);
                
                if (json_last_error() !== JSON_ERROR_NONE) {
                    $submitDocument = $errorMessage;
                }
                
                return response()->json([
                    'success' => false,
                    'message' => 'Document submission failed',
                    'error' => $submitDocument
                ], 500);
            }    
        }   

        if (!empty($errors)) {
            return response()->json([
                'success' => false,
                'message' => 'Some self-billing failed to submit due to errors',
                'errors' => $errors
            ]);
        }
        
        return response()->json([
           'success' => true,
           'message' => 'Self Billing Submitted Successfully',
        ]);
    }

    public function formatEInvoiceData($selfBilling,$schoolEInvoice,$schoolSettings,$invoiceType)
    {
        // Basic invoice information
        $data = [
            'invoice_type' => $invoiceType,
            'id' => $this->invoiceNoGenerator($selfBilling->type, $selfBilling->uid),
        ];

        if(!empty($selfBilling->self_billing_reference)){
            $selfBillingEInvoice = SelfBillingEInvoice::where('self_billing_id',$selfBilling->self_billing_reference)
                ->where('status',1)
                ->orderByDesc('id')
                ->first();
            if($selfBillingEInvoice){
                $data['billing_references']['invoice_document'] = [
                    'id' => $selfBillingEInvoice->internal_id?? '',
                    'uuid' => $selfBillingEInvoice->uuid?? ''
                ];
            }
        }

        // Billing Information
        if ($selfBilling->billingInfo) {
            $billingInfo = $selfBilling->billingInfo;
            if(!empty($billingInfo->bill_reference_num)){
                $data['billing_references']['additional_document'] = [
                    'id' => $selfBilling->billingInfo->bill_reference_num ?? '',
                    'uuid' => '************00000000'
                ];
            }

            if(!empty($billingInfo->prepayment_amount || !empty($billingInfo->prepayment_datetime)) || !empty($billingInfo->prepayment_reference_num)){
                $data['prepaid_payment'] = [
                    'amount' => $billingInfo->prepayment_amount?? '',
                    'datetime' => $billingInfo->prepayment_datetime?? '',
                    'id' => $billingInfo->prepayment_reference_num?? ''
                ];
            }
        }

        // Supplier information
        if($selfBilling->supplier){
            $data['supplier'] = [
                'address' => array_filter([
                    'city' => $selfBilling->supplier->city ?? '',
                    'postcode' => $selfBilling->supplier->postcode ?: null,
                    'country_subentity_code' => $selfBilling->supplier->state ?? ''
                ]),
                'address_line' => array_filter([
                    $selfBilling->supplier->addressLine1 ?? '',
                    $selfBilling->supplier->addressLine2 ?: null,
                    $selfBilling->supplier->addressLine3 ?: null
                ]),
                'country_code' => $selfBilling->supplier->country ?? '',
                'legal_entity' => $selfBilling->supplier->name ?? '',
                'contact_phone' => $selfBilling->supplier->phone_number ?? '',
                'contact_email' => $selfBilling->supplier->email ?? '',
                'msic_code' => $selfBilling->supplier->msic_code ?? '',
                'party_identification' => array_filter([
                    'TIN' => $selfBilling->supplier->tin_number ?? 'EI00000000010',
                    $selfBilling->supplier->id_type => $selfBilling->supplier->id_type_no ?? '************',
                    'SST' => $selfBilling->supplier->sst_number ?? 'NA',
                    'TTX' => $selfBilling->supplier->ttx_number ?? 'NA'
                ])
            ];
            if(!empty($selfBilling->importInfo->authorization_number)){
                $data['supplier']['accounting_party_account_id'] = $selfBilling->importInfo->authorization_number;
            }
        }

        // Customer Information
        if($schoolEInvoice){
            $schoolSettings = $this->cache->getSchoolSettings(['*'],$schoolEInvoice->school_id);
            $data['customer'] = [
                'address' => array_filter([
                    'city' => $schoolEInvoice->city ?? '',
                    'postcode' => $schoolEInvoice->postal_code ?: null,
                    'country_subentity_code' => $schoolEInvoice->state ?? ''
                ]),
                'address_line' => array_filter([
                    $schoolEInvoice->address_line1 ?? '',
                    $schoolEInvoice->address_line2 ?: null,
                    $schoolEInvoice->address_line3 ?: null
                ]),
                'country_code' => $schoolEInvoice->country ?? '',
                'legal_entity' => $schoolSettings['school_name'] ?? '',
                'contact_phone' => $schoolSettings['school_phone'] ?? '',
                'contact_email' => $schoolSettings['school_email'] ?? '',
                'party_identification' => array_filter([
                    'TIN' => !empty($schoolSettings['client_tin']) ? $schoolSettings['client_tin']: 'EI00000000010',
                    'SST' => $schoolEInvoice->sst_registration_number ?? 'NA',
                    ($schoolSettings['client_tin_type'] === 'personal' ? 'NRIC' : 'BRN') => !empty($schoolSettings['client_id_number']) ? $schoolSettings['client_id_number'] : '************'
                ])
            ];
        }

        // Delivery information
        if($selfBilling->shipping){
            $data['delivery'] = [
                'address' => array_filter([
                    'city' => $selfBilling->shipping->city ?? '',
                    'postcode' => $selfBilling->shipping->postal_code ?: null,
                    'country_subentity_code' => $selfBilling->shipping->state ?? ''
                ]),
                'address_line' => array_filter([
                    $selfBilling->shipping->address_line1 ?? '',
                    $selfBilling->shipping->address_line2  ?: null,
                    $selfBilling->shipping->address_line3  ?: null
                ]),
                'country_code' => $selfBilling->shipping->country ?? '',
                'legal_entity' => $selfBilling->shipping->recipient_name ?? '',
                'party_identification' => array_filter([
                    'TIN' => $selfBilling->shipping->tin_number ?? 'EI00000000010',
                    $selfBilling->shipping->id_type => $selfBilling->shipping->id_type_no ?? '************'
                ])
            ];

            if($selfBilling->importInfo->details_other_charges > 0){
                $data['delivery']['allowance_charge'] = [
                    'charge_indicator' => true,
                    'amount' => $selfBilling->importInfo->details_other_charges?? 0,
                    'description' => $selfBilling->importInfo->details_other_charges_description ?? ''
                ];
            }
        }

        // Document line items
        if(count($selfBilling->details) > 0){
            $data['tax_total'] = [];
            $documentLine['tax_sub_totals'] = [];
            $taxTotalAmount = 0;
            $data['document_line'] = [];
            foreach($selfBilling->details as $detail){
                $documentLine = [
                    'id' => $detail->id ?? '',
                    'quantity' => $detail->quantity ?? 1,
                    'line_amount' => $detail->total_excluding_tax,
                    'item' => [
                        'description' => $detail->description ?? '',
                        'classifications' => [
                            [
                                'code' => preg_match('/^(\d+)\s*-/', $detail->classification_code ?? '', $matches) ? $matches[1] : ($detail->classification_code ?? ''),
                                'type' => 'CLASS'
                            ]
                        ]
                    ],
                    'price' => [
                        'amount' => $detail->unit_price ?? 0
                    ],
                    'price_extension' => [
                        'amount' => $detail->total_amount ?? 0
                    ],
                    'allowance_charges' => array_filter([
                        $detail->total_discount_amount > 0 ? [
                            'charge_indicator' => false,
                            'reason' => $detail->discount_description ?? '',
                            'multiplier' => $detail->discount_rate ?? 0,
                            'amount' => $detail->total_discount_amount ?? 0
                        ] : null,
                        $detail->total_charge_rate > 0 ? [
                            'charge_indicator' => true,
                            'reason' => $detail->charge_description ?? '',
                            'multiplier' => $detail->charge_rate ?? 0,
                            'amount' => $detail->total_charge_rate ?? 0
                        ] : null
                    ])
                ];

                if(!empty($detail->tariff_code)){
                    $documentLine['item']['classifications'][] = [
                        'code' => $detail->tariff_code?? '',
                        'type' => 'PTC'
                    ];
                }

                if(!empty($detail->country_of_origin)){
                    $documentLine['item']['country'] = $detail->country_of_origin;
                }
                //Invoice Tax Total
                $taxTotalAmount += $detail->taxDetails->sum('tax_amount') ?? 0;

                $documentLine['tax_total'] = [
                    'amount' => $detail->taxDetails->sum('tax_amount') ?? 0,
                ];

                foreach($detail->taxDetails as $taxDetail){
                    $taxSubTotal = [
                        'taxable_amount' => (float) $detail->total_excluding_tax ?? 0,
                        'tax_amount' => (float) $taxDetail->tax_amount ?? 0,
                        'tax_scheme' => [
                            'id' => 'OTH'
                        ]
                    ];
                    
                    if ((float) ($taxDetail->tax_percentage ?? 0) !== 0) {
                        $taxSubTotal['percent'] = (float) $taxDetail->tax_percentage;
                    }

                    $taxSubTotal['tax_category'] = array_filter([
                        'id' => $taxDetail->tax_type?? '',
                        'percent' => (float) ($taxDetail->tax_percentage ?? 0),
                    ]);
                    $documentLine['tax_sub_totals'][] = $taxSubTotal;
                    $data['tax_total']['tax_sub_totals'][] = $taxSubTotal;
                }

                if(!empty($detail->total_exempted) && $detail->total_exempted > 0){
                    $data['tax_total']['tax_sub_totals'][] = [
                        'taxable_amount' => (float) $detail->total_excluding_tax ?? 0,
                        'tax_amount' => (float) $detail->total_exempted?? 0,
                        'tax_scheme' => [
                            'id' => 'OTH'
                        ],
                        'tax_category' => [
                            'id' => 'E',
                            'tax_exemption_reason' => $detail->tax_exempted_details?? ''
                        ]
                    ];
                }
                $data['document_line'][] = $documentLine;
            }
            $data['tax_total']['tax_amount'] = $taxTotalAmount;
        }

        // Additional document references
        if($selfBilling->importInfo){
            $data['additional_document_reference'] = array_filter([
                !empty($selfBilling->importInfo->reference_num_form1_9) ? [
                    'id' => $selfBilling->importInfo->reference_num_form1_9,
                    'document_type' => 'CustomsImportForm'
                ] : null,
                !empty($selfBilling->importInfo->fta_information) ? [
                    'id' => 'FTA',
                    'document_type' => 'FreeTradeAgreement',
                    'description' => $selfBilling->importInfo->fta_information
                ] : null,
                !empty($selfBilling->importInfo->reference_num_form2) ? [
                    'id' => $selfBilling->importInfo->reference_num_form2,
                    'document_type' => 'K2'
                ] : null,
                !empty($selfBilling->importInfo->incoterms) ? [
                    'id' => $selfBilling->importInfo->incoterms
                ] : null
            ]);
        }
        
        // Monetary totals
        $data['legal_monetary_total'] = [
            'line_extension_amount' => $selfBilling->total_net_amount,
            'tax_exclusive_amount' => $selfBilling->total_excluding_tax,
            'tax_inclusive_amount' => $selfBilling->total_including_tax,
            'allowance_total_amount' => $selfBilling->invoice_discount_value,
            'charge_total_amount' => $selfBilling->invoice_charge_value,
            'payable_rounding_amount' => $selfBilling->total_rounding_amount,
            'payable_amount' => $selfBilling->total_payable_amount
        ];

        if($selfBilling->invoice_discount_value > 0){
            $data['allowance_charges'][] = [
                'charge_indicator' => false,
                'reason' => $selfBilling->invoice_discount_description,
                'amount' => $selfBilling->invoice_discount_value
            ];
        }

        if($selfBilling->invoice_charge_value > 0){
            $data['allowance_charges'][] = [
                'charge_indicator' => true,
               'reason' => $selfBilling->invoice_charge_description,
                'amount' => $selfBilling->invoice_charge_value
            ];
        }

        // Invoice period
        if($selfBilling->billingInfo){
            $data['invoice_period'] = array_filter([
                'start_date' => $selfBilling->billingInfo->billing_start_date ?:null,
                'end_date' => $selfBilling->billingInfo->billing_end_date ?: null,
                'description' => $selfBilling->billingInfo->frequency_billing ?: null
            ]);
            if(!empty($selfBilling->billingInfo->supplier_bank_acc)){
                $data['payment_means'] = [
                    'description' => $selfBilling->billingInfo->supplier_bank_acc ?? ''
                ];
            }
            if(!empty($selfBilling->billingInfo->payment_terms)){
                $data['payment_terms'] = [
                    'note' => $selfBilling->billingInfo->payment_terms ?? ''
                ];
            }
        }

        return $data;
    }

    public function cancelEInvoice($id,Request $request){
        ResponseService::noFeatureThenRedirect('E-Invoicing');
        ResponseService::noAnyRoleThenRedirect(['School Admin']);
        $selfBilling = SelfBilling::find($id);
        $selfBillingEInvoice = SelfBillingEInvoice::where('self_billing_id',$id)->orderByDesc('id')->first();
        if(!$selfBilling){
            ResponseService::errorResponse('Self Billing not found');
        }

        if(!$selfBillingEInvoice){
            ResponseService::errorResponse('Self Billing E-Invoice not found');
        }

        if(empty($request->reason)){
            ResponseService::errorResponse('Reason is required');
        }

        $result = EInvoiceHelper::cancelDocument($selfBilling->school_id, $selfBillingEInvoice->uuid, $request->reason);
        if($result['success']){
            $selfBillingEInvoice->status = 3;
            $selfBillingEInvoice->cancel_reason = $request->reason;
            $selfBillingEInvoice->deleted_at = now();
            $selfBillingEInvoice->save();
            ResponseService::successResponse('Self Billing cancelled successfully');
        }
    }

    private function eInvoiceValidationError() {
        return response()->json([
            'success' => false,
            'message' => 'E-invoice must be validated'
        ], 422);
    }

    public function showInvoice($selfBillingId,$selfBillingType)
    {
        ResponseService::noFeatureThenRedirect('E-Invoicing');
        ResponseService::noAnyRoleThenRedirect(['School Admin']);
    
        $selfBilling = SelfBilling::with(['details', 'details.taxDetails','importInfo', 'billingInfo', 'shipping', 'supplier'])->find($selfBillingId);
        
        // Check if this is not an invoice and has references
        $referenceInvoiceNo = null;
        if($selfBilling->type != 'INV' && $selfBilling->self_billing_reference) {
            $referenceSelfBilling = SelfBilling::find($selfBilling->self_billing_reference);
            if($referenceSelfBilling) {
                $referenceInvoiceNo = $this->invoiceNoGenerator($referenceSelfBilling->type, $referenceSelfBilling->uid);
                $selfBilling->reference_invoice_no = $referenceInvoiceNo;
            }
        }
        $selfBilling->invoice_no = $this->invoiceNoGenerator($selfBilling->type,$selfBilling->uid);
        $selfBilling->invoice_type = $this->getInvoiceName($selfBilling->type);
        $school = $this->cache->getSchoolSettings();
        $schoolSettings = DB::select("SELECT data FROM school_settings WHERE name = 'horizontal_logo' AND school_id = " . Auth::user()->school_id);
        $schoolLogo = '';
        if (COUNT($schoolSettings)) {
            $schoolLogo = $schoolSettings[0]->data;
        }
        $selfBillingEInvoice = SelfBillingEInvoice::where('self_billing_id',$selfBillingId)->orderByDesc('id')->first();

        if(isset($selfBillingEInvoice) && $selfBillingEInvoice->status != 0){
            $documentSummary = json_decode($selfBillingEInvoice->document_summary);

            if(!empty($documentSummary[0]->dateTimeValidated)){
                $selfBillingEInvoice->date_time_validated = date('d M Y H:i:s', strtotime($documentSummary[0]->dateTimeValidated));
            }
            $url = env('E_INVOICE_URL').'/'.$selfBillingEInvoice->uuid.'/share/'.$selfBillingEInvoice->long_id;
            // dd($url);
            $selfBillingEInvoice->e_invoice_url = (new QRCode)->render($url);
        }

        $schoolEInvoice = DB::table('e_invoice')->where('school_id', Auth::user()->school_id)->first();
        // dd($selfBilling);
        // return view('self-billing.receipt',compact('selfBilling','school','schoolLogo','selfBillingEInvoice','schoolEInvoice'));
        $pdf = Pdf::loadView('self-billing.receipt', compact('selfBilling','school','schoolLogo','selfBillingEInvoice','schoolEInvoice'));

        return $pdf->stream('self-billing-invoice.pdf');
    }
    
    public function getInvoiceCode($selfBillingType){
        $invoiceCode = '';
        switch($selfBillingType){
            case 'INV':
                $invoiceCode = 'self_billed_invoice';
                break; 
            case 'CN':
                $invoiceCode = 'self_billed_credit_note';
                break;
            case 'DB':
                $invoiceCode = 'self_billed_debit_note';
                break;
            case 'RF':
                $invoiceCode = 'self_billed_refund_note';
                break;
        }
        return $invoiceCode;
    }

    public function getInvoiceName($selfBillingType){
        $invoiceName = '';
        switch($selfBillingType){
            case 'INV':
                $invoiceName ='Self Billed Invoice';
                break;
            case 'CN':
                $invoiceName ='Self Billed Credit Note';
                break;
            case 'DB':
                $invoiceName ='Self Billed Debit Note';
                break;
            case 'RF':
                $invoiceName ='Self Billed Refund Note';
                break;
        }
        return $invoiceName;
    }


}
