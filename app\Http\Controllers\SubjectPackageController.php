<?php

namespace App\Http\Controllers;

use App\Models\Students;
use App\Models\SubjectPackage;
use App\Models\File;
use App\Services\ResponseService;
use App\Repositories\SchoolSetting\SchoolSettingInterface;
use App\Repositories\User\UserInterface;
use App\Repositories\Files\FilesInterface;
use App\Services\CachingService;
use App\Repositories\ClassSchool\ClassSchoolInterface;
use App\Repositories\SessionYear\SessionYearInterface;
use App\Repositories\SystemSetting\SystemSettingInterface;
use App\Repositories\Student\StudentInterface;
use App\Repositories\Gallery\GalleryInterface;
use App\Services\BootstrapTableService;
use App\Services\FileSizeLimitService;
use Carbon\Carbon;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Auth;
use Throwable;

class SubjectPackageController extends Controller
{

    private SessionYearInterface $sessionYear;
    private SchoolSettingInterface $schoolSettings;
    // private MediumInterface $medium;
    private ClassSchoolInterface $classes;
    private GalleryInterface $gallery;
    private FilesInterface $files;
    private ClassSchoolInterface $class;
    private UserInterface $user;
    private CachingService $cache;
    private StudentInterface $student;
    private SystemSettingInterface $systemSetting;
    private SubjectPackage $subjectPackage;

    public function __construct(SessionYearInterface $sessionYear, SchoolSettingInterface $schoolSettings, GalleryInterface $gallery, ClassSchoolInterface $classes, UserInterface $user, FilesInterface $files, CachingService $cache, ClassSchoolInterface $classSchool, StudentInterface $student, SystemSettingInterface $systemSetting, SubjectPackage $subjectPackage)
    {

        $this->sessionYear = $sessionYear;
        $this->schoolSettings = $schoolSettings;
        $this->classes = $classes;
        $this->gallery = $gallery;
        $this->subjectPackage = $subjectPackage;
        $this->files = $files;
        $this->user = $user;
        $this->cache = $cache;
        $this->class = $classSchool;
        $this->student = $student;
        $this->systemSetting = $systemSetting;
    }

    public function packageIndex()
    {
        $user = Auth::user()->school_id;
        // Fees Data With Few Selected Data
        $classes = $this->classes->all(['*'], ['medium', 'sections']);
        $session_year_all = $this->sessionYear->all(['id', 'name', 'default']);
        $packages = DB::table('subject_package')
            ->select('id', 'name')
            ->where('school_id', Auth::user()->school_id)
            ->get();

        $totalPackage = DB::table('subject_package')
            ->where('school_id', Auth::user()->school_id)
            ->count();

        $packageSales = DB::table('purchase_package')
            ->where('school_id', Auth::user()->school_id)
            ->count();

        return response(view('subject-package.package', compact('packages', 'classes', 'session_year_all', 'totalPackage', 'packageSales')));
    }

    public function newPackageIndex()
    {
        $user = Auth::user()->school_id;
        $classes = $this->classes->all(['*'], ['medium', 'sections']);
        $session_year_all = $this->sessionYear->all(['id', 'name', 'default']);

        $subjects = DB::table('subjects')
            ->select('id', 'name')
            ->where('school_id', $user)
            ->get();

        return response(view('subject-package.new_package', compact('classes', 'session_year_all', 'subjects')));
    }

    public function historyIndex(Request $request, $student_id = null)
    {
        $user = Auth::user()->school_id;
        $classes = $this->classes->all(['*'], ['medium', 'sections']);

        $subjects = DB::table('subjects')
            ->select('id', 'name')
            ->where('school_id', $user)
            ->get();

        $packages = DB::table('subject_package')
            ->select('id', 'name', 'subject_id')
            ->where('school_id', Auth::user()->school_id)
            ->get();

        $query = DB::table('purchase_package as pp')
            ->leftJoin('subject_package as sp', 'pp.package_id', '=', 'sp.id')
            ->leftJoin('files as f', 'sp.thumbnail_id', '=', 'f.id')
            ->join('students as s', 's.id', '=', 'pp.student_id')
            ->join('class_sections as cs', 's.class_section_id', '=', 'cs.id')
            ->join('users as u', 'u.id', '=', 's.user_id')
            ->select(
                DB::raw('ROW_NUMBER() OVER(ORDER BY sp.updated_at DESC) as no'),
                'pp.id',
                'sp.name',
                's.id as student_id',
                'cs.class_id',
                DB::raw("CONCAT(u.first_name, ' ', u.last_name) AS full_name"),
                'sp.price',
                'sp.description',
                'sp.expiry_days',
                'sp.total_sessions',
                'pp.status as package_status',
                'pp.date as purchase_date',
                'sp.subject_id',
                'f.file_url as thumbnail_url'
            );

        if (isset($student_id)) {
            $query->where('student_id', $student_id);
            // ->get();
            $student_id = $query->pluck('student_id')->first();
        }

        return response(view('subject-package.history', compact('classes', 'subjects', 'packages', 'student_id')));
    }

    public function purchaseIndex(Request $request)
    {
        $user = Auth::user()->school_id;

        $classes = $this->classes->all(['*'], ['medium', 'sections']);

        $subjects = DB::table('subjects')
            ->select('id', 'name')
            ->where('school_id', $user)
            ->get();

        $packages = DB::table('subject_package')
            ->select('id', 'name', 'subject_id')
            ->where('school_id', Auth::user()->school_id)
            ->get();

        // $subjectIds = explode(',',$packages->subject_id);
        

        return response(view('subject-package.purchase', compact('classes', 'subjects', 'packages')));
    }

    public function usageIndex()
    {
        $schoolId = Auth::user()->school_id;
        $classes = $this->class->all(['*'], ['stream', 'medium'])->where('school_id', $schoolId);

        $packages = DB::table('subject_package')
            ->select('id', 'name', 'subject_id')
            ->where('school_id', Auth::user()->school_id)
            ->get();

        return response(view('subject-package.usage', compact('packages', 'classes')));
    }

    public function store(Request $request)
    {

        $request->validate([
            'name' => 'required|string',
            'description' => 'nullable|string',
            'price' => 'required|numeric',
            'expiry_days' => 'required|numeric',
            'total_sessions' => 'required|numeric',
            'subject_id' => 'required|array'
        ]);

        $schoolId = Auth::user()->school_id;

        DB::beginTransaction();

        try {
            $subject_id = implode(',', $request->subject_id);

            $subjectPackage = SubjectPackage::create([
                'name' => $request->name,
                'description' => $request->description ?? '',
                'price' => $request->price,
                'expiry_days' => $request->expiry_days,
                'total_sessions' => $request->total_sessions,
                'subject_id' => $subject_id,
                'school_id' => Auth::user()->school_id
            ]);

            if ($request->hasFile('thumbnail')) {
                if (FileSizeLimitService::checkFileSizeLimit($schoolId)) {
                    throw new \Exception('Storage capacity not enough');
                }

                $thumbnailFile = $request->file('thumbnail');
                $thumbnailSize = $thumbnailFile->getSize();
                $thumbnailSizeKB = round($thumbnailSize / 1024, 2);

                $file = File::create([
                    'modal_type' => SubjectPackage::class,
                    'modal_id' => $subjectPackage->id,
                    'file_name' => basename($thumbnailFile->getClientOriginalName(), '.' . $thumbnailFile->getClientOriginalExtension()),
                    'file_url' => $thumbnailFile->store('files', 'public'),
                    'type' => 1,
                    'school_id' => $schoolId,
                    'file_size' => $thumbnailSizeKB,
                ]);

                $subjectPackage->thumbnail_id = $file->id;
                $subjectPackage->save();
            }

            DB::commit();
            return ResponseService::successResponse('Data Saved Successfully');
        } catch (\Exception $e) {
            DB::rollback();
            return ResponseService::errorResponse($e->getMessage());  // Return the error message
        }
    }

    public function purchaseStore(Request $request)
    {
        try {
            $request->validate([
                'package_id' => 'required|numeric',
                'date' => 'required|date',
                'student_id' => 'required|array',
                'student_id.*' => 'required|numeric'
            ]);

            // Check if any selected student has credit_status = 1
            $invalidStudents = DB::table('students')
                ->whereIn('students.id', $request->student_id)
                ->where('credit_status', 1)
                ->join('users', 'students.user_id', '=', 'users.id')
                ->select(DB::raw("CONCAT(users.first_name, ' ', users.last_name) AS name"))
                ->pluck('name');

            if ($invalidStudents->isNotEmpty()) {
                return ResponseService::errorResponse('Cannot assign package to students: ' . $invalidStudents->implode(', '));
            }

            $sessionYear = $this->cache->getDefaultSessionYear();
            $schoolId = Auth::user()->school_id;

            $school = DB::table('schools')
                ->select('*')
                ->where('id', $schoolId)
                ->first();


            $formattedDate = Carbon::createFromFormat('d-m-Y', $request->date);
            $remainingSession = DB::table('subject_package')
                ->where('id', $request->package_id)
                ->value('total_sessions');

            $totalSession = DB::table('subject_package')
                ->where('id', $request->package_id)
                ->value('total_sessions');

            DB::beginTransaction();
            foreach ($request->student_id as $studentId) {

                $studentId = (int)$studentId;

                $user = DB::table('users')
                    ->join('students', 'users.id', '=', 'students.user_id')
                    ->select('users.id', 'users.first_name', 'users.last_name')
                    ->where('students.id', $studentId)
                    ->first();

                DB::table('purchase_package')->insert([
                    'package_id' => $request->package_id,
                    'student_id' => $studentId,
                    'date' => $formattedDate,
                    'total_sessions' => $totalSession,
                    'school_id' => Auth::user()->school_id,
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now()
                ]);

                $pp_id = DB::table('purchase_package')
                    ->where('created_at', '<=', Carbon::now())
                    ->orderBy('id', 'DESC')
                    ->pluck('id')
                    ->first();

                DB::table('package_usage')->insert([
                    'purchase_package_id' => $pp_id,
                    'student_id' => $studentId,
                    'remaining_session' => $remainingSession,
                    'school_id' => Auth::user()->school_id,
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now()
                ]);

                $fees_amount = DB::table('subject_package')
                    ->select('price')
                    ->where('id', $request->package_id)
                    ->first();

                $classId = $request->class_id[0];

                $package_name = DB::table('subject_package')
                    ->select('name')
                    ->where('id', $request->package_id)
                    ->first();

                $studentFees = DB::table('student_fees')
                    ->insertGetId([
                        'name' => 'Package Purchase',
                        'due_date' => Carbon::now()->addDays(7),
                        'due_charges' => 0,
                        'due_charges_amount' => 0,
                        'early_offer' => 0,
                        'early_offer_amount' => 0,
                        'early_date' => Carbon::now(),
                        'class_id' => $classId,
                        'school_id' => $schoolId,
                        'session_year_id' => $sessionYear->id,
                        'student_id' => $studentId,
                        'status' => 'published',
                        'created_at' => Carbon::now(),
                        'updated_at' => Carbon::now(),
                    ]);

                $id = $studentFees;

                DB::table('student_fees_details')
                    ->insert([
                        'school_id' => $schoolId,
                        'student_fees_id' => $studentFees,
                        'fees_type_name' => $package_name->name,
                        'fees_type_amount' => $fees_amount->price,
                        'classification_code' => '010 - Education fees',
                        'unit' => 'E48 - service unit',
                        'quantity' => 1,
                        'created_at' => Carbon::now(),
                        'updated_at' => Carbon::now(),
                    ]);

                $latestUID = DB::table('student_fees')->where('school_id', Auth::user()->school_id)->where('status', 'published')->whereNull('deleted_at')->select('uid')->orderBy('uid', 'desc')->value('uid');
                $uid = $latestUID ? $latestUID + 1 : 1;

                    while (DB::table('student_fees')
                        ->where('uid', $uid)
                        ->where('school_id', Auth::user()->school_id)
                        ->where('status', 'published')
                        ->whereNull('deleted_at')
                        ->exists()
                    ) {
                        $uid++;
                    }
                    DB::table('student_fees')->where('id', $studentFees)->update(["uid" => $uid]);
                    $uid++;

                DB::table('student_fees_paids')
                ->insert([
                    'school_id' => $schoolId,
                    'student_fees_id' => $studentFees,
                    'mode' => 1,
                    'is_fully_paid' => 1,
                    'amount' => $fees_amount->price,
                    'due_charges' => 0,
                    'status' => 1,
                    'date' => Carbon::now(),
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now(),
                ]);

            }

            DB::commit();

            $checkUser = DB::select('SELECT * FROM students s, users u WHERE s.user_id = u.id AND u.id = ?', [$user->id]);
            if (count($checkUser) > 0) {
                $allUser = DB::select('SELECT DISTINCT s.guardian_id, sf.student_id FROM student_fees sf JOIN students s ON sf.student_id = s.id JOIN users u ON s.user_id = u.id WHERE u.id = ?', [$user->id]);
            } else {
                $allUser = DB::select('SELECT u.id FROM student_fees sf JOIN users u ON sf.student_id = u.id WHERE sf.student_id = ? AND sf.id = ?', [$user->id, $id]);
            }

            $notifyUser = [];
            foreach ($allUser as $data) {
                if (isset($data->guardian_id)) {
                    $notifyUser[] = $data->guardian_id; // guardian
                }
            }

            if ($notifyUser !== null) {

                $title = 'Package Purchase Confirmation';
                $type = 'Package'; // Get The Type for Notification
                $body = "{$school->name}\n{$user->first_name} {$user->last_name} has successfully purchased the package: {$package_name->name} for {$formattedDate}. Thank you, and happy learning!";

                send_notification($notifyUser, $title, $body, $type); // Send Notification
            }

            return ResponseService::successResponse('Data Saved Successfully');
        } catch (\Exception $e) {
            DB::rollback();
            ResponseService::errorResponse('Error Saving Data: ' . ' ' . $e->getMessage());
        };
    }

    public function show($id = null)
    {
        $offset = request('offset', 0);
        $limit = request('limit', 10);
        $sort = request('sort', 'id');
        $order = request('order', 'DESC');
        $startDate = request('start_date');
        $endDate = request('end_date');
        $package_id = request('package_id');

        $query = DB::table('subject_package as sp')
            ->leftJoin('files as f', 'sp.thumbnail_id', '=', 'f.id') // Join with the files 
            ->where('sp.school_id', Auth::user()->school_id)
            ->select(
                DB::raw('ROW_NUMBER() OVER(ORDER BY sp.updated_at DESC) as no'),
                'sp.id',
                'sp.name',
                'sp.price',
                'sp.description',
                'sp.expiry_days',
                'sp.total_sessions',
                'sp.created_at',
                'f.file_url as thumbnail_url'
            );

        if ($package_id) {
            $query->where('sp.id', $package_id);
        }
        if (isset($id)) {
            $query->where('sp.id', $id);
            $id = $query->pluck('sp.id')->first();
        }

        if ($startDate && $endDate) {
            $query->whereBetween(DB::raw('DATE(sp.updated_at)'), [date('Y-m-d', strtotime($startDate)), date('Y-m-d', strtotime($endDate))]);
        }

        $salesCounts = DB::table('purchase_package as pp')
            ->select('pp.package_id', DB::raw('COUNT(*) as purchase_count'))
            ->groupBy('pp.package_id')
            ->pluck('purchase_count', 'package_id');

        $total = $query->count();

        $query->orderBy('sp.created_at', $order);

        $records = $query->offset($offset)
            ->limit($limit)
            ->get();

        $bulkData = [];
        $bulkData['total'] = $total;
        $rows = [];
        $no = $offset + 1;

        foreach ($records as $row) {
            $operate = '';
            $operate = BootstrapTableService::editButton(route('subject-package.edit', $row->id), false);
            $operate .= BootstrapTableService::deleteButton(route('subject-package.destroy', $row->id), false);
            $tempRow = (array) $row;
            $tempRow['sales'] = isset($salesCounts[$row->id]) ? $salesCounts[$row->id] : 0;
            $tempRow['operate'] = $operate;
            $tempRow['no'] = $no++;
            $tempRow['thumbnail'] = $row->thumbnail_url;

            $rows[] = $tempRow;
        }

        $bulkData['rows'] = $rows;

        return response()->json($bulkData);
    }

    public function purchaseShow()
    {
        $offset = request('offset', 0);
        $limit = request('limit', 10);
        $sort = request('sort', 'created_at'); 
        $order = request('order', 'DESC');
        $startDate = request('start_date');
        $endDate = request('end_date');
        $package_id = request('package_id');
        $student_id = request('student_id'); // Add this line

        $baseQuery = DB::table('purchase_package as pp')
            ->leftJoin('subject_package as sp', 'pp.package_id', '=', 'sp.id')
            ->leftJoin('files as f', 'sp.thumbnail_id', '=', 'f.id')
            ->join('students as s', 's.id', '=', 'pp.student_id')
            ->join('users as u', 'u.id', '=', 's.user_id')
            ->where('pp.school_id', Auth::user()->school_id);

        // Add student filter
        if ($student_id) {
            $baseQuery->where('pp.student_id', $student_id);
        }

        if ($package_id) {
            $baseQuery->where('pp.package_id', $package_id);
        }

        if ($startDate && $endDate) {
            $baseQuery->whereBetween(DB::raw('DATE(pp.date)'), [
                date('Y-m-d', strtotime($startDate)), 
                date('Y-m-d', strtotime($endDate))
            ]);
        }

        $total = $baseQuery->count();

        $query = $baseQuery->select(
            'pp.id as purchase_id',
            'pp.package_id',
            DB::raw('ROW_NUMBER() OVER(ORDER BY sp.updated_at DESC) as no'),
            'pp.student_id',
            'sp.name',
            DB::raw("CONCAT(u.first_name, ' ', u.last_name) AS full_name"),
            DB::raw('(SELECT SUM(total_sessions) FROM purchase_package 
                     WHERE student_id = pp.student_id 
                     AND package_id = pp.package_id 
                     AND (status = 0 OR status = 2)) as total_sessions'),
            'f.file_url as thumbnail_url',
            'pp.date as purchase_date',
            DB::raw('DATE_ADD(pp.date, INTERVAL sp.expiry_days DAY) as expiry_date'),
            DB::raw('(SELECT remaining_session FROM package_usage 
                     WHERE purchase_package_id = pp.id 
                     ORDER BY created_at DESC LIMIT 1) as remaining'),
            'pp.status as package_status',
            'pp.total_sessions'
        )
        ->groupBy('pp.id', 'pp.package_id', 'pp.student_id', 'sp.name', 
                  'sp.updated_at', 'u.first_name', 'u.last_name', 'f.file_url', 'pp.created_at', 'pp.status');  

        $records = $query->orderBy('pp.created_at', $order)  
            ->offset($offset)
            ->limit($limit)
            ->get();

        $bulkData = [];
        $bulkData['total'] = $total;
        $rows = [];
        $no = $offset + 1;

        foreach ($records as $row) {
            $operate = '';
            $operate .= BootstrapTableService::button('fa fa-eye', route('subject-package.history', $row->student_id), ['btn-gradient-primary']);
            $tempRow = (array) $row;
            $tempRow['operate'] = $operate;
            $tempRow['no'] = $no++;
            $tempRow['thumbnail'] = $row->thumbnail_url;
            $tempRow['purchase_date'] = Carbon::parse($row->purchase_date)->format('Y-m-d');
            $tempRow['expiry_date'] = Carbon::parse($row->expiry_date)->format('Y-m-d');

            $rows[] = $tempRow;
        }

        $bulkData['rows'] = $rows;
        return response()->json($bulkData);
    }

    public function historyShow($student_id = null)
    {
        $offset = request('offset', 0);
        $limit = request('limit', 10);
        $sort = request('sort', 'id');
        $order = request('order', 'DESC');
        $startDate = request('start_date');
        $endDate = request('end_date');
        $package_id = request('package_id');
        $studentId = request('studentId');
        $student_id = request('student_id');
        $class_id = request('class_id');
        $status = request('status');

        $query = DB::table('purchase_package as pp')
            ->leftJoin('subject_package as sp', 'pp.package_id', '=', 'sp.id')
            ->leftJoin('files as f', 'sp.thumbnail_id', '=', 'f.id')
            ->join('students as s', 's.id', '=', 'pp.student_id')
            ->join('class_sections as cs', 's.class_section_id', '=', 'cs.id')
            ->join('users as u', 'u.id', '=', 's.user_id')
            ->where('pp.school_id', Auth::user()->school_id)
            ->select(
                DB::raw('ROW_NUMBER() OVER(ORDER BY sp.updated_at DESC) as no'),
                'pp.id',
                'sp.name',
                's.id as student_id',
                'cs.class_id',
                DB::raw("CONCAT(u.first_name, ' ', u.last_name) AS full_name"),
                'sp.price',
                'sp.description',
                'sp.expiry_days',
                'sp.total_sessions',
                'pp.status as package_status',
                'pp.date as purchase_date',
                'sp.subject_id',
                'f.file_url as thumbnail_url'
            );

        if ($package_id) {
            $query->where('pp.package_id', $package_id);
        }

        if ($studentId) {
            $query->where('student_id', $studentId);
        } else if (isset($student_id)) {
            $query->where('student_id', $student_id);
        }

        if ($class_id) {
            $query->where('cs.class_id', $class_id);
        }

        if ($startDate && $endDate) {
            $query->whereBetween(DB::raw('DATE(pp.date)'), [date('Y-m-d', strtotime($startDate)), date('Y-m-d', strtotime($endDate))]);
        }

        if ($status) {
            $query->where('pp.status', $status);
        }


        $total = $query->count(); // Get total records based on current filters

        $query->orderBy('pp.date', $order);

        $records = $query->offset($offset)
            ->limit($limit)
            ->get();

        $bulkData = [];
        $bulkData['total'] = $total;
        $rows = [];
        $no = $offset + 1;


        foreach ($records as $row) {
            $pp_id = $row->id;

            $remaining_days = DB::table('purchase_package as pp')
                ->join('subject_package as sp', 'pp.package_id', '=', 'sp.id')
                ->select('sp.expiry_days', 'pp.date as purchase_date')
                ->where('pp.id', $pp_id)
                ->first();

            if ($remaining_days) {
                $purchaseDate = Carbon::parse($remaining_days->purchase_date);
                $expiryDays = $remaining_days->expiry_days;

                $expirationDate = $purchaseDate->addDays($expiryDays);
                $formattedExpirationDate = $expirationDate->format('Y-m-d');

                $remainingDays = $expirationDate->diffInDays(Carbon::now());

                   
            }
            $expire_soon = DB::table('package_usage')
                ->select('remaining_session')
                ->where('purchase_package_id', $pp_id)
                ->orderBy('created_at', 'DESC')
                ->first();

            if ($remainingDays < 7) {
                DB::table('purchase_package')
                    ->where('id', $pp_id)
                    ->where('status', '!=', '1')
                    ->update([
                        'status' => 2
                ]);
            }
            $operate = '';
            $operate = BootstrapTableService::editButton(route('subject-package.historyEdit', $row->id), false);
            $operate .= BootstrapTableService::deleteButton(route('subject-package.purchaseDestroy', $row->id), false);
            $tempRow = (array) $row;
            $tempRow['expiry_date'] = $formattedExpirationDate;
            $tempRow['remaining'] = $expire_soon->remaining_session ?? null;
            $tempRow['operate'] = $operate;
            $tempRow['no'] = $no++;
            $tempRow['thumbnail'] = $row->thumbnail_url;

            $rows[] = $tempRow;
        }

        $bulkData['rows'] = $rows;

        return response()->json($bulkData);
    }

    public function usageShow(Request $request)
    {
        $offset = request('offset', 0);
        $limit = request('limit', 10);
        $sort = request('sort', 'id');
        $order = request('order', 'DESC');
        $startDate = request('start_date');
        $endDate = request('end_date');
        $package_id = request('package_id');
        $classId = request('class_id');
        $studentId = request('student_id');

        $query = DB::table('package_usage as pu')
            ->join('purchase_package as pp', 'pu.purchase_package_id', '=', 'pp.id') // create connection for package name and stuff
            ->leftJoin('subject_package as sp', 'pp.package_id', '=', 'sp.id') // create connection for package name and stuff
            ->leftJoin('files as f', 'sp.thumbnail_id', '=', 'f.id') // join files for thumbnail
            ->join('students as s', 's.id', '=', 'pu.student_id') // join for student name
            ->join('users as u', 'u.id', '=', 's.user_id') // ^^^
            ->join('class_sections as cs', 's.class_section_id', '=', 'cs.id')
            ->where('pu.school_id', Auth::user()->school_id)
            ->select(
                DB::raw('ROW_NUMBER() OVER(ORDER BY sp.updated_at DESC) as no'),
                'pu.id',
                'sp.name',
                's.id as student_id',
                DB::raw("CONCAT(u.first_name, ' ', u.last_name) AS full_name"),
                'sp.expiry_days',
                'pu.remaining_session',
                'pp.date as purchase_date',
                'f.file_url as thumbnail_url',
                'pu.deduct',
                'pu.created_at'
            );

        //need adjust
        if ($package_id) {
            $query->where('pu.purchase_package_id', $package_id);
        }
        if (isset($id)) {
            $query->where('sp.id', $id);
            $id = $query->pluck('sp.id')->first();
        }

        if (!empty($classId)) {
            $query->where('cs.class_id', $classId);
        }

        if (!empty($studentId)) {
            $query->where('s.id', $studentId);
        }

        if ($startDate && $endDate) {
            $query->whereBetween(DB::raw('DATE(pu.created_at)'), [date('Y-m-d', strtotime($startDate)), date('Y-m-d', strtotime($endDate))]);
        }

        $total = $query->count();

        $query->orderBy('pu.created_at', $order);

        $records = $query->offset($offset)
            ->limit($limit)
            ->get();

        $bulkData = [];
        $bulkData['total'] = $total;
        $rows = [];
        $no = $offset + 1;

        foreach ($records as $row) {
            // dd($row);
            $remaining_days = DB::table('package_usage as pu')
                ->join('purchase_package as pp', 'pu.purchase_package_id', '=', 'pp.id')
                ->join('subject_package as sp', 'pp.package_id', '=', 'sp.id')
                ->select('sp.expiry_days', 'pp.date as purchase_date', 'pu.purchase_package_id')
                ->where('pp.student_id', $row->student_id)
                ->first();

            if ($remaining_days) {
                $purchaseDate = Carbon::parse($remaining_days->purchase_date);
                $expiryDays = $remaining_days->expiry_days;

                $expirationDate = $purchaseDate->addDays($expiryDays);
                $formattedExpirationDate = $expirationDate->format('d-m-Y');

                $remainingDays = $expirationDate->diffInDays(Carbon::now());

                // // Send notification for session deduction
                // if ($row->deduct < 0) {
                //     $student = DB::table('students')
                //         ->where('id', $row->student_id)
                //         ->first();
                    
                //     if ($student) {
                //         $notifyUser = [$student->guardian_id, $student->user_id];
                //         $title = 'Session Updated';
                //         $body = "Clock-in successful! You have {$row->remaining_session} sessions left (expires {$formattedExpirationDate})";
                //         $type = "session_update";
                        
                //         send_notification($notifyUser, $title, $body, $type);
                //     }
                // }

                if (Carbon::now() > $expirationDate) {
                    DB::table('purchase_package')
                        ->where('package_id', $remaining_days->purchase_package_id)
                        ->update(['status' => 1]);
                    $remainingDays = 0;
                }
            }
            $operate = '';
            $operate .= BootstrapTableService::deleteButton(route('subject-package.usageDestroy', $row->id), false);
            $tempRow = (array) $row;
            $tempRow['remaining_days'] = $remainingDays;
            $tempRow['operate'] = $operate;
            $tempRow['no'] = $no++;
            $tempRow['thumbnail'] = $row->thumbnail_url;

            $rows[] = $tempRow;
        }

        $bulkData['rows'] = $rows;

        return response()->json($bulkData);
    }

    public function edit($id)
    {
        $user = Auth::user()->school_id;

        $packages = DB::table('subject_package as sp')
            ->leftJoin('files as f', 'sp.thumbnail_id', '=', 'f.id')
            ->select(
                DB::raw('ROW_NUMBER() OVER(ORDER BY sp.updated_at DESC) as no'),
                'sp.id',
                'sp.name',
                'sp.price',
                'sp.description',
                'sp.expiry_days',
                'sp.total_sessions',
                'sp.updated_at',
                'sp.subject_id',
                'f.file_url as thumbnail_url'
            )
            ->where('sp.id', $id)->first();

        if (!$packages) {
            ResponseService::logErrorResponse($packages, 'Package not found.');
            return redirect()->route('subject-package.index')->withErrors('Package not found.');
        }

        $subjectIds = explode(',', $packages->subject_id);

        $subjects = DB::table('subjects')
            ->select('id', 'name')
            ->where('school_id', $user)
            ->get();



        return view('subject-package.edit', compact('packages', 'subjects', 'subjectIds'));
    }

    public function historyEdit($id)
    {
        $user = Auth::user()->school_id;

        $packages = DB::table('purchase_package as pp')
            ->join('subject_package as sp', 'pp.package_id', '=', 'sp.id')
            ->leftJoin('files as f', 'sp.thumbnail_id', '=', 'f.id')
            ->join('students as s', 's.id', '=', 'pp.student_id')
            ->join('users as u', 'u.id', '=', 's.user_id')
            ->select(
                'pp.id as id',
                'pp.package_id as package_id',
                'sp.name',
                's.id as student_id',
                DB::raw("CONCAT(u.first_name, ' ', u.last_name) AS full_name"),
                'pp.date as purchase_date',
                'pp.status'
            )
            ->where('pp.id', $id)
            ->first();


        $classes = $this->classes->all(['*'], ['medium', 'sections']);

        $students = DB::table('students as s')
            ->join('users as u', 's.user_id', '=', 'u.id')
            ->select('s.id', DB::raw("CONCAT(u.first_name, ' ', u.last_name) AS full_name"))
            ->get();

        $allPackages = DB::table('subject_package')
            ->select('id', 'name')
            ->where('id', '!=', $packages->package_id)
            ->get();

        $packageSales = DB::table('purchase_package')
            ->count();


        if (!$packages) {
            ResponseService::logErrorResponse($packages, 'Package not found.');
            return redirect()->route('subject-package.index')->withErrors('Package not found.');
        }

        // $subjectIds = explode(',', $packages->subject_id);it

        return view('subject-package.history_edit', ['packages' => $packages, 'allPackages' => $allPackages, 'packageSales' => $packageSales, 'students' => $students, 'classes' => $classes, 'purchase_date' => $packages->purchase_date]);
    }

    public function update(Request $request, $id)
    {
        $subjectId = implode(',', $request->subject_id);
        $request->validate([
            'name' => 'required|string',
            'description' => 'nullable|string',
            'price' => 'required|numeric',
            'expiry_days' => 'required|numeric',
            'total_sessions' => 'required|numeric',
        ]);

        $schoolId = Auth::user()->school_id;

        DB::beginTransaction();

        try {

            $subjectPackage = SubjectPackage::findOrFail($id);

            $subjectPackage->name = $request->name;
            $subjectPackage->description = $request->description ?? '';
            $subjectPackage->price = $request->price;
            $subjectPackage->expiry_days = $request->expiry_days;
            $subjectPackage->total_sessions = $request->total_sessions;
            $subjectPackage->updated_at = Carbon::now();
            $subjectPackage->subject_id = $subjectId;

            if ($request->hasFile('thumbnail')) {
                if (FileSizeLimitService::checkFileSizeLimit($schoolId)) {
                    throw new \Exception('Storage capacity not enough');
                }

                if ($subjectPackage->thumbnail_id) {
                    $oldFile = File::find($subjectPackage->thumbnail_id);
                    if ($oldFile) {
                        Storage::disk('public')->delete($oldFile->file_url);
                        $oldFile->delete();
                    }
                }

                $thumbnailFile = $request->file('thumbnail');
                $thumbnailSize = $thumbnailFile->getSize();
                $thumbnailSizeKB = round($thumbnailSize / 1024, 2);

                $file = File::create([
                    'modal_type' => SubjectPackage::class,
                    'modal_id' => $subjectPackage->id,
                    'file_name' => basename($thumbnailFile->getClientOriginalName(), '.' . $thumbnailFile->getClientOriginalExtension()),
                    'file_url' => $thumbnailFile->store('files', 'public'),
                    'type' => 1,
                    'school_id' => $schoolId,
                    'file_size' => $thumbnailSizeKB,
                ]);

                $subjectPackage->thumbnail_id = $file->id;
            }

            $subjectPackage->save();

            DB::commit();
            return ResponseService::successResponse('Data Update Successfully');
        } catch (\Exception $e) {
            DB::rollback();
            return ResponseService::errorResponse($e->getMessage());  // Return the error message
        }
    }

    public function historyUpdate(Request $request, $id)
    {
        $request->validate([
            'package_id' => 'required|numeric',
            'date' => 'required|date',
            'student_id' => 'required|numeric',
            'status' => 'required|numeric',
        ]);

        $formattedDate = Carbon::createFromFormat('d-m-Y', $request->date);

        DB::beginTransaction();
        try {
            $purchasePackage = DB::table('purchase_package')->where('id', $id)->first();

            if (!$purchasePackage) {
                abort(404, 'Subject Package not found.');
            }

            DB::table('purchase_package')
                ->where('id', $id)
                ->update([
                    'package_id' => $request->package_id,
                    'student_id' => $request->student_id,
                    'date' => $formattedDate,
                    'status' => $request->status,
                    'updated_at' => Carbon::now()
                ]);

            $packageInfo = DB::table('purchase_package as pp')
                ->join('subject_package as sp', 'pp.package_id', '=', 'sp.id')
                ->join('students as s', 's.id', '=', 'pp.student_id')
                ->join('users as u', 'u.id', '=', 's.user_id')
                ->select('sp.expiry_days', 'pp.date as purchase_date', 'sp.name as package_name', 
                        's.guardian_id', 's.user_id', 'u.first_name', 'u.last_name')
                ->where('pp.id', $id)
                ->first();

            if ($packageInfo) {
                $purchaseDate = Carbon::parse($packageInfo->purchase_date);
                $expiryDays = $packageInfo->expiry_days;
                $expirationDate = $purchaseDate->addDays($expiryDays);
                $formattedExpirationDate = $expirationDate->format('Y-m-d');
                $formattedExpiryTime = $expirationDate->format('h:i A');
                $remainingDays = $expirationDate->diffInDays(Carbon::now());
                $notifyUser = [$packageInfo->guardian_id, $packageInfo->user_id];

                if (Carbon::now() >= $expirationDate) {
                    DB::table('purchase_package')
                        ->where('id', $id)
                        ->update(['status' => 1]);

                    // Post-Expiry Notification
                    if (Carbon::now()->diffInDays($expirationDate) <= 1) {
                        $title = 'Package Expired';
                        $body = "❌ Access expired! Renew now to restore your {$packageInfo->package_name}";
                        send_notification($notifyUser, $title, $body, "package_expiry");
                    }
                } else if ($remainingDays <= 30) {
                    DB::table('purchase_package')
                        ->where('id', $id)
                        ->update(['status' => 2]);

                    if ($remainingDays == 7) {
                        $title = '7 Days Until Package Expiry';
                        $body = "🔔 Friendly reminder: Your {$packageInfo->package_name} expires in 7 days (on {$formattedExpirationDate}). Renew now to avoid disruption!";
                        send_notification($notifyUser, $title, $body, "package_expiry");
                    }
                    else if ($remainingDays == 3) {
                        $title = '3 Days Until Package Expiry';
                        $body = "⚠️ Only 3 days left! Your {$packageInfo->package_name} expires soon. Renew now to avoid disruption!";
                        send_notification($notifyUser, $title, $body, "package_expiry");
                    }
                    else if ($remainingDays == 1) {
                        $title = 'Package Expires Tomorrow';
                        $body = "⏳ LAST DAY! Your {$packageInfo->package_name} expires tomorrow at {$formattedExpiryTime}. Renew now to avoid disruption!";
                        send_notification($notifyUser, $title, $body, "package_expiry");
                    }
                } else {
                    DB::table('purchase_package')
                        ->where('id', $id)
                        ->update(['status' => 0]);
                }
            }

            DB::commit();
            return ResponseService::successResponse('Data Update Successfully');
        } catch (\Exception $e) {
            DB::rollback();
            return ResponseService::errorResponse($e->getMessage());
        }
    }

    public function destroy($id)
    {
        try {
            $packages = DB::table('subject_package as sp')
                ->where('sp.id', $id)
                ->first();

            if ($packages) {
                $file = DB::table('files')
                    ->where('id', $packages->thumbnail_id)
                    ->first();

                if ($file) {
                    $thumbnailPath = $file->file_url;

                    if (Storage::disk('public')->exists($thumbnailPath)) {
                        Storage::disk('public')->delete($thumbnailPath);
                    }

                    DB::table('files')->where('id', $file->id)->delete();
                }

                DB::table('subject_package')->where('id', $id)->delete();

                return response()->json(['success' => true, 'message' => 'Record deleted successfully.']);
            }
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Error deleting record: ' . $e->getMessage()]);
        }
    }

    public function purchaseDestroy($id)
    {
        try {
            $packages = DB::table('purchase_package as pp')
                ->where('pp.id', $id)
                ->first();

            $packageUsage = DB::table('package_usage as pu')
                ->where('pu.purchase_package_id', $id)
                ->first();

            if ($packages && $packageUsage) {
                DB::table('purchase_package')->where('id', $id)->delete();
                DB::table('package_usage')->where('purchase_package_id', $id)->delete();

                return response()->json(['success' => true, 'message' => 'Record deleted successfully.']);
            }
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Error deleting record: ' . $e->getMessage()]);
        }
    }

    public function usageDestroy($id)
    {
        try {
            $packages = DB::table('package_usage as pu')
                ->where('pu.id', $id)
                ->first();

            if ($packages) {
                DB::table('package_usage')->where('id', $id)->delete();

                return response()->json(['success' => true, 'message' => 'Record deleted successfully.']);
            }
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Error deleting record: ' . $e->getMessage()]);
        }
    }

    public function getStudents()
    {
        $search = request('search');
        
        $students = DB::table('students as s')
            ->join('users as u', 'u.id', '=', 's.user_id')
            ->where('s.school_id', Auth::user()->school_id)
            ->where('u.status', 1)  // Only active students (status = 1)
            ->whereNull('u.deleted_at')  // Not deleted
            ->where(function($query) use ($search) {
                if ($search) {
                    $query->where(DB::raw("CONCAT(u.first_name, ' ', u.last_name)"), 'LIKE', "%{$search}%")
                          ->orWhere('u.first_name', 'LIKE', "%{$search}%")
                          ->orWhere('u.last_name', 'LIKE', "%{$search}%");
                }
            })
            ->select(
                's.id',
                DB::raw("CONCAT(u.first_name, ' ', u.last_name) as text")
            )
            ->get();

        return response()->json($students);
    }
}
