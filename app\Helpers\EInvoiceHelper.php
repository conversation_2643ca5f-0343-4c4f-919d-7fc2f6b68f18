<?php

namespace App\Helpers;

use Exception;
use Illuminate\Support\Facades\DB;
use App\Services\ResponseService;
use Klsheng\Myinvois\MyInvoisClient;

use Klsheng\Myinvois\Ubl\Invoice;
use Klsheng\Myinvois\Ubl\CreditNote;
use Klsheng\Myinvois\Ubl\DebitNote;
use Klsheng\Myinvois\Ubl\RefundNote;
use Klsheng\Myinvois\Ubl\SelfBilledInvoice;
use Klsheng\Myinvois\Ubl\SelfBilledCreditNote;
use Klsheng\Myinvois\Ubl\SelfBilledDebitNote;
use Klsheng\Myinvois\Ubl\SelfBilledRefundNote;
use Klsheng\Myinvois\Ubl\Address;
use Klsheng\Myinvois\Ubl\AddressLine;
use Klsheng\Myinvois\Ubl\Country;
use Klsheng\Myinvois\Ubl\LegalEntity;
use Klsheng\Myinvois\Ubl\Contact;
use Klsheng\Myinvois\Ubl\AccountingParty;
use Klsheng\Myinvois\Ubl\Party;
use Klsheng\Myinvois\Ubl\PartyIdentification;
use Klsheng\Myinvois\Ubl\AllowanceCharge;
use Klsheng\Myinvois\Ubl\Shipment;
use Klsheng\Myinvois\Ubl\Delivery;
use Klsheng\Myinvois\Ubl\TaxTotal;
use Klsheng\Myinvois\Ubl\TaxScheme;
use Klsheng\Myinvois\Ubl\TaxCategory;
use Klsheng\Myinvois\Ubl\TaxSubTotal;
use Klsheng\Myinvois\Ubl\Item;
use Klsheng\Myinvois\Ubl\CommodityClassification;
use Klsheng\Myinvois\Ubl\Price;
use Klsheng\Myinvois\Ubl\ItemPriceExtension;
use Klsheng\Myinvois\Ubl\InvoiceLine;
use Klsheng\Myinvois\Ubl\CreditNoteLine;
use Klsheng\Myinvois\Ubl\DebitNoteLine;
use Klsheng\Myinvois\Ubl\AdditionalDocumentReference;
use Klsheng\Myinvois\Ubl\LegalMonetaryTotal;
use Klsheng\Myinvois\Ubl\InvoicePeriod;
use Klsheng\Myinvois\Ubl\PayeeFinancialAccount;
use Klsheng\Myinvois\Ubl\PaymentMeans;
use Klsheng\Myinvois\Ubl\PaymentTerms;
use Klsheng\Myinvois\Ubl\BillingReference;
use Klsheng\Myinvois\Ubl\PrepaidPayment;
use Klsheng\Myinvois\Ubl\TaxExchangeRate;
use Klsheng\Myinvois\Ubl\InvoiceDocumentReference;
use Klsheng\Myinvois\Ubl\Builder\XmlDocumentBuilder;
use Klsheng\Myinvois\Ubl\Builder\JsonDocumentBuilder;
use Klsheng\Myinvois\Ubl\Constant\MSICCodes;
use Klsheng\Myinvois\Ubl\Constant\InvoiceTypeCodes;

class EInvoiceHelper
{
    private const PROD_MODE = true;
    private const P12_FILE_PATH = 'resources/cert/CERT_19054711_openssl3+.p12';
    private const P12_PASSWORD = 'neIZqyDRwE';
    private const client_id = 'ea730673-1a7c-4b71-9d10-b412c3c0a94d';
    private const client_secret = '6396397d-79a2-4e6e-9f34-ea5926bd2fe9';
    private const client_secret_2 = '2afdcb61-45eb-4bbb-b92c-bfa1ced85165';
    /**
     * Load a .p12 certificate file
     * 
     * @param string $filePath Path to the .p12 file
     * @param string $password Password for the certificate
     * @return string The file contents
     * @throws Exception If file cannot be loaded
     */
    public static function loadP12Certificate(string $filePath, string $password): string
    {
        // Check if the file exists and is readable
        if (!file_exists($filePath)) {
            throw new Exception('The .p12 file does not exist: ' . $filePath);
        }
        if (!is_readable($filePath)) {
            throw new Exception('The .p12 file is not readable: ' . $filePath);
        }

        // Load the .p12 file
        $p12Content = file_get_contents($filePath);
        if ($p12Content === false) {
            throw new Exception('Failed to read the .p12 file');
        }

        return $p12Content;
    }

    /**
     * Initialize and login to MyInvois client
     * 
     * @param string $clientId
     * @param string $clientSecret
     * @param string $clientTin
     * @param bool $prodMode
     * @param string $p12FilePath
     * @param string $p12Password
     * @return MyInvoisClient
     * @throws Exception If initialization fails
     */
    private static function initializeMyInvoisClient(
        string $clientId,
        string $clientSecret,
        string $clientTin,
        bool $prodMode = true,
        string $p12FilePath = '',
        string $p12Password = ''
    ): MyInvoisClient {
        $client = new MyInvoisClient($clientId, $clientSecret, $prodMode);
        
        if ($p12FilePath && $p12Password) {
            self::loadP12Certificate($p12FilePath, $p12Password);
        }
        
        $client->login($clientTin);
        return $client;
    }
    
    private static function getClientCredentials(int $schoolId)
    {
        $settings = DB::table('school_settings')
        ->where('school_id', $schoolId)
        ->whereIn('name', ['client_id', 'client_secret_1', 'client_secret_2', 'client_tin'])
        ->pluck('data', 'name');
        
        if (!$settings->has('client_id') || !$settings->has('client_secret_1') || !$settings->has('client_secret_2') || !$settings->has('client_tin')) {
            return [
                'success' => false,
                'message' => 'Required MyInvois client credentials not found in school settings',
                'errors' => [
                    'client_credentials' => 'Missing required credentials in school settings'
                ]
            ];
        }
        
        return [
            'success' => true,
            'client_id' => self::client_id,
            'client_secret' => self::client_secret,
            'client_secret_2' => self::client_secret_2,
            'client_tin' => $settings['client_tin']
        ];
    }

    private static function initializeClient(int $schoolId)
    {
       $credentials = self::getClientCredentials($schoolId);
       if (!$credentials['success']) {
           return $credentials;
       }
       $clientId = $credentials['client_id'];
       $clientSecret = $credentials['client_secret'];
       $clientTin = $credentials['client_tin'];
       
       $prodMode = self::PROD_MODE;
       $p12FilePath = base_path(self::P12_FILE_PATH);
       $p12Password = self::P12_PASSWORD;
       
       try {
           return [
               'success' => true,
               'message' => 'Client initialized successfully',
               'data' => self::initializeMyInvoisClient($clientId, $clientSecret, $clientTin, $prodMode, $p12FilePath, $p12Password)
           ];
       } catch (\Exception $e) {
           return [
               'success' => false,
               'message' => 'Client initialization failed',
               'errors' => $e->getMessage()
           ];
       }
    }
    
    public static function searchTaxPayerTin($schoolId, string $taxPayerName, string $idType, string $idValue)
    {
        $client = self::initializeClient($schoolId);
        if (!$client['success']) {
            return $client;
        }
        $client = $client['data'];
        
        try {
            return [
                'success' => true,
                'message' => 'Tax payer search successful',
                'data' => $client->searchTaxPayerTin($taxPayerName, $idType, $idValue)
            ];
        } catch (\Exception $exception) {
            $errorMessage = $exception->getMessage();
            $result = json_decode($errorMessage);
            
            return [
                'success' => false,
                'message' => 'Error searching tax payer TIN',
                'errors' => json_last_error() === JSON_ERROR_NONE ? $result : $errorMessage
            ];
        }
    }
    
    public static function validateTaxPayerTin(int $schoolId, string $tin, string $idType, string $idValue)
    {
        $client = self::initializeClient($schoolId);
        if (!$client['success']) {
            return $client;
        }
        $client = $client['data'];
        
        try {
            $result = $client->validateTaxPayerTin($tin, $idType, $idValue);
            
            return [
                'success' => true,
                'message' => 'Tax payer validation successful',
                'data' => $result
            ];
        } catch (\Exception $exception) {
            $errorMessage = $exception->getMessage();
            $result = json_decode($errorMessage);
            
            return [
                'success' => false,
                'message' => 'Error validating tax payer',
                'errors' => json_last_error() === JSON_ERROR_NONE ? $result : $errorMessage
            ];
        }
    }
    
    public static function getNotifications(int $schoolId, $dateFrom, $dateTo, $type, $language, $status, $pageNo, $pageSize)
    {
        $client = self::initializeClient($schoolId);
        if (!$client['success']) {
            return $client;
        }
        $client = $client['data'];
        
        try {
            return [
                'success' => true,
                'message' => 'Notifications retrieved successfully',
                'data' => $client->getNotifications($dateFrom, $dateTo, $type, $language, $status, $pageNo, $pageSize)
            ];
        } catch (\Exception $exception) {
            $errorMessage = $exception->getMessage();
            $result = json_decode($errorMessage);
            
            return [
                'success' => false,
                'message' => 'Error getting notifications',
                'errors' => json_last_error() === JSON_ERROR_NONE ? $result : $errorMessage
            ];
        }
    }
    
    public static function getTaxPayerFromQrcode(int $schoolId, string $qrCodeText)
    {
        $client = self::initializeClient($schoolId);
        if (!$client['success']) {
            return $client;
        }
        $client = $client['data'];
        
        try {
            return [
                'success' => true,
                'message' => 'Tax payer retrieved successfully',
                'data' => $client->getTaxPayerFromQrcode($qrCodeText)
            ];
        } catch (\Exception $exception) {
            $errorMessage = $exception->getMessage();
            $result = json_decode($errorMessage);
            
            return [
                'success' => false,
                'message' => 'Error retrieving tax payer from QR code',
                'errors' => json_last_error() === JSON_ERROR_NONE ? $result : $errorMessage
            ];
        }
    }

    public static function cancelDocument(int $schoolId, string $id, string $reason)
    {
        $client = self::initializeClient($schoolId);
        if (!$client['success']) {
            return $client;
        }
        $client = $client['data'];
        
        try {
            return [
                'success' => true,
                'message' => 'Document cancelled successfully',
                'data' => $client->cancelDocument($id, $reason)
            ];
        } catch (\Exception $exception) {
            $errorMessage = $exception->getMessage();
            $result = json_decode($errorMessage);
            
            return [
                'success' => false,
                'message' => 'Error cancelling document',
                'errors' => json_last_error() === JSON_ERROR_NONE ? $result : $errorMessage
            ];
        }
    }

    public static function rejectDocument(int $schoolId, string $id, string $reason)
    {
        $client = self::initializeClient($schoolId);
        if (!$client['success']) {
            return $client;
        }
        $client = $client['data'];
        
        try {
            return [
                'success' => true,
                'message' => 'Document rejected successfully',
                'data' => $client->rejectDocument($id, $reason)
            ];
        } catch (\Exception $exception) {
            $errorMessage = $exception->getMessage();
            $result = json_decode($errorMessage);
            
            return [
                'success' => false,
                'message' => 'Error rejecting document',
                'errors' => json_last_error() === JSON_ERROR_NONE ? $result : $errorMessage
            ];
        }
    }

    public static function generateDocumentQrCodeUrl(int $schoolId, string $id, string $longId)
    {
        $client = self::initializeClient($schoolId);
        if (!$client['success']) {
            return $client;
        }
        $client = $client['data'];
        
        try {
            return [
                'success' => true,
                'message' => 'Document QR code URL generated successfully',
                'data' => $client->generateDocumentQrCodeUrl($id, $longId)
            ];
        } catch (\Exception $exception) {
            $errorMessage = $exception->getMessage();
            $result = json_decode($errorMessage);
            
            return [
                'success' => false,
                'message' => 'Error generating document QR code URL',
                'errors' => json_last_error() === JSON_ERROR_NONE ? $result : $errorMessage
            ];
        }
    }
 
    public static function getSubmission(int $schoolId, string $id)
    {
        $client = self::initializeClient($schoolId);
        if (!$client['success']) {
            return $client;
        }
        $client = $client['data'];
        
        try {
            return [
                'success' => true,
                'message' => 'Submission retrieved successfully',
                'data' => $client->getSubmission($id)
            ];
        } catch (\Exception $exception) {
            $errorMessage = $exception->getMessage();
            $result = json_decode($errorMessage);
            
            return [
                'success' => false,
                'message' => 'Error getting submission',
                'errors' => json_last_error() === JSON_ERROR_NONE ? $result : $errorMessage
            ];
        }
    }

    public static function getDocument(int $schoolId, string $id)
    {
        $client = self::initializeClient($schoolId);
        if (!$client['success']) {
            return $client;
        }
        $client = $client['data'];
        
        try {
            return [
                'success' => true,
                'message' => 'Document retrieved successfully',
                'data' => $client->getDocument($id)
            ];
        } catch (\Exception $exception) {
            $errorMessage = $exception->getMessage();
            $result = json_decode($errorMessage);
            
            return [
                'success' => false,
                'message' => 'Error getting document',
                'errors' => json_last_error() === JSON_ERROR_NONE ? $result : $errorMessage
            ];
        }
    }

    public static function getDocumentDetail(int $schoolId, string $id)
    {
        $client = self::initializeClient($schoolId);
        if (!$client['success']) {
            return $client;
        }
        $client = $client['data'];
        
        try {
            return [
                'success' => true,
                'message' => 'Document detail retrieved successfully',
                'data' => $client->getDocumentDetail($id)
            ];
        } catch (\Exception $exception) {
            $errorMessage = $exception->getMessage();
            $result = json_decode($errorMessage);
            
            return [
                'success' => false,
                'message' => 'Error getting document detail',
                'errors' => json_last_error() === JSON_ERROR_NONE ? $result : $errorMessage
            ];
        }
    }

    public static function searchDocuments(
        int $schoolId,
        string $submissionDateFrom,
        string $submissionDateTo,
        ?string $id = null,
        int $pageNo = 1,
        int $pageSize = 100,
        ?string $issueDateFrom = null,
        ?string $issueDateTo = null,
        ?string $direction = null,
        ?string $status = null,
        ?string $documentType = null,
        ?string $searchQuery = null
    ) {
        $client = self::initializeClient($schoolId);
        if (!$client['success']) {
            return $client;
        }
        $client = $client['data'];
        
        try {
            // Convert date strings to DateTime objects
            $submissionFrom = new \DateTime($submissionDateFrom);
            $submissionTo = new \DateTime($submissionDateTo);
            $issueFrom = $issueDateFrom ? new \DateTime($issueDateFrom) : null;
            $issueTo = $issueDateTo ? new \DateTime($issueDateTo) : null;
            
            return [
                'success' => true,
                'message' => 'Documents retrieved successfully',
                'data' => $client->searchDocuments(
                    $id,
                    $submissionFrom,
                    $submissionTo,
                    $pageNo,
                    $pageSize,
                    $issueFrom,
                    $issueTo,
                    $direction,
                    $status,
                    $documentType,
                    $searchQuery
                )
            ];
        } catch (\Exception $exception) {
            $errorMessage = $exception->getMessage();
            $result = json_decode($errorMessage);
            
            return [
                'success' => false,
                'message' => 'Error searching documents',
                'errors' => json_last_error() === JSON_ERROR_NONE ? $result : $errorMessage
            ];
        }
    }

    public static function getRecentDocuments(
        int $schoolId,
        ?int $pageNo = null,
        ?int $pageSize = null,
        ?string $submissionDateFrom = null,
        ?string $submissionDateTo = null,
        ?string $issueDateFrom = null,
        ?string $issueDateTo = null,
        ?string $direction = null,
        ?string $status = null,
        ?string $documentType = null,
        ?string $receiverId = null,
        ?string $receiverIdType = null,
        ?string $receiverTin = null,
        ?string $issuerId = null,
        ?string $issuerIdType = null,
        ?string $issuerTin = null
    ) {
        $client = self::initializeClient($schoolId);
        if (!$client['success']) {
            return $client;
        }
        $client = $client['data'];
        
        try {
            // Convert date strings to DateTime objects if provided
            $submissionFrom = $submissionDateFrom ? new \DateTime($submissionDateFrom) : null;
            $submissionTo = $submissionDateTo ? new \DateTime($submissionDateTo) : null;
            $issueFrom = $issueDateFrom ? new \DateTime($issueDateFrom) : null;
            $issueTo = $issueDateTo ? new \DateTime($issueDateTo) : null;
            
            return [
                'success' => true,
                'message' => 'Recent documents retrieved successfully',
                'data' => $client->getRecentDocuments(
                    $pageNo,
                    $pageSize,
                    $submissionFrom,
                    $submissionTo,
                    $issueFrom,
                    $issueTo,
                    $direction,
                    $status,
                    $documentType,
                    $receiverId,
                    $receiverIdType,
                    $receiverTin,
                    $issuerId,
                    $issuerIdType,
                    $issuerTin
                )
            ];
        } catch (\Exception $exception) {
            $errorMessage = $exception->getMessage();
            $result = json_decode($errorMessage);
            
            return [
                'success' => false,
                'message' => 'Error getting recent documents',
                'errors' => json_last_error() === JSON_ERROR_NONE ? $result : $errorMessage
            ];
        }
    }
    
    public static function submitDocument(int $schoolId, array $documents)
    {
        $client = self::initializeClient($schoolId);
        if (!$client['success']) {
            return $client;
        }
        $client = $client['data'];
        
        try {
            return [
                'success' => true,
                'message' => 'Document submitted successfully',
                'data' => $client->submitDocument($documents)
            ];
        } catch (\Exception $exception) {
            $errorMessage = $exception->getMessage();
            $result = json_decode($errorMessage);
            
            return [
                'success' => false,
                'message' => 'Error submitting document',
                'errors' => json_last_error() === JSON_ERROR_NONE ? $result : $errorMessage
            ];
        }
    }
    
    public static function getAllDocumentTypes(int $schoolId)
    {
        $client = self::initializeClient($schoolId);
        if (!$client['success']) {
            return $client;
        }
        $client = $client['data'];
        
        try {
            return [
                'success' => true,
                'message' => 'Document types retrieved successfully',
                'data' => $client->getAllDocumentTypes()
            ];
        } catch (\Exception $exception) {
            $errorMessage = $exception->getMessage();
            $result = json_decode($errorMessage);
            
            return [
                'success' => false,
                'message' => 'Error getting document types',
                'errors' => json_last_error() === JSON_ERROR_NONE ? $result : $errorMessage
            ];
        }
    }
    
    public static function mapInvoiceTypeCode(string $typeCode): string
    {
        $invoiceTypeMap = [
            'invoice' => InvoiceTypeCodes::INVOICE,
            'credit_note' => InvoiceTypeCodes::CREDIT_NOTE,
            'debit_note' => InvoiceTypeCodes::DEBIT_NOTE,
            'refund_note' => InvoiceTypeCodes::REFUND_NOTE,
            'self_billed_invoice' => InvoiceTypeCodes::SELF_BILLED_INVOICE,
            'self_billed_credit_note' => InvoiceTypeCodes::SELF_BILLED_CREDIT_NOTE,
            'self_billed_debit_note' => InvoiceTypeCodes::SELF_BILLED_DEBIT_NOTE,
            'self_billed_refund_note' => InvoiceTypeCodes::SELF_BILLED_REFUND_NOTE
        ];
        
        $typeCode = strtolower($typeCode);
        return $invoiceTypeMap[$typeCode] ?? InvoiceTypeCodes::INVOICE;
    }

    public static function createXmlDocument($invoiceTypeCode, $data) {
        $builder = new XmlDocumentBuilder();
        
        // Extract all data from the JSON input
        // $invoiceTypeCode = $data['invoice_type'] ?? 'invoice';
        $id = $data['id'] ?? null;
        $supplier = $data['supplier'] ?? null;
        $customer = $data['customer'] ?? null;
        $delivery = $data['delivery'] ?? null;
        $billingReference = $data['billing_references'] ?? null;
        $prepaidPayment = $data['prepaid_payment'] ?? null;
        $documentLine = $data['document_line'] ?? null;
        $additionalDocumentReference = $data['additional_document_reference'] ?? null;
        $legalMonetaryTotal = $data['legal_monetary_total'] ?? null;
        $invoicePeriod = $data['invoice_period'] ?? null;
        $paymentMeans = $data['payment_means'] ?? null;
        $paymentTerms = $data['payment_terms'] ?? null;
        $allowanceCharges = $data['allowance_charges'] ?? null;
        $taxTotal = $data['tax_total'] ?? null;
        $taxExchangeRate = $data['tax_exchange_rate'] ?? null;
        return self::createBuilder($builder, $invoiceTypeCode, $id, $supplier, $customer, $delivery, $billingReference, $prepaidPayment, $documentLine, $additionalDocumentReference, $legalMonetaryTotal, $invoicePeriod, $paymentMeans, $paymentTerms, $allowanceCharges, $taxTotal, $taxExchangeRate);
    }

    private static function createBuilder($builder, $invoiceTypeCode, $id, $supplier, $customer, $delivery, $billingReference, $prepaidPayment, $documentLine, $additionalDocumentReference, $legalMonetaryTotal, $invoicePeriod, $paymentMeans, $paymentTerms, $allowanceCharges, $taxTotal, $taxExchangeRate)
    {
        $document = self::createDocument($invoiceTypeCode, $id, $supplier, $customer, $delivery, $billingReference, $prepaidPayment, $documentLine, $additionalDocumentReference, $legalMonetaryTotal, $invoicePeriod, $paymentMeans, $paymentTerms, $allowanceCharges, $taxTotal, $taxExchangeRate);

        $builder->setDocument($document);
        $builder->setIssuerKeys(null);
        
        $p12FilePath = base_path(self::P12_FILE_PATH);
        $p12Password = self::P12_PASSWORD;
        $builder->createSignature($p12FilePath, null, $p12Password);
        return $builder->build();
    }

    private static function getDocumentInstance($invoiceTypeCode)
    {
        switch($invoiceTypeCode) {
            case InvoiceTypeCodes::CREDIT_NOTE:
                return new CreditNote();
                break;
            case InvoiceTypeCodes::DEBIT_NOTE:
                return new DebitNote();
                break;
            case InvoiceTypeCodes::REFUND_NOTE:
                return new RefundNote();
                break;
            case InvoiceTypeCodes::SELF_BILLED_INVOICE:
                return new SelfBilledInvoice();
                break;
            case InvoiceTypeCodes::SELF_BILLED_CREDIT_NOTE:
                return new SelfBilledCreditNote();
                break;
            case InvoiceTypeCodes::SELF_BILLED_DEBIT_NOTE:
                return new SelfBilledDebitNote();
                break;
            case InvoiceTypeCodes::SELF_BILLED_REFUND_NOTE:
                return new SelfBilledRefundNote();
                break;
            default:
                return new Invoice();
                break;
        }
    }

    private static function createDocument($invoiceTypeCode, $id, $supplier, $customer, $delivery, $billingReference, $prepaidPayment, $documentLine, $additionalDocumentReference, $legalMonetaryTotal, $invoicePeriod, $paymentMeans, $paymentTerms, $allowanceCharges, $taxTotal, $taxExchangeRate)
    {
        $issueDateTime = new \DateTime('now', new \DateTimeZone('UTC'));
        $issueDateTime->modify('-1 day'); // Yesterday

        $document = self::getDocumentInstance($invoiceTypeCode);
        $document->setId($id);
        $document->setIssueDateTime($issueDateTime);

        //Set the type to support signature 
        $typeCode = $document->getInvoiceTypeCode(); // Get original type code
        $document->setInvoiceTypeCode($typeCode, '1.1'); // 1.1 is with digital signature verification

        $document = self::setBillingReference($document, $billingReference);
        $document = self::setPrepaidPayment($document, $prepaidPayment);
        $document = self::setSupplier($document, $supplier);
        $document = self::setCustomer($document, $customer);
        $document = self::setDelivery($document, $delivery);
        $document = self::setDocumentLine($document, $documentLine);
        $document = self::setAdditionalDocumentReference($document, $additionalDocumentReference);
        $document = self::setLegalMonetaryTotal($document, $legalMonetaryTotal);
        $document = self::setInvoicePeriod($document, $invoicePeriod);
        $document = self::setPaymentMeans($document, $paymentMeans);
        $document = self::setPaymentTerms($document, $paymentTerms);
        $document = self::setAllowanceCharges($document, $allowanceCharges);
        $document = self::setTaxTotal($document, $taxTotal);
        $document = self::setTaxExchangeRate($document, $taxExchangeRate);

        return $document;
    }

    private static function setBillingReference($document, $billingReferenceData)
    {
        if (!$billingReferenceData) {
            return $document;
        }
        
        $invoiceTypeCode = $document->getInvoiceTypeCode();

        // Handle multiple billing references if provided as array
        $reference = is_array($billingReferenceData) ? $billingReferenceData : [$billingReferenceData];

        // Handle AdditionalDocumentReference
        if (isset($reference['additional_document']) && isset($reference['additional_document']['id'])) {
            $billingReference = new BillingReference();
            $additionalDocumentReference = new AdditionalDocumentReference();
            $additionalDocumentReference->setId($reference['additional_document']['id']);
            $billingReference->setAdditionalDocumentReference($additionalDocumentReference);

            $document->addBillingReference($billingReference);
        }

        // Handle InvoiceDocumentReference for credit note, refund note and debit note
        if (($invoiceTypeCode == InvoiceTypeCodes::CREDIT_NOTE || 
            $invoiceTypeCode == InvoiceTypeCodes::REFUND_NOTE || 
            $invoiceTypeCode == InvoiceTypeCodes::DEBIT_NOTE ||
            $invoiceTypeCode == InvoiceTypeCodes::SELF_BILLED_CREDIT_NOTE ||
            $invoiceTypeCode == InvoiceTypeCodes::SELF_BILLED_REFUND_NOTE ||
            $invoiceTypeCode == InvoiceTypeCodes::SELF_BILLED_DEBIT_NOTE) && 
            isset($reference['invoice_document'])) {
            $billingReference = new BillingReference();
            $invoiceDocumentReference = new InvoiceDocumentReference();
            if (isset($reference['invoice_document']['id'])) {
                $invoiceDocumentReference->setId($reference['invoice_document']['id']);
            }
            if (isset($reference['invoice_document']['uuid'])) {
                $invoiceDocumentReference->setUuid($reference['invoice_document']['uuid']);
            }
            $billingReference->setInvoiceDocumentReference($invoiceDocumentReference);

            $document->addBillingReference($billingReference);
        }

        return $document;
    }

    private static function setPrepaidPayment($document, $prepaidPaymentData)
    {
        if ($prepaidPaymentData) {
            return $document;
        }
        $prepaidPayment = new PrepaidPayment();
        if (isset($prepaidPaymentData['id'])) {
            $prepaidPayment->setId($prepaidPaymentData['id']);
        }
        if (isset($prepaidPaymentData['amount'])) {
            $prepaidPayment->setPaidAmount($prepaidPaymentData['amount']);
        }
        if (isset($prepaidPaymentData['datetime'])) {
            $prepaidPayment->setPaidDateTime(new \DateTime($prepaidPaymentData['datetime']));
        }
        $document->setPrepaidPayment($prepaidPayment);

        return $document;
    }

    private static function setSupplier($document, $partyDetail)
    {
        if(empty($partyDetail)) {
            return $document;
        }

        $address = new Address();
        if (isset($partyDetail['address']['city'])) {
            $address->setCityName($partyDetail['address']['city']);
        }
        if (isset($partyDetail['address']['postcode'])) {
            $address->setPostalZone($partyDetail['address']['postcode']);
        }
        if (isset($partyDetail['address']['country_subentity_code'])) {
            $address->setCountrySubentityCode($partyDetail['address']['country_subentity_code']);
        }

        if (isset($partyDetail['address_line']) && is_array($partyDetail['address_line'])) {
            foreach ($partyDetail['address_line'] as $line) {
                $addressLine = new AddressLine();
                $addressLine->setLine($line);
                $address->addAddressLine($addressLine);
            }
        }

        if (isset($partyDetail['country_code'])) {
            $country = new Country();
            $country->setIdentificationCode($partyDetail['country_code']);
            $address->setCountry($country);
        }

        $legalEntity = new LegalEntity();
        if (isset($partyDetail['legal_entity'])) {
            $legalEntity->setRegistrationName($partyDetail['legal_entity']);
        }

        $contact = new Contact();
        if (isset($partyDetail['contact_phone'])) {
            $contact->setTelephone($partyDetail['contact_phone']);
        }
        if (isset($partyDetail['contact_email'])) {
            $contact->setElectronicMail($partyDetail['contact_email']);
        }

        $supplier = new Party();

        if (isset($partyDetail['party_identification']) && is_array($partyDetail['party_identification'])) {
            foreach($partyDetail['party_identification'] as $key => $value) {
                $partyIdentification = new PartyIdentification();
                $partyIdentification->setId($value, $key);
                $supplier->addPartyIdentification($partyIdentification);
            }
        }

        $supplier->setPostalAddress($address);
        $supplier->setLegalEntity($legalEntity);
        $supplier->setContact($contact);

        if (isset($partyDetail['msic_code'])) {
            $msicCode = $partyDetail['msic_code'];
            $msicCodeDesc = MSICCodes::getDescription($msicCode);
            $supplier->setIndustryClassificationCode($msicCode, $msicCodeDesc);
        }

        $accountingParty = new AccountingParty();
        if (isset($partyDetail['accounting_party_account_id'])) {
            $accountingParty->setAdditionalAccountID($partyDetail['accounting_party_account_id']);
        }
        $accountingParty->setParty($supplier);

        return $document->setAccountingSupplierParty($accountingParty);
    }

    private static function setCustomer($document, $partyDetail)
    {
        if(empty($partyDetail)) {
            return $document;
        }

        $address = new Address();
        if (isset($partyDetail['address']['city'])) {
            $address->setCityName($partyDetail['address']['city']);
        }
        if (isset($partyDetail['address']['postcode'])) {
            $address->setPostalZone($partyDetail['address']['postcode']);
        }
        if (isset($partyDetail['address']['country_subentity_code'])) {
            $address->setCountrySubentityCode($partyDetail['address']['country_subentity_code']);
        }

        if (isset($partyDetail['address_line']) && is_array($partyDetail['address_line'])) {
            foreach ($partyDetail['address_line'] as $line) {
                $addressLine = new AddressLine();
                $addressLine->setLine($line);
                $address->addAddressLine($addressLine);
            }
        }

        if (isset($partyDetail['country_code'])) {
            $country = new Country();
            $country->setIdentificationCode($partyDetail['country_code']);
            $address->setCountry($country);
        }

        $legalEntity = new LegalEntity();
        if (isset($partyDetail['legal_entity'])) {
            $legalEntity->setRegistrationName($partyDetail['legal_entity']);
        }

        $contact = new Contact();
        if (isset($partyDetail['contact_phone'])) {
            $contact->setTelephone($partyDetail['contact_phone']);
        }
        if (isset($partyDetail['contact_email'])) {
            $contact->setElectronicMail($partyDetail['contact_email']);
        }

        $customer = new Party();

        if (isset($partyDetail['party_identification']) && is_array($partyDetail['party_identification'])) {
            foreach($partyDetail['party_identification'] as $key => $value) {
                $partyIdentification = new PartyIdentification();
                $partyIdentification->setId($value, $key);
                $customer->addPartyIdentification($partyIdentification);
            }
        }

        $customer->setPostalAddress($address);
        $customer->setLegalEntity($legalEntity);
        $customer->setContact($contact);

        $accountingParty = new AccountingParty();
        $accountingParty->setParty($customer);
        
        return $document->setAccountingCustomerParty($accountingParty);
    }
    
    private static function setDelivery($document, $partyDetail)
    {
        if(empty($partyDetail)) {
            return $document;
        }

        $address = new Address();
        if (isset($partyDetail['address']['city'])) {
            $address->setCityName($partyDetail['address']['city']);
        }
        if (isset($partyDetail['address']['postcode'])) {
            $address->setPostalZone($partyDetail['address']['postcode']);
        }
        if (isset($partyDetail['address']['country_subentity_code'])) {
            $address->setCountrySubentityCode($partyDetail['address']['country_subentity_code']);
        }

        if (isset($partyDetail['address_line']) && is_array($partyDetail['address_line'])) {
            foreach ($partyDetail['address_line'] as $line) {
                $addressLine = new AddressLine();
                $addressLine->setLine($line);
                $address->addAddressLine($addressLine);
            }
        }

        if (isset($partyDetail['country_code'])) {
            $country = new Country();
            $country->setIdentificationCode($partyDetail['country_code']);
            $address->setCountry($country);
        }

        $legalEntity = new LegalEntity();
        if (isset($partyDetail['legal_entity'])) {
            $legalEntity->setRegistrationName($partyDetail['legal_entity']);
        }

        $deliveryParty = new Party();

        if (isset($partyDetail['party_identification']) && is_array($partyDetail['party_identification'])) {
            foreach($partyDetail['party_identification'] as $key => $value) {
                $partyIdentification = new PartyIdentification();
                $partyIdentification->setId($value, $key);
                $deliveryParty->addPartyIdentification($partyIdentification);
            }
        }

        $deliveryParty->setPostalAddress($address);
        $deliveryParty->setLegalEntity($legalEntity);

        $shipment = new Shipment();
        if (isset($partyDetail['shipment']['id'])) {
            $shipment->setId($partyDetail['shipment']['id']);
        }

        if (isset($partyDetail['shipment']['allowance_charge']['amount'])) {
            $freightAllowanceCharge = new AllowanceCharge();
            $freightAllowanceCharge->setAmount($partyDetail['shipment']['allowance_charge']['amount']);
            
            if (isset($partyDetail['shipment']['allowance_charge']['charge_indicator'])) {
                $freightAllowanceCharge->setChargeIndicator($partyDetail['shipment']['allowance_charge']['charge_indicator']);
            }
            if (isset($partyDetail['shipment']['allowance_charge']['reason'])) {
                $freightAllowanceCharge->setAllowanceChargeReason($partyDetail['shipment']['allowance_charge']['reason']);
            }
            
            $shipment->setFreightAllowanceCharge($freightAllowanceCharge);
        }

        $delivery = new Delivery();
        $delivery->setDeliveryParty($deliveryParty);
        $delivery->setShipment($shipment);

        return $document->setDelivery($delivery);
    }
    
    private static function setDocumentLine($document, $lineData)
    {
        if(empty($lineData)) {
            return $document;
        }

        $documentLines = [];
        foreach ($lineData as $line) {
            $allowanceCharges = [];
            if (isset($line['allowance_charges']) && is_array($line['allowance_charges'])) {
                foreach ($line['allowance_charges'] as $charge) {
                    $allowanceCharge = new AllowanceCharge();
                    if (isset($charge['charge_indicator'])) {
                        $allowanceCharge->setChargeIndicator($charge['charge_indicator']);
                    }
                    if (isset($charge['reason'])) {
                        $allowanceCharge->setAllowanceChargeReason($charge['reason']);
                    }
                    if (isset($charge['multiplier'])) {
                        $allowanceCharge->setMultiplierFactorNumeric($charge['multiplier']);
                    }
                    if (isset($charge['amount'])) {
                        $allowanceCharge->setAmount($charge['amount']);
                    }
                    $allowanceCharges[] = $allowanceCharge;
                }
            }

            $taxTotal = new TaxTotal();
            if (isset($line['tax_total']['amount'])) {
                $taxTotal->setTaxAmount($line['tax_total']['amount']);
            }
            if (isset($line['tax_sub_totals']) && is_array($line['tax_sub_totals'])) {
                foreach ($line['tax_sub_totals'] as $subTotal) {
                    $taxScheme = new TaxScheme();
                    if (isset($subTotal['tax_scheme']['id'])) {
                        $taxScheme->setId($subTotal['tax_scheme']['id']);
                    }

                    $taxCategory = new TaxCategory();
                    if (isset($subTotal['tax_category']['id'])) {
                        $taxCategory->setId($subTotal['tax_category']['id']);
                    }
                    if (isset($subTotal['tax_category']['percent'])) {
                        $taxCategory->setPercent($subTotal['tax_category']['percent']);
                    }
                    if (isset($subTotal['tax_category']['tax_exemption_reason'])) {
                        $taxCategory->setTaxExemptionReason($subTotal['tax_category']['tax_exemption_reason']);
                    }
                    if (isset($subTotal['tax_scheme']['id'])) {
                        $taxCategory->setTaxScheme($taxScheme);
                    }

                    $taxSubTotal = new TaxSubTotal();
                    if (isset($subTotal['taxable_amount'])) {
                        $taxSubTotal->setTaxableAmount($subTotal['taxable_amount']);
                    }
                    if (isset($subTotal['tax_amount'])) {
                        $taxSubTotal->setTaxAmount($subTotal['tax_amount']);
                    }
                    if (isset($subTotal['percent'])) {
                        $taxSubTotal->setPercent($subTotal['percent']);
                    }
                    $taxSubTotal->setTaxCategory($taxCategory);
                    $taxTotal->addTaxSubTotal($taxSubTotal);
                }
            }

            $item = new Item();
            if (isset($line['item']['description'])) {
                $item->setDescription($line['item']['description']);
            }

            // if(isset($line['item']['country'])){
            //     $country = new Country();
            //     $country->setIdentificationCode($line['item']['country']);
            //     $item->setCountry($country);
            // }

            if (isset($line['item']['classifications']) && is_array($line['item']['classifications'])) {
                foreach ($line['item']['classifications'] as $classification) {
                    $commodityClassification = new CommodityClassification();
                    if (isset($classification['code']) && isset($classification['type'])) {
                        $commodityClassification->setItemClassificationCode($classification['code'], $classification['type']);
                    }
                    $item->addCommodityClassification($commodityClassification);
                }
            }

            $price = new Price();
            if (isset($line['price']['amount'])) {
                $price->setPriceAmount($line['price']['amount']);
            }

            $itemPriceExtension = new ItemPriceExtension();
            if (isset($line['price_extension']['amount'])) {
                $itemPriceExtension->setAmount($line['price_extension']['amount']);
            }

            $documentLine = new InvoiceLine();
            if (isset($line['id'])) {
                $documentLine->setId($line['id']);
            }
            if (isset($line['quantity']) && isset($line['unit'])) {
                $documentLine->setInvoicedQuantity($line['quantity'], $line['unit']);
            }
            if (isset($line['quantity']) && !isset($line['unit'])) {
                $documentLine->setInvoicedQuantity($line['quantity']);
            }
            if (isset($line['line_amount'])) {
                $documentLine->setLineExtensionAmount($line['line_amount']);
            }
            if(isset($line['line_extension_amount'])) {
                $documentLine->setLineExtensionAmount($line['line_extension_amount']);
            }
            $documentLine->setAllowanceCharges($allowanceCharges);
            $documentLine->setTaxTotal($taxTotal);
            $documentLine->setItem($item);
            $documentLine->setPrice($price);
            $documentLine->setItemPriceExtension($itemPriceExtension);
            $documentLines[] = $documentLine;
        }

        return $document->setInvoiceLines($documentLines);
    }

    private static function setAdditionalDocumentReference($document, $additionalDocumentReferenceData)
    {
        if(empty($additionalDocumentReferenceData)) {
            return $document;
        }
        $additionalDocumentReferences = [];

        if (isset($additionalDocumentReferenceData) && is_array($additionalDocumentReferenceData)) {
            foreach ($additionalDocumentReferenceData as $ref) {
                $additionalDocumentReference = new AdditionalDocumentReference();
                
                if (isset($ref['id'])) {
                    $additionalDocumentReference->setId($ref['id']);
                }
                if (isset($ref['document_type'])) {
                    $additionalDocumentReference->setDocumentType($ref['document_type']);
                }
                if (isset($ref['description'])) {
                    $additionalDocumentReference->setDocumentDescription($ref['description']);
                }
                
                $additionalDocumentReferences[] = $additionalDocumentReference;
            }
        }

        return $document->setAdditionalDocumentReferences($additionalDocumentReferences);
    }

    private static function setLegalMonetaryTotal($document, $legalMonetaryTotalData)
    {
        if(empty($legalMonetaryTotalData)) {
            return $document;
        }
        $legalMonetaryTotal = new LegalMonetaryTotal();
        
        if (isset($legalMonetaryTotalData['line_extension_amount'])) {
            $legalMonetaryTotal->setLineExtensionAmount($legalMonetaryTotalData['line_extension_amount']);
        }
        if (isset($legalMonetaryTotalData['tax_exclusive_amount'])) {
            $legalMonetaryTotal->setTaxExclusiveAmount($legalMonetaryTotalData['tax_exclusive_amount']);
        }
        if (isset($legalMonetaryTotalData['tax_inclusive_amount'])) {
            $legalMonetaryTotal->setTaxInclusiveAmount($legalMonetaryTotalData['tax_inclusive_amount']);
        }
        if (isset($legalMonetaryTotalData['allowance_total_amount'])) {
            $legalMonetaryTotal->setAllowanceTotalAmount($legalMonetaryTotalData['allowance_total_amount']);
        }
        if (isset($legalMonetaryTotalData['charge_total_amount'])) {
            $legalMonetaryTotal->setChargeTotalAmount($legalMonetaryTotalData['charge_total_amount']);
        }
        if (isset($legalMonetaryTotalData['payable_rounding_amount'])) {
            $legalMonetaryTotal->setPayableRoundingAmount($legalMonetaryTotalData['payable_rounding_amount']);
        }
        if (isset($legalMonetaryTotalData['payable_amount'])) {
            $legalMonetaryTotal->setPayableAmount($legalMonetaryTotalData['payable_amount']);
        }

        return $document->setLegalMonetaryTotal($legalMonetaryTotal);
    }

    private static function setInvoicePeriod($document, $invoicePeriodData)
    {
        if(empty($invoicePeriodData)) {
            return $document;
        }
        $invoicePeriod = new InvoicePeriod();
        
        if (isset($invoicePeriodData['start_date'])) {
            $invoicePeriod->setStartDate(new \DateTime($invoicePeriodData['start_date']));
        }
        if (isset($invoicePeriodData['end_date'])) {
            $invoicePeriod->setEndDate(new \DateTime($invoicePeriodData['end_date']));
        }
        if (isset($invoicePeriodData['description'])) {
            $invoicePeriod->setDescription($invoicePeriodData['description']);
        }

        return $document->setInvoicePeriod($invoicePeriod);
    }

    private static function setPaymentMeans($document, $paymentMeansData)
    {
        if(empty($paymentMeansData)) {
            return $document;
        }
        $payeeFinancialAccount = new PayeeFinancialAccount();
        if (isset($paymentMeansData['description'])) {
            $payeeFinancialAccount->setId($paymentMeansData['description']);
        }
        $paymentMeans = new PaymentMeans();
        $paymentMeans->setPayeeFinancialAccount($payeeFinancialAccount);

        return $document->setPaymentMeans($paymentMeans);
    }

    private static function setPaymentTerms($document, $paymentTermsData)
    {
        if(empty($paymentTermsData)) {
            return $document;
        }
        $paymentTerms = new PaymentTerms();
        
        if (isset($paymentTermsData['note'])) {
            $paymentTerms->setNote($paymentTermsData['note']);
        }

        return $document->setPaymentTerms($paymentTerms);
    }
    
    private static function setAllowanceCharges($document, $allowanceChargesData)
    {
        if(empty($allowanceChargesData)) {
            return $document;
        }
        $allowanceCharges = [];
        
        foreach ($allowanceChargesData as $chargeData) {
            $allowanceCharge = new AllowanceCharge();
            if (isset($chargeData['charge_indicator'])) {
                $allowanceCharge->setChargeIndicator($chargeData['charge_indicator']);
            }
            if (isset($charge['multiplier'])) {
                $allowanceCharge->setMultiplierFactorNumeric($chargeData['multiplier']);
            }
            if (isset($chargeData['reason'])) {
                $allowanceCharge->setAllowanceChargeReason($chargeData['reason']);
            }
            if (isset($chargeData['amount'])) {
                $allowanceCharge->setAmount($chargeData['amount']);
            }
            $allowanceCharges[] = $allowanceCharge;
        }

        return $document->setAllowanceCharges($allowanceCharges);
    }

    private static function setTaxTotal($document, $taxTotalData)
    {
        if(empty($taxTotalData)) {
            return $document;
        }
        $taxTotal = new TaxTotal();
        
        if (isset($taxTotalData['tax_amount'])) {
            $taxTotal->setTaxAmount($taxTotalData['tax_amount']);
        }

        if (isset($taxTotalData['tax_sub_totals']) && is_array($taxTotalData['tax_sub_totals'])) {
            foreach ($taxTotalData['tax_sub_totals'] as $subTotal) {
                $taxScheme = new TaxScheme();
                if (isset($subTotal['tax_scheme']['id'])) {
                    $taxScheme->setId($subTotal['tax_scheme']['id']);
                }

                $taxCategory = new TaxCategory();
                if (isset($subTotal['tax_category']['id'])) {
                    $taxCategory->setId($subTotal['tax_category']['id']);
                }
                if (isset($subTotal['tax_category']['percent'])) {
                    $taxCategory->setPercent($subTotal['tax_category']['percent']);
                }
                if (isset($subTotal['tax_category']['tax_exemption_reason'])) {
                    $taxCategory->setTaxExemptionReason($subTotal['tax_category']['tax_exemption_reason']);
                }
                if (isset($subTotal['tax_scheme']['id'])) {
                    $taxCategory->setTaxScheme($taxScheme);
                }

                $taxSubTotal = new TaxSubTotal();
                if (isset($subTotal['taxable_amount'])) {
                    $taxSubTotal->setTaxableAmount($subTotal['taxable_amount']);
                }
                if (isset($subTotal['tax_amount'])) {
                    $taxSubTotal->setTaxAmount($subTotal['tax_amount']);
                }
                if (isset($subTotal['percent'])) {
                    $taxSubTotal->setPercent($subTotal['percent']);
                }
                $taxSubTotal->setTaxCategory($taxCategory);
                $taxTotal->addTaxSubTotal($taxSubTotal);
            }
        }

        return $document->setTaxTotal($taxTotal);
    }
    
    private static function setTaxExchangeRate($document, $taxExchangeRateData)
    {
        if(empty($taxExchangeRateData)) {
            return $document;
        }
        $taxExchangeRate = new TaxExchangeRate();
        
        if (isset($taxExchangeRateData['source_currency'])) {
            $taxExchangeRate->setSourceCurrencyCode($taxExchangeRateData['source_currency']);
        }
        
        if (isset($taxExchangeRateData['target_currency'])) {
            $taxExchangeRate->setTargetCurrencyCode($taxExchangeRateData['target_currency']);
        }
        
        if (isset($taxExchangeRateData['rate'])) {
            $taxExchangeRate->setCalculationRate($taxExchangeRateData['rate']);
        }

        return $document->setTaxExchangeRate($taxExchangeRate);
    }
}