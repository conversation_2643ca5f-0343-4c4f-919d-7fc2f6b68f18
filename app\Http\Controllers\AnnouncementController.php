<?php

namespace App\Http\Controllers;

use App\Repositories\Announcement\AnnouncementInterface;
use App\Repositories\AnnouncementClass\AnnouncementClassInterface;
use App\Repositories\ClassSection\ClassSectionInterface;
use App\Repositories\ClassSubject\ClassSubjectInterface;
use App\Repositories\Files\FilesInterface;
use App\Repositories\Student\StudentInterface;
use App\Repositories\StudentSubject\StudentSubjectInterface;
use App\Repositories\SubjectTeacher\SubjectTeacherInterface;
use App\Services\BootstrapTableService;
use App\Services\CachingService;
use App\Services\ResponseService;
use App\Services\GeneralFunctionService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use App\Services\FileSizeLimitService;
use Throwable;
use TypeError;
use Exception;

class AnnouncementController extends Controller {

    private AnnouncementInterface $announcement;
    private ClassSectionInterface $classSection;
    private SubjectTeacherInterface $subjectTeacher;
    private StudentInterface $student;
    private FilesInterface $files;
    private StudentSubjectInterface $studentSubject;
    private ClassSubjectInterface $classSubject;
    private CachingService $cache;
    private AnnouncementClassInterface $announcementClass;

    public function __construct(AnnouncementInterface $announcement, ClassSectionInterface $classSection, SubjectTeacherInterface $subjectTeacher, StudentInterface $student, FilesInterface $files, StudentSubjectInterface $studentSubject, ClassSubjectInterface $classSubject, CachingService $cachingService, AnnouncementClassInterface $announcementClass) {
        $this->announcement = $announcement;
        $this->classSection = $classSection;
        $this->subjectTeacher = $subjectTeacher;
        $this->student = $student;
        $this->files = $files;
        $this->studentSubject = $studentSubject;
        $this->classSubject = $classSubject;
        $this->cache = $cachingService;
        $this->announcementClass = $announcementClass;
    }


    public function index() {
        ResponseService::noFeatureThenRedirect('Announcement Management');
        ResponseService::noPermissionThenRedirect('announcement-list');
        $class_section = $this->classSection->builder()->with('class', 'class.stream', 'section', 'medium')->get(); // Get the Class Section of Teacher
        $subjectTeachers = $this->subjectTeacher->builder()->with(['subject:id,name,type'])->get();
        return view('announcement.index', compact('class_section', 'subjectTeachers'));
    }

    public function store(Request $request) {
        ResponseService::noFeatureThenRedirect('Announcement Management');
        ResponseService::noPermissionThenRedirect('announcement-create');
        $request->validate([
            'title'            => 'required',
            'class_section_id' => 'required',
            'file.*'           => 'nullable|mimes:jpeg,png,jpg,gif,svg,webp,pdf,doc,docx,xml,xlsx|max:5120',
        ], [
            'class_section_id.required' => trans('the_class_section_field_id_required'),
        ]);
        try {
            DB::beginTransaction();
            $sessionYear = $this->cache->getDefaultSessionYear(); // Get Current Session Year
            // Custom Announcement Array to Store Data
            $announcementData = array(
                'title'           => $request->title,
                'description'     => $request->description,
                'session_year_id' => $sessionYear->id,
            );

            $announcement = $this->announcement->create($announcementData); // Store Data
            $announcementClassData = array();

            if (!empty($request->class_subject_id)) {

                // When Subject is passed then Store the data according to Subject Teacher
                $teacherId = Auth::user()->id; // Teacher ID
                $subjectTeacherData = $this->subjectTeacher->builder()->whereIn('class_section_id', $request->class_section_id)->where(['teacher_id' => $teacherId, 'class_subject_id' => $request->class_subject_id])->with('subject')->first(); // Get the Subject Teacher Data
                $subjectName = $subjectTeacherData->subject_with_name; // Subject Name

                // Check the Subject Type and Select Students According to it for Notification
                $getClassSubjectType = $this->classSubject->findById($request->class_subject_id,['type']);
                if ($getClassSubjectType->type == 'Elective') {
                    $notifyUser = $this->studentSubject->builder()->select('student_id')->whereIn('class_section_id', $request->class_section_id)->where(['class_subject_id' => $request->class_subject_id])->get()->pluck('student_id'); // Get the Student's ID According to Class Subject
                    // $notifyUser = $this->student->builder()->select('user_id')->whereIn('user_id', $getStudentId)->get()->pluck('user_id'); // Get the Student's User ID
                } else {
                    $notifyUser = $this->student->builder()->select('user_id')->whereIn('class_section_id', $request->class_section_id)->get()->pluck('user_id'); // Get the All Student's User ID In Specified Class
                }

                // Set class section with subject
                foreach ($request->class_section_id as $class_section) {
                    $announcementClassData[] = [
                        'announcement_id'  => $announcement->id,
                        'class_section_id' => $class_section,
                        'class_subject_id' => $request->class_subject_id
                    ];
                }
                $title = trans('New announcement in ') . $subjectName; // Title for Notification

            } else {
                $notifyUser = $this->student->builder()->select('user_id')->whereIn('class_section_id', $request->class_section_id)->get()->pluck('user_id'); // Get the Student's User ID of Specified Class for Notification

                // Set class sections
                foreach ($request->class_section_id as $class_section) {
                    $announcementClassData[] = [
                        'announcement_id'  => $announcement->id,
                        'class_section_id' => $class_section
                    ];
                }
                $title = trans('New announcement'); // Title for Notification
            }
            $this->announcementClass->upsert($announcementClassData, ['announcement_id', 'class_section_id', 'school_id'], ['announcement_id', 'class_section_id', 'school_id', 'class_subject_id']);

            // If File Exists
            $schoolId = Auth::getUser()->school_id;
            if ($request->hasFile('file')) {
                $fileData = array(); // Empty FileData Array
                $fileInstance = $this->files->model(); // Create A File Model Instance
                $announcementModelAssociate = $fileInstance->modal()->associate($announcement); // Get the Association Values of File with Announcement
                foreach ($request->file as $file_upload) {
                    if($file_upload){
                        if (FileSizeLimitService::checkFileSizeLimit($schoolId)) {
                            return ResponseService::errorResponse('storage capacity not enough');}
                        }
                    $fileSize = $file_upload->getSize();
                    $fileSizeKB = round($fileSize / 1024, 2);
                    // Create Temp File Data Array
                    $tempFileData = array(
                        'modal_type' => $announcementModelAssociate->modal_type,
                        'modal_id'   => $announcementModelAssociate->modal_id,
                        'file_name'  => $file_upload->getClientOriginalName(),
                        'type'       => 1,
                        'file_url'   => $file_upload,
                        'file_size'  => $fileSizeKB,
                    );
                    $fileData[] = $tempFileData; // Store Temp File Data in Multi-Dimensional File Data Array
                }
                $this->files->createBulk($fileData); // Store File Data
            }
            if ($notifyUser !== null && !empty($title)) {
                $guardianIds = collect();
                foreach ($notifyUser as $user){
                    $guardianId = DB::table('students')
                    ->where('user_id', $user)
                    ->pluck('guardian_id');

                    $guardianIds = $guardianIds->merge($guardianId);
                }
                $notifyUser = $notifyUser->merge($guardianIds);
      
                $type = 'Class Section'; // Get The Type for Notification
                $body = $request->title; // Get The Body for Notification
                send_notification($notifyUser, $title, $body, $type); 
            }
            DB::commit();

            ResponseService::successResponse('Data Stored Successfully');
        } catch (Throwable $e) {
            $notificationStatus = app(GeneralFunctionService::class)->wrongNotificationSetup($e);
            if ($notificationStatus) {
                DB::rollBack();
                ResponseService::logErrorResponse($e, "Announcement Controller -> Update Method");
                ResponseService::errorResponse();
            } else {
                DB::commit();
                ResponseService::warningResponse("Data Stored successfully. But App push notification not send.");
            }
            
        }
    }

    public function update($id, Request $request) {
        ResponseService::noFeatureThenRedirect('Announcement Management');
        ResponseService::noPermissionThenRedirect('announcement-edit');
        $validator = Validator::make($request->all(), [
            'title'            => 'required',
            'class_section_id' => 'required',
            'file.*'           => 'nullable|mimes:jpeg,png,jpg,gif,svg,webp,pdf,doc,docx,xml,xlsx|max:5120',
        ]);
        if ($validator->fails()) {
            ResponseService::errorResponse($validator->errors()->first());
        }
        try {
            DB::beginTransaction();
            $sessionYear = $this->cache->getDefaultSessionYear(); // Get Current Session Year

            // Custom Announcement Array to Store Data
            $announcementData = array(
                'title'           => $request->title,
                'description'     => $request->description,
                'session_year_id' => $sessionYear->id,
            );

            $announcement = $this->announcement->update($id, $announcementData); // Store Data
            $announcementClassData = array();
            $oldClassSection = $this->announcement->findById($id)->announcement_class->pluck('class_section_id')->toArray();

            //Check the Assign Data
            if (!empty($request->class_subject_id)) {

                // When Subject is passed then Store the data according to Subject Teacher
                // $teacherId = Auth::user()->teacher->id; // Teacher ID
                $teacherId = Auth::user()->id; // Teacher ID foreign key directly assign to user table

                $subjectTeacherData = $this->subjectTeacher->builder()->whereIn('class_section_id', $request->class_section_id)->where(['teacher_id' => $teacherId, 'class_subject_id' => $request->class_subject_id])->first(); // Get the Subject Teacher Data
                $subjectName = $subjectTeacherData->subject->name; // Subject Name

                // Check the Subject Type and Select Students According to it for Notification
                $getClassSubjectType = $this->classSubject->builder()->where('id', $request->class_subject_id)->pluck('type')->first();
                if ($getClassSubjectType == 'Elective') {
                    $getStudentId = $this->studentSubject->builder()->select('student_id')->whereIn('class_section_id', $request->class_section_id)->where(['class_subject_id' => $request->class_subject_id])->get()->pluck('student_id'); // Get the Student's ID According to Class Subject
                    $notifyUser = $this->student->builder()->select('user_id')->whereIn('id', $getStudentId)->get()->pluck('user_id'); // Get the Student's User ID
                } else {
                    $notifyUser = $this->student->builder()->select('user_id')->whereIn('class_section_id', $request->class_section_id)->get()->pluck('user_id'); // Get the All Student's User ID In Specified Class
                }

                // Set class sections with subject
                foreach ($request->class_section_id as $class_section) {
                    $announcementClassData[] = [
                        'announcement_id'   => $announcement->id,
                        'class_section_id'  => $class_section,
                        'class_subject_id'  => $request->class_subject_id
                    ];

                    // Check class section
                    $key = array_search($class_section, $oldClassSection);
                    if ($key !== false) {
                        unset($oldClassSection[$key]);
                    }
                }

                $title = trans('Updated announcement in') . $subjectName; // Title for Notification


            } else {
                // When only Class Section is passed
                $notifyUser = $this->student->builder()->select('user_id')->whereIn('class_section_id', $request->class_section_id)->get()->pluck('user_id'); // Get the Student's User ID of Specified Class for Notification


                // Set class sections
                foreach ($request->class_section_id as $class_section) {
                    $announcementClassData[] = [
                        'announcement_id'  => $announcement->id,
                        'class_section_id' => $class_section
                    ];
                    // Check class section
                    $key = array_search($class_section, $oldClassSection);
                    if ($key !== false) {
                        unset($oldClassSection[$key]);
                    }
                }
                $title = trans('Updated announcement'); // Title for Notification
            }

            $this->announcementClass->upsert($announcementClassData, ['announcement_id', 'class_section_id', 'school_id'], ['announcement_id', 'class_section_id', 'school_id', 'class_subject_id']);

            // Delete announcement class sections
            $this->announcementClass->builder()->where('announcement_id', $id)->whereIn('class_section_id', $oldClassSection)->delete();


            // If File Exists
            $schoolId = Auth::getUser()->school_id;
            if ($request->hasFile('file')) {
                $fileData = array(); // Empty FileData Array
                $fileInstance = $this->files->model(); // Create A File Model Instance
                $announcementModelAssociate = $fileInstance->modal()->associate($announcement); // Get the Association Values of File with Announcement
                foreach ($request->file as $file_upload) {
                    if($file_upload){
                        if (FileSizeLimitService::checkFileSizeLimit($schoolId)) {
                            return ResponseService::errorResponse('storage capacity not enough');}
                        }
                    // Create Temp File Data Array
                    $fileSize = $file_upload->getSize();
                    $fileSizeKB = round($fileSize / 1024, 2);
                    $tempFileData = array(
                        'modal_type' => $announcementModelAssociate->modal_type,
                        'modal_id'   => $announcementModelAssociate->modal_id,
                        'file_name'  => $file_upload->getClientOriginalName(),
                        'type'       => 1,
                        'file_url'   => $file_upload,
                        'file_size'  => $fileSizeKB,
                    );
                    $fileData[] = $tempFileData; // Store Temp File Data in Multi-Dimensional File Data Array
                }
                $this->files->createBulk($fileData); // Store File Data
            }

            if ($notifyUser !== null && !empty($title)) {
                $type = 'Announcement'; 
                $body = $request->title; 
                send_notification($notifyUser, $title, $body, $type); // Send Notification
            }

            DB::commit();
            ResponseService::successResponse('Data Updated Successfully');
        } catch (Throwable $e) {
            $notificationStatus = app(GeneralFunctionService::class)->wrongNotificationSetup($e);
            if ($notificationStatus) {
                DB::rollBack();
                ResponseService::logErrorResponse($e, "Announcement Controller -> Update Method");
                ResponseService::errorResponse();
            } else {
                DB::commit();
                ResponseService::warningResponse("Data Stored successfully. But App push notification not send.");
            }
            

            // if (Str::contains($e->getMessage(), [
            //         'does not exist','file_get_contents'
            //     ])) {
            //     DB::commit();
            //     ResponseService::warningResponse("Data Stored successfully. But App push notification not send.");
            // } else {
            //     DB::rollBack();
            //     ResponseService::logErrorResponse($e, "Announcement Controller -> Update Method");
            //     ResponseService::errorResponse();
            // }
        }
    }

    public function show() {
        ResponseService::noFeatureThenRedirect('Announcement Management');
        ResponseService::noPermissionThenRedirect('announcement-list');
        $offset = request('offset', 0);
        $limit = request('limit', 10);
        $sort = request('sort', 'id');
        $order = request('order', 'ASC');
        $search = request('search');

        $sql = $this->announcement->builder()->with('file', 'announcement_class.class_section.class', 'announcement_class.class_section.section', 'announcement_class.class_section.medium' ,'announcement_class.class_subject.subject')
            ->where(function ($q) use ($search) {
                $q->when($search, function ($query) use ($search) {
                $query->where('id', 'LIKE', "%$search%")
                    ->orwhere('title', 'LIKE', "%$search%")
                    ->orwhere('description', 'LIKE', "%$search%");
                });
            });


        $total = $sql->count();
        $sql->orderBy($sort, $order)->skip($offset)->take($limit);
        $res = $sql->get();
        $bulkData = array();
        $bulkData['total'] = $total;
        $rows = array();
        $no = 1;
        $user = Auth::user();
        foreach ($res as $row) {
            $operate = '';
            $class_section = array();
            $class_section_id = array();
            $class_subject_id = '';
            foreach ($row->announcement_class as $class) {
                if (Auth::user()->can('announcement-edit')) {
                    //Show Edit and Soft Delete Buttons
                    $operate = BootstrapTableService::editButton(route('announcement.update', $row->id));
                }
                
                if (Auth::user()->can('announcement-delete')) {
                    $operate .= BootstrapTableService::deleteButton(route('announcement.destroy', $row->id));
                }
                $class_section_id[] = $class->class_section_id;

                // Add teacher subject
                if ($class->class_subject_id) {
                    $class_subject_id = $class->class_subject_id;
                    $class_section[] = $class->class_section->full_name . ' #' . $class->class_subject->subject->name;
                } else {
                    $class_section[] = $class->class_section->full_name;
                }
            }

            $tempRow = $row->toArray();
            $tempRow['id'] = $row->id;
            $tempRow['no'] = $no++;
            $tempRow['class_subject_id'] = $class_subject_id;
            $tempRow['class_sections'] = $class_section_id;
            $tempRow['assignto'] = $class_section;
            $tempRow['operate'] = $operate;
            $rows[] = $tempRow;
        }
        $bulkData['rows'] = $rows;
        return response()->json($bulkData);
    }

    public function destroy($id) {
        ResponseService::noFeatureThenRedirect('Announcement Management');
        ResponseService::noPermissionThenSendJson('announcement-delete');
        try {
            DB::beginTransaction();
            $this->announcement->deleteById($id);
            DB::commit();
            ResponseService::successResponse('Data Deleted Successfully');
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "Announcement Controller -> Destroy Method");
            ResponseService::errorResponse();
        }
    }

    public function fileDelete($id) {
        ResponseService::noFeatureThenRedirect('Announcement Management');
        ResponseService::noPermissionThenRedirect('announcement-delete');
        try {
            DB::beginTransaction();

            // Find the Data by FindByID
            $file = $this->files->findById($id);

            // Delete the file data
            $file->delete();

            DB::commit();
            ResponseService::successResponse('Data Deleted Successfully');
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "Announcement Controller -> fileDelete Method");
            ResponseService::errorResponse();
        }
    }

    public function announcementIndex() {
        
        $school = DB::select(query: "SELECT * FROM schools WHERE status = 1");
        return view('announcement.announcement-news', compact('school'));
    }
    
    public function storeAnnouncementNews(Request $request) {
        // Validate the incoming request
        $request->validate([
            'title'         => 'required|string|max:255',
            'file.*'        => 'nullable|mimes:jpeg,png,pdf,doc,docx|max:5000', 
            'school_id'     => 'required|array',
        ]);
    

        $filePath = '';
        if ($request->hasFile('file')) {
            $file = $request->file('file');
            $filePath = time() . '_' . $file->getClientOriginalName();
            $filePath = $file->store('announcements', 'public');
        }
    
        // Insert data into the database
        $announcementId = DB::table('announcement_news')->insertGetId([
            'title'         => $request->title,
            'file'          => $filePath, // Store file path in the 'file' column
            'created_at'    => now(),
            'updated_at'    => now(),
        ]);
    
        // Retrieve school IDs from request or default to all active schools
        $schoolIds = array_filter($request->input('school_id', []), fn($value) => !is_null($value) && $value !== '');
        if(empty($schoolIds)){
            $schoolIds = DB::table('schools')
            ->whereNull('deleted_at')
            ->pluck('id')
            ->toArray();
        }
        // Insert data into the announcement_schools table
        foreach ($schoolIds as $schoolId) {
            DB::table('announcement_schools')->insert([
                'announcement_id' => $announcementId,
                'school_id' => $schoolId,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    
        // Return success response
        return ResponseService::successResponse('Data Stored Successfully');
    }

    public function showAnnouncementNews(Request $request) {
        $offset = $request->input('offset', 0);
        $limit = $request->input('limit', 99999);
        $sort = $request->input('sort', 'id');
        $order = $request->input('order', 'ASC');
        $search = $request->input('search');
    
        $query = DB::table('announcement_news')
        ->select('announcement_news.*');
    
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('title', 'LIKE', "%{$search}%");
            });
        }
        
        $total = $query->count();
        $announcements = $query->orderBy($sort, $order)
                               ->offset($offset)
                               ->limit($limit)
                               ->get();
    
        $rows = [];
        $no = $offset + 1;
    
        foreach ($announcements as $announcement) {
            $operate = '';
            $operate .= BootstrapTableService::editButton(route('announcement-news.edit', $announcement->id));
            $operate .= BootstrapTableService::deleteButton(route('announcement-news.delete', $announcement->id));
    
            $rows[] = [
                'id' => $announcement->id,
                'no' => $no++,
                'title' => $announcement->title,
                'file' => $announcement->file,
                'operate' => $operate,
            ];
        }
        $bulkData = [
            'total' => $total,
            'rows' => $rows,
        ];
    
        return response()->json($bulkData);
    }

    public function editAnnouncementNews(Request $request, $id)
    {
        $request->validate([
            'title'             => 'nullable',
            'file'              => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:5000',
        ]);

        try {
            DB::beginTransaction();

            // Retrieve existing record from the database
            $announcementNews  = DB::table('announcement_news')->where('id', $id)->first();

            if (!$announcementNews) {
                throw new Exception('Announcement news  record not found.');
            }

            // Prepare the data to update
            $data = [
                'title'             => $request->input('title'),
            ];
            // Check if a new file is uploaded
            if ($request->hasFile('edit_file')) {
                // Delete the old file if it exists
                if ($announcementNews->file) {
                    $oldFilePath = storage_path('app/public/' . $announcementNews->file);
                    if (file_exists($oldFilePath)) {
                        unlink($oldFilePath);
                    }
                }

                // Store the new file
                $file = $request->file('edit_file');
                $path = $file->store('announcements', 'public'); 
                $data['file'] = $path; 
            }

            // Update the record in the database
            DB::table('announcement_news')->where('id', $id)->update($data);

            DB::commit();
            ResponseService::successResponse('Data Updated Successfully');
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "AnnouncementNews  Controller -> Update Method");
            ResponseService::errorResponse();
        }
    }

    public function announcementNewDestroy($id)
    {
        try {
            DB::beginTransaction(); 
            
            $announcementNews = DB::table('announcement_news')->where('id', $id)->first();
    
            if ($announcementNews && $announcementNews->file) {
                $filePath = public_path('storage/' . $announcementNews->file);
                if (file_exists($filePath)) {
                    unlink($filePath); // Delete the file from the storage
                }
            }
    
            DB::table('announcement_news')->where('id', $id)->delete();
            DB::table('announcement_schools')->where('announcement_id', $id)->delete();
            DB::commit();
    
            return ResponseService::successResponse('Data Deleted Successfully');
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "Announcement Controller -> Destroy Method");
            return ResponseService::errorResponse();
        }
    }

    public function popupAnnouncementNews(Request $request, $id = null)
    {
        $query = DB::table('announcement_news');
        $schoolId = $request->school_id;
        $latestAnnouncement = DB::table('announcement_news')
        ->join('announcement_schools', 'announcement_news.id', '=', 'announcement_schools.announcement_id')
        ->where('announcement_schools.school_id', $schoolId) 
        ->where('announcement_schools.status',0)
        ->orderBy('announcement_news.id', 'desc')
        ->select('announcement_news.id', 'announcement_news.title', 'announcement_news.file')
        ->first();

        if ($latestAnnouncement) {
            $updateAnnouncement = DB::table('announcement_schools')->where('announcement_id', $latestAnnouncement->id)->update([
                'status'    => 1,
            ]);                
            if ($updateAnnouncement) {
                return response()->json([
                    'id'    => $latestAnnouncement->id,
                    'title' => $latestAnnouncement->title,
                    'file'  => $latestAnnouncement->file,
                ]);
            }
        } else {
            return response()->json(['error' => 'No announcements found']);
        }
    }
}
