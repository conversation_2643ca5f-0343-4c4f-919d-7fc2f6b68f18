<?php

namespace App\Http\Controllers;

use App\Models\Addon;
use App\Models\AdmissionNotification;
use App\Models\Feature;
use App\Models\Package;
use App\Models\PaymentConfiguration;
use App\Repositories\Announcement\AnnouncementInterface;
use App\Repositories\ClassSchool\ClassSchoolInterface;
use App\Repositories\Exam\ExamInterface;
use App\Repositories\Fees\FeesInterface;
use App\Repositories\FeesPaid\FeesPaidInterface;
use App\Repositories\Holiday\HolidayInterface;
use App\Repositories\Leave\LeaveInterface;
use App\Repositories\PaymentTransaction\PaymentTransactionInterface;
use App\Repositories\School\SchoolInterface;
use App\Repositories\SessionYear\SessionYearInterface;
use App\Repositories\Stream\StreamInterface;
use App\Repositories\Subscription\SubscriptionInterface;
use App\Repositories\Timetable\TimetableInterface;
use App\Repositories\User\UserInterface;
use App\Services\CachingService;
use App\Services\SubscriptionService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Models\Mediums;
use App\Models\Section;
use App\Repositories\StudentFees\StudentFeesInterface;
use App\Models\StudentFee;
use Stripe\Balance;

class DashboardController extends Controller {
    private UserInterface $user;
    private AnnouncementInterface $announcement;
    private SubscriptionInterface $subscription;
    private SchoolInterface $school;
    private LeaveInterface $leave;
    private HolidayInterface $holiday;
    private CachingService $cache;
    private ClassSchoolInterface $class;
    private TimetableInterface $timetable;
    private SubscriptionService $subscriptionService;
    private ExamInterface $exam;
    private SessionYearInterface $sessionYear;
    private StreamInterface $stream;
    private FeesInterface $fees;
    private FeesPaidInterface $feesPaid;
    private PaymentTransactionInterface $paymentTransaction;
    private StudentFeesInterface $studentFee;

    public function __construct(UserInterface $user, AnnouncementInterface $announcement, SubscriptionInterface $subscription, SchoolInterface $school,LeaveInterface $leave, HolidayInterface $holiday, CachingService $cache, ClassSchoolInterface $class, TimetableInterface $timetable, SubscriptionService $subscriptionService, ExamInterface $exam, SessionYearInterface $sessionYear, StreamInterface $stream, FeesInterface $fees, FeesPaidInterface $feesPaid, PaymentTransactionInterface $paymentTransaction, StudentFeesInterface $studentFee) {
        $this->middleware('auth');
        $this->user = $user;
        $this->announcement = $announcement;
        $this->subscription = $subscription;
        $this->school = $school;
        $this->leave = $leave;
        $this->holiday = $holiday;
        $this->cache = $cache;
        $this->class = $class;
        $this->timetable = $timetable;
        $this->subscriptionService = $subscriptionService;
        $this->exam = $exam;
        $this->sessionYear = $sessionYear;
        $this->stream = $stream;
        $this->fees = $fees;
        $this->feesPaid = $feesPaid;
        $this->paymentTransaction = $paymentTransaction;
        $this ->studentFee = $studentFee;
    }

    public function index() {
        $mediumCount = Mediums::count(); 
        $sectionCount = Section::count(); 
        if($mediumCount == 0){
            $medium = ['name' => '', 'school_id' => $schoolData->id];
            Mediums::create($medium);
        }
        if($sectionCount == 0){
            $sections = ['name' => '', 'school_id' => $schoolData->id];
            Section::create($sections);
        }

        $teacher = $student = $parent = $teachers = $subscription = $prepiad_upcoming_plan = $prepiad_upcoming_plan_type = $check_payment = null;
        $boys = $girls = $license_expire = 0;
        $previous_subscriptions = array();
        $announcement = array();
        $holiday = array();
        $total_students = $male_students = $female_students = $timetables = $classData = $fees_detail = array();
        $paymentConfiguration = '';
        $settings = app(CachingService::class)->getSystemSettings();
        $system_settings = $settings;
        $settings = app(CachingService::class)->getSystemSettings();
 
        $role = Auth::user()->role;       
        if($role == 'Guardian'){
            Auth::logout();
            return redirect('/login');
        }
        // School Admin Dashboard
        if (Auth::user()->hasRole('School Admin') || Auth::user()->school_id) {
            // Counters
            $teacher = $this->user->builder()->role("Teacher")->count();
            $student = $this->user->builder()->role("Student")->has('student')->count();
            $parent = $this->user->guardian()->whereHas('child.user', function ($q) {
                $q->owner();
            })->count();
            
            if ($student > 0) {
                $boys_count = $this->user->builder()->role('Student')->where('gender', 'male')->count();
                $girls_count = $this->user->builder()->role('Student')->where('gender', 'female')->count();
                $boys = round((($boys_count * 100) / $student), 2);
                $girls = round(($girls_count * 100) / $student, 2);
                $total_students = $student;
            }
            $classes_counter = $this->class->builder()->count();
            $streams = $this->stream->builder()->count();
            // End Counters

            $subscription = $this->subscriptionService->active_subscription(Auth::user()->school_id);
            $schoolSettings = $this->cache->getSchoolSettings();

            if ($subscription) {
                $license_expire = Carbon::now()->diffInDays(Carbon::parse($subscription->end_date)) + 1;
            }

            $sessionYear = $this->sessionYear->builder()->select('id','name','default')->get();
            $paymentConfiguration = '';
            // For prepaid upcoming plans, please make the payment before your current subscription expires.
            if ($license_expire <= ($settings['current_plan_expiry_warning_days'] ?? 7) && $subscription) {
                if (isset($schoolSettings['auto_renewal_plan']) && $schoolSettings['auto_renewal_plan']) {
                    $next_plan_start_date = Carbon::parse($subscription->end_date)->addDay()->format('Y-m-d');

                    $prepiad_upcoming_plan = $this->subscription->builder()->with('package')->whereDate('start_date',$next_plan_start_date)->first();
                    // Create new entry or update existing record
                    // 1 => Already set upcoming plan update subscription
                    // 0 => Set current subscription plan as upcoming
                    $prepiad_upcoming_plan_type = 1;
    
                    if (!$prepiad_upcoming_plan) {
                        // Add current subscription in the upcoming subscription
                        $prepiad_upcoming_plan = $subscription;
                        $prepiad_upcoming_plan_type = 0;
                    }

                    // Please verify if you have already made the payment.
                    if ($prepiad_upcoming_plan->package->type == 0 && $subscription->id != $prepiad_upcoming_plan->id) {
                        $check_payment = $this->subscription->builder()->where('id',$prepiad_upcoming_plan->id)->whereHas('subscription_bill.transaction', function($q) {
                            $q->where('payment_status',"succeed");
                        })->first();
                    }

                    $paymentConfiguration = PaymentConfiguration::where('school_id', null)->where('status',1)->first();
                }
            }

            $previous_subscriptions = $this->subscription->builder()->with('subscription_bill.transaction')->get()->whereIn('status', [3, 4, 5]);

            $defaultSessionYear = $this->cache->getDefaultSessionYear();

            $holiday = $this->holiday->builder()->whereDate('date', '>=', Carbon::now()->format('Y-m-d'))->whereDate('date', '<=', $defaultSessionYear->end_date)->orderBy('date', 'ASC')->get();

            $announcement = $this->announcement->builder()->whereHas('announcement_class', function ($q) {
                $q->where('class_subject_id', null);
            })->limit(5)->orderBy('id','desc')->get();

            
            // Attendance graph
            $class_names = $this->class->builder()->with('medium','stream')->get()->pluck('full_name','id');

            // Exam result
            $exams = $this->exam->builder()->groupBy('name')->get();

            // Fees Details
            $student_ids = $this->studentFee->builder()->with([
                'student_fees_detail' => function($q) {
                    $q->where('optional',0);
                }
            ])
            ->wherehas('student_fees_paid')
            ->where('session_year_id',$defaultSessionYear->id)
            ->groupby('student_id')->pluck('student_id');
            
            $otherStudents = DB::table('students')
                ->where('session_year_id', $defaultSessionYear->id)
                ->where('school_id', Auth::user()->school_id)
                ->whereNotExists(function($query) use ($defaultSessionYear) {
                    $query->select(DB::raw(1))
                          ->from('student_fees')
                          ->whereRaw('student_fees.student_id = students.id')
                          ->where('session_year_id', $defaultSessionYear->id);
                })
                ->count();

            $unPaidFees = DB::table('students')
                        ->whereNotIn('id',$student_ids)
                        ->where('session_year_id',$defaultSessionYear->id)
                        ->where('school_id',Auth::user()->school_id)
                        ->count();

            $unPaidFees -= $otherStudents;

            $partialPaidCount = $this->studentFee->builder()->with([
                'student_fees_details' => function($q) {
                    $q->where('optional',0);
                }
            ])
            ->wherehas('student_fees_paid',function($q){
                $q->where('is_fully_paid',0);
            })
            ->where('session_year_id',$defaultSessionYear->id)
            ->groupBy('student_id')
            ->get()
            ->count();

            $unPaidFees += $partialPaidCount;

            $fullPaidFees = $this->studentFee->builder()->with([
                'student_fees_details' => function($q) {
                    $q->where('optional',0);
                }
            ])
            ->wherehas('student_fees_paid',function($q){
                $q->where('is_fully_paid',1);
            })
            ->where('session_year_id',$defaultSessionYear->id)
            ->groupBy('student_id')
            ->orderBy('id')
            ->get()
            ->count();

            if ($fullPaidFees == 0) {
                $unPaidFees = 0;
            }

            $fees_detail = [
                'unPaidFees' => $unPaidFees,
                'fullPaidFees' => $fullPaidFees,
                'otherFees' => $otherStudents
            ]; 

            $schoolId = Auth::user()->school_id;
            $announcement_news = DB::table('announcement_news')
                ->join('announcement_schools', 'announcement_news.id', '=', 'announcement_schools.announcement_id')
                ->where('announcement_schools.school_id', $schoolId)
                ->where('announcement_schools.status', 1)
                ->orderBy('announcement_news.id', 'desc')
                ->select('announcement_news.id', 'announcement_news.title', 'announcement_news.file')
                ->get();

            // // Update the status of the latest announcement to 1
            // DB::table('announcement_schools')
            //     ->whereIn('announcement_id', $announcement_news->pluck('id'))
            //     ->update(['status' => 1]);
            
            $e_invoice_reminder = false;
            $e_invoice_exists = DB::table('e_invoice')->where('school_id', $schoolId)->exists();
            if($e_invoice_exists){
                $incomplete_data_count = DB::table('e_invoice')
                ->where('school_id', $schoolId)
                ->where(function ($query) {
                    $query->whereNull('new_company_registration_number')
                    ->orWhere('new_company_registration_number', '')
                    ->orWhereNull('company_business_activity')
                    ->orWhere('company_business_activity', '')
                    ->orWhereNull('tax_identification_number')
                    ->orWhere('tax_identification_number', '')
                    ->orWhereNull('address_line1')
                    ->orWhere('address_line1', '')
                    ->orWhereNull('city')
                    ->orWhere('city', '')
                    ->orWhereNull('postal_code')
                    ->orWhere('postal_code', '')
                    ->orWhereNull('country')
                    ->orWhere('country', '')
                    ->orWhereNull('state')
                    ->orWhere('state', '');
                })
                ->count();
                if($incomplete_data_count > 0) {
                    $e_invoice_reminder = true;
                }
            } else {
                $e_invoice_reminder = true;
            }
            
            $maxFileSize = (float)DB::table('schools')
            ->where('id', $schoolId)
            ->value('capacity');

            // $capacityGB = $maxFileSize / (1024 * 1024);

            $totalFileSize1 = DB::table('files')
                ->where('school_id', $schoolId)
                ->sum('file_size');
            
            $totalFileSize2 = DB::table('student_progress')
                ->where('school_id', $schoolId)
                ->sum('file_size');
            
            $totalFileSize3 = DB::table('subjects')
                ->where('school_id', $schoolId)
                ->sum('file_size');

            $totalFileSize4 = DB::table('users')
                ->where('school_id', $schoolId)
                ->sum('file_size');

            $totalFileSize5 = DB::table('galleries')
                ->where('school_id', $schoolId)
                ->sum('file_size');

            $totalFileSize6 = DB::table('timetable_configurations')
            ->where('school_id', $schoolId)
            ->sum('file_size');

            
            $totalFileSize = $totalFileSize1 + $totalFileSize2 + $totalFileSize3 + $totalFileSize4 +$totalFileSize5 + $totalFileSize6;
            
            $totalFileSizeGB = $totalFileSize / (1024 * 1024);

            $branches = DB::table('school_branch')
            ->where('school_id', $schoolId)
            ->pluck('branch_id')
            ->toArray();

            $allId = array_merge([$schoolId], $branches);

            //Daily Collection Report (Total Fees)
            $rowCollection = [];
            foreach ($allId as $schoolId) {
                $getSessionYear=$this->cache->getDefaultSessionYear($schoolId);
                $totalCollection = DB::table('student_fees_paids as sfp')
                ->join('student_fees','student_fees.id','=','sfp.student_fees_id')
                ->join('schools as s', 'sfp.school_id', '=', 's.id')
                // ->join('student_fees as sf', 'sfp.student_fees_id', '=', 'sf.id')
                // ->join('school_branch as sb', 'sfp.school_id', '=', 'sb.branch_id')
                ->select(
                    'sfp.school_id',
                    's.name as school_name',
                    DB::raw('SUM(sfp.amount) AS total_amount')
                    )
                ->where('sfp.school_id', $schoolId)
                // ->where('sf.session_year_id', $sessionYearId)
                ->where('sfp.is_fully_paid', 1)
                ->where('sfp.status',1)
                ->groupBy('s.name');
                if(isset($getSessionYear->id)){
                    $totalCollection =$totalCollection->where('student_fees.session_year_id',$getSessionYear->id)->first();
                } else {
                    $totalCollection =$totalCollection->first();
                }

                $tempRow = [];

                $tempRow['school_id'] = $totalCollection->school_id ?? '';
                $tempRow['school_name'] = $totalCollection->school_name ?? '';
                $tempRow['total_amount'] = $totalCollection->total_amount ?? '';

                $rowCollection[] = $tempRow;
            }


            //Payroll Report (Salary)
            $rowPayroll = [];
            foreach ($allId as $schoolId) {
                $getSessionYear=$this->cache->getDefaultSessionYear($schoolId);
                $totalPayroll = DB::table('expenses as e')
                    ->join('schools as s', 'e.school_id', '=', 's.id')
                    ->select(
                        'e.school_id',
                        's.name as school_name',
                        DB::raw('SUM(amount) AS total_amount')
                    )
                    ->whereNull('category_id')
                    ->where('school_id', $schoolId);
                    if(isset($getSessionYear->id)){
                        $totalPayroll=$totalPayroll ->where('session_year_id',$getSessionYear->id)->first();
                    }

                $tempRow = [];

                $tempRow['school_id'] = $totalPayroll->school_id ?? '';
                $tempRow['school_name'] = $totalPayroll->school_name ?? '';
                $tempRow['total_amount'] = $totalPayroll->total_amount ?? '';

                $rowPayroll[] = $tempRow;
            }

            //Expenses Report (Expenses)
            $rowExpenses = [];
            foreach ($allId as $schoolId) {
                $getSessionYear=$this->cache->getDefaultSessionYear($schoolId);
                // $school = DB::table('schools')->where('id',$schoolId)->first();
                $totalExpenses = DB::table('expenses as e')
                    ->join('schools as s', 'e.school_id', '=', 's.id')
                    ->select(
                        'e.school_id',
                        's.name as school_name',
                        DB::raw('SUM(e.amount) AS total_amount')
                    )
                    ->where('e.school_id', $schoolId)
                    ->where('category_id', '!=', 3)
                    ->whereNotNull('category_id')
                    ->groupBy('s.name');
                    if(isset($getSessionYear->id)){
                        $totalExpenses =$totalExpenses->where('session_year_id',$getSessionYear->id) ->first();
                    } else {
                        $totalExpenses =$totalExpenses->first();
                    }

                $tempRow = [];

                $tempRow['school_id'] = $totalExpenses->school_id ?? '';
                $tempRow['school_name'] = $totalExpenses->school_name ?? '';
                $tempRow['total_amount'] = $totalExpenses->total_amount ?? '';

                $rowExpenses[] = $tempRow;
            }

            //Income Report

            $rowIncome = [];

            foreach ($allId as $allIds) {
                $getSessionYear=$this->cache->getDefaultSessionYear($allIds);
                $schoolName= DB::table('schools')->where('id',$allIds)->first();
                $totalFees =  DB::table('student_fees_paids as sfp')
                ->join('student_fees','student_fees.id','=','sfp.student_fees_id')
                ->join('schools as s', 'sfp.school_id', '=', 's.id')
                ->select(
                    'sfp.school_id',
                    's.name as school_name',
                    DB::raw('SUM(sfp.amount) AS total_amount')
                )
                ->where('sfp.school_id', $allIds)
                ->where('sfp.is_fully_paid', 1)
                ->where('sfp.status',1)
                ->groupBy('s.name');
                if(isset($getSessionYear->id)){
                    $totalFees=$totalFees->where('student_fees.session_year_id',$getSessionYear->id);
                }
                $totalFees= $totalFees->first();      
                $total_fees = $totalFees->total_amount ?? 0; 
                    
                $totalSalary = DB::table('expenses as e')
                ->select(DB::raw('SUM(amount) AS total_amount'))
                ->whereNull('category_id')
                ->where('school_id', $allIds);
                if(isset($getSessionYear->id)){
                    $totalSalary=$totalSalary->where('session_year_id',$getSessionYear->id);
                }
                $totalSalary=$totalSalary->first();         
            
                $total_salary = $totalSalary->total_amount ?? 0;
                    
                $totalExpenses = DB::table('expenses as e')
                ->join('schools as s', 'e.school_id', '=', 's.id')
                ->select(
                    'e.school_id',
                    's.name as school_name',
                    DB::raw('SUM(e.amount) AS total_amount')
                )
                ->where('e.school_id', $allIds)
                ->where('category_id', '!=', 3)
                ->whereNotNull('category_id')
                ->groupBy('s.name');

                if(isset($getSessionYear->id)){
                    $totalExpenses=$totalExpenses->where('session_year_id',$getSessionYear->id);
                }
                $totalExpenses=$totalExpenses->first();    
                    
                $total_expenses = $totalExpenses->total_amount ?? 0;
                    
                $totalCreditNote = DB::table('credit_note_details as cnd')
                    ->join('credit_note as cn', 'cn.id', '=', 'cnd.credit_note_id')
                    ->select(DB::raw('SUM(cnd.credit_note_amount * cnd.quantity) AS total_amount'))
                    ->where('cn.status', 'published')
                    ->where('cnd.school_id', $allIds);
                if(isset($getSessionYear->id)){
                    $totalCreditNote=$totalCreditNote->where('session_year_id',$getSessionYear->id);
                }
                $totalCreditNote=$totalCreditNote->first();    
                    
                $total_credit_note = $totalCreditNote->total_amount ?? 0;
                    
                $grand_total = $total_fees - $total_salary - $total_expenses - $total_credit_note;
                $rowIncome[] = [
                    'school_id' => $allIds,
                    'school_name' => $schoolName->name ?? 'Empty',
                    'total_fees' => $total_fees,
                    'total_salary' => $total_salary,
                    'total_expenses' => $total_expenses,
                    'total_credit_note' => $total_credit_note,
                    'grand_total' => $grand_total
                ];
            }
                
            $percentage = 0.0;
            if((float)$maxFileSize > 0){
                $percentage =min(($totalFileSizeGB / $maxFileSize) * 100, 100);
            }
                
            $progress = [
                'fileSize' => $totalFileSizeGB,
                'maxFileSize' => $maxFileSize,
                'percentage' => $percentage,
            ];

            $ranking = 1;//start with 1st rank

            // $scores = DB::select("
            //     SELECT r.student_id, 
            //         s.user_id, 
            //         r.score_total, 
            //         u.image,
            //         CONCAT(u.first_name, ' ', u.last_name) AS studentName
            //     FROM rewards r
            //     JOIN students s ON r.student_id = s.id 
            //     JOIN users u ON s.user_id = u.id
            //     AND s.school_id = ?
            //     AND r.deleted_at IS NULL
            //     AND r.created_at = (
            //         SELECT MAX(r2.created_at) 
            //         FROM rewards r2 
            //         WHERE r2.student_id = r.student_id
            //         AND r.deleted_at IS NULL
            //     )
            //     ORDER BY r.score_total DESC
            // ", [Auth::user()->school_id]);
            $scores = DB::select("
                SELECT r.student_id,
                    s.user_id,
                    r.score_total,
                    u.image,
                    CONCAT(u.first_name, ' ', u.last_name) AS studentName
                FROM rewards r
                JOIN (
                    SELECT student_id, MAX(created_at) as max_created_at
                    FROM rewards
                    WHERE deleted_at IS NULL
                    GROUP BY student_id
                ) latest_rewards ON r.student_id = latest_rewards.student_id AND r.created_at = latest_rewards.max_created_at
                JOIN students s ON r.student_id = s.id
                JOIN users u ON s.user_id = u.id
                WHERE s.school_id = ?
                AND r.deleted_at IS NULL
                ORDER BY r.score_total DESC;
            ", [Auth::user()->school_id]);

            // \Log::info('Scores query result for school_id ' . Auth::user()->school_id, [
            //     'has_data' => !empty($scores),
            //     'count' => count($scores ?? [])
            // ]);
       
            foreach ($scores as $score) {
                $score->rank = $ranking++; // Increase rank by 1
            }

            $studName = DB::select(" SELECT r.student_id, s.user_id, concat(u.first_name,' ',u.last_name) as studentName
            FROM rewards r 
            JOIN students s ON r.student_id = s.id 
            JOIN users u ON s.user_id = u.id
            WHERE s.school_id = ?
            ",[Auth::user()->school_id]);

            $admissionNotification = DB::table('admission_notification as a')
                                    ->join('users as u','u.id','=','a.user_id')
                                    ->select('a.*',DB::raw("CONCAT(u.first_name,' ',u.last_name) as full_name"))
                                    ->where('a.school_id',Auth::user()->school_id)
                                    ->orderByDesc('a.created_at')
                                    ->limit(10)
                                    ->get();
            $totalAdmissionNotification = $admissionNotification->filter(function ($notification){
                return $notification->status == 0;
            })->count();
        }
       
        // Super admin dashboard
        $super_admin = [
            'total_school'    => 0,
            'active_school'   => 0,
            'deactive_school' => 0,
        ];
        if (Auth::user()->hasRole('Super Admin') || !Auth::user()->school_id) {
            $school = $this->school->builder()->get();
            $total_school = $school->count();
            $active_school = $school->where('status', 1)->count();
            $deactive_school = $school->where('status', 0)->count();
            $packages = Package::where('is_trial',0)->count();

            $super_admin = [
                'total_school'    => $total_school,
                'active_schools'   => $active_school,
                'inactive_schools' => $deactive_school,
                'total_packages' => $packages
            ];

            $paymentTransaction = $this->paymentTransaction->builder()->has('subscription_bill')->select(DB::raw('YEAR(MIN(created_at)) as min_year'))->value('min_year');

            if ($paymentTransaction) {
                $start_year = $paymentTransaction;
            } else {
                $start_year = Carbon::now()->format('Y');
            }

            $schools = $this->school->builder()->select('id','name','admin_id','logo')->with('user:id,first_name,last_name')->orderBy('id','DESC')->take(5)->get();
            $staffs = $this->user->builder()->select('id','first_name','last_name','image')->has('staff')->with('roles','support_school.school:id,name')->whereHas('roles', function ($q) {
                $q->where('custom_role', 1)->whereNot('name', 'Teacher');
            })->get();

            $addons = Addon::select('id','name','feature_id')->withCount('addon_subscription_count')->with('feature')->get();

            $labels = [];
            $data = [];
            foreach ($addons as $key => $addon) {
                $labels[] = $addon->feature->short_name ?? '';
                $data[] = $addon->addon_subscription_count_count;
            }
            $addon_graph = [
                $labels,
                $data
            ];

            $packages = Package::select('id','name')->where('is_trial',0)->withCount('subscription')->get();

            $package_labels = [];
            $package_data = [];
            foreach ($packages as $key => $package) {
                $package_labels[] = $package->name;
                $package_data[] = $package->subscription_count;
            }
            $package_graph = [
                $package_labels,
                $package_data
            ];
        }

        // Timetable
        if (Auth::user()->hasRole('Teacher')) {
            $date = Carbon::now();
            $fullDayName = $date->format('l');
            $timetables = $this->timetable->builder()
                ->whereHas('subject_teacher', function ($q) {
                    $q->where('teacher_id', Auth::user()->id);
                })
                ->where('day', $fullDayName)->orderBy('start_time', 'ASC')
                ->with('subject:id,name,type', 'class_section.class', 'class_section.section', 'class_section.medium')->get();
        }

        if ((Auth::user()->hasRole('School Admin') || Auth::user()->school_id) && (!Auth::user()->hasRole('Teacher') && !Auth::user()->hasRole('Super Admin')) ) {
            return view('dashboard', compact('teacher', 'parent', 'student', 'announcement', 'teachers', 'boys', 'girls', 'total_students','license_expire', 'subscription', 'previous_subscriptions', 'holiday', 'classData', 'prepiad_upcoming_plan', 'prepiad_upcoming_plan_type','check_payment','sessionYear','classes_counter','streams','exams', 'fees_detail', 'settings', 'class_names','announcement_news','e_invoice_reminder','progress','scores','admissionNotification','totalAdmissionNotification','rowCollection','rowPayroll','rowIncome'));
        }
        if (Auth::user()->hasRole('Teacher')) {
            return view('teacher_dashboard', compact('teacher', 'parent', 'student', 'announcement', 'teachers', 'boys', 'girls', 'holiday', 'timetables', 'classData','sessionYear','classes_counter','streams','class_names','total_students','exams','announcement_news'));
        }

        if (Auth::user()->hasRole('Super Admin') || Auth::user()->school_id == null) {
            return view('dashboard', compact('settings', 'super_admin','boys', 'girls', 'fees_detail','start_year','schools','staffs','addon_graph','package_graph', 'paymentConfiguration'));
        }
    }

    public function updateMailNotification(){
        $admissionNotification = AdmissionNotification::owner()->where('status',0)->get();
        foreach($admissionNotification as $a){
            AdmissionNotification::where('id',$a->id)->update(['status' => 1]);
        }
        return response()->json(['success' => true]);
    }
}
