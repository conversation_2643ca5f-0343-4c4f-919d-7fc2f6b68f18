<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class StudentFeesDetail extends Model
{
    use HasFactory;

    protected $fillable = [
        'student_fees_id',
        'school_id',
        'item_code',
        'fees_type_name',
        'fees_type_amount',
        'classification_code',
        'unit',
        'quantity',
        'discount',
        'tax',
        'optional',
        'remarks',
        'is_individual_fees',
    ];

    public function scopeOwner($query) {
        if (Auth::user()->hasRole('Super Admin')) {
            return $query;
        }

        if (Auth::user()->hasRole('School Admin') || Auth::user()->hasRole('Teacher')) {
            return $query->where('school_id', Auth::user()->school_id);
        }

        if (Auth::user()->hasRole('Student')) {
            return $query->where('school_id', Auth::user()->school_id);
        }

        return $query;
    }
}
