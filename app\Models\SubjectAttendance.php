<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SubjectAttendance extends Model
{
    protected $table = 'subject_attendances';
    
    protected $fillable = [
        "id",
        "school_id",
        "subjects_id",
        "student_id",
        "teacher_id",
        "status",
        "date",
        "clock_in",
        "clock_out",
        'in_temperature',
        'out_temperature',
        "total_time",
        "user_id",
        "commission_typee",
        "commission_amountt",
        "fees_per_session",
        "fees_per_month",
        "store"
    ];

    // Relationship with User model for students
    public function student()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }
}
