<?php

namespace App\Http\Controllers;

use Throwable;
use DOMDocument;
use DOMElement;
use DOMXPath;
use Carbon\Carbon;
use App\Models\Students;
use App\Models\StudentFee; 
use Illuminate\Http\Request;
use chillerlan\QRCode\QRCode;
use App\Models\StudentFeesPaid;
use Barryvdh\DomPDF\Facade\Pdf;
use App\Services\CachingService;
use App\Models\StudentFeesDetail;
use App\Services\ResponseService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use App\Services\BootstrapTableService;
use App\Services\EInvoiceFormatService;
use App\Repositories\User\UserInterface;
use Illuminate\Support\Facades\Validator;
use Klsheng\Myinvois\Helper\MyInvoisHelper;
use App\Helpers\EInvoiceHelper;
use App\Repositories\Medium\MediumInterface;
use App\Repositories\Student\StudentInterface;
use App\Repositories\FeesType\FeesTypeInterface;
use App\Repositories\ClassSchool\ClassSchoolInterface;
use App\Repositories\SessionYear\SessionYearInterface;
use App\Repositories\StudentFees\StudentFeesInterface;
use App\Repositories\CompulsoryFee\CompulsoryFeeInterface;
use App\Repositories\SchoolSetting\SchoolSettingInterface;
use App\Repositories\SystemSetting\SystemSettingInterface;
use App\Repositories\StudentFeeType\StudentFeeTypeInterface;
use App\Repositories\StudentFeesPaid\StudentFeesPaidInterface;
use App\Repositories\StudentFeesDetail\StudentFeesDetailInterface;
use App\Repositories\PaymentTransaction\PaymentTransactionInterface;
use App\Repositories\PaymentConfiguration\PaymentConfigurationInterface;
use Illuminate\Validation\Rule;



class StudentFeesController extends Controller
{
    private StudentFeesInterface $studentFees;
    private SessionYearInterface $sessionYear;
    private SchoolSettingInterface $schoolSettings;
    private MediumInterface $medium;
    private FeesTypeInterface $feesType;
    private ClassSchoolInterface $classes;
    private ClassSchoolInterface $class;
    private StudentFeesDetailInterface $studentFeesDetail;
    private UserInterface $user;
    private StudentFeesPaidInterface $studentFeesPaid;
    private CompulsoryFeeInterface $compulsoryFee;
    private CachingService $cache;
    private PaymentConfigurationInterface $paymentConfigurations;
    private StudentInterface $student;
    private PaymentTransactionInterface $paymentTransaction;
    private SystemSettingInterface $systemSetting;
    private StudentFeeTypeInterface $studentFeeType;
    private EInvoiceFormatService $eInvoiceFormatService;

    public function __construct(StudentFeesInterface $studentFees, SessionYearInterface $sessionYear, SchoolSettingInterface $schoolSettings, MediumInterface $medium, FeesTypeInterface $feesType, ClassSchoolInterface $classes, StudentFeesDetailInterface $studentFeesDetail, UserInterface $user, StudentFeesPaidInterface $studentFeesPaid, CompulsoryFeeInterface $compulsoryFee, CachingService $cache, PaymentConfigurationInterface $paymentConfigurations, ClassSchoolInterface $classSchool, StudentInterface $student, PaymentTransactionInterface $paymentTransaction, SystemSettingInterface $systemSetting, StudentFeeTypeInterface $studentFeeType, EInvoiceFormatService $eInvoiceFormatService)
    {
        $this->studentFees = $studentFees;
        $this->sessionYear = $sessionYear;
        $this->schoolSettings = $schoolSettings;
        $this->medium = $medium;
        $this->feesType = $feesType;
        $this->classes = $classes;
        $this->studentFeesDetail = $studentFeesDetail;
        $this->user = $user;
        $this->studentFeesPaid = $studentFeesPaid;
        $this->compulsoryFee = $compulsoryFee;
        $this->cache = $cache;
        $this->paymentConfigurations = $paymentConfigurations;
        $this->class = $classSchool;
        $this->student = $student;
        $this->paymentTransaction = $paymentTransaction;
        $this->systemSetting = $systemSetting;
        $this->studentFeeType = $studentFeeType;
        $this->eInvoiceFormatService = $eInvoiceFormatService;
    }

    public function index()
    {
        ResponseService::noFeatureThenRedirect('Fees Management');
        ResponseService::noPermissionThenRedirect('fees-list');
        // $startYear= 2023;
        // $endYear = date('Y');
        // $yearRange=range($startYear,$endYear);
        $sessionYear = $this->sessionYear->builder()->pluck('name', 'id');
        $defaultSessionYear = $this->cache->getDefaultSessionYear();

        $classes = $this->class->all(['*'], ['stream', 'medium', 'stream']);
        $simpleClassDropdown = $this->class->builder()->pluck('name', 'id');
        //$sessionYear = array_combine($yearRange,$yearRange);//session year here means the year no relate to the session year set by school
        //$defaultSessionYear = date('Y');
        $mediums = $this->medium->builder()->pluck('name', 'id');
        $feesTypeData = $this->feesType->all();
        $itemCode = DB::table('fees_type_master')->where('school_id',Auth::user()->school_id)->get();
        return view('student-fees.index', compact('simpleClassDropdown', 'classes', 'feesTypeData', 'sessionYear', 'defaultSessionYear', 'mediums', 'itemCode'));
    }

    public function store(Request $request)
    {
        ResponseService::noFeatureThenRedirect('Fees Management');
        ResponseService::noPermissionThenRedirect('fees-create');
        $request->validate([
            'due_date'                            => 'required|date' . ($request->early_date ? '|after:early_date' : ''),
            'due_charges_percentage'              => 'nullable|numeric',
            'due_charges_amount'                  => 'nullable|numeric',
            'early_date'                          => 'nullable|date',
            'early_offer_percentage'              => 'nullable|numeric',
            'early_offer_amount'                  => 'nullable|numeric',
            'class_id'                            => 'required|array',
            'class_id.*'                          => 'required|numeric',
            'student_id'                          => 'array',
            'student_id.*'                        => 'numeric',
            'total_cycles'                        => 'nullable|numeric|min:1',  
            'recurring_invoice'                   => 'nullable|string|in:no,1,2,3,6,12',  
        ], [
            'due_date.after' => 'The due date must be after the early date.',
        ]);
        if ($request->early_date === null) {
            $request->validate([
                'due_date' => 'required|date', 
            ]);
        }

        if(isset($request->tax_type) && $request->tax_percentage == null) {
            $request->validate([
                'tax_percentage' => 'required|numeric',
            ]);
        }


        try {
            DB::beginTransaction();
            $sessionYear = $this->cache->getDefaultSessionYear();
            $name = (!empty($request->name)) ? $request->name . " - " : "";
            $classStudents = [];
            $recurringInvoice = $request->recurring_invoice;
            $dueDate = Carbon::parse($request->due_date);
    
            // Calculate the next_due_date based on the selected recurring period
            if ($recurringInvoice && $recurringInvoice != 'no') {
                $monthsToAdd = (int) $recurringInvoice;  // Convert the selected option to an integer
                $nextDueDate = $dueDate->addMonths($monthsToAdd);  // Add the appropriate number of months
            } else {
                $nextDueDate = $dueDate;  // If no recurring invoice, set next_due_date same as due_date
            }

            $totalStudents = 0;
            $totalFeesCreated = 0;
            $totalCompulsoryFees = 0;

            //Individual student fees
            if ($request->has('is_individual_fees') && $request->is_individual_fees == 1) { 
                if (is_array($request->student_id) && is_array($request->individual_fees)) {
                    $students = Students::with(["class_section", "class_section.class"])
                        ->whereIn("id", $request->student_id)->get();
                    $totalStudents = count($students);
        
                    foreach ($students as $student) {
                        if (isset($request->individual_fees[0]['fees_type_name'])) {
                            $studentFeesData = [
                                'items' => $request->individual_fees
                            ];
                        } else {                        
                            $studentFeesData = collect($request->individual_fees)
                            ->firstWhere('student_id', $student->id);
                        }
                        
                        if (!$studentFeesData || empty($studentFeesData['items'])) {
                            continue; // No fee items for this student
                        }
                    
                        $totalFeesCreated++;

                        $fees = $this->studentFees->create([
                            'name'               => $name . $student->class_section->class->full_name,
                            'due_date'           => $request->due_date,
                            'due_charges'        => $request->due_charges_percentage ?? 0,
                            'due_charges_amount' => $request->due_charges_amount ?? 0,
                            'early_date'         => $request->early_date ? Carbon::parse($request->early_date)->format('Y-m-d') : null,
                            'early_offer'        => $request->early_offer_percentage ?? 0,
                            'early_offer_amount' => $request->early_offer_amount ?? 0,
                            'class_id'           => $student->class_section->class_id,
                            'school_id'          => $student->school_id,
                            'session_year_id'    => $sessionYear->id,
                            'student_id'         => $student->id,
                            'total_cycles'       => $request->total_cycles ?? null,
                            'recurring_invoice'  => $recurringInvoice ?? null, 
                            'invoice_date'       => $request->invoice_date ?? null, 
                            'auto_publish_date'  => $request->auto_publish_date ?? null, 
                            'tax_type'           => $request->tax_type,
                            'tax_percentage'     => $request->tax_percentage ?? '0.00',
                            'created_at_draft'   => now()
                        ]);
                    
                        $feeDetail = [];
                        $totalCompulsoryFees = 0;
                        foreach ($studentFeesData['items'] as $item) {
                            // Use unit_price if that's what your request sends
                            $unitPrice = $item['unit_price'] ?? $item['fees_type_amount'] ?? 0;
                            $quantity = $item['quantity'] ?? 1;
                            $discount = $item['discount'] ?? 0;
                            $tax = $item['tax'] ?? 0;
                        
                            $originalUnitPrice = $unitPrice;
                            
                            // Calculate the total amount with discount and tax
                            $amount = $originalUnitPrice * $quantity;
                            $amount = $amount - ($amount * ($discount / 100));
                            $amount = $amount + ($amount * ($tax / 100));
                            $totalCompulsoryFees += $amount;
                        
                            $feeDetail[] = [
                                "student_fees_id"      => $fees->id,
                                "fees_type_name"       => $item['fees_type_name'] ?? '',
                                "fees_type_amount"     => $originalUnitPrice, // Store the original unit price
                                "classification_code"  => $item['classification_code'] ?? '',
                                "quantity"             => $quantity,
                                "unit"                 => $item['unit'] ?? '',
                                "discount"             => $discount,
                                "tax"                  => $tax,
                                "optional"             => 0,
                                "school_id"            => $student->school_id,
                                "remarks"              => $item['remarks'] ?? '',
                            ];
                        }
                    
                        // Add early discount and overdue fees details
                        if(isset($fees->early_offer) && $fees->early_offer > 0){
                            $earlyfees = ($totalCompulsoryFees *$fees->early_offer/100)*-1;
                        } else {
                            $earlyfees = $fees->early_offer_amount*-1;
                        }
                        $earlyfees = number_format($earlyfees, 2, '.', '');
                    
                        if(isset($fees->due_charges) && $fees->due_charges > 0){
                            $duefees = $totalCompulsoryFees *$fees->due_charges/100;
                        } else {
                            $duefees = $fees->due_charges_amount;
                        }
                        $duefees = number_format($duefees, 2, '.', '');
                    
                        $feeDetail[] = array(
                            "student_fees_id"   => $fees->id,
                            "fees_type_name"    => "Early Discount",
                            "fees_type_amount"  => $earlyfees,
                            "classification_code" => "Early Discount",
                            "quantity"          => 1,
                            "unit"              => "-",
                            "discount"          => 0,
                            "tax"               => 0,
                            "optional"          => 0,
                            "school_id"         => $student->school_id,
                            "remarks"           => "-",
                        );
                    
                        $feeDetail[] = array(
                            "student_fees_id"   => $fees->id,
                            "fees_type_name"    => "Overdue Fees",
                            "fees_type_amount"  => $duefees,
                            "classification_code" => "Overdue Fees",
                            "quantity"          => 1,
                            "unit"              => "-",
                            "discount"          => 0,
                            "tax"               => 0,
                            "optional"          => 0,
                            "school_id"         => $student->school_id,
                            "remarks"           => "-",
                        );
                    
                        if (COUNT($feeDetail) > 0) {
                            $this->studentFeesDetail->upsert($feeDetail, ['student_fees_id'], ['fees_type_name', 'fees_type_amount', 'classification_code', 'unit', 'quantity','optional','tax', 'discount', 'remarks']);
                            \Log::info('Final fee details upserted', [
                                'student_fees_id' => $fees->id
                            ]);
                        }
                    }
                } else {
                    \Log::info('student_id or individual_fees is not an array', [
                        'student_id' => $request->student_id,
                        'individual_fees' => $request->individual_fees
                    ]);
                }
            }
            
            else if (count($request->student_id) > 0) {
                $classStudents = Students::with(["class_section", "class_section.class"])->whereIn("id", $request->student_id)->get();

                $totalStudents = COUNT($classStudents);
                foreach ($classStudents as $classStudent) {
                    $feeDetail = [];
                    $feeTypes = $this->studentFeeType->builder()->where("student_id", $classStudent->id)->get();
                    if (COUNT($feeTypes) > 0) {
                        $totalFeesCreated = $totalFeesCreated + 1;
                        $fees = $this->studentFees->create([
                            'name'               => $name . $classStudent->class_section->class->full_name,
                            'due_date'           => $request->due_date,
                            'due_charges'        => $request->due_charges_percentage ?? 0,
                            'due_charges_amount' => $request->due_charges_amount ?? 0,
                            'early_date' => $request->early_date ? Carbon::parse($request->early_date)->format('Y-m-d') : null,
                            'early_offer'        => $request->early_offer_percentage ?? 0,
                            'early_offer_amount' => $request->early_offer_amount ?? 0,
                            'class_id'           => $classStudent->class_section->class_id,
                            'school_id'          => $classStudent->school_id,
                            'session_year_id'    => $sessionYear->id,
                            'student_id'         => $classStudent->id,
                            'total_cycles'       => $request->total_cycles ?? null,
                            'recurring_invoice'  => $request->recurring_invoice ?? null, 
                            'invoice_date'       => $request->invoice_date ?? null, 
                            'auto_publish_date'  => $request->auto_publish_date ?? null, 
                            'tax_type'           => $request->tax_type,
                            'tax_percentage'     => $request->tax_percentage ?? '0.00',
                            'created_at_draft'   => now()
                        ]);
                        foreach ($feeTypes as $data) {
                          
                            
                            $totalCompulsoryFees += ($data->unit_price - ($data->unit_price * ($data->discount ?? 0) / 100)
                            + ($data->unit_price * ($data->tax ?? 0) / 100 ))*$data->quantity;

                            

                            // $total_amount = $data->unit_price * $data->quantity;
                            $feeDetail[] = array(
                                "student_fees_id"   => $fees->id,
                                "fees_type_name"    => $data->fees_type_name,
                                "fees_type_amount"  => $data->unit_price,
                                "classification_code" => $data->classification_code,
                                "quantity"          => $data->quantity ?? 1,
                                "unit"              => $data->unit,
                                "discount"          => $data->discount,
                                "tax"               => $data->tax,
                                "optional"          => 0,
                                "school_id"         => $classStudent->school_id,
                                "remarks"           => $data->remarks,
                                // "item_code"         => $data->item_code ?? ''
                                // "student_fee_types_id" => $data->id,
                            );
                        }
  
                        if(isset($fees->early_offer) && $fees->early_offer > 0){
                            $earlyfees = ($totalCompulsoryFees *$fees->early_offer/100)*-1;
                            }else{
                                $earlyfees =$fees->early_offer_amount*-1;
                            }
                        $earlyfees = number_format($earlyfees, 2, '.', '');
                   
                        if(isset($fees->due_charges) && $fees->due_charges > 0){
                            $duefees = $totalCompulsoryFees *$fees->due_charges/100;
                            }else{
                                $duefees =$fees->due_charges_amount;
                            }
                        $duefees = number_format($duefees, 2, '.', '');

                        $feeDetail[] = array(
                            "student_fees_id"   => $fees->id,
                            "fees_type_name"    => "Early Discount",
                            "fees_type_amount"  => $earlyfees,
                            "classification_code" => "Early Discount",
                            "quantity"          => 1,
                            "unit"              => "-",
                            "discount"          => 0,
                            "tax"               => 0,
                            "optional"          => 0,
                            "school_id"         => $classStudent->school_id,
                            "remarks"           => "-",
                            // "item_code"         => null
                        );

                        $feeDetail[] = array(
                            "student_fees_id"   => $fees->id,
                            "fees_type_name"    => "Overdue Fees",
                            "fees_type_amount"  => $duefees,
                            "classification_code" => "Overdue Fees",
                            "quantity"          => 1,
                            "unit"              => "-",
                            "discount"          => 0,
                            "tax"               => 0,
                            "optional"          => 0,
                            "school_id"         => $classStudent->school_id,
                            "remarks"           => "-",
                            // "item_code"         => null
                        );

                        if (COUNT($feeDetail) > 0) {
                            $this->studentFeesDetail->upsert($feeDetail, ['student_fees_id'], ['fees_type_name', 'fees_type_amount', 'classification_code', 'unit', 'quantity','optional','tax', 'discount', 'remarks']);
                        }
                    }
                }
            } else {
                $classes = $this->class->builder()->whereIn("id", $request->class_id)->with('stream', 'medium')->get();

                foreach ($request->class_id as $class_id) {
                    $class = $classes->first(function ($data) use ($class_id) {
                        return $data->id == $class_id;
                    });

                    $classStudents = DB::select("
                        SELECT  s.id AS student_id
                                , s.school_id
                                , cs.class_id
                        FROM    students s
                        JOIN    class_sections cs
                        ON      s.class_section_id = cs.id
                        WHERE   s.session_year_id = ?
                        AND     cs.class_id = ?
                        AND     s.id IN (
                            SELECT  student_id
                            FROM    student_fee_types
                        )
                    ", [$sessionYear->id, $class_id]);

                    $totalStudents = COUNT($classStudents);
                    foreach ($classStudents as $classStudent) {
                        $feeDetail = [];
                        $feeTypes = $this->studentFeeType->builder()->where("student_id", $classStudent->student_id)->get();
                        if (COUNT($feeTypes) > 0) {
                            $totalFeesCreated = $totalFeesCreated + 1;
                            $fees = $this->studentFees->create([
                                'name'               => $name . $class->full_name,
                                'due_date'           => $request->due_date,
                                'due_charges'        => $request->due_charges_percentage,
                                'due_charges_amount' => $request->due_charges_amount,
                                'early_date' => $request->early_date ? Carbon::parse($request->early_date)->format('Y-m-d') : null,
                                'early_offer'        => $request->early_offer_percentage ?? 0,
                                'early_offer_amount' => $request->early_offer_amount ?? 0,
                                'class_id'           => $class_id,
                                'school_id'          => $classStudent->school_id,
                                'session_year_id'    => $sessionYear->id,
                                'student_id'         => $classStudent->student_id,
                                'total_cycles'       => $request->total_cycles ?? null,
                                'recurring_invoice'  => $request->recurring_invoice ?? null,
                                'invoice_date'       => $request->invoice_date ?? null, 
                                'auto_publish_date'  => $request->auto_publish_date ?? null, 
                            ]);
                            foreach ($feeTypes as $data) {
                                // $totalAmount = $data->fees_type_amount * $data->quantity;
                                $feeDetail[] = array(
                                    "student_fees_id"   => $fees->id,
                                    "fees_type_name"    => $data->fees_type_name,
                                    "fees_type_amount"  => $data->unit_price ?? 0,
                                    "classification_code" => $data->classification_code,
                                    "unit"              => $data->unit,
                                    "optional"          => 0,
                                    "school_id" => $classStudent->school_id
                                );
                            }
                            if (COUNT($feeDetail) > 0) {
                                $this->studentFeesDetail->upsert($feeDetail, ['student_fees_id'], ['fees_type_name', 'fees_type_amount', 'classification_code', 'unit', 'optional']);
                            }
                        }
                    }
                }
            }

            DB::commit();
            ResponseService::successResponse(
                $totalFeesCreated . ' of ' . $totalStudents . ' invoice(s) created successfully' . ($totalFeesCreated != $totalStudents ? ' (Please ensure the selected students are assigned with at least 1 fee type)' : '')
            );
        } catch (Throwable $e) {
            DB::rollback();
            ResponseService::logErrorResponse($e, "FeesController -> Store Method");
            ResponseService::errorResponse();
        }
    }

    public function show()
    {
        ResponseService::noFeatureThenRedirect('Fees Management');
        ResponseService::noPermissionThenRedirect('fees-list');
        $offset = request('offset', 0);
        $limit = request('limit', 10);
        $sort = request('sort', 'id');
        $order = request('order', 'DESC');
        $search = request('search');
        $showDeleted = request('show_deleted') == 1;
        // $session_year_id = request('session_year_id');
        $start_date = request('start_date');
        $start_date = $start_date ? Carbon::createFromFormat('d-m-Y', $start_date)->startOfDay()->format('Y-m-d H:i:s') : null;
        $end_date = request('end_date');
        $end_date = $end_date ? Carbon::createFromFormat('d-m-Y', $end_date)->endOfDay()->format('Y-m-d H:i:s') : null;
      
        $class_id = request('class_id');
        $student_id = request('student_id');
        $medium_id = request('medium_id');
        $ticked = request('list_single');
        $einvoice_status = request('einvoice_status');
        $paymentStatus = request('payment_status');
        
        if ($ticked == 'list-ticked') {
            $sql2 = DB::table('student_fees as sf')
                        ->join('student_fees_details as sfd', 'sf.id', 'sfd.student_fees_id')
                        ->join('students as s', 'sf.student_id', '=', 's.id')
                        ->join('users as u', 's.user_id', '=', 'u.id')
                        ->select(
                            'sf.id',
                            'sfd.student_fees_id',
                            DB::raw("CONCAT(u.first_name, ' ', u.last_name) as student"),
                            DB::raw("CONCAT('INV', LPAD(sf.uid, 8, '0')) as invoice_number"),
                            'sf.invoice_date',
                            'sf.created_at',
                            'sf.auto_publish_date',
                            'sf.uid',
                            'sf.name',
                            'u.rfid_id AS rfid',
                            'sf.status',
                            's.id as student_id',
                            'sf.due_date',
                            'sf.due_charges',
                            'sf.due_charges_amount',
                            'sf.early_date',
                            'sf.early_offer',
                            'sf.early_offer_amount',
                            'sfd.fees_type_name',
                            'sfd.quantity',
                            'sfd.remarks',
                            'sfd.fees_type_amount',
                            DB::raw("sfd.fees_type_amount * sfd.quantity as total_amount"),
                            'sf.status',
                        ) 
                        ->where('sf.school_id', Auth::user()->school_id)
                        ->when($start_date && $end_date, function ($query) use ($start_date, $end_date) {
                            $query->whereBetween('sf.created_at', [$start_date, $end_date]);
                        })
                        ->when($start_date && !$end_date, function ($query) use ($start_date) {
                            $query->where('sf.created_at', '>=', $start_date);
                        })
                        ->when(!$start_date && $end_date, function ($query) use ($end_date) {
                            $query->where('sf.created_at', '<=', $end_date);
                        })
                        ->when($search, function ($query) use ($search) {
                            $query->where('sf.name', 'LIKE', "%$search%")
                                ->orWhere(DB::raw("CONCAT_WS(' ',u.first_name,u.last_name)"), "LIKE", "%$search%")
                                ->orWhere('s.rfid', 'LIKE', "%$search%")
                                ->orWhere(DB::raw("CONCAT('INV', LPAD(sf.uid, 8, '0'))"), 'LIKE', "%$search%");
                        })->whereNotIn('sfd.fees_type_name', ['Early Discount', 'Overdue Fees']);
            $total = $sql2->count();
            $sql2->orderBy($sort, $order)->skip($offset)->take($limit);
            $res = $sql2->get();
            $bulkData = array();
            $bulkData['total'] = $total;
            $rows = array();
            $no = 1;
            $sequence = 1;
            $lastId = null;
            foreach ($res as $row) {
                $operate = '';
                // if ($showDeleted) {
                //     $operate .= BootstrapTableService::restoreButton(route('student-fees.restore', $row->id));
                //     $operate .= BootstrapTableService::trashButton(route('student-fees.trash', $row->id));
                // } else {
                //     $operate .= BootstrapTableService::editButton(route('student-fees.edit', $row->id), false);
                //     $operate .= BootstrapTableService::deleteButton(route('student-fees.destroy', $row->id));
                //     // $operate .= BootstrapTableService::button(
                //     //     'fa fa-paper-plane',
                //     //     'javascript:doSetStudentFeeStatus(' . $row->id . ');',
                //     //     ['btn-gradient-primary'],
                //     //     ['title' => "Update Status"]
                //     // );
                //     // $operate .= BootstrapTableService::button('fa fa-eye', route('student-fees.paid.receipt.pdf', ['id' => $row->id,'filename' => 'invoice']), ['btn', 'btn-xs', 'btn-gradient-primary', 'btn-rounded', 'btn-icon', 'generate-paid-fees-pdf'], ['target' => "_blank", 'data-id' => $row->student_fee_id, 'title' => trans('generate_pdf') . ' ' . trans('fees')]);
                // }
                // $tempRow = (array) $row;
                $tempRow['no'] = $no++;
                $feesDetail = 0;
                $tempRow['invoice_number'] = $row->uid !== NULL ? ("INV" . sprintf('%08d', $row->uid)) : '-';
                // foreach ($row->student_fees_details as $fees) {
                //     $feesDetail += $fees->fees_type_amount * $fees->quantity;
                // }
                $tempRow['name'] = $row->name;
                $tempRow['rfid'] = $row->rfid;
                $tempRow['student.full_name'] = $row->student;
                $tempRow['created_at'] = $row->created_at;
                $tempRow['due_charges'] = $row->due_charges;
                $tempRow['due_charges_amount'] = $row->due_charges_amount;
                $tempRow['due_date'] = $row->due_date;
                $tempRow['early_date'] = empty($row->early_date) ? null : date('d-m-Y', strtotime($row->early_date));
                $tempRow['early_offer'] = $row->early_offer;
                $tempRow['early_offer_amount'] = $row->early_offer_amount;
                $tempRow['fees_type_name'] = $row->fees_type_name;
                $tempRow['quantity'] = $row->quantity;
                $tempRow['remarks'] = $row->remarks;
                $tempRow['compulsory_fees'] = number_format($row->fees_type_amount, 2);
                $tempRow['total_fees'] = number_format($row->total_amount,2);
                $tempRow['status'] = $row->status;
                $currentDate = date('Y-m-d');
                $due_date = $row->due_date;
                $due_date = date("Y-m-d", strtotime($due_date));
                $early_date = $early_date = empty($row->early_date) ? null : $row->early_date;
                $early_date = date("Y-m-d", strtotime($early_date));
                $feesPaid = DB::select('SELECT * FROM student_fees_paids WHERE student_fees_id = ? ', [$row->id]);
                // $total_fees = $row->student_fees_details->sum('fees_type_amount');
                // $total_fees = $row->total_amount;
                // if ($currentDate > $due_date && !$feesPaid) {
                //     if (!$row->due_charges) {
                //         $total_fees += $row->due_charges_amount;
                //     } else {
                //         $total_fees += ($total_fees * ($row->due_charges / 100));
                //     }
                // } elseif ($currentDate > $due_date && $feesPaid) {
                //     if ($due_date < $feesPaid[0]->date) {
                //         if (!$row->due_charges) {
                //             $total_fees += $row->due_charges_amount;
                //         } else {
                //             $total_fees += ($total_fees * ($row->due_charges / 100));
                //         }
                //     }
                // }
                // if ($currentDate <= $early_date && !$feesPaid) {
                //     if (!$row->early_offer) {
                //         $total_fees -= $row->early_offer_amount;
                //     } else {
                //         $total_fees -= ($total_fees * ($row->early_offer / 100));
                //     }
                // } elseif ($currentDate <= $early_date && $feesPaid) {
                //     $total_fees = $feesDetail;
                // }
                // $tempRow['total_fees'] = number_format($total_fees, 2);
                $tempRow['operate'] = $operate;
                $e_invoice_status = false;
                $e_invoice_exist = DB::table('e_invoice_guardian')->join('students', 'students.guardian_id', '=', 'e_invoice_guardian.guardian_id')->where('e_invoice_guardian.school_id',Auth::user()->school_id)->where('students.id', $row->student_id)->select('e_invoice_guardian.guardian_id')->first();
                if ($e_invoice_exist) {
                    $incompleteDataCount = DB::table('e_invoice_guardian')
                                            ->where('guardian_id', $e_invoice_exist->guardian_id)
                                            ->where('school_id',Auth::user()->school_id)
                                            ->where(function ($query) {
                                                $query->whereNull('name')
                                                    ->orWhere('name', '')
                                                    ->orWhereNull('ic_no')
                                                    ->orWhere('ic_no', '')
                                                    ->orWhereNull('tax_identification_number')
                                                    ->orWhere('tax_identification_number', '')
                                                    ->orWhereNull('address')
                                                    ->orWhere('address', '')
                                                    ->orWhereNull('city')
                                                    ->orWhere('city', '')
                                                    ->orWhereNull('postal_code')
                                                    ->orWhere('postal_code', '')
                                                    ->orWhereNull('country')
                                                    ->orWhere('country', '')
                                                    ->orWhereNull('state')
                                                    ->orWhere('state', '');
                                            })
                                            ->count();
                    if ($incompleteDataCount === 0) {
                        $e_invoice_status = true;
                    }
                    $tempRow['e_invoice_status'] = $e_invoice_status;
                    $sql_code = DB::table('e_invoice_guardian')->join('students', 'students.guardian_id', '=', 'e_invoice_guardian.guardian_id')->where('students.school_id',Auth::user()->school_id)->where('students.id', $row->student_id)->value('e_invoice_guardian.sql_code');
                    $tempRow['sql_code'] = $sql_code;

                    $currentId = $row->student_fees_id;
                    if($currentId == $lastId){
                        $tempRow['sequence'] = $sequence++;
                    } else {
                        $sequence = 1;
                        $tempRow['sequence'] = $sequence++;
                        $lastId = $currentId;
                    }

                    $rows[] = $tempRow;
                
                    
                }
                $tempRow['e_invoice_status'] = $e_invoice_status;
                $sql_code = DB::table('e_invoice_guardian')
                            ->join('students', 'students.guardian_id', '=', 'e_invoice_guardian.guardian_id')
                            ->where('e_invoice_guardian.school_id', Auth::user()->school_id) // Specify the table for school_id
                            ->where('students.id', $row->student_id)
                            ->value('e_invoice_guardian.sql_code');
                $tempRow['sql_code'] = $sql_code;
                $currentId = $row->student_fees_id;
                if($currentId == $lastId){
                    $tempRow['sequence'] = $sequence++;
                } else {
                    $sequence = 1;
                    $tempRow['sequence'] = $sequence++;
                    $lastId = $currentId;
                }
                $rows[] = $tempRow;
            }
            $bulkData['rows'] = $rows;
            return response()->json($bulkData);
        }

        $sql = $this->studentFees->builder()
            ->with('student', 'student.user', 'class:id,name,stream_id,medium_id', 'class.medium:id,name', 'class.stream:id,name', 'student_fees_details')
            ->select('student_fees.*') // Ensure you select the columns of interest
            ->where('student_fees.school_id', Auth::user()->school_id)
            ->withoutGlobalScope('Illuminate\Database\Eloquent\SoftDeletingScope')
            ->where(function($query) {
                if (request('show_deleted') == 1) {
                    $query->whereNotNull('student_fees.deleted_at');
                } else {
                    $query->whereNull('student_fees.deleted_at');
                }
            })
            ->when(request()->has('payment_status') && !empty(request('payment_status')), function($query) {
                $status = request('payment_status');
                
                if (in_array('viewAll', $status)) {
                    return $query; 
                }
                
                $query->where(function($q) use ($status) {
                    if (in_array('paid', $status)) {
                        $q->orWhereExists(function($subquery) {
                            $subquery->from('student_fees_paids')
                                ->whereRaw('student_fees_paids.student_fees_id = student_fees.id')
                                ->where('is_fully_paid', 1);
                        });
                    }
                    if (in_array('unpaid', $status)) {
                        $q->orWhereNotExists(function($subquery) {
                            $subquery->from('student_fees_paids')
                                ->whereRaw('student_fees_paids.student_fees_id = student_fees.id');
                        });
                    }
                    if (in_array('pending', $status)) {
                        $q->orWhereExists(function($subquery) {
                            $subquery->from('student_fees_paids')
                                ->whereRaw('student_fees_paids.student_fees_id = student_fees.id')
                                ->where('is_fully_paid', 0);
                        });
                    }
                });
            })
            ->where(function ($query) use ($search, $class_id) {
                $query->when($search, function ($query) use ($search, $class_id) {
                    $query->where('name', 'LIKE', "%$search%")
                        ->where('school_id', Auth::user()->school_id)
                        ->orwhere('due_date', 'LIKE', "%$search%")
                        ->orwhere('due_charges', 'LIKE', "%$search%")
                        ->orwhere('status', 'LIKE', "%$search%")
                        ->orWhere(DB::raw("CONCAT('INV', LPAD(uid, 9, '0'))"), 'LIKE', "%$search%")
                        ->orWhereHas("student.user", function ($query) use ($search) {
                            $query->where(DB::raw("CONCAT_WS(' ',first_name,last_name)"), "LIKE", "%$search%")
                            ->orWhere('rfid_id', 'LIKE', "%$search%");
                        })
                        ->orWhereIn("id", function ($query) use ($search, $class_id) {
                            $query->select("student_fees_id")
                                ->from("student_fees_details")
                                ->when($class_id, function ($query) use ($class_id) {
                                    if (count($class_id) > 0)
                                        $query->whereIn('class_id', $class_id);
                                })
                                ->groupBy("student_fees_id")
                                ->having(DB::raw("SUM(fees_type_amount * quantity)"), "LIKE", "%$search%");
                        })
                        ->orWhereRelation("student_fees_details", function ($query) use ($search) {
                            $query->where("fees_type_name", 'LIKE', "%$search%");
                        });
                });
            })
            // ->when($school_id, function ($query) use ($school_id) {
            //     $query->whereIn('school_id', Auth::user()->school_id);
            // })
            // ->when($session_year_id, function ($query) use ($session_year_id) {
            //     $query->where('session_year_id', $session_year_id);
            //     //$query->whereYear('created_at', $session_year_id);
            // })
            ->when($class_id, function ($query) use ($class_id) {
                if (count($class_id) > 0)
                    $query->whereIn('class_id', $class_id);
            })
            ->when($student_id, function ($query) use ($student_id) {
                if (count($student_id) > 0)
                    $query->whereIn('student_id', $student_id);
            })
            ->when($start_date && $end_date, function ($query) use ($start_date, $end_date) {
                $query->whereBetween('created_at', [$start_date, $end_date]);
            })
            ->when($start_date && !$end_date, function ($query) use ($start_date) {
                $query->where('created_at', '>=', $start_date);
            })
            ->when(!$start_date && $end_date, function ($query) use ($end_date) {
                $query->where('created_at', '<=', $end_date);
            })
            ->when(request()->has('einvoice_status') && !empty(request('einvoice_status')), function($query) {
                $einvoice_status = request('einvoice_status');
                if ($einvoice_status[0] !== '-') {
                    $query->join('student_fees_einvoice', 'student_fees_einvoice.student_fees_id', '=', 'student_fees.id')
                        ->whereIn('student_fees_einvoice.status', $einvoice_status);
                } else {
                    $query->leftJoin('student_fees_einvoice', 'student_fees_einvoice.student_fees_id', '=', 'student_fees.id')
                        ->whereNull('student_fees_einvoice.status');
                }
            })
            ->when($medium_id, function ($query) use ($medium_id) {
                $query->whereHas('class', function ($q) use ($medium_id) {
                    $q->where('medium_id', $medium_id);
                });
            });
        
           
        $total = $sql->count();
        $sql->orderBy($sort, $order)->skip($offset)->take($limit);
        $res = $sql->get();
        $bulkData = array();
        $bulkData['total'] = $total;
        $rows = array();
        $no = 1;

        foreach ($res as $row) {
            $totalDiscount=[];
            $totalTax=[];
            $sumDiscount = 0;
            $sumTax = 0;
            $sumFee=0;
            $i=0;
            $studentFeeSubmitted = DB::table('student_fees_einvoice')->where('student_fees_id',$row->id)->orderByDesc('id')->first();



            $consolidates = DB::table('student_fees_consolidate')
                            ->where('school_id',Auth::user()->school_id)
                            ->where('reference_uid',$row->uid)
                            ->get(['student_fees_id']);

            $studentFeesDetail = DB::table('student_fees_details')
                                    ->select('*')
                                    ->where('student_fees_id', '=', $row->id)
                                    ->whereNotIn('fees_type_name', ['Early Discount', 'Overdue Fees'])
                                    ->get(); 

            //Get total discount and tax
            foreach($studentFeesDetail as $studentFeeDetail){
                // Initialize array elements if not set
                if (!isset($totalDiscount[$i])) {
                    $totalDiscount[$i] = 0;
                }
                if (!isset($totalTax[$i])) {
                    $totalTax[$i] = 0;
                }

                // calculate discount for each item
                if($studentFeeDetail->discount > 0){
                    $itemDiscount = $studentFeeDetail->fees_type_amount * $studentFeeDetail->discount / 100;
                    $totalDiscount[$i] += $itemDiscount * $studentFeeDetail->quantity;
                    $sumDiscount += $totalDiscount[$i];
                    $tempRow['discount_amount'] = $totalDiscount[$i];
                }

                // calculate tax for each item
                if($studentFeeDetail->tax > 0){
                    $discountedAmount = $studentFeeDetail->fees_type_amount - ($studentFeeDetail->fees_type_amount * $studentFeeDetail->discount / 100);
                    $itemTax = $discountedAmount * $studentFeeDetail->tax / 100;
                    $totalTax[$i] += $itemTax * $studentFeeDetail->quantity;
                    $sumTax += $totalTax[$i];
                    $tempRow['tax_amount'] = $totalDiscount[$i];
                    
                }
                $sumFee+=$studentFeeDetail->fees_type_amount * $studentFeeDetail->quantity;

                $i++;
            }

            foreach($row->compulsory_fees as $c){
                $addDiscount = $c->fees_type_amount * $c->discount / 100;
                $c->fees_type_amount -= $addDiscount ?? 0;
                $addTax = $c->fees_type_amount * $c->tax / 100;
                $c->fees_type_amount += $addTax ?? 0;
                $c->fees_type_amount = $c->fees_type_amount * ($c->quantity ?? 1);
            }


            $operate = '';
            if ($showDeleted) {
                $operate .= BootstrapTableService::menuRestoreButton('restore',route('student-fees.restore', $row->id));
                $operate .= BootstrapTableService::menuTrashButton('delete',route('student-fees.trash', $row->id));
            } else {
                $operate .= BootstrapTableService::menuEditButton('edit',route('student-fees.edit', $row->id), false);
                if (!isset($studentFeeSubmitted) || $studentFeeSubmitted->status != 1) {
                    $operate .= BootstrapTableService::menuDeleteButton('delete',route('student-fees.destroy', $row->id));
                }
                // $operate .= BootstrapTableService::button(
                //     'fa fa-paper-plane',
                //     'javascript:doSetStudentFeeStatus(' . $row->id . ');',
                //     ['btn-gradient-primary'],
                //     ['title' => "Update Status"]
                // );
                $operate .= BootstrapTableService::menuButton('Update Status','javascript:doSetStudentFeeStatus(' . $row->id . ');',);
                // $operate .= BootstrapTableService::button('fa fa-eye', route('student-fees.paid.receipt.pdf', ['id' => $row->id, 'filename' => 'invoice']), ['btn', 'btn-xs', 'btn-gradient-primary', 'btn-rounded', 'btn-icon', 'generate-paid-fees-pdf'], ['target' => "_blank", 'data-id' => $row->student_fee_id, 'title' => trans('generate_pdf') . ' ' . trans('fees')]);
                $operate .= BootstrapTableService::menuButton('invoice', route('student-fees.paid.receipt.pdf', ['id' => $row->id, 'filename' => 'invoice']), ['generate-paid-fees-pdf'], ['target' => "_blank", 'data-id' => $row->student_fee_id]);

            }
            $tempRow = $row->toArray();
            
            $tempRow['no'] = $no++;
            $tempRow['quantity'] = $row->uid !== NULL ? ("INV" . sprintf('%08d', $row->uid)) : '-';
            $tempRow['invoice_number'] = $row->uid !== NULL ? ("INV" . sprintf('%08d', $row->uid)) : '-';
            $tempRow['compulsory_fees'] = number_format($sumFee - $sumDiscount + $sumTax, 2);
            $tempRow['rfid_id'] = $row->student->user->rfid_id ?? '-';
            //$tempRow['student.full_name'] = $row->student->full_name;

            $feesStatus = DB::table("student_fees_paids")
                ->select(
                    "mode",
                    DB::raw('MAX(student_fees_paids.status) as fees_status'),
                    "date as fee_paid_date",
                    "is_fully_paid"
                )
                ->where("student_fees_paids.student_fees_id", $row->id)
                ->groupBy('mode', 'date', 'is_fully_paid')
                ->first();

            if ($feesStatus) {
                $tempRow['fees_status'] = $feesStatus->fees_status;
                $tempRow['fee_paid_date'] = $feesStatus->fee_paid_date;
                $tempRow['mode'] = $feesStatus->mode;
            } else {
                $tempRow['fees_status'] = '';
                $tempRow['fee_paid_date'] = '';
                $tempRow['mode'] = '';
            }
            $currentDate = date('Y-m-d');
            $due_date = $row->due_date;
            $due_date = date("Y-m-d", strtotime($due_date));
            $early_date = $row->early_date;
            $early_date = date("Y-m-d", strtotime($early_date));
            $studentFees = DB::table('student_fees')->where('id',$row->id)->first();
            $feesPaid = DB::select('SELECT * FROM student_fees_paids WHERE student_fees_id = ? ', [$row->id]);
            $total_fees = $sumFee - $sumDiscount + $sumTax;

            if(isset($row->tax_percentage) && $row->tax_percentage > 0){
                $extraTax = ($total_fees * ($row->tax_percentage / 100));
                $total_fees += $extraTax;
                $tempRow['extra_tax_amount'] = number_format($extraTax,2);
            }

            if ($currentDate > $due_date && !$feesPaid) {
                if (!$studentFees->due_charges) {
                    $total_fees += $studentFees->due_charges_amount;
                } else {
                    $total_fees += ($total_fees * ($studentFees->due_charges / 100));
                }
            } elseif ($currentDate > $due_date && $feesPaid) {
                if ($due_date < $feesPaid[0]->date) {
                    if (!$studentFees->due_charges) {
                        $total_fees += $studentFees->due_charges_amount;
                    } else {
                        $total_fees += ($total_fees * ($studentFees->due_charges / 100));
                    }
                }
            }
            if ($currentDate <= $early_date && !$feesPaid  && $currentDate < $due_date) {
                if (!$studentFees->early_offer) {
                    $total_fees -= $studentFees->early_offer_amount;
                } else {
                    $total_fees -= ($total_fees * ($studentFees->early_offer / 100));
                }
            } elseif ($feesPaid){
                if($feesPaid[0]->date <= $early_date  && $currentDate < $due_date){
                    if (!$studentFees->early_offer) {
                        $total_fees -= $studentFees->early_offer_amount;
                    } else {
                        $total_fees -= ($total_fees * ($studentFees->early_offer / 100));
                    }
                }
            }

            $tempRow['total_fees'] = number_format($total_fees, 2);
            if(count($consolidates) > 0){
                $studentFeesIds = $consolidates->pluck('student_fees_id')->toArray();

                $studentFeesPaids = DB::table('student_fees_paids')
                ->select('*')
                ->wherein('student_fees_id', $studentFeesIds)
                ->sum('amount');                
                $tempRow['compulsory_fees'] = number_format($studentFeesPaids, 2);
                $tempRow['total_fees'] = number_format($studentFeesPaids, 2);
                $tempRow['due_date'] = '-';
                $tempRow['total_cycles'] = '-';
                $tempRow['status'] = '-';
                $tempRow['student.full_name'] = '-';
                if ($showDeleted) {
                    $operate = BootstrapTableService::menuRestoreButton('restore',route('student-fees.restore', $row->id));
                    $operate .= BootstrapTableService::menuTrashButton('delete',route('student-fees.trash', $row->id));
                }else{
                    $operate = BootstrapTableService::menuButton('invoice', route('student-fees.paid.receipt.pdf', ['id' => $row->id, 'filename' => 'invoice']), ['generate-paid-fees-pdf'], ['target' => "_blank", 'data-id' => $row->student_fee_id]);
                    if (!isset($studentFeeSubmitted) || $studentFeeSubmitted->status != 1) {
                        $operate .= BootstrapTableService::menuDeleteButton('delete',route('student-fees.destroy', $row->id));
                    }
                }
            }
            $tempRow['early_date'] = empty($row->early_date) ? null : date('d-m-Y', strtotime($row->early_date));
            $tempRow['total_cycles'] = $row->total_cycles ?? '0'; 
            if(!isset($row->recurring_invoice) && $row->current_cycle && $row->total_cycles){
                $tempRow['total_cycles'] = $row->current_cycle . "/" .$row->total_cycles;
            }
            $tempRow['recurring_invoice'] = $row->recurring_invoice; 
            $tempRow['quantity'] = "-";

            if($studentFeeSubmitted){
                $tempRow['submitted_e_invoice'] = $studentFeeSubmitted->status;
            }
            if (isset($studentFeeSubmitted) && $studentFeeSubmitted->status == 1){
                $operate .= BootstrapTableService::menuButton('cancel e-invoice',route('student-fees.cancel-e-invoicing',['id' => $row->id]), ['cancel-einvoice']);
                $operate .= BootstrapTableService::menuButton('check_status', route('student-fees.check-e-invoice-status', ['id' => $row->id]), ['check-einvoice-status']);
            } else if (isset($studentFeeSubmitted) && $studentFeeSubmitted->status == 0) {
                $operate .= BootstrapTableService::menuButton('check_status', route('student-fees.check-e-invoice-status', ['id' => $row->id]), ['check-einvoice-status']);
            } else if ($row->status == 'published') {
                $operate .= BootstrapTableService::menuButton('submit e-invoice',route('student-fees.submit-e-invoicing',['id' => $row->id]), ['submit-einvoice']);
            }
            $tempRow['operate'] = BootstrapTableService::menuItem($operate);

            $e_invoice_status = false;
            $e_invoice_exist = DB::table('e_invoice_guardian')->join('students', 'students.guardian_id', '=', 'e_invoice_guardian.guardian_id')->where('e_invoice_guardian.school_id',Auth::user()->school_id)->where('students.id', $row->student_id)->select('e_invoice_guardian.guardian_id','e_invoice_guardian.sql_code')->first();
            if ($e_invoice_exist) {
                $incompleteDataCount = DB::table('e_invoice_guardian')
                    ->where('guardian_id', $e_invoice_exist->guardian_id)
                    ->where('school_id',Auth::user()->school_id)
                    ->where(function ($query) {
                        $query->whereNull('name')
                            ->orWhere('name', '')
                            ->orWhereNull('ic_no')
                            ->orWhere('ic_no', '')
                            ->orWhereNull('tax_identification_number')
                            ->orWhere('tax_identification_number', '')
                            ->orWhereNull('address')
                            ->orWhere('address', '')
                            ->orWhereNull('city')
                            ->orWhere('city', '')
                            ->orWhereNull('postal_code')
                            ->orWhere('postal_code', '')
                            ->orWhereNull('country')
                            ->orWhere('country', '')
                            ->orWhereNull('state')
                            ->orWhere('state', '');
                    })
                    ->count();
                if ($incompleteDataCount === 0) {
                    $e_invoice_status = true;
                }
            }
            $tempRow['e_invoice_status'] = $e_invoice_status;
            $sql_code=DB::table('e_invoice_guardian')->join('students', 'students.guardian_id', '=', 'e_invoice_guardian.guardian_id')->where('e_invoice_guardian.school_id',Auth::user()->school_id)->where('students.id', $row->student_id)->value('e_invoice_guardian.sql_code');
            $tempRow['sql_code'] = $sql_code;
            // $tempRow['select_multiple'] = '<input type="checkbox" class="select-row selectDetail" data-id="' . $row->id .'">';
            $rows[] = $tempRow;
        }
        
        $bulkData['rows'] = $rows;
      
        return response()->json($bulkData);
    }

    public function edit($id)
    {
        ResponseService::noFeatureThenRedirect('Fees Management');
        ResponseService::noPermissionThenRedirect('fees-edit');
        $classes = $this->class->all(['*'], ['stream', 'medium', 'stream']);
        $feesTypeData = $this->feesType->all();
        
        $fees = $this->studentFees->builder()
        ->with([
            'student_fees_details' => function ($query) {
                $query->whereNotIn('fees_type_name', ['Overdue Fees', 'Early Discount']);
            },
            'student.user',
            'class.medium'
        ])
        ->withCount('student_fees_paid')
        ->findOrFail($id);
        if($fees->compulsory_fees){
            foreach($fees->compulsory_fees as $f){
                if($f){
                    $checkItemCode = DB::table('fees_type_master')->where('name',$f->fees_type_name)->where('school_id',Auth::user()->school_id)->first();
                    if($checkItemCode){
                        $f->item_code = $checkItemCode->item_code; 
                        $f->remarks = DB::table('student_fees_details')
                        ->where('student_fees_id', $fees->id)
                        ->where('fees_type_name', $f->fees_type_name)
                        ->value('remarks')?? $checkItemCode->remarks ?? null;
                } else {
                    $f->item_code = "";
                    $f->remarks = null;
                    }
                }
            }
        }
        $itemCode = DB::table('fees_type_master')->where('school_id',Auth::user()->school_id)->get();
        $referenceUID = null;
        if(isset($fees->recurring_reference)){
            $referenceUID = DB::table('student_fees')->where('id', $fees->recurring_reference)->value('uid');
        }

        // Format the invoice number for display in the form
        $formattedInvoiceNumber = $fees->uid ? $fees->uid : '';

        return view('student-fees.edit', compact('classes', 'feesTypeData', 'fees', 'itemCode', 'referenceUID', 'formattedInvoiceNumber'));
    }

    public function update(Request $request, $id)
    {
        ResponseService::noFeatureThenRedirect('Fees Management');
        ResponseService::noPermissionThenRedirect('fees-edit');

        $request->validate([
            'due_date'                            => 'required|date',
            'due_charges_percentage'              => 'nullable|numeric',
            'due_charges_amount'                  => 'nullable|numeric',
            'early_date'                          => 'nullable | date',
            'early_offer_percentage'              => 'nullable | numeric',
            'early_offer_amount'                  => 'nullable | numeric',
            'status'                              => 'required',
            'invoice_date'                        => 'required',
            'compulsory_fees_type.*.remarks'      => 'nullable|string',
            'invoice_number' => [
                'nullable',
                'numeric',
                Rule::unique('student_fees', 'uid') // Table and column
                    ->whereNull('deleted_at')       // Explicitly exclude soft-deleted records
                    ->where('school_id', Auth::user()->school_id)
                    ->ignore($id, 'id'),            // Exclude current record by primary key
            ],
        ]);

        try {
            DB::beginTransaction();
            $sessionYear = $this->cache->getDefaultSessionYear();

            // Fees Data Store
            $feesData = array(
                'name'               => $request->name,
                'invoice_date'       => Carbon::parse($request->invoice_date)->format('Y-m-d'),
                'due_date'           => $request->due_date,
                'due_charges'        => $request->due_charges_percentage ?? 0,
                'due_charges_amount' => $request->due_charges_amount ?? 0,
                'early_date' => $request->early_date ? Carbon::parse($request->early_date)->format('Y-m-d') : null,
                'early_offer'        => $request->early_offer_percentage ?? 0,
                'early_offer_amount' => $request->early_offer_amount ?? 0,
                'status' => $request->status,
                'recurring_invoice' => $request->recurring_invoice ?? null,
                'total_cycles'      => $request->recurring_invoice != null ? $request->total_cycles : null,
                'tax_type'          => $request->tax_type ?? null,
                'tax_percentage'    => $request->tax_percentage,
            );
            if ($request->created_at) {
                $existingCreatedAt = Carbon::parse($request->created_at);
                $currentTime = DB::table('student_fees')->where('id', $id)->value('created_at');
                $currentCreatedAtCarbon = Carbon::parse($currentTime);
                $updatedCreatedAt = $currentCreatedAtCarbon->copy()->setDate(
                    $existingCreatedAt->year,
                    $existingCreatedAt->month,
                    $existingCreatedAt->day
                );
                if ($updatedCreatedAt) {
                    $feesData['created_at'] = $updatedCreatedAt->format('Y-m-d H:i:s');
                }
            }
            if ($request->has('invoice_number') && !empty($request->invoice_number)) {
                $feesData['uid'] = $request->invoice_number;
            }
            $fees = $this->studentFees->update($id, $feesData);
            if($request->status == "published" && empty($fees->uid)){
                $latestUID = DB::table('student_fees')->where('school_id', Auth::user()->school_id)->where('status', 'published')->whereNull('deleted_at')->select('uid')->orderBy('uid', 'desc')->value('uid');
                $uid = $latestUID ? $latestUID + 1 : 1;
                while (DB::table('student_fees')
                    ->where('uid', $uid)
                    ->where('school_id', Auth::user()->school_id)
                    ->where('status', 'published')
                    ->exists()
                ) {
                    $uid++;
                }
                DB::table('student_fees')->where('id', $fees->id)->update(["uid" => $uid,"created_at" => now()]);
                $uid++;
            }

            $feeDetail = [];
            $totalCompulsoryFees = 0;
            //$feeTypes = $this->studentFeeType->builder()->where("student_id",$classStudent->student_id)->get();
            foreach ($request->compulsory_fees_type as $data) {
                $totalCompulsoryFees += ($data['fees_type_amount'] - ($data['fees_type_amount'] * ($data['discount'] ?? 0) / 100)
                + ($data['fees_type_amount'] * ($data['tax'] ?? 0) / 100 ))*$data['quantity'];

                if(isset($data['item_code'])){
                    $itemCode = DB::table("fees_type_master")
                    ->where("id", $data['item_code'])
                    ->value('item_code'); 
                }
                $feeDetail[] = array(
                    "id"                    => $data['id'],
                    "student_fees_id"       => $fees->id,
                    "fees_type_name"        => $data['fees_type_name'],
                    "fees_type_amount"      => $data['fees_type_amount'],
                    "classification_code"   => $data['classification_code'],
                    "unit"                  => $data['unit'],
                    "quantity"              => $data['quantity'],
                    "discount"              => $data['discount'],
                    "tax"                   => $data['tax'],
                    "optional"              => 0,
                    "school_id"             => $fees->school_id,
                    "item_code"             => $itemCode ?? null,
                    "remarks"               => $data['remarks']?? null,
                );
            }

            if (isset($feeDetail)) {
                $this->studentFeesDetail->upsert($feeDetail, ['id'], ['fees_type_name', 'fees_type_amount', 'classification_code', 'unit', 'quantity', 'optional','tax','discount','remarks']);
            }

            //Add Extra Tax 
            if(isset($fees->tax_type) && $fees->tax_percentage > 0){
                $totalCompulsoryFees += ($totalCompulsoryFees *$fees->tax_percentage/100);
            }

            if(isset($fees->early_offer) && $fees->early_offer > 0){
                $earlyfees = ($totalCompulsoryFees *$fees->early_offer/100)*-1;
            }else{
                $earlyfees =$fees->early_offer_amount*-1;
            }
            $earlyfees = number_format($earlyfees, 2, '.', '');
       
            if(isset($fees->due_charges) && $fees->due_charges > 0){
                $duefees = $totalCompulsoryFees *$fees->due_charges/100;
            }else{
                $duefees =$fees->due_charges_amount;
            }
            $duefees = number_format($duefees, 2, '.', '');


            $feeDetails = DB::select('SELECT * FROM student_fees_details WHERE fees_type_name = ? AND student_fees_id = ?', ["Early Discount", $fees->id]);
            if (!empty($feeDetails)) {
                DB::table('student_fees_details')
                    ->where('id', $feeDetails[0]->id)
                    ->update(['fees_type_amount' => $earlyfees ?? 0]);
            }

            $feeDetails = DB::select('SELECT * FROM student_fees_details WHERE fees_type_name = ? AND student_fees_id = ?', ["Overdue Fees", $fees->id]);
            if (!empty($feeDetails)) {
                DB::table('student_fees_details')
                    ->where('id', $feeDetails[0]->id)
                    ->update(['fees_type_amount' => $duefees ?? 0]);
            }
            
            DB::commit();
            ResponseService::successRedirectResponse(route('student-fees.index'), 'Data Update Successfully');
        } catch (Throwable $e) {
            DB::rollback();
            // ResponseService::errorResponse($e->getMessage());
            ResponseService::errorRedirectResponse();
        }
    }

    public function destroy($id)
    {
        ResponseService::noFeatureThenRedirect('Fees Management');
        ResponseService::noPermissionThenSendJson('fees-delete');
        $feeStatus = DB::table('student_fees_paids')
            ->where('student_fees_id', $id)
            ->value('is_fully_paid');

        if ($feeStatus) {
            return response()->json([
                'error' => true,
                'message' => 'Cannot Delete Paid Fees'
            ], 400);
        }

        $consolidateInvoice = DB::table('student_fees')
        ->join('student_fees_consolidate', 'student_fees_consolidate.reference_uid', '=', 'student_fees.uid')
        ->where('student_fees.id', $id)
        ->get();
        try {
            if(COUNT($consolidateInvoice)   > 0 && isset($consolidateInvoice)){
                DB::beginTransaction();
                $referenceUids = $consolidateInvoice->pluck('reference_uid')->toArray();
                $this->studentFees->deleteById($id);

                DB::table('student_fees_consolidate')
                    ->whereIn('reference_uid', $referenceUids)
                    ->update(['deleted_at' => now()]);

             
                DB::commit();
                ResponseService::successResponse("Data Deleted Successfully");
            }else{
                DB::beginTransaction();
                $this->studentFees->deleteById($id);
                DB::commit();
                ResponseService::successResponse("Data Deleted Successfully");
            }
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "StudentFeesController -> Store Method");
            ResponseService::errorResponse();
        }
    }

    public function restore(int $id)
    {
        ResponseService::noFeatureThenRedirect('Fees Management');
        ResponseService::noPermissionThenRedirect('fees-delete');
        $consolidateInvoice = DB::table('student_fees')
        ->join('student_fees_consolidate', 'student_fees_consolidate.reference_uid', '=', 'student_fees.uid')
        ->where('student_fees.id', $id)
        ->get();
        try {
            if(COUNT($consolidateInvoice)   > 0 && isset($consolidateInvoice)){
                DB::beginTransaction();
                $referenceUids = $consolidateInvoice->pluck('reference_uid')->toArray();
                $this->studentFees->findOnlyTrashedById($id)->restore();

                DB::table('student_fees_consolidate')
                    ->whereIn('reference_uid', $referenceUids)
                    ->update(['deleted_at' => null]);

                DB::commit();
                ResponseService::successResponse("Data Restored Successfully");
            }else{
                $this->studentFees->findOnlyTrashedById($id)->restore();
                ResponseService::successResponse("Data Restored Successfully");
            }
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function search(Request $request)
    {
        ResponseService::noFeatureThenRedirect('Fees Management');
        try {

            $data = [];
            $chkCnt = $this->studentFees->builder()
                ->where('session_year_id', $request->session_year_id)
                ->count();

            if ($chkCnt > 0) {
                $data = $this->studentFees->builder()
                    ->where('session_year_id', $request->session_year_id)
                    ->select([DB::raw("MIN(id) AS id"), 'name'])
                    ->groupBy("name")
                    ->get();
            }

            ResponseService::successResponse("Data Restored Successfully", $data);
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function getFeeClass(Request $request)
    {
        $feeClasses = DB::select("
            SELECT  c.id
                    , c.name
            FROM    classes c
            WHERE   c.id IN (
                SELECT class_id
                FROM    student_fees
                WHERE   id = ?
            )
        ", [$request->student_fee_id]);

        return response()->json($feeClasses);
    }

    public function getClassStudent(Request $request)
    {
        $filterVal = $request->class_id;
        if (gettype($request->class_id) == "array") {
            $filterVal = implode(",", $filterVal);
        }


        $sessionYearId = $this->cache->getDefaultSessionYear()->id;
        if ($request->session_year_id) {
            $sessionYearId = $request->session_year_id;
        }
        
        $query = "
            SELECT  s.id
                    , concat(u.first_name,' ', u.last_name) AS fullname
                    , s.school_id
            FROM    students s
            JOIN    class_sections cs
            ON      s.class_section_id = cs.id
            JOIN    users u
            ON      s.user_id = u.id
            WHERE   s.session_year_id = ?
            AND     u.status = 1
        ";
        $params = [$sessionYearId];
        if (!empty($filterVal)) {
            // Convert $filterVal to an array if it's not already
            $filterArray = is_array($filterVal) ? $filterVal : explode(',', $filterVal);
            $placeholders = implode(',', array_fill(0, count($filterArray), '?'));
            $query .= " AND cs.class_id IN ($placeholders)";
            $params = array_merge($params, $filterArray);
        }
        $classStudents = DB::select($query, $params);

        return response()->json($classStudents);
    }

    public function getDocumentClassStudent(Request $request)
    {
        $filterVal = $request->class_id;
        if (gettype($request->class_id) == "array") {
            $filterVal = implode(",", $filterVal);
        }


        $sessionYearId = $this->cache->getDefaultSessionYear()->id;
        if ($request->session_year_id) {
            $sessionYearId = $request->session_year_id;
        }
        

        $classStudents = DB::select("
            SELECT  s.id
                    , concat(u.first_name,' ', u.last_name) AS fullname
                    , s.school_id
            FROM    students s
            JOIN    class_sections cs
            ON      s.class_section_id = cs.id
            JOIN    users u
            ON      s.user_id = u.id
            WHERE   cs.id IN ($filterVal)
            AND     s.session_year_id = ?
            AND     u.status = 1
        ", [$sessionYearId]);
        

        return response()->json($classStudents);
    }

    public function getSessionClass(Request $request)
    {

        $sessionYearId = $request->session_year_id;
        $sessionClasses = DB::select("
            SELECT DISTINCT c.id, c.name
            FROM classes c
            JOIN student_fees sf ON c.id = sf.class_id
            JOIN session_years sy ON sf.session_year_id = sy.id
            WHERE sy.id = ?;

        ", [$sessionYearId]);

        return response()->json($sessionClasses);
    }

    public function trash($id)
    {
        ResponseService::noFeatureThenRedirect('Fees Management');
        ResponseService::noPermissionThenRedirect('fees-delete');
        try {
            $this->studentFees->findOnlyTrashedById($id)->forceDelete();
            ResponseService::successResponse("Data Deleted Permanently");
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function deleteStudentFeeDetail($id)
    {
        ResponseService::noFeatureThenRedirect('Fees Management');
        try {
            DB::beginTransaction();
            $this->studentFeesDetail->DeleteById($id);
            DB::commit();
            ResponseService::successResponse("Data Deleted Successfully");
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
        }
    }

    public function studentFeesPaidListIndex()
    {
        ResponseService::noFeatureThenRedirect('Fees Management');
        ResponseService::noPermissionThenRedirect('fees-paid');

        // Fees Data With Few Selected Data
        $fees = $this->studentFees->builder()
            ->where("session_year_id", $this->cache->getDefaultSessionYear()->id)
            ->select([DB::raw("MIN(id) AS id"), 'name'])
            ->groupBy("name")
            ->orderBy('created_at_draft','desc')
            ->get();
        $classes = $this->classes->all(['*'], ['medium', 'sections']);
        $session_year_all = $this->sessionYear->all(['id', 'name', 'default','start_date','end_date']);
        // dd($session_year_all);

        return response(view('student-fees.student_fees_paid', compact('fees', 'classes', 'session_year_all')));
    }

    public function feesCollectionReportIndex()
    {
        ResponseService::noFeatureThenRedirect('Reporting');
        ResponseService::noPermissionThenRedirect('fees-paid');

        // Fees Data With Few Selected Data
        $fees = $this->studentFees->builder()
            ->where("session_year_id", $this->cache->getDefaultSessionYear()->id)
            ->select([DB::raw("MIN(id) AS id"), 'name'])
            ->groupBy("name")
            ->get();
        $classes = $this->classes->all(['*'], ['medium', 'sections']);
        $session_year_all = $this->sessionYear->all(['id', 'name', 'default']);
        $currencySymbol = $this->cache->getSchoolSettings('currency_symbol');
        return response(view('student-fees.fees_collection_report', compact('fees', 'classes', 'session_year_all', 'currencySymbol')));
    }

    public function feesPendingReportIndex()
    {
        ResponseService::noFeatureThenRedirect('Reporting');
        ResponseService::noPermissionThenRedirect('fees-paid');

        // Fees Data With Few Selected Data
        $fees = $this->studentFees->builder()
            ->where("session_year_id", $this->cache->getDefaultSessionYear()->id)
            ->select([DB::raw("MIN(id) AS id"), 'name'])
            ->groupBy("name")
            ->get();
        $classes = $this->classes->all(['*'], ['medium', 'sections']);
        $session_year_all = $this->sessionYear->all(['id', 'name', 'default']);
        $currencySymbol = $this->cache->getSchoolSettings('currency_symbol');
        return response(view('student-fees.fees_pending_report', compact('fees', 'classes', 'session_year_all', 'currencySymbol')));
    }

    public function feesOutstandingReportIndex()
    {
        ResponseService::noFeatureThenRedirect('Reporting');
        ResponseService::noPermissionThenRedirect('fees-paid');

        // Fees Data With Few Selected Data
        $fees = $this->studentFees->builder()
            ->where("session_year_id", $this->cache->getDefaultSessionYear()->id)
            ->select([DB::raw("MIN(id) AS id"), 'name'])
            ->groupBy("name")
            ->get();
        $classes = $this->classes->all(['*'], ['medium', 'sections']);
        $session_year_all = $this->sessionYear->all(['id', 'name', 'default']);
        $currencySymbol = $this->cache->getSchoolSettings('currency_symbol');
        return response(view('student-fees.fees_outstanding_report', compact('fees', 'classes', 'session_year_all', 'currencySymbol')));
    }

    public function feesStudentReportIndex()
    {
        ResponseService::noFeatureThenRedirect('Reporting');
        ResponseService::noPermissionThenRedirect('fees-paid');

        // Fees Data With Few Selected Data
        $fees = $this->studentFees->builder()
            ->where("session_year_id", $this->cache->getDefaultSessionYear()->id)
            ->select([DB::raw("MIN(id) AS id"), 'name'])
            ->groupBy("name")
            ->get();
        $classes = $this->classes->all(['*'], ['medium', 'sections']);
        $session_year_all = $this->sessionYear->all(['id', 'name', 'default']);
        $currencySymbol = $this->cache->getSchoolSettings('currency_symbol');
        $student_name_all = DB::table('students')
        ->join('users', 'users.id', '=', 'students.user_id')
        ->select('students.id', DB::raw("CONCAT(users.first_name, ' ', users.last_name) as full_name"))
        ->groupBy('full_name')
        ->where('users.school_id', '=', auth()->user()->school_id) 
        ->get();
        return response(view('student-fees.fees_student_report', compact('fees', 'classes', 'session_year_all', 'currencySymbol','student_name_all')));
    }

    public function feesTypeReportIndex()
    {
        ResponseService::noFeatureThenRedirect('Reporting');
        ResponseService::noPermissionThenRedirect('fees-paid');

        // Fees Data With Few Selected Data
        $fees = $this->studentFees->builder()
            ->where("session_year_id", $this->cache->getDefaultSessionYear()->id)
            ->select([DB::raw("MIN(id) AS id"), 'name'])
            ->groupBy("name")
            ->get();
        $classes = $this->classes->all(['*'], ['medium', 'sections']);
        $session_year_all = $this->sessionYear->all(['id', 'name', 'default']);
        $currencySymbol = $this->cache->getSchoolSettings('currency_symbol');
        return response(view('student-fees.fees_type_report', compact('fees', 'classes', 'session_year_all', 'currencySymbol')));
    }

    public function feesClassReportIndex()
    {
        ResponseService::noFeatureThenRedirect('Reporting');
        ResponseService::noPermissionThenRedirect('fees-paid');

        // Fees Data With Few Selected Data
        $fees = $this->studentFees->builder()
            ->where("session_year_id", $this->cache->getDefaultSessionYear()->id)
            ->select([DB::raw("MIN(id) AS id"), 'name'])
            ->groupBy("name")
            ->get();
        $classes = $this->classes->all(['*'], ['medium', 'sections']);
        $session_year_all = $this->sessionYear->all(['id', 'name', 'default']);
        $currencySymbol = $this->cache->getSchoolSettings('currency_symbol');
        return response(view('student-fees.fees_class_report', compact('fees', 'classes', 'session_year_all', 'currencySymbol')));
    }

    public function feesAllReportIndex() {
        ResponseService::noFeatureThenRedirect('Reporting');
        ResponseService::noPermissionThenRedirect('fees-paid');

        $fees = $this->studentFees->builder()
            ->where("session_year_id", $this->cache->getDefaultSessionYear()->id)
            ->select([DB::raw("MIN(id) AS id"), 'name'])
            ->groupBy("name")
            ->get();
        $classes = $this->classes->all(['*'], ['medium', 'sections']);
        $simpleClassDropdown = $this->class->builder()->pluck('name', 'id');
        $session_year_all = $this->sessionYear->all(['id', 'name', 'default']);
        $currencySymbol = $this->cache->getSchoolSettings('currency_symbol');
        return response(view('student-fees.fees_all_report', compact('fees', 'classes', 'session_year_all', 'currencySymbol', 'simpleClassDropdown')));
    }

    public function studentFeesPaidList(Request $request)
    {
        ResponseService::noFeatureThenRedirect('Fees Management');
        ResponseService::noPermissionThenRedirect('fees-paid');
        $offset = request('offset', 0);
        $limit = request('limit', 10);
        $sort = request('sort', 'id');
        $order = request('order', 'DESC');
        $requestSessionYearId = (int)request('session_year_id');
        $sessionYearId = $requestSessionYearId ?? $this->cache->getDefaultSessionYear()->id;

        $searchParm = [$requestSessionYearId];
        $searchFilter = " WHERE sf.session_year_id = ?
                          AND sf.deleted_at IS NULL";

        $paidStatus = $request->input('paid_status');


      
        if (is_array($paidStatus)) {
            // Initialize the filter
            $searchFilter .= " AND (";
        
            $conditions = [];
            if (in_array("", $paidStatus)) {
                $conditions[] = "1 = 1"; // Always true, meaning no filter is applied
            }
            if (in_array("0", $paidStatus)) {
                $conditions[] = "(sfp.is_fully_paid IS NULL OR sfp.is_fully_paid = 0)";
            }
            if (in_array("1", $paidStatus)) {
                $conditions[] = "sfp.status = 1";
            }
            if (in_array("2", $paidStatus)) {
                $conditions[] = "sfp.status = 2";
            }
            if (in_array("3", $paidStatus)) {
                $conditions[] = "sfp.status = 3";
            }
        
            // Join the conditions with OR and append to searchFilter
            if (count($conditions) > 0) {
                $searchFilter .= implode(" OR ", $conditions);
            }
        
            // Close the condition block
            $searchFilter .= ")";
        }


        //filter date ....
        if ($request->start_date && !$request->end_date) {
            $searchFilter .= empty($searchFilter) ? " WHERE " : " AND ";
            $searchFilter .= " sf.created_at >= ?";

            $searchParm[] = date('Y-m-d', strtotime($request->start_date));
        } else if (!$request->start_date && $request->end_date) {
            $searchFilter .= empty($searchFilter) ? " WHERE " : " AND ";
            $searchFilter .= " sf.created_at <= ? ";

            $searchParm[] = date('Y-m-d', strtotime($request->end_date));
        } else if ($request->start_date && $request->end_date) {
            $searchFilter .= empty($searchFilter) ? " WHERE " : " AND ";
            $searchFilter .= " (sf.created_at BETWEEN ? AND ?) ";

            $searchParm[] = date('Y-m-d', strtotime($request->start_date)) . " 00:00:00";
            $searchParm[] = date('Y-m-d', strtotime($request->end_date))  . " 23:59:59";
        }

        if ($request->fees_id) {
            $searchFilter .= empty($searchFilter) ? " WHERE " : " AND ";
            $searchFilter .= " sf.name IN (SELECT name FROM student_fees WHERE id = ?) ";

            $searchParm[] = $request->fees_id;
        }

        //filter class
        if ($request->class_id) {
            $searchFilter .= empty($searchFilter) ? " WHERE " : " AND ";
            $searchFilter .= " c.id = ? ";

            $searchParm[] = $request->class_id;
        }

        if ($request->student_id) {
            $searchFilter .= empty($searchFilter) ? " WHERE " : " AND ";
            $searchFilter .= " s.id = ? ";

            $searchParm[] = $request->student_id;
        }

        if (!empty($request->search)) {
            $searchFilter .= empty($searchFilter) ? " WHERE " : " AND ";
            $searchFilter .= " (u.id LIKE ?
                OR      CONCAT(u.first_name ,' ', u.last_name) LIKE ?
                OR      c.name LIKE ?
                OR      u.rfid_id LIKE ? 
                OR EXISTS (
                    SELECT      student_fees_id
                    FROM        student_fees_details
                    WHERE       student_fees_id = sf.id
                    GROUP BY    student_fees_id
                    HAVING      SUM(fees_type_amount) LIKE ?
                )
                OR      sfp.date LIKE ?
                OR CONCAT('INV', LPAD(sf.uid, 8, '0')) LIKE ?
            )";

            $searchParm[] = "%$request->search%";
            $searchParm[] = "%$request->search%";
            $searchParm[] = "%$request->search%";
            $searchParm[] = "%$request->search%";  // Add this line to bind RFID search parameter
            $searchParm[] = "%$request->search%";
            $searchParm[] = "%$request->search%";
            $searchParm[] = "%$request->search%";
        }


        $paginationFilter = "
            ORDER BY    sf.created_at DESC,
                        {$sort} {$order}
            LIMIT       {$limit}
            OFFSET      {$offset}
        ";

        $ttlSql = DB::SELECT("
            SELECT      COUNT(*) AS total
            FROM        users u
            JOIN        students s
            ON          u.id = s.user_id
            JOIN        class_sections cs
            ON          cs.id = s.class_section_id
            JOIN        classes c
            ON          c.id = cs.class_id
            JOIN        student_fees sf
            ON          sf.student_id = s.id
            AND         sf.status = 'published'
            LEFT JOIN   student_fees_paids sfp
            ON          sfp.student_fees_id = sf.id
            {$searchFilter}
        ", $searchParm);

        $res = DB::select("
            SELECT      u.id
                        , s.id AS student_id
                        , u.rfid_id  
                        , sf.id AS student_fee_id
                        ,sf.uid AS student_fee_uid
                        , sf.session_year_id
                        , CONCAT(u.first_name ,' ', u.last_name) AS full_name
                        , c.name AS class_name
                        , sfp.is_fully_paid
                        , sfp.date AS fee_paid_date
                        , sfp.is_fully_paid AS fees_status
                        , sfp.status AS pending_status
                        , (
                            SELECT  SUM(((fees_type_amount - (fees_type_amount * COALESCE(discount,0) / 100)) + 
                            ((fees_type_amount - (fees_type_amount * COALESCE(discount,0) / 100)) * COALESCE(tax,0) /100)) * quantity)
                            FROM    student_fees_details
                            WHERE   student_fees_id = sf.id
                            AND     fees_type_name NOT IN ('Early Discount', 'Overdue Fees')
                        ) AS total_compulsory_fees
                        , sf.due_date
                        , sf.uid
                        , sf.early_date
                        , sfp.mode
                        , sfp.payment_detail
                        , sf.created_at
                        , sfp.remark
            FROM        users u
            JOIN        students s
            ON          u.id = s.user_id
            JOIN        class_sections cs
            ON          cs.id = s.class_section_id
            JOIN        classes c
            ON          c.id = cs.class_id
            JOIN        student_fees sf
            ON          sf.student_id = s.id
            AND         sf.status = 'published'
            LEFT JOIN   student_fees_paids sfp
            ON          sfp.student_fees_id = sf.id
            {$searchFilter}
            {$paginationFilter}
        ", $searchParm);

        $total = $ttlSql[0]->total;


        $bulkData = array();
        $bulkData['total'] = $total;
        $rows = array();
        $no = 1;

        foreach ($res as $row) {
            $tempRow = $row;
            $tempRow->rfid_id = $row->rfid_id ?? '-'; 
            $tempRow->no = $no++;
            $tempRow->fees_status = null;
            $tempRow->invoice_number = "INV" . sprintf('%08d', $row->student_fee_uid ?? '');
            $currentDate = date('Y-m-d');
            $due_date = date("Y-m-d", strtotime($row->due_date));
            $due_charges = "";
            $early_date = date("Y-m-d", strtotime($row->early_date));
            $early_offer = "";
            $remarkData = '';
            $total_compulsory_fees = floatval(str_replace(',', '', $row->total_compulsory_fees));
            $studentFees = DB::table('student_fees')->where('id',$row->student_fee_id)->first();
            $feesPaid = DB::select('SELECT * FROM student_fees_paids WHERE student_fees_id = ? ', [$row->student_fee_id]);

            //Add Extra Tax
            if(isset($studentFees->tax_percentage) && $studentFees->tax_percentage > 0){
                $total_compulsory_fees += ($total_compulsory_fees * ($studentFees->tax_percentage / 100));
            }

            if ($currentDate > $due_date && !$feesPaid) {
                if (!$studentFees->due_charges) {
                    $total_compulsory_fees += $studentFees->due_charges_amount;
                } else {
                    $total_compulsory_fees += ($total_compulsory_fees * ($studentFees->due_charges / 100));
                }
            } elseif ($currentDate > $due_date && $feesPaid) {
                if ($due_date < $feesPaid[0]->date) {
                    if (!$studentFees->due_charges) {
                        $total_compulsory_fees += $studentFees->due_charges_amount;
                    } else {
                        $total_compulsory_fees += ($total_compulsory_fees * ($studentFees->due_charges / 100));
                    }
                }
            }
            if ($currentDate <= $early_date && !$feesPaid) {
                if (!$studentFees->early_offer) {
                    $total_compulsory_fees -= $studentFees->early_offer_amount;
                } else {
                    $total_compulsory_fees -= ($total_compulsory_fees * ($studentFees->early_offer / 100));
                }
            } else if ($feesPaid && $feesPaid[0]->date <= $early_date) {
                if (!$studentFees->early_offer) {
                    $total_compulsory_fees -= $studentFees->early_offer_amount;
                } else {
                    $total_compulsory_fees -= ($total_compulsory_fees * ($studentFees->early_offer / 100));
                }
            }

            $tempRow->total_compulsory_fees = number_format($total_compulsory_fees,2);
            $existCreditNote = DB::table('credit_note')->select('credit_note.uid')->where('credit_note.student_fee_id','=',$row->student_fee_id)->first();
            $existDebitNote = DB::table('debit_note')->select('debit_note.uid')->where('debit_note.student_fee_id','=',$row->student_fee_id)->first();
            $existRefundNote = DB::table('refund_note')->select('refund_note.uid')->where('refund_note.student_fee_id','=',$row->student_fee_id)->first();

            if($existCreditNote){
                $paddedUid = str_pad($existCreditNote->uid, 6, '0', STR_PAD_LEFT);
                $tempRow->remarks = 'CN' . $paddedUid;
            }

            if($existDebitNote){
                $paddedUid = str_pad($existDebitNote->uid, 6, '0', STR_PAD_LEFT);
                $tempRow->remarks = 'DN' . $paddedUid;
            }

            if($existRefundNote){
                $paddedUid = str_pad($existRefundNote->uid, 6, '0', STR_PAD_LEFT);
                $tempRow->remarks = 'RF' . $paddedUid;
            }

            // if ($row->is_fully_paid == 0 && $currentDate <= $early_date) {
            //     $studentFees = DB::select("
            //         SELECT early_offer, early_offer_amount
            //         FROM student_fees
            //         WHERE id = ?
            //     ", [$row->student_fee_id]);
            //     if (count($studentFees) > 0) {
            //         $early_offer_percentage = $studentFees[0]->early_offer ?? 0;
            //         $early_offer_amount = $studentFees[0]->early_offer_amount ?? 0;
            //         $early_offer = 0;

            //         if (!$early_offer_percentage) {
            //             $early_offer -= $early_offer_amount;
            //         } else {
            //             $early_offer -= $total_compulsory_fees * $early_offer_percentage / 100;
            //             $early_offer = number_format($early_offer, 2, '.', '');
                        
            //         }
            //         $total = $total_compulsory_fees + $early_offer;
            //     } else {
            //         $total = $total_compulsory_fees;
            //     }

            //     $tempRow->total_compulsory_fees = number_format($total, 2);
            // } else if ($tempRow->total_compulsory_fees == null) {
            //     $tempRow->total_compulsory_fees = '0.00';
            // } else {
            //     //$tempRow->total_compulsory_fees = $tempRow->total_compulsory_fees;
            //     $tempRow->total_compulsory_fees = sprintf('%0.2f', $tempRow->total_compulsory_fees);
            //     //$tempRow->total_compulsory_fees = number_format($tempRow->total_compulsory_fees, 2);
            // }

            // if ($row->is_fully_paid == 0 && $currentDate > $due_date) {
            //     $studentFees = DB::select("
            //         SELECT due_charges, due_charges_amount
            //         FROM student_fees
            //         WHERE id = ?
            //     ", [$row->student_fee_id]);
            //     if (count($studentFees) > 0) {
            //         $due_charges_percentage = $studentFees[0]->due_charges ?? 0;
            //         $due_charges_amount = $studentFees[0]->due_charges_amount ?? 0;
            //         $due_charges = 0;

            //         if (!$due_charges_percentage) {
            //             $due_charges += $due_charges_amount;
            //         } else {
                       
            //             $due_charges += ($total_compulsory_fees * ($due_charges_percentage / 100));
            //             $due_charges = number_format($due_charges, 2, '.', '');
            //         }
            //         $total = $total_compulsory_fees + $due_charges;
            //     } else {
            //         $total = $total_compulsory_fees;
            //     }
            //     $tempRow->total_compulsory_fees = number_format($total, 2);
            // } else if ($tempRow->total_compulsory_fees == null) {
            //     $tempRow->total_compulsory_fees = '0.00';
            // } else {
            //     //$tempRow->total_compulsory_fees = $tempRow->total_compulsory_fees;
            //     // $tempRow->total_compulsory_fees = sprintf('%0.2f', $tempRow->total_compulsory_fees);
            //     $tempRow->total_compulsory_fees = number_format(str_replace(',', '', $tempRow->total_compulsory_fees), 2);

            //     //$tempRow->total_compulsory_fees = number_format($tempRow->total_compulsory_fees, 2);
            // }

            $tempRow->payment_type = StudentFeesPaid::viewMode($tempRow->mode);
            $operate = '';

            // $creditNotes = DB::select("
            //         SELECT student_fee_id
            //         FROM credit_note");

            //         dd($creditNotes);
            if (empty($row->is_fully_paid)) {
                $operate .= BootstrapTableService::button('fa fa-dollar', route('student-fees.compulsory.index', [$row->student_fee_id, $row->student_id]), ['btn', 'btn-xs', 'btn-gradient-success', 'btn-rounded', 'btn-icon'], ['title' => trans('Compulsory Fees')]);
            }

            if (!empty($row->is_fully_paid)) {
                if($row->is_fully_paid == 1 && $row->pending_status == 1){
                    $tempRow->reciept_num = "R" . str_pad($row->uid, 8, "0", STR_PAD_LEFT); 
                }
                if ($row->pending_status == 2) {
                    $operate .= '<button type="button" class="btn btn-xs btn-gradient-success btn-rounded btn-icon" 
                    onclick="confirmCompulsoryFees(' . $row->student_fee_id . ', ' . ')" 
                    title="' . trans('Confirm Compulsory Fees') . '">
                    <i class="fa fa-check"></i> ' . '
                    </button>';
                } else  if ($row->pending_status == 3) {
                } else {
                    $creditNotes = DB::select("
                    SELECT student_fee_id
                    FROM credit_note
                    WHERE student_fee_id = ?", [$row->student_fee_id]);

                    $debitNotes = DB::select("
                    SELECT student_fee_id
                    FROM debit_note
                    WHERE student_fee_id = ?", [$row->student_fee_id]);

                    $hasCredit = !empty($creditNotes);

                    $hasDebit = !empty($debitNotes);

                    if ($hasCredit && !$hasDebit) {
                        $row->pending_status = 4; // Only Credit
                    } elseif (!$hasCredit && $hasDebit) {
                        $row->pending_status = 5; // Only Debit
                    } elseif ($hasCredit && $hasDebit) {
                        $row->pending_status = 6; // Both Credit and Debit (optional, based on your logic)
                    }
                    
                    $operate .= BootstrapTableService::menuButton('receipt',route('student-fees.paid.receipt.pdf', ['id' => $row->student_fee_id,'receipt','isPaid' => true]),['generate-paid-fees-pdf'],['target' => "_blank"]);
                    $operate .= BootstrapTableService::menuButton('invoice',route('student-fees.paid.receipt.pdf', ['id' => $row->student_fee_id,'invoice','isPaid' => false]),['generate-paid-fees-pdf'],['target' => "_blank"]);
                    $operate .= BootstrapTableService::menuButton('edit',route('student-fees.paid-date.edit', ['id' => $row->student_fee_id,'invoice','isPaid' => false]),['generate-paid-fees-pdf'],['target' => "_blank"]);
                    $operate = BootstrapTableService::menuItem($operate);
                    // $operate .= BootstrapTableService::button('fa fa-file-pdf-o', route('student-fees.paid.receipt.pdf', $row->student_fee_id), ['btn', 'btn-xs', 'btn-gradient-info', 'btn-rounded', 'btn-icon', 'generate-paid-fees-pdf'], ['target' => "_blank", 'data-id' => $row->student_fee_id, 'title' => trans('generate_pdf') . ' ' . trans('fees')]);
                    $tempRow->fees_status = $row->is_fully_paid;
                    $paid_amount = DB::select('SELECT amount FROM student_fees_paids WHERE student_fees_id = ?', [$row->student_fee_id]);
                    $paid_amount = $paid_amount[0]->amount;
                    $tempRow->total_compulsory_fees = number_format($paid_amount, 2);
                } 
                // else {
                //     $operate .= BootstrapTableService::button('fa fa-file-pdf-o', route('student-fees.paid.receipt.pdf', $row->student_fee_id), ['btn', 'btn-xs', 'btn-gradient-info', 'btn-rounded', 'btn-icon', 'generate-paid-fees-pdf'], ['target' => "_blank", 'data-id' => $row->student_fee_id, 'title' => trans('generate_pdf') . ' ' . trans('fees')]);
                //     $tempRow->fees_status = $row->is_fully_paid;
                //     $paid_amount = DB::select('SELECT amount FROM student_fees_paids WHERE student_fees_id = ?', [$row->student_fee_id]);
                //     $paid_amount = $paid_amount[0]->amount;
                //     $tempRow->total_compulsory_fees = number_format($paid_amount, 2);
                // }
            }




            $tempRow->operate = $operate;


            if ($tempRow->payment_type == 'bank') {
                $relativePath = $row->payment_detail;

                // $baseUrl = 'https://schola.test/';
                // $imageUrl = $baseUrl . $relativePath;
                $tempRow->payment_detail = $relativePath;
            } else {
                $paymentDetailArray =  explode(";", $row->payment_detail);
                $tempRow->payment_detail = implode("<br/>", $paymentDetailArray);
            }

            //Rejected Remark
            $studentFeesRejected = DB::table('student_fees_rejected')->where('student_fees_id',$row->student_fee_id)->first();
            if($studentFeesRejected && !$row->is_fully_paid){
                $tempRow->payment_detail = "Status: Rejected <br>Date: ". date_format(date_create($studentFeesRejected->created_at),"d/m/Y h:i:a")."<br>Remark: ".$studentFeesRejected->remark;
            }

            ///remarks
            $remarkData = $row->remark;

            if ($remarkData === null) {
               
                $tempRow->remark = '';
            } else {
                
                $tempRow->remarks = $remarkData;
            }
            
            $rows[] = $tempRow;
        }
            
            $bulkData['rows'] = $rows;
            
        return response()->json($bulkData);
    }

    public function feesCollectionReportList(Request $request)
    {
        ResponseService::noFeatureThenRedirect('Reporting');
        //ResponseService::noPermissionThenRedirect('fees-paid');
        $offset = request('offset', 0);
        $limit = request('limit', 99999);
        $sort = request('sort', 'id');
        $order = request('order', 'DESC');
        $requestSessionYearId = (int)request('session_year_id');
        $sessionYearId = $requestSessionYearId ?? $this->cache->getDefaultSessionYear()->id;

        $searchParm = [$requestSessionYearId];
        $searchFilter = " WHERE sf.session_year_id = ?
                                    AND sfp.status = 1
                          AND sf.deleted_at IS NULL";


        $searchFilter .= "
                AND sfp.is_fully_paid = 1
            ";

        // filter by payment type
        if ($request->paid_status == "1") {
            $searchFilter .= "
                AND sfp.mode = 1
            ";
        } else if ($request->paid_status == "2") {
            $searchFilter .= "
                AND sfp.mode = 2
            ";
        } else if ($request->paid_status == "3") {
            $searchFilter .= "
                AND sfp.mode = 3
            ";
        } else if ($request->paid_status == "4") {
            $searchFilter .= "
                AND sfp.mode = 4
            ";
        }


        //filter date ....
        if ($request->start_date && !$request->end_date) {
            $searchFilter .= empty($searchFilter) ? " WHERE " : " AND ";
            $searchFilter .= " sfp.date >= ?";

            $searchParm[] = date('Y-m-d', strtotime($request->start_date));
        } else if (!$request->start_date && $request->end_date) {
            $searchFilter .= empty($searchFilter) ? " WHERE " : " AND ";
            $searchFilter .= " sfp.date <= ? ";

            $searchParm[] = date('Y-m-d', strtotime($request->end_date));
        } else if ($request->start_date && $request->end_date) {
            $searchFilter .= empty($searchFilter) ? " WHERE " : " AND ";
            $searchFilter .= " (sfp.date BETWEEN ? AND ?) ";

            $searchParm[] = date('Y-m-d', strtotime($request->start_date)) . " 00:00:00";
            $searchParm[] = date('Y-m-d', strtotime($request->end_date))  . " 23:59:59";
        }

        if ($request->fees_id) {
            $searchFilter .= empty($searchFilter) ? " WHERE " : " AND ";
            $searchFilter .= " sf.name IN (SELECT name FROM student_fees WHERE id = ?) ";

            $searchParm[] = $request->fees_id;
        }

        //filter class
        if ($request->class_id) {
            $searchFilter .= empty($searchFilter) ? " WHERE " : " AND ";
            $searchFilter .= " c.id = ? ";

            $searchParm[] = $request->class_id;
        }

        if ($request->student_id) {
            $searchFilter .= empty($searchFilter) ? " WHERE " : " AND ";
            $searchFilter .= " s.id = ? ";

            $searchParm[] = $request->student_id;
        }

        if (!empty($request->search)) {
            $searchFilter .= empty($searchFilter) ? " WHERE " : " AND ";
            $searchFilter .= " (u.id LIKE ?
                OR      CONCAT(u.first_name ,' ', u.last_name) LIKE ?
                OR      c.name LIKE ?
                OR EXISTS (
                    SELECT      student_fees_id
                    FROM        student_fees_paids
                    WHERE       student_fees_id = sf.id
                    GROUP BY    student_fees_id
                    HAVING      amount LIKE ?
                )
                OR      sfp.date LIKE ?
                OR CONCAT('INV', LPAD(sf.uid, 8, '0')) LIKE ?
            )";

            $searchParm[] = "%$request->search%";
            $searchParm[] = "%$request->search%";
            $searchParm[] = "%$request->search%";
            $searchParm[] = "%$request->search%";
            $searchParm[] = "%$request->search%";
            $searchParm[] = "%$request->search%";
        }


        $paginationFilter = "
            ORDER BY    sfp.is_fully_paid,
                        sf.created_at DESC,
                        {$sort} {$order}
            LIMIT       {$limit}
            OFFSET      {$offset}
        ";

        $ttlSql = DB::SELECT("
            SELECT      COUNT(*) AS total
            FROM        users u
            JOIN        students s
            ON          u.id = s.user_id
            JOIN        class_sections cs
            ON          cs.id = s.class_section_id
            JOIN        classes c
            ON          c.id = cs.class_id
            JOIN        student_fees sf
            ON          sf.student_id = s.id
            AND         sf.status = 'published'
            LEFT JOIN   student_fees_paids sfp
            ON          sfp.student_fees_id = sf.id
            {$searchFilter}
        ", $searchParm);

        $res = DB::select("
            SELECT      u.id
                        , s.id AS student_id
                        , sf.id AS student_fee_id
                        , sf.session_year_id
                        , CONCAT(u.first_name ,' ', u.last_name) AS full_name
                        , c.name AS class_name
                        , sfp.is_fully_paid
                        , sfp.date AS fee_paid_date
                        , sfp.is_fully_paid AS fees_status
                        , sfp.amount AS total_compulsory_fees
                        , sf.due_date
                        , sf.name
                        , sfp.mode
                        , sf.created_at
                        ,sf.uid
            FROM        users u
            JOIN        students s
            ON          u.id = s.user_id
            JOIN        class_sections cs
            ON          cs.id = s.class_section_id
            JOIN        classes c
            ON          c.id = cs.class_id
            JOIN        student_fees sf
            ON          sf.student_id = s.id
            AND         sf.status = 'published'
            LEFT JOIN   student_fees_paids sfp
            ON          sfp.student_fees_id = sf.id
            {$searchFilter}
            {$paginationFilter}
        ", $searchParm);


        $total = $ttlSql[0]->total;


        $bulkData = array();
        $bulkData['total'] = $total;
        $rows = array();
        $no = 1;

        foreach ($res as $row) {

            $tempRow = $row;
            $tempRow->no = $no++;
            $tempRow->fees_status = null;
            $tempRow->payment_type = StudentFeesPaid::viewMode($tempRow->mode);
            $tempRow->invoice_number = "INV" . sprintf('%08d', $row->uid ?? '');
            //$row->total_compulsory_fees = number_format($row->total_compulsory_fees, 2);

            $operate = '';

            $tempRow->operate = $operate;
            $rows[] = $tempRow;
        }
        $bulkData['rows'] = $rows;
        return response()->json($bulkData);
    }

    public function feesPendingReportList(Request $request)
    {
        ResponseService::noFeatureThenRedirect('Reporting');
        ResponseService::noPermissionThenRedirect('fees-paid');
        $offset = request('offset', 0);
        $limit = request('limit', 999999);
        $sort = request('sort', 'id');
        $order = request('order', 'DESC');
        $requestSessionYearId = (int)request('session_year_id');
        $sessionYearId = $requestSessionYearId ?? $this->cache->getDefaultSessionYear()->id;

        $searchParm = [$requestSessionYearId];

        $searchFilter = " WHERE sf.session_year_id = ?
                          AND sf.deleted_at IS NULL";

        $searchFilter .= "
                AND (sfp.is_fully_paid IS NULL
                OR    sfp.is_fully_paid = 0 
                OR sfp.status != 1)
            ";

            // Filter by fees_status (OverDue or Pending based on due_date)
            if ($request->fees_status) {
                $searchFilter .= empty($searchFilter) ? " WHERE " : " AND ";

                if ($request->fees_status == 'OverDue') {
                    // OverDue: fees due date is before today's date
                    $searchFilter .= " sf.due_date < CURDATE() ";
                } else if ($request->fees_status == 'Pending') {
                    // Pending: fees due date is today or in the future
                    $searchFilter .= " sf.due_date >= CURDATE() ";
                }
            }

        //filter date ....
        if ($request->start_date && !$request->end_date) {
            $searchFilter .= empty($searchFilter) ? " WHERE " : " AND ";
            $searchFilter .= " sf.created_at >= ?";

            $searchParm[] = date('Y-m-d', strtotime($request->start_date));
        } else if (!$request->start_date && $request->end_date) {
            $searchFilter .= empty($searchFilter) ? " WHERE " : " AND ";
            $searchFilter .= " sf.created_at <= ? ";

            $searchParm[] = date('Y-m-d', strtotime($request->end_date));
        } else if ($request->start_date && $request->end_date) {
            $searchFilter .= empty($searchFilter) ? " WHERE " : " AND ";
            $searchFilter .= " (sf.created_at BETWEEN ? AND ?) ";

            $searchParm[] = date('Y-m-d', strtotime($request->start_date)) . " 00:00:00";
            $searchParm[] = date('Y-m-d', strtotime($request->end_date))  . " 23:59:59";
        }

        if ($request->fees_id) {
            $searchFilter .= empty($searchFilter) ? " WHERE " : " AND ";
            $searchFilter .= " sf.name IN (SELECT name FROM student_fees WHERE id = ?) ";

            $searchParm[] = $request->fees_id;
        }

        //filter class
        if ($request->class_id) {
            $searchFilter .= empty($searchFilter) ? " WHERE " : " AND ";
            $searchFilter .= " c.id = ? ";

            $searchParm[] = $request->class_id;
        }

        if ($request->student_id) {
            $searchFilter .= empty($searchFilter) ? " WHERE " : " AND ";
            $searchFilter .= " s.id = ? ";

            $searchParm[] = $request->student_id;
        }

        if (!empty($request->search)) {
            $searchFilter .= empty($searchFilter) ? " WHERE " : " AND ";
            // $searchFilter .= " (u.id LIKE ?
            //     OR      CONCAT(u.first_name ,' ', u.last_name) LIKE ?
            //     OR      c.name LIKE ?
            //     OR EXISTS (
            //         SELECT      student_fees_id
            //         FROM        student_fees_details
            //         WHERE       student_fees_id = sf.id
            //         GROUP BY    student_fees_id
            //         HAVING      SUM(fees_type
            //     OR  
            // )";
             $searchFilter .= " (u.id LIKE ?
                OR      CONCAT(u.first_name ,' ', u.last_name) LIKE ?
                OR      c.name LIKE ?
                OR CONCAT('INV', LPAD(sf.uid, 8, '0')) LIKE ?
            )";

            $searchParm[] = "%$request->search%";
            $searchParm[] = "%$request->search%";
            $searchParm[] = "%$request->search%";
            $searchParm[] = "%$request->search%";
            //$searchParm[] = "%$request->search%";
        }

       

        $paginationFilter = "
            ORDER BY    sfp.is_fully_paid,
                        sf.created_at DESC,
                        {$sort} {$order}
            LIMIT       {$limit}
            OFFSET      {$offset}
        ";

        $ttlSql = DB::SELECT("
            SELECT      COUNT(*) AS total
            FROM        users u
            JOIN        students s
            ON          u.id = s.user_id
            JOIN        class_sections cs
            ON          cs.id = s.class_section_id
            JOIN        classes c
            ON          c.id = cs.class_id
            JOIN        student_fees sf
            ON          sf.student_id = s.id
            AND         sf.status = 'published'
            LEFT JOIN   student_fees_paids sfp
            ON          sfp.student_fees_id = sf.id
            {$searchFilter}
        ", $searchParm);

        $res = DB::select("
            SELECT      u.id
                        , s.id AS student_id
                        , sf.id AS student_fee_id
                        , sf.session_year_id
                        , CONCAT(u.first_name ,' ', u.last_name) AS full_name
                        , c.name AS class_name
                        , sfp.is_fully_paid
                        , sfp.date AS fee_paid_date
                        , sfp.is_fully_paid AS fees_status
                        , (
                            SELECT  SUM( (fees_type_amount - (fees_type_amount * COALESCE(discount,0)/100) + ((fees_type_amount - (fees_type_amount * COALESCE(discount,0)/100)) * COALESCE(tax,0)/100) ) * quantity ) 
                            FROM    student_fees_details
                            WHERE   student_fees_id = sf.id
                            AND     fees_type_name NOT IN ('Early Discount', 'Overdue Fees')
                        ) AS total_compulsory_fees
                        ,sf.uid as invoice_num
                        , sf.due_date
                        , sf.name
                        , sf.due_charges
                        , sf.due_charges_amount
                        , sf.early_date
                        , sf.early_offer
                        , sf.early_offer_amount
                        , sfp.mode
                        , sf.created_at
            FROM        users u
            JOIN        students s
            ON          u.id = s.user_id
            JOIN        class_sections cs
            ON          cs.id = s.class_section_id
            JOIN        classes c
            ON          c.id = cs.class_id
            JOIN        student_fees sf
            ON          sf.student_id = s.id
            AND         sf.status = 'published'
            LEFT JOIN   student_fees_paids sfp
            ON          sfp.student_fees_id = sf.id
            {$searchFilter}
            {$paginationFilter}
        ", $searchParm);


        $total = $ttlSql[0]->total;


        $bulkData = array();
        $bulkData['total'] = $total;
        $rows = array();
        $no = 1;

        foreach ($res as $row) {
            $tempRow = $row;
            $tempRow->no = $no++;
            $tempRow->fees_status = null;
            $tempRow->payment_type = StudentFeesPaid::viewMode($tempRow->mode);
            $tempRow -> invoice_num ="INV". str_pad($row->invoice_num,8,"0",STR_PAD_LEFT);
            $currentDate = date('Y-m-d');


            if (strtotime($row->due_date) < strtotime(date('Y-m-d'))) {
                $tempRow->fees_status = "OverDue"; 
            }else{
                $tempRow->fees_status = "Unpaid";
            }
            
            //Add Extra Tax
            $studentFees = DB::table('student_fees')->where('id',$row->student_fee_id)->first();
            if(isset($studentFees->tax_percentage) && $studentFees->tax_percentage > 0){
                $row->total_compulsory_fees += ($row->total_compulsory_fees * ($studentFees->tax_percentage / 100));
            }


            if($row->due_date !== null){
                if ($currentDate > $row->due_date) {
                    if (!$row->due_charges) {
                        $row->total_compulsory_fees += $row->due_charges_amount;
                    } else {
                        $row->total_compulsory_fees += ($row->total_compulsory_fees * ($row->due_charges / 100));
                    }
                } 
            } 

            if($row->early_date !== null){
                if($currentDate <= $row->early_date){
                    if (!$row->early_offer) {
                        $row->total_compulsory_fees -= $row->early_offer_amount;
                    } else {
                        $row->total_compulsory_fees -= ($row->total_compulsory_fees * ($row->early_offer / 100));
                    }
                }   
            }

            $operate = '';
            
           

            $tempRow->operate = $operate;
            $rows[] = $tempRow;
        }
        $bulkData['rows'] = $rows;
        return response()->json($bulkData);
    }

    // public function feesOutstandingReportList(Request $request)
    // {
    //     ResponseService::noFeatureThenRedirect('Reporting');
    //     ResponseService::noPermissionThenRedirect('fees-paid');
    //     $offset = request('offset', 0);
    //     $limit = request('limit', 999999);
    //     $sort = request('sort', 'id');
    //     $order = request('order', 'DESC');
    //     $requestSessionYearId = (int)request('session_year_id');
    //     $sessionYearId = $requestSessionYearId ?? $this->cache->getDefaultSessionYear()->id;

    //     $searchParm = [$requestSessionYearId];

    //     $searchFilter = " WHERE sf.session_year_id = ?
    //                       AND sf.deleted_at IS NULL";

    //     $searchFilter .= "
    //             AND (sfp.is_fully_paid IS NULL
    //             OR    sfp.is_fully_paid = 0 )  
    //             AND sf.due_date < CURDATE()
    //         ";

    //     //filter date ....
    //     if ($request->start_date && !$request->end_date) {
    //         $searchFilter .= empty($searchFilter) ? " WHERE " : " AND ";
    //         $searchFilter .= " sf.created_at >= ?";

    //         $searchParm[] = date('Y-m-d', strtotime($request->start_date));
    //     } else if (!$request->start_date && $request->end_date) {
    //         $searchFilter .= empty($searchFilter) ? " WHERE " : " AND ";
    //         $searchFilter .= " sf.created_at <= ? ";

    //         $searchParm[] = date('Y-m-d', strtotime($request->end_date));
    //     } else if ($request->start_date && $request->end_date) {
    //         $searchFilter .= empty($searchFilter) ? " WHERE " : " AND ";
    //         $searchFilter .= " (sf.created_at BETWEEN ? AND ?) ";

    //         $searchParm[] = date('Y-m-d', strtotime($request->start_date)) . " 00:00:00";
    //         $searchParm[] = date('Y-m-d', strtotime($request->end_date))  . " 23:59:59";
    //     }

    //     if ($request->fees_id) {
    //         $searchFilter .= empty($searchFilter) ? " WHERE " : " AND ";
    //         $searchFilter .= " sf.name IN (SELECT name FROM student_fees WHERE id = ?) ";

    //         $searchParm[] = $request->fees_id;
    //     }

    //     //filter class
    //     if ($request->class_id) {
    //         $searchFilter .= empty($searchFilter) ? " WHERE " : " AND ";
    //         $searchFilter .= " c.id = ? ";

    //         $searchParm[] = $request->class_id;
    //     }

    //     if ($request->student_id) {
    //         $searchFilter .= empty($searchFilter) ? " WHERE " : " AND ";
    //         $searchFilter .= " s.id = ? ";

    //         $searchParm[] = $request->student_id;
    //     }

    //     if (!empty($request->search)) {
    //         $searchFilter .= empty($searchFilter) ? " WHERE " : " AND ";
    //         $searchFilter .= " (u.id LIKE ?
    //             OR      CONCAT(u.first_name ,' ', u.last_name) LIKE ?
    //             OR      c.name LIKE ?
    //             OR EXISTS (
    //                 SELECT      student_fees_id
    //                 FROM        student_fees_details
    //                 WHERE       student_fees_id = sf.id
    //                 GROUP BY    student_fees_id
    //                 HAVING 
    //                 FORMAT(
    //                 SUM(fees_type_amount) +
    //                 IF(sf.due_charges_amount IS NOT NULL AND sf.due_charges_amount != 0,
    //                 sf.due_charges_amount,
    //                 SUM(fees_type_amount) * sf.due_charges / 100
    //                 ),2) LIKE ?
    //             )
    //             OR      sfp.date LIKE ?
    //         )";

    //         $searchParm[] = "%$request->search%";
    //         $searchParm[] = "%$request->search%";
    //         $searchParm[] = "%$request->search%";
    //         $searchParm[] = "%$request->search%";
    //         $searchParm[] = "%$request->search%";
    //     }


    //     $paginationFilter = "
    //         ORDER BY    sfp.is_fully_paid,
    //                     sf.created_at DESC,
    //                     {$sort} {$order}
    //         LIMIT       {$limit}
    //         OFFSET      {$offset}
    //     ";

    //     $ttlSql = DB::SELECT("
    //         SELECT      COUNT(*) AS total
    //         FROM        users u
    //         JOIN        students s
    //         ON          u.id = s.user_id
    //         JOIN        class_sections cs
    //         ON          cs.id = s.class_section_id
    //         JOIN        classes c
    //         ON          c.id = cs.class_id
    //         JOIN        student_fees sf
    //         ON          sf.student_id = s.id
    //         AND         sf.status = 'published'
    //         LEFT JOIN   student_fees_paids sfp
    //         ON          sfp.student_fees_id = sf.id
    //         {$searchFilter}
    //     ", $searchParm);

    //     $res = DB::select("
    //         SELECT      u.id
    //                     , s.id AS student_id
    //                     , sf.id AS student_fee_id
    //                     , sf.session_year_id
    //                     , CONCAT(u.first_name ,' ', u.last_name) AS full_name
    //                     , c.name AS class_name
    //                     , sfp.is_fully_paid
    //                     , sfp.date AS fee_paid_date
    //                     , sfp.is_fully_paid AS fees_status
    //                     , (
    //                         SELECT  SUM(fees_type_amount)
    //                         FROM    student_fees_details
    //                         WHERE   student_fees_id = sf.id
    //                     ) AS total_compulsory_fees
    //                     , sf.due_date
    //                     , sf.name
    //                     , sf.due_charges
    //                     , sf.due_charges_amount
    //                     , sfp.mode
    //                     , sf.created_at
    //         FROM        users u
    //         JOIN        students s
    //         ON          u.id = s.user_id
    //         JOIN        class_sections cs
    //         ON          cs.id = s.class_section_id
    //         JOIN        classes c
    //         ON          c.id = cs.class_id
    //         JOIN        student_fees sf
    //         ON          sf.student_id = s.id
    //         AND         sf.status = 'published'
    //         LEFT JOIN   student_fees_paids sfp
    //         ON          sfp.student_fees_id = sf.id
    //         {$searchFilter}
    //         {$paginationFilter}
    //     ", $searchParm);


    //     $total = $ttlSql[0]->total;


    //     $bulkData = array();
    //     $bulkData['total'] = $total;
    //     $rows = array();
    //     $no = 1;

    //     foreach ($res as $row) {

    //         $tempRow = $row;
    //         $tempRow->no = $no++;
    //         $tempRow->fees_status = null;
    //         $tempRow->payment_type = StudentFeesPaid::viewMode($tempRow->mode);

    //         $operate = '';
    //         //$row->total_compulsory_fees = number_format($row->total_compulsory_fees, 2);
    //         if ($row->due_charges) {
    //             $row->total_compulsory_fees += ($row->total_compulsory_fees * ($row->due_charges / 100));
    //         } else {
    //             $row->total_compulsory_fees += $row->due_charges_amount;
    //         }

    //         $tempRow->operate = $operate;
    //         $rows[] = $tempRow;
    //     }
    //     $bulkData['rows'] = $rows;
    //     return response()->json($bulkData);
    // }

    public function feesStudentReportList(Request $request)
    {
        ResponseService::noFeatureThenRedirect('Reporting');
        ResponseService::noPermissionThenRedirect('fees-paid');
        $offset = request('offset', 0);
        $limit = request('limit', 999999);
        $sort = request('sort', 'id');
        $order = request('order', 'DESC');
        $requestSessionYearId = (int)request('session_year_id');
        $sessionYearId = $requestSessionYearId ?? $this->cache->getDefaultSessionYear()->id;

        $searchParm = [$requestSessionYearId];
        $searchFilter = " WHERE sf.session_year_id = ?
                          AND sf.deleted_at IS NULL";

        $paidStatus = $request->input('paid_status');
        $ticked = request('list_single');



        if (is_array($paidStatus)) {
            // Initialize the filter
            $searchFilter .= " AND (";
        
            $conditions = [];
            if (in_array("", $paidStatus)) {
                $conditions[] = "1 = 1"; // Always true, meaning no filter is applied
            }
            if (in_array("0", $paidStatus)) {
                $conditions[] = "(sfp.is_fully_paid IS NULL OR sfp.is_fully_paid = 0)";
            }
            if (in_array("1", $paidStatus)) {
                $conditions[] = "sfp.status = 1";
            }
            if (in_array("2", $paidStatus)) {
                $conditions[] = "sfp.status = 2";
            }
            if (in_array("3", $paidStatus)) {
                $conditions[] = "sfp.status = 3";
            }
        
            // Join the conditions with OR and append to searchFilter
            if (count($conditions) > 0) {
                $searchFilter .= implode(" OR ", $conditions);
            }
        
            // Close the condition block
            $searchFilter .= ")";
        }

      
        //filter date ....
        // if ($request->start_date && !$request->end_date) {
        //     $searchFilter .= empty($searchFilter) ? " WHERE " : " AND ";
        //     $searchFilter .= " sf.created_at >= ?";

        //     $searchParm[] = date('Y-m-d', strtotime($request->start_date));
        // } else if (!$request->start_date && $request->end_date) {
        //     $searchFilter .= empty($searchFilter) ? " WHERE " : " AND ";
        //     $searchFilter .= " sf.created_at <= ? ";

        //     $searchParm[] = date('Y-m-d', strtotime($request->end_date));
        // } else if ($request->start_date && $request->end_date) {
        //     $searchFilter .= empty($searchFilter) ? " WHERE " : " AND ";
        //     $searchFilter .= " (sf.created_at BETWEEN ? AND ?) ";

        //     $searchParm[] = date('Y-m-d', strtotime($request->start_date)) . " 00:00:00";
        //     $searchParm[] = date('Y-m-d', strtotime($request->end_date))  . " 23:59:59";
        // }

        //filter by month
        $filterMonth = $request->input('filter_month');
        if($filterMonth){
            $searchFilter .= empty($searchFilter) ? " WHERE " : " AND ";
            $searchFilter .= " MONTH(sf.invoice_date) = ? ";
            $searchParm[] = $filterMonth; 
        }
        // if ($request->fees_id) {
        //     $searchFilter .= empty($searchFilter)?" WHERE ":" AND ";
        //     $searchFilter .= " sf.name IN (SELECT name FROM student_fees WHERE id = ?) ";

        //     $searchParm[] = $request->fees_id;
        // }

        //filter class
        if ($request->class_id) {
            // dd($request->class_id);
            $searchFilter .= empty($searchFilter)?" WHERE ":" AND ";
            $searchFilter .= " c.id = ? ";

            $searchParm[] = $request->class_id;
        }

        // if($request->student_id){
        //     $searchFilter .= empty($searchFilter)?" WHERE ":" AND ";
        //     $searchFilter .= " s.id = ? ";

        //     $searchParm[] = $request->student_id;
        // }

        $studentId = (int) $request->input('search');
        if ($studentId > 0) {
            // Student ID filtering
            $searchFilter .= empty($searchFilter) ? " WHERE " : " AND ";
            $searchFilter .= " (
            sf.student_id = ? 
            OR  u.id LIKE ?
                OR CONCAT(u.first_name ,' ', u.last_name) LIKE ?
                OR c.name LIKE ?
                OR EXISTS (
                    SELECT student_fees_id
                    FROM student_fees_details sfd
                    WHERE student_fees_id = sf.id
                    GROUP BY student_fees_id
                    HAVING SUM(fees_type_amount) LIKE ?
                )
                OR sfp.date LIKE ?
                OR CONCAT('INV', LPAD(sf.uid, 9, '0')) LIKE ?
            )";

            $searchParm[] = $studentId;
            $searchParm[] = "%$request->search%";
            $searchParm[] = "%$request->search%";
            $searchParm[] = "%$request->search%";
            $searchParm[] = "%$request->search%";
            $searchParm[] = "%$request->search%";
            $searchParm[] = "%$request->search%";
           
            
        } else {
            // General search filtering across multiple fields
            $searchFilter .= empty($searchFilter) ? " WHERE " : " AND ";
            $searchFilter .= " (
                u.id LIKE ?
                OR CONCAT(u.first_name ,' ', u.last_name) LIKE ?
                OR c.name LIKE ?
                OR EXISTS (
                    SELECT student_fees_id
                    FROM student_fees_details sfd
                    WHERE student_fees_id = sf.id
                    GROUP BY student_fees_id
                    HAVING SUM(fees_type_amount) LIKE ?
                )
                OR sfp.date LIKE ?
                OR CONCAT('INV', LPAD(sf.uid, 9, '0')) LIKE ?
            )";

    

            $searchParm[] = "%$request->search%";
            $searchParm[] = "%$request->search%";
            $searchParm[] = "%$request->search%";
            $searchParm[] = "%$request->search%";
            $searchParm[] = "%$request->search%";
            $searchParm[] = "%$request->search%";
        }

        $paginationFilter = "
            ORDER BY    sfp.is_fully_paid,
                        sf.created_at DESC,
                        {$sort} {$order}
            LIMIT       {$limit}
            OFFSET      {$offset}
        ";

        $ttlSql = DB::SELECT("
            SELECT      COUNT(*) AS total
            FROM        users u
            JOIN        students s
            ON          u.id = s.user_id
            JOIN        class_sections cs
            ON          cs.id = s.class_section_id
            JOIN        classes c
            ON          c.id = cs.class_id
            JOIN        student_fees sf
            ON          sf.student_id = s.id
            AND         sf.status = 'published'
            LEFT JOIN   student_fees_paids sfp
            ON          sfp.student_fees_id = sf.id
            {$searchFilter}
        ", $searchParm);

        $res = DB::select("
            SELECT      u.id
                        , s.id AS student_id
                        , sf.id AS student_fee_id
                        , sf.session_year_id
                        , CONCAT(u.first_name ,' ', u.last_name) AS full_name
                        , c.name AS class_name
                        , sfp.is_fully_paid
                        , sfp.date AS fee_paid_date
                        , sfp.is_fully_paid AS fees_status
                        , (
                            SELECT  SUM(fees_type_amount)
                            FROM    student_fees_details
                            WHERE   student_fees_id = sf.id
                            AND     fees_type_name NOT IN ('Early Discount', 'Overdue Fees')
                        ) AS total_compulsory_fees
                        , sf.due_date 
                        , sf.early_date
                        , sf.name
                        , sfp.mode
                        , sfp.status AS pending_status
                        , sf.created_at
                        , sf.uid
                        , sfd.student_fees_id
                        , sfd.quantity
                        , sfd.unit
                        , sfd.fees_type_amount
                        , sfd.fees_type_name
                        , sfd.discount
                        , sfd.tax
                        , sf.invoice_date
                        , (SELECT CONCAT('first_name', ' ', 'last_name') FROM users WHERE id = s.guardian_id) AS guardian_name
            FROM        users u
            JOIN        students s
            ON          u.id = s.user_id
            JOIN        class_sections cs
            ON          cs.id = s.class_section_id
            JOIN        classes c
            ON          c.id = cs.class_id
            JOIN        student_fees sf
            ON          sf.student_id = s.id
            AND         sf.status = 'published'
            JOIN        student_fees_details sfd 
            ON          sfd.student_fees_id = sf.id
            LEFT JOIN   student_fees_paids sfp
            ON          sfp.student_fees_id = sf.id
      
            {$searchFilter}
            {$paginationFilter}
           
        ", $searchParm);


        $total = $ttlSql[0]->total;


        $bulkData = array();
        $bulkData['total'] = $total;
        $rows = array();
        $no = 0;
        $sequence = 1;
        $lastId = null;
        $previousInv ="";

        if ($ticked == 'list-ticked') {
            foreach ($res as $row) {
                $showEarlyDiscount = false;
                $showOverdueFees = false;

                $tempRow = $row;
                $tempRow->no = $no++;
                $tempRow->fees_status = null;
                $currentDate = date('Y-m-d');
                $due_date = date("Y-m-d", strtotime($row->due_date));
                $early_date = date("Y-m-d", strtotime($row->early_date));
                $newRow = new StudentFee();

                $itemCode = DB::table('fees_type_master')->select('item_code')
                ->where('name', $row->fees_type_name)
                ->where('school_id', Auth::user()->school_id) 
                ->first();

                if($row->fees_type_name !== "Overdue Fees" && $row->fees_type_name !== "Early Discount"){
                    // Condition to check if the current invoice is the same as the previous
                    if ($row->uid !== $previousInv) {
                        // Calculate total compulsory fees
                        $row->total_compulsory_fees = DB::table('student_fees_details')
                            ->where('student_fees_id', $row->student_fees_id)
                            ->whereNotIn('fees_type_name', ['Overdue Fees', 'Early Discount'])
                            ->sum(DB::raw('(fees_type_amount - (fees_type_amount * COALESCE(discount, 0) / 100) + ((fees_type_amount - (fees_type_amount * COALESCE(discount, 0) / 100)) * COALESCE(tax, 0) / 100)) * quantity'));

                        $sql_code=DB::table('e_invoice_guardian')->join('students', 'students.guardian_id', '=', 'e_invoice_guardian.guardian_id')->where('students.school_id', Auth::user()->school_id)->where('students.id', $row->student_id)->value('e_invoice_guardian.sql_code');
                        $feesStatus = DB::table("student_fees_paids")->select("mode","status as pending_status","date as fee_paid_date","is_fully_paid as fees_status")->where("student_fees_paids.student_fees_id",$row->student_fee_id)
                        ->first();

                    
                        $due_charges = 0;
                        $early_offer = 0;
                        if ($row->is_fully_paid == 0 && $currentDate > $due_date) {
                        
                            $studentFees = DB::select("
                            SELECT due_charges, due_charges_amount
                            FROM student_fees
                            WHERE id = ?
                        ", [$row->student_fees_id]);

                            if (count($studentFees) > 0) {
                                $due_charges_percentage = $studentFees[0]->due_charges ?? 0;
                                $due_charges_amount = $studentFees[0]->due_charges_amount ?? 0;

                                // Calculate due charges
                                if (!$due_charges_percentage) {
                                    $due_charges += $due_charges_amount;
                                
                                } else {
                                    $due_charges += ($row->total_compulsory_fees * ($due_charges_percentage / 100));
                                    $due_charges = number_format($due_charges, 2, '.', '');
                                
                                }
                            }
                        } else if($row->is_fully_paid == 1 && $row->fee_paid_date > $due_date){
                            $studentFees = DB::select("
                            SELECT due_charges, due_charges_amount
                            FROM student_fees
                            WHERE id = ?
                        ", [$row->student_fees_id]);
                        if (count($studentFees) > 0) {
                            $due_charges_percentage = $studentFees[0]->due_charges ?? 0;
                            $due_charges_amount = $studentFees[0]->due_charges_amount ?? 0;

                            // Calculate due charges
                            if (!$due_charges_percentage) {
                                $due_charges += $due_charges_amount;
                            
                            } else {
                                $due_charges += ($row->total_compulsory_fees * ($due_charges_percentage / 100));
                                $due_charges = number_format($due_charges, 2, '.', '');
                            
                            }
                        }
                        }

                        if ($row->is_fully_paid == 0 && $currentDate <= $early_date) {
                            $studentFees = DB::select("
                                SELECT early_offer, early_offer_amount
                                FROM student_fees
                                WHERE id = ?
                            ", [$row->student_fees_id]);

                            if (count($studentFees) > 0) {
                                $early_offer_percentage = $studentFees[0]->early_offer ?? 0;
                                $early_offer_amount = $studentFees[0]->early_offer_amount ?? 0;
                                if (!$early_offer_percentage) {
                                    $early_offer -= $early_offer_amount;
                                } else {
                                    $early_offer -= $row->total_compulsory_fees * $early_offer_percentage / 100;
                                    $early_offer = number_format($early_offer, 2, '.', '');
                                    
                                }
                            }
                        }else if($row->is_fully_paid == 1 && $row->fee_paid_date <= $early_date){
                            $studentFees = DB::select("
                            SELECT early_offer, early_offer_amount
                            FROM student_fees
                            WHERE id = ?
                        ", [$row->student_fees_id]);

                        if (count($studentFees) > 0) {
                            $early_offer_percentage = $studentFees[0]->early_offer ?? 0;
                            $early_offer_amount = $studentFees[0]->early_offer_amount ?? 0;
                            if (!$early_offer_percentage) {
                                $early_offer -= $early_offer_amount;
                            } else {
                                $early_offer -= $row->total_compulsory_fees * $early_offer_percentage / 100;
                                $early_offer = number_format($early_offer, 2, '.', '');
                                
                            }
                        }
                        }
                    

                        if ($due_charges||$early_offer) {
                            $feesType = $due_charges > 0 ? "Overdue Fees" : "Early Discount";
                            $feesAmount = $due_charges > 0 ? $due_charges : $early_offer;
                        
                            $newRow->student_fees_id = $row->student_fees_id;
                            $newRow->payment_type = StudentFeesPaid::viewMode($row->mode);
                            $newRow->unit_price = $feesAmount;
                            $newRow->item_code = $itemCode ? $itemCode->item_code : null;
                            $newRow->fees_type_name = $feesType;
                            $newRow->created_at = $row->created_at;
                            $newRow->uid = $row->uid;
                            $newRow->sql_code = $sql_code;
                            $newRow->full_name = $row->full_name;
                            $newRow->class_name = $row->class_name;
                            $newRow->quantity = 1;
                            $newRow->remarks = "-";
                            $newRow->unit = "-";
                            $newRow->total_price = $feesAmount;
                            $newRow->sequence = $sequence++;
                            $newRow->no = $no++;
                        
                            if ($feesStatus) {
                                $newRow->fees_status = $feesStatus->fees_status;
                                $newRow->pending_status = $feesStatus->pending_status;
                                $newRow->fee_paid_date = $feesStatus->fee_paid_date;
                                $newRow->mode = $feesStatus->mode;
                            }
                        
                            if ($due_charges > 0) {
                                $newRow->due_date = $due_date;
                        
                            } else {
                                $newRow->early_date = $row->early_date;
                            }
                            
                            
                        }

                        // Update the previous invoice
                        $previousInv = $row->uid;
                    }

                    if (isset($newRow) && ($newRow->fees_type_name == "Overdue Fees" || $newRow->fees_type_name == "Early Discount")) {
                        $rows[] = $newRow;
                
                    }

                    $tempRow->payment_type = StudentFeesPaid::viewMode($tempRow->mode);
                    $feesStatus = DB::table("student_fees_paids")->select("mode","status as pending_status","date as fee_paid_date","is_fully_paid as fees_status")->where("student_fees_paids.student_fees_id",$row->student_fee_id)
                    ->first();

                    if ($feesStatus) {
                        $tempRow->fees_status = $feesStatus->fees_status; 
                        $tempRow->pending_status = $feesStatus->pending_status;
                        $tempRow->fee_paid_date = $feesStatus->fee_paid_date;
                        $tempRow->mode = $feesStatus->mode;
                    }


                    // $operate = '';

                    // if (empty($row->is_fully_paid)) {
                    //     $operate .= BootstrapTableService::button('fa fa-dollar', route('student-fees.compulsory.index', [$row->student_fee_id, $row->student_id]), ['btn', 'btn-xs', 'btn-gradient-success', 'btn-rounded', 'btn-icon'], ['title' => trans('Compulsory Fees')]);
                    // }

                    // if (!empty($row->is_fully_paid)) {
                    //     $operate .= BootstrapTableService::button('fa fa-file-pdf-o', route('student-fees.paid.receipt.pdf', $row->student_fee_id), ['btn', 'btn-xs', 'btn-gradient-info', 'btn-rounded', 'btn-icon', 'generate-paid-fees-pdf'], ['target' => "_blank", 'data-id' => $row->student_fee_id, 'title' => trans('generate_pdf') . ' ' . trans('fees')]);
                    //     $tempRow->fees_status = $row->is_fully_paid;
                    //     $paid_amount = DB::select('SELECT amount FROM student_fees_paids WHERE student_fees_id = ?', [$row->student_fee_id]);
                    //     $paid_amount = $paid_amount[0]->amount;
                    //     $tempRow->total_compulsory_fees = number_format($paid_amount, 2);
                    // }

                    $sql_code=DB::table('e_invoice_guardian')->join('students', 'students.guardian_id', '=', 'e_invoice_guardian.guardian_id')->where('students.school_id', Auth::user()->school_id)->where('students.id', $row->student_id)->value('e_invoice_guardian.sql_code');
                    $tempRow->sql_code = $sql_code;

                    $currentId = $row->student_fees_id;
                    if($currentId == $lastId){
                        $tempRow->sequence = $sequence++;
                    } else {
                        $sequence = 1;
                        $tempRow->sequence = $sequence++;
                        $lastId = $currentId;
                    }

                    $tempRow->uom = $row->unit;
                    $tempRow->unit_price=$row->fees_type_amount;
                    $tempRow->fees_type_name = $row->fees_type_name;

                    $discountedPrice = $row->fees_type_amount - ($row->fees_type_amount * ($row->discount ?? 0) / 100);
                    $taxAmount = $row->fees_type_amount * ($row->tax ?? 0) / 100;
                    $totalPrice = round(($discountedPrice + $taxAmount) * $row->quantity, 2);

                    $tempRow->total_price = $totalPrice;
                    $tempRow->item_code = $itemCode ? $itemCode->item_code : null;

                    $tempRow->quantity = $row->quantity;
                    $tempRow->discount = $row->discount;
                    $tempRow->tax=$row->tax;
                    $tempRow->no = $no;
                    // $tempRow->operate = $operate;
                    $rows[] = $tempRow;
                }

                $row->created_at = $row->invoice_date ? ($row->invoice_date.' 00:00:00') : $row->created_at;
                //     if($row->fees_type_name == "Overdue Fees"){
                    
                //         if ($row->is_fully_paid == 0 && $currentDate > $due_date) {
                //             $showOverdueFees = true;
                //         }
                //     }

                //     if($row->fees_type_name == "Early Discount"){
                    
                //         if ($row->is_fully_paid == 1 && $currentDate <= $row->early_date && $currentDate < $due_date) {
                //             $showEarlyDiscount = true;
                //         }
                //     }

                //     if ($showOverdueFees || $showEarlyDiscount) {
                    
                //         $feesStatus = DB::table("student_fees_paids")->select("mode","status as pending_status","date as fee_paid_date","is_fully_paid as fees_status")->where("student_fees_paids.student_fees_id",$row->student_fee_id)
                //         ->first();

                //         if ($feesStatus) {
                //             $tempRow->fees_status = $feesStatus->fees_status; 
                //             $tempRow->pending_status = $feesStatus->pending_status;
                //             $tempRow->fee_paid_date = $feesStatus->fee_paid_date;
                //             $tempRow->mode = $feesStatus->mode;
                //         }
                //         $sql_code=DB::table('e_invoice_guardian')->join('students', 'students.guardian_id', '=', 'e_invoice_guardian.guardian_id')->where('students.id', $row->student_id)->value('e_invoice_guardian.sql_code');
                //         $tempRow->sql_code = $sql_code;
                        

                //         $currentId = $row->student_fees_id;
                //         if($currentId == $lastId){
                //             $tempRow->sequence = $sequence++;
                //         } else {
                //             $sequence = 1;
                //             $tempRow->sequence = $sequence++;
                //             $lastId = $currentId;
                //         }

                //         $tempRow->uom = $row->unit;
                //         $tempRow->unit_price=$row->fees_type_amount;
                //         $tempRow->fees_type_name = $row->fees_type_name;

                //         $discountedPrice = $row->fees_type_amount - ($row->fees_type_amount * ($row->discount ?? 0) / 100);
                //         $taxAmount = $row->fees_type_amount * ($row->tax ?? 0) / 100;
                //         $totalPrice = round(($discountedPrice + $taxAmount) * $row->quantity, 2);

                //         $tempRow->total_price = $totalPrice;

                //         $tempRow->quantity = $row->quantity;
                //         $tempRow->discount = $row->discount;
                //         $tempRow->tax=$row->tax;

                
                //         $rows[] = $tempRow;
                //     }
            }
            $bulkData['rows'] = $rows;
            return response()->json($bulkData);
        } else {            
            $res2 = DB::select("
            SELECT      u.id
                        , s.id AS student_id
                        , sf.id AS student_fee_id
                        , sf.session_year_id
                        , CONCAT(u.first_name ,' ', u.last_name) AS full_name
                        , c.name AS class_name
                        , sfp.is_fully_paid
                        , sfp.date AS fee_paid_date
                        , sfp.is_fully_paid AS fees_status
                        , (
                            SELECT  SUM(fees_type_amount * quantity)
                            FROM    student_fees_details
                            WHERE   student_fees_id = sf.id
                            AND     fees_type_name NOT IN ('Early Discount', 'Overdue Fees')
                        ) AS total_compulsory_fees
                        , sf.due_date 
                        , sf.early_date
                        , sf.name
                        , sfp.mode
                        , sfp.status AS pending_status
                        , sf.created_at
                        , sf.uid
                        , sf.invoice_date
                        , (SELECT CONCAT(first_name, ' ', last_name) FROM users WHERE id = s.guardian_id) AS guardian_name
            FROM        users u
            JOIN        students s
            ON          u.id = s.user_id
            JOIN        class_sections cs
            ON          cs.id = s.class_section_id
            JOIN        classes c
            ON          c.id = cs.class_id
            JOIN        student_fees sf
            ON          sf.student_id = s.id
            AND         sf.status = 'published'
            LEFT JOIN   student_fees_paids sfp
            ON          sfp.student_fees_id = sf.id
      
            {$searchFilter}
            {$paginationFilter}
           
            ", $searchParm);


            $total = $ttlSql[0]->total;


            $bulkData = array();
            $bulkData['total'] = $total;
            $rows = array();
            $no = 1;
            $sequence = 1;
            $lastId = null;
            $previousInv ="";
        

            foreach($res2 as $row){
               
                $due_charges = 0;
                $early_offer = 0;
                $tempRow = $row;
                $currentDate = date('Y-m-d');
                $due_date = date("Y-m-d", strtotime($row->due_date));
                $early_date = date("Y-m-d", strtotime($row->early_date));

                
                $tempRow->created_at = $row->invoice_date ? ($row->invoice_date.' 00:00:00') : $tempRow->created_at;

                $tempRow->no = $no++;
                $tempRow->fees_status = null;
                $tempRow->quantity = null;
                $tempRow->unit_price = number_format( $tempRow->total_compulsory_fees, 2, '.', '');
                $tempRow->total_price = number_format( $row->total_compulsory_fees, 2, '.', '');
                $sql_code=DB::table('e_invoice_guardian')->join('students', 'students.guardian_id', '=', 'e_invoice_guardian.guardian_id')->where('students.school_id', Auth::user()->school_id)->where('students.id', $row->student_id)->value('e_invoice_guardian.sql_code');
                $tempRow->sql_code = $sql_code;

                $studentDetails = DB::table('student_fees_details')->where('student_fees_id',$row->student_fee_id)->where('school_id',Auth::user()->school_id)->get();

                $sumDiscount = 0;
                $sumTax = 0;

                foreach($studentDetails as $studentDetail){
                    $sumDiscount += ($studentDetail->fees_type_amount * (($studentDetail->discount ?? 0) / 100)) * $studentDetail->quantity;
                    $sumTax += (($studentDetail->fees_type_amount - ($studentDetail->fees_type_amount * (($studentDetail->discount ?? 0) / 100))) * (($studentDetail->tax ?? 0) / 100)) * $studentDetail->quantity;
                }
                $total_compulsory_fees = $row->total_compulsory_fees - $sumDiscount + $sumTax;
                //Add Extra Tax
                $studentFees = DB::table('student_fees')->where('id',$row->student_fee_id)->first();
                if(isset($studentFees->tax_percentage) && $studentFees->tax_percentage > 0){
                    $extra_tax = number_format(($total_compulsory_fees * ($studentFees->tax_percentage / 100)), 2, '.', '');
                    $total_compulsory_fees += $extra_tax;
                }
                $tempRow->total_price = number_format( $total_compulsory_fees, 2, '.', '');

                if ($row->is_fully_paid == 0 && $currentDate > $due_date) {
                       
                    $studentFees = DB::select("
                        SELECT due_charges, due_charges_amount
                        FROM student_fees
                        WHERE id = ?
                    ", [$row->student_fee_id]);

                    if (count($studentFees) > 0) {
                        $due_charges_percentage = $studentFees[0]->due_charges ?? 0;
                        $due_charges_amount = $studentFees[0]->due_charges_amount ?? 0;

                        // Calculate due charges
                        if (!$due_charges_percentage) {
                            $due_charges += $due_charges_amount;
                           
                        } else {
                            $due_charges += ($total_compulsory_fees * ($due_charges_percentage / 100));
                            $due_charges = number_format($due_charges, 2, '.', '');
                          
                        }

                        $tempRow->total_price = number_format( $total_compulsory_fees + $due_charges, 2, '.', '');
                    }
                }else if($row->is_fully_paid == 1 && $row->fee_paid_date > $due_date){
                    $studentFees = DB::select("
                    SELECT due_charges, due_charges_amount
                    FROM student_fees
                    WHERE id = ?
                ", [$row->student_fee_id]);

                if (count($studentFees) > 0) {
                    $due_charges_percentage = $studentFees[0]->due_charges ?? 0;
                    $due_charges_amount = $studentFees[0]->due_charges_amount ?? 0;

                    // Calculate due charges
                    if (!$due_charges_percentage) {
                        $due_charges += $due_charges_amount;
                       
                    } else {
                        $due_charges += ($total_compulsory_fees * ($due_charges_percentage / 100));
                        $due_charges = number_format($due_charges, 2, '.', '');
                      
                    }

                    $tempRow->total_price = number_format( $total_compulsory_fees + $due_charges, 2, '.', '');
                }
                }

                if ($row->is_fully_paid == 0 && $currentDate <= $early_date) {
                    $studentFees = DB::select("
                        SELECT early_offer, early_offer_amount
                        FROM student_fees
                        WHERE id = ?
                    ", [$row->student_fee_id]);

                    if (count($studentFees) > 0) {
                        $early_offer_percentage = $studentFees[0]->early_offer ?? 0;
                        $early_offer_amount = $studentFees[0]->early_offer_amount ?? 0;
                        if (!$early_offer_percentage) {
                            $early_offer -= $early_offer_amount;
                           
                        } else {
                            $early_offer -= $total_compulsory_fees * $early_offer_percentage / 100;
                            $early_offer = number_format($early_offer, 2, '.', '');
                           
                            
                        }
                   
                        $tempRow->total_price = number_format( $total_compulsory_fees + $early_offer, 2, '.', '');
                    }
                    }else if($row->is_fully_paid == 1 && $row->fee_paid_date <= $early_date){
                        $studentFees = DB::select("
                        SELECT early_offer, early_offer_amount
                        FROM student_fees
                        WHERE id = ?
                    ", [$row->student_fee_id]);

                    if (count($studentFees) > 0) {
                        $early_offer_percentage = $studentFees[0]->early_offer ?? 0;
                        $early_offer_amount = $studentFees[0]->early_offer_amount ?? 0;
                        if (!$early_offer_percentage) {
                            $early_offer -= $early_offer_amount;
                        
                        } else {
                            $early_offer -= $total_compulsory_fees * $early_offer_percentage / 100;
                            $early_offer = number_format($early_offer, 2, '.', '');
                        
                            
                        }
                        $tempRow->total_price = number_format( $total_compulsory_fees + $early_offer, 2, '.', '');
                    }
                }

                $tempRow->payment_type = StudentFeesPaid::viewMode($tempRow->mode);
                $feesStatus = DB::table("student_fees_paids")->select("mode","status as pending_status","date as fee_paid_date","is_fully_paid as fees_status")->where("student_fees_paids.student_fees_id",$row->student_fee_id)
                ->first();

                if ($feesStatus) {
                    $tempRow->fees_status = $feesStatus->fees_status; 
                    $tempRow->pending_status = $feesStatus->pending_status;
                    $tempRow->fee_paid_date = $feesStatus->fee_paid_date;
                    $tempRow->mode = $feesStatus->mode;
                }

                $rows[] = $tempRow;
            }

            $bulkData['rows'] = $rows;
            return response()->json($bulkData);
        }
    }

    public function feesTypeReportList(Request $request)
    {
        ResponseService::noFeatureThenRedirect('Reporting');
        ResponseService::noPermissionThenRedirect('fees-paid');

        $schoolId = Auth::user()->school_id;
        $requestSessionYearId = (int)request('session_year_id');
        $sessionYearId = $requestSessionYearId ?? $this->cache->getDefaultSessionYear()->id;

        $formattedStartDate = date('Y-m-d', strtotime($request->start_date));
        $formattedEndDate = date('Y-m-d', strtotime($request->end_date));

        $chartData = DB::table('student_fees_details')
            ->join('student_fees', 'student_fees_details.student_fees_id', '=', 'student_fees.id')
            ->select(
                'student_fees_details.fees_type_name',
                DB::raw('SUM(student_fees_details.quantity) as total_quantity'),
                DB::raw('SUM(student_fees_details.fees_type_amount) AS total_amount')
            )
            ->where('student_fees_details.school_id', $schoolId)
            ->where('student_fees.session_year_id', $sessionYearId)
            ->whereBetween('student_fees_details.created_at', [$formattedStartDate, $formattedEndDate])
            ->groupBy('student_fees_details.fees_type_name')
            ->get();

        $totalRevenue = DB::SELECT('SELECT SUM(total_amount) AS total_revenue
            FROM (
                SELECT
                    sfd.fees_type_name,
                    SUM(sfd.fees_type_amount) AS total_amount
                FROM student_fees_details sfd
                INNER JOIN student_fees sf ON sfd.student_fees_id = sf.id
                WHERE sfd.school_id = ? 
                AND sf.session_year_id = ?
                AND sfd.created_at BETWEEN ? AND ?
                GROUP BY sfd.fees_type_name
            ) AS subquery', [$schoolId, $sessionYearId, $formattedStartDate, $formattedEndDate]);

        $totalRevenue = $totalRevenue[0]->total_revenue;
        $formattedRevenue = number_format($totalRevenue, 2);

        return response()->json([
            'chartData' => $chartData,
            'totalRevenue' => $formattedRevenue,
        ]);
    }

    public function feesClassReportList(Request $request)
    {
        ResponseService::noFeatureThenRedirect('Reporting');
        ResponseService::noPermissionThenRedirect('fees-paid');

        $offset = request('offset', 0);
        $limit = request('limit', 999999);
        $sort = request('sort', 'id');
        $order = request('order', 'DESC');
        $requestSessionYearId = (int)request('session_year_id');
        $sessionYearId = $requestSessionYearId ?? $this->cache->getDefaultSessionYear()->id;

        $searchParm = [$requestSessionYearId];
        $searchFilter = " WHERE sf.session_year_id = ?
                          AND sf.deleted_at IS NULL";

        if ($request->paid_status == "0") {
            $searchFilter .= "
                AND (sfp.is_fully_paid IS NULL
                OR    sfp.is_fully_paid = 0 )
            ";
        } else if ($request->paid_status == "1") {
            $searchFilter .= "
                AND sfp.is_fully_paid = 1
            ";
        }

        //filter date ....
        if ($request->start_date && !$request->end_date) {
            $searchFilter .= empty($searchFilter) ? " WHERE " : " AND ";
            $searchFilter .= " sf.created_at >= ?";

            $searchParm[] = date('Y-m-d', strtotime($request->start_date));
        } else if (!$request->start_date && $request->end_date) {
            $searchFilter .= empty($searchFilter) ? " WHERE " : " AND ";
            $searchFilter .= " sf.created_at <= ? ";

            $searchParm[] = date('Y-m-d', strtotime($request->end_date));
        } else if ($request->start_date && $request->end_date) {
            $searchFilter .= empty($searchFilter) ? " WHERE " : " AND ";
            $searchFilter .= " (sf.created_at BETWEEN ? AND ?) ";

            $searchParm[] = date('Y-m-d', strtotime($request->start_date)) . " 00:00:00";
            $searchParm[] = date('Y-m-d', strtotime($request->end_date))  . " 23:59:59";
        }

        //filter class
        if ($request->class_id) {
            $searchFilter .= empty($searchFilter) ? " WHERE " : " AND ";
            $searchFilter .= " c.id = ? ";

            $searchParm[] = $request->class_id;
        }

        if (!empty($request->search)) {
            $searchFilter .= empty($searchFilter) ? " WHERE " : " AND ";
            $searchFilter .= " (u.id LIKE ?
                OR      CONCAT(u.first_name ,' ', u.last_name) LIKE ?
                OR      c.name LIKE ?
                OR EXISTS (
                    SELECT      student_fees_id
                    FROM        student_fees_details
                    WHERE       student_fees_id = sf.id
                    GROUP BY    student_fees_id
                    HAVING      SUM(fees_type_amount) LIKE ?
                )
                OR      sfp.date LIKE ?
                OR CONCAT('INV', LPAD(sf.uid, 8, '0')) LIKE ?
            )";

            $searchParm[] = "%$request->search%";
            $searchParm[] = "%$request->search%";
            $searchParm[] = "%$request->search%";
            $searchParm[] = "%$request->search%";
            $searchParm[] = "%$request->search%";
            $searchParm[] = "%$request->search%";
        }

        $paginationFilter = "
            ORDER BY    sfp.is_fully_paid,
                        sf.created_at DESC,
                        {$sort} {$order}
            LIMIT       {$limit}
            OFFSET      {$offset}
        ";

        $ttlSql = DB::SELECT("
            SELECT      COUNT(*) AS total
            FROM        users u
            JOIN        students s
            ON          u.id = s.user_id
            JOIN        class_sections cs
            ON          cs.id = s.class_section_id
            JOIN        classes c
            ON          c.id = cs.class_id
            JOIN        student_fees sf
            ON          sf.student_id = s.id
            AND         sf.status = 'published'
            LEFT JOIN   student_fees_paids sfp
            ON          sfp.student_fees_id = sf.id
            {$searchFilter}
        ", $searchParm);

        $res = DB::select("
            SELECT      u.id
                        , s.id AS student_id
                        , sf.id AS student_fee_id
                        , sf.session_year_id
                        , CONCAT(u.first_name ,' ', u.last_name) AS full_name
                        , c.name AS class_name
                        , sfp.is_fully_paid
                        , sfp.date AS fee_paid_date
                        , sfp.is_fully_paid AS fees_status
                        , (
                            SELECT  SUM(fees_type_amount)
                            FROM    student_fees_details
                            WHERE   student_fees_id = sf.id
                            AND     fees_type_name NOT IN ('Early Discount', 'Overdue Fees')
                        ) AS total_compulsory_fees
                        , sf.due_date
                        , sfp.mode
                        , sf.created_at
                        ,sf.uid
            FROM        users u
            JOIN        students s
            ON          u.id = s.user_id
            JOIN        class_sections cs
            ON          cs.id = s.class_section_id
            JOIN        classes c
            ON          c.id = cs.class_id
            JOIN        student_fees sf
            ON          sf.student_id = s.id
            AND         sf.status = 'published'
            LEFT JOIN   student_fees_paids sfp
            ON          sfp.student_fees_id = sf.id
            {$searchFilter}
            {$paginationFilter}
        ", $searchParm);


        $total = $ttlSql[0]->total;

        $bulkData = array();
        $bulkData['total'] = $total;
        $rows = array();
        $no = 1;

        foreach ($res as $row) {

            $tempRow = $row;
            $tempRow->no = $no++;
            $tempRow->fees_status = null;
            $currentDate = date('Y-m-d');
            $due_date = date("Y-m-d", strtotime($row->due_date));
            $due_charges = "";

            if ($row->is_fully_paid == 0 && $currentDate > $due_date) {
                $studentFees = DB::select("
                    SELECT due_charges, due_charges_amount
                    FROM student_fees
                    WHERE id = ?
                ", [$row->student_fee_id]);
                if (count($studentFees) > 0) {
                    $due_charges_percentage = $studentFees[0]->due_charges ?? 0;
                    $due_charges_amount = $studentFees[0]->due_charges_amount ?? 0;
                    $due_charges = 0;

                    if (!$due_charges_percentage) {
                        $due_charges += $due_charges_amount;
                    } else {
                        $due_charges += ($row->total_compulsory_fees * ($due_charges_percentage / 100));
                    }
                    $total = $row->total_compulsory_fees + $due_charges;
                } else {
                    $total = $row->total_compulsory_fees;
                }
                $tempRow->total_compulsory_fees = number_format($total, 2);
            }

            if ($tempRow->total_compulsory_fees == null) {
                $tempRow->total_compulsory_fees = '0.00';
            } else {
                //$tempRow->total_compulsory_fees = $tempRow->total_compulsory_fees;
                $tempRow->total_compulsory_fees = sprintf('%0.2f', $tempRow->total_compulsory_fees);
                //$tempRow->total_compulsory_fees = number_format($tempRow->total_compulsory_fees, 2);
            }

            $tempRow->payment_type = StudentFeesPaid::viewMode($tempRow->mode);

            $operate = '';
            if (empty($row->is_fully_paid)) {
                $operate .= BootstrapTableService::button('fa fa-dollar', route('student-fees.compulsory.index', [$row->student_fee_id, $row->student_id]), ['btn', 'btn-xs', 'btn-gradient-success', 'btn-rounded', 'btn-icon'], ['title' => trans('Compulsory Fees')]);
            }

            if (!empty($row->is_fully_paid)) {
                $operate .= BootstrapTableService::button('fa fa-file-pdf-o', route('student-fees.paid.receipt.pdf', $row->student_fee_id), ['btn', 'btn-xs', 'btn-gradient-info', 'btn-rounded', 'btn-icon', 'generate-paid-fees-pdf'], ['target' => "_blank", 'data-id' => $row->student_fee_id, 'title' => trans('generate_pdf') . ' ' . trans('fees')]);
                $tempRow->fees_status = $row->is_fully_paid;
                $paid_amount = DB::select('SELECT amount FROM student_fees_paids WHERE student_fees_id = ?', [$row->student_fee_id]);
                $paid_amount = $paid_amount[0]->amount;
                $tempRow->total_compulsory_fees = number_format($paid_amount, 2);
            }

            $tempRow->operate = $operate;
            $rows[] = $tempRow;
        }
        $bulkData['rows'] = $rows;
        return response()->json($bulkData);
    }

    public function feesAllReportList() {
        $offset = request('offset', 0);
        $limit = request('limit', 99999);
        $sort = request('sort', 'id');
        $order = request('order', 'DESC');
        $startDate = request('start_date');
        $endDate = request('end_date');
        $classId = request('class_id');
        $studentId = request('student_id');
        $requestSessionYearId = (int)request('session_year_id');
        $sessionYearId = $requestSessionYearId ?? $this->cache->getDefaultSessionYear()->id;

        $searchParm = [$requestSessionYearId];
        $searchFilter = " WHERE sf.session_year_id = ?
                          AND sf.deleted_at IS NULL";


        $searchFilter .= "
                AND sfp.is_fully_paid = 1
            ";

        $users = DB::table('users as u')
        ->join('students as s', 'u.id', 's.user_id')
        ->select('u.id', 'u.first_name', 'u.last_name')
        ->where('s.id', '')
        ->first();

        $query = DB::table('users as u')
            ->join('students as s', 'u.id', 's.user_id')
            ->join('class_sections as cs', 'cs.id', 's.class_section_id')
            ->join('classes as c', 'c.id', 'cs.class_id')
            ->join('student_fees as sf', 'sf.student_id', 's.id')
            ->leftJoin('student_fees_paids as sfp', 'sf.id', 'sfp.student_fees_id')
            ->select(
                'u.id as user_id',
                's.id as student_id',
                'sf.id as student_fee_id',
                'sf.session_year_id',
                'cs.class_id',
                DB::raw("CONCAT(u.first_name, ' ', u.last_name) as full_name"),
                'c.name as class_name',
                DB::raw("CONCAT('INV', LPAD(sf.uid, 8, '0')) as invoice_number"),
                'sf.name',
                'sfp.is_fully_paid',
                DB::raw('
                (   SELECT SUM(fees_type_amount)
                FROM student_fees_details
                WHERE student_fees_id = sf.id
                ) as total_compulsory_fees'
            ),
            'sf.due_charges_amount',
            'sf.created_at',
            'sfp.date as fee_paid_date',
            'sf.due_date',
            'sf.due_charges_amount',
            'sf.uid',
            'sfp.mode',
            'sfp.amount',
            'sf.early_date'
        );


        if ($startDate && $endDate) {
            $query->whereBetween(DB::raw('DATE(sf.updated_at)'), [date('Y-m-d', strtotime($startDate)), date('Y-m-d', strtotime($endDate))]);
        }
        if($classId) {
            $query->where('cs.class_id', $classId);
        }

        $query->orderBy('sf.uid', $order);

        $records = $query->offset($offset)
            ->limit($limit)
            ->get();
        $total = $records->count(); // Get total records based on current filters
        $bulkData = [];
        $bulkData['total'] = $total;
        $rows = [];
        $no = $offset + 1;

        foreach ($records as $row) {

            $isFullyPaid = $row->is_fully_paid;
    
            $tempRow = (array)$row;
            $tempRow['no'] = $no++;
            $tempRow['fee_due_date'] = $row->due_date;
            $tempRow['payment_type'] = $row->mode;
            $currentDate = date('Y-m-d');
            $due_date = date("Y-m-d", strtotime($row->due_date));
            $due_charges = "";
            $early_date = date("Y-m-d", strtotime($row->early_date));
            $early_offer = "";

            if ($row->is_fully_paid == 0 && $currentDate > $due_date) {
                $studentFees = DB::select("
                    SELECT due_charges, due_charges_amount
                    FROM student_fees
                    WHERE id = ?
                ", [$row->student_fee_id]);
                if (count($studentFees) > 0) {
                    $due_charges_percentage = $studentFees[0]->due_charges ?? 0;
                    $due_charges_amount = $studentFees[0]->due_charges_amount ?? 0;
                    $due_charges = 0;

                    if (!$due_charges_percentage) {
                        $due_charges += $due_charges_amount;
                    } else {
                        $due_charges += ($row->total_compulsory_fees * ($due_charges_percentage / 100));
                    }
                    $total = $row->total_compulsory_fees + $due_charges;
                } else {
                    $total = $row->total_compulsory_fees;
                }
                $tempRow['total_compulsory_fees'] = number_format($total, 2);
            } else if ($tempRow['total_compulsory_fees'] == null) {
                $tempRow['total_compulsory_fees'] = '0.00';
            } else {
                //$tempRow->total_compulsory_fees = $tempRow->total_compulsory_fees;
                // $tempRow->total_compulsory_fees = sprintf('%0.2f', $tempRow->total_compulsory_fees);
                $tempRow['total_compulsory_fees'] = number_format(str_replace(',', '', $tempRow['total_compulsory_fees']), 2);

                //$tempRow->total_compulsory_fees = number_format($tempRow->total_compulsory_fees, 2);
            }

            if ($row->is_fully_paid) {
                $tempRow['fees_status'] = 0; //paid
            } else if ($row->is_fully_paid == 0 && $currentDate < $due_date) {
                $tempRow['fees_status'] = 1; //pending
            } else if ($row->is_fully_paid == 0 && $currentDate >= $due_date) {
                $tempRow['fees_status'] = 2; //outstanding
            }

            $rows[] = $tempRow;
        }

        $bulkData['rows'] = $rows;

        return response()->json($bulkData);
    }

    public function studentFeeSummary(Request $request)
    {
        //required : session year , start , end
        //optional : fee , class

        $settings = $this->cache->getSchoolSettings();
        $searchParm = [];
        $searchParm[] = $request->session_year_id;
        $searchParm[] = "published";

        $dateFilter = "";
        if ($request->start_date && !$request->end_date) {
            $dateFilter .= " AND created_at >= ? ";
            $searchParm[] = date('Y-m-d', strtotime($request->start_date)) . " 00:00:00";
        } else if (!$request->start_date && $request->end_date) {
            $dateFilter .= " AND created_at <= ? ";
            $searchParm[] = date('Y-m-d', strtotime($request->end_date)) . " 23:59:59";
        } else if ($request->start_date && $request->end_date) {
            $dateFilter .= " AND (created_at BETWEEN ? AND ?) ";
            $searchParm[] = date('Y-m-d', strtotime($request->start_date)) . " 00:00:00";
            $searchParm[] = date('Y-m-d', strtotime($request->end_date)) . " 23:59:59";
        }


        $feeFilter = "";
        if ($request->fees_id) {
            $feeFilter .= " AND name IN (SELECT name FROM student_fees WHERE id = ?) ";
            $searchParm[] = $request->fees_id;
        }

        $classFilter = "";
        if ($request->class_id) {
            $classFilter .= " AND class_id = ? ";
            $searchParm[] = $request->class_id;
        }

        $totalFeeQuery = "
            SELECT SUM(fees_type_amount) AS total_fees
            FROM student_fees_details sfd
            WHERE sfd.student_fees_id IN (
                SELECT id
                FROM student_fees sf
                WHERE sf.session_year_id = ?
                AND sf.status = ?
                AND sf.deleted_at IS NULL
                {$dateFilter}
                {$classFilter}
                {$feeFilter}
                AND NOT EXISTS (
                    SELECT 1
                    FROM student_fees_paids sfp
                    WHERE sfp.student_fees_id = sf.id
                )
            )
        ";
        $totalFeeResult = DB::select($totalFeeQuery, $searchParm);

        $searchParm2 = array();
        $searchFilter = " WHERE sf.session_year_id = ".$request->session_year_id." AND sf.deleted_at IS NULL";
        $searchFilter .= " AND (";
        $conditions = [];
        $conditions[] = "(sfp.is_fully_paid IS NULL OR sfp.is_fully_paid = 0)";
        $conditions[] = "sfp.status = 1";
        $conditions[] = "sfp.status = 2";
        $searchFilter .= implode(" OR ", $conditions);
        $searchFilter .= ")";

        //filter date ....
        if ($request->start_date && !$request->end_date) {
            $searchFilter .= empty($searchFilter) ? " WHERE " : " AND ";
            $searchFilter .= " sf.created_at >= ?";

            $searchParm2[] = date('Y-m-d', strtotime($request->start_date));
        } else if (!$request->start_date && $request->end_date) {
            $searchFilter .= empty($searchFilter) ? " WHERE " : " AND ";
            $searchFilter .= " sf.created_at <= ? ";

            $searchParm2[] = date('Y-m-d', strtotime($request->end_date));
        } else if ($request->start_date && $request->end_date) {
            $searchFilter .= empty($searchFilter) ? " WHERE " : " AND ";
            $searchFilter .= " (sf.created_at BETWEEN ? AND ?) ";

            $searchParm2[] = date('Y-m-d', strtotime($request->start_date)) . " 00:00:00";
            $searchParm2[] = date('Y-m-d', strtotime($request->end_date))  . " 23:59:59";
        }

        if ($request->fees_id) {
            $searchFilter .= empty($searchFilter)?" WHERE ":" AND ";
            $searchFilter .= " sf.name IN (SELECT name FROM student_fees WHERE id = ?) ";

            $searchParm2[] = $request->fees_id;
        }

        //filter class
        if ($request->class_id) {
            $searchFilter .= empty($searchFilter)?" WHERE ":" AND ";
            $searchFilter .= " c.id = ? ";

            $searchParm2[] = $request->class_id;
        }

        $res2 = DB::select("
            SELECT      u.id
                        , s.id AS student_id
                        , sf.id AS student_fee_id
                        , sf.session_year_id
                        , CONCAT(u.first_name ,' ', u.last_name) AS full_name
                        , c.name AS class_name
                        , sfp.is_fully_paid
                        , sfp.date AS fee_paid_date
                        , sfp.is_fully_paid AS fees_status
                        , (
                            SELECT  SUM(fees_type_amount * quantity)
                            FROM    student_fees_details
                            WHERE   student_fees_id = sf.id
                            AND     fees_type_name NOT IN ('Early Discount', 'Overdue Fees')
                        ) AS total_compulsory_fees
                        , sf.due_date 
                        , sf.early_date
                        , sf.name
                        , sfp.mode
                        , sfp.status AS pending_status
                        , sf.created_at
                        , sf.uid
            FROM        users u
            JOIN        students s
            ON          u.id = s.user_id
            JOIN        class_sections cs
            ON          cs.id = s.class_section_id
            JOIN        classes c
            ON          c.id = cs.class_id
            JOIN        student_fees sf
            ON          sf.student_id = s.id
            AND         sf.status = 'published'
            LEFT JOIN   student_fees_paids sfp
            ON          sfp.student_fees_id = sf.id
            {$searchFilter}           
        ", $searchParm2);

            $totalAmount = 0;
            foreach($res2 as $row){
                $tempRow = $row;
                $due_charges = 0;
                $early_offer = 0;
                $tempRow = $row;
                $currentDate = date('Y-m-d');
                $due_date = date("Y-m-d", strtotime($row->due_date));
                $early_date = date("Y-m-d", strtotime($row->early_date));

                $tempRow->fees_status = null;
                $tempRow->quantity = null;
                $tempRow->unit_price = number_format( $tempRow->total_compulsory_fees, 2, '.', '');
                $tempRow->total_price = number_format( $row->total_compulsory_fees, 2, '.', '');


                $studentDetails = DB::table('student_fees_details')->where('student_fees_id',$row->student_fee_id)->where('school_id',Auth::user()->school_id)->get();

                $sumDiscount = 0;
                $sumTax = 0;

                foreach($studentDetails as $studentDetail){
                    $sumDiscount += ($studentDetail->fees_type_amount * (($studentDetail->discount ?? 0) / 100)) * $studentDetail->quantity;
                    $sumTax += (($studentDetail->fees_type_amount - ($studentDetail->fees_type_amount * (($studentDetail->discount ?? 0) / 100))) * (($studentDetail->tax ?? 0) / 100)) * $studentDetail->quantity;
                }
                $total_compulsory_fees = $row->total_compulsory_fees - $sumDiscount + $sumTax;
                $tempRow->total_price = number_format( $total_compulsory_fees, 2, '.', '');
                

                if ($row->is_fully_paid == 0 && $currentDate > $due_date) {
                    $studentFees = DB::select("
                        SELECT due_charges, due_charges_amount
                        FROM student_fees
                        WHERE id = ?
                    ", [$row->student_fee_id]);

                    if (count($studentFees) > 0) {
                        $due_charges_percentage = $studentFees[0]->due_charges ?? 0;
                        $due_charges_amount = $studentFees[0]->due_charges_amount ?? 0;

                        // Calculate due charges
                        if (!$due_charges_percentage) {
                            $due_charges += $due_charges_amount;
                           
                        } else {
                            $due_charges += ($total_compulsory_fees * ($due_charges_percentage / 100));
                            $due_charges = number_format($due_charges, 2, '.', '');
                          
                        }

                        $tempRow->total_price = number_format( $total_compulsory_fees + $due_charges, 2, '.', '');
                    }
                } else if($row->is_fully_paid == 1 && $row->fee_paid_date > $due_date){
                    $studentFees = DB::select("
                        SELECT due_charges, due_charges_amount
                        FROM student_fees
                        WHERE id = ?
                    ", [$row->student_fee_id]);

                    if (count($studentFees) > 0) {
                        $due_charges_percentage = $studentFees[0]->due_charges ?? 0;
                        $due_charges_amount = $studentFees[0]->due_charges_amount ?? 0;

                        // Calculate due charges
                        if (!$due_charges_percentage) {
                            $due_charges += $due_charges_amount;
                        
                        } else {
                            $due_charges += ($total_compulsory_fees * ($due_charges_percentage / 100));
                            $due_charges = number_format($due_charges, 2, '.', '');
                        
                        }

                        $tempRow->total_price = number_format( $total_compulsory_fees + $due_charges, 2, '.', '');
                    }
                }

                if ($row->is_fully_paid == 0 && $currentDate <= $early_date) {
                    $studentFees = DB::select("
                        SELECT early_offer, early_offer_amount
                        FROM student_fees
                        WHERE id = ?
                    ", [$row->student_fee_id]);

                    if (count($studentFees) > 0) {
                        $early_offer_percentage = $studentFees[0]->early_offer ?? 0;
                        $early_offer_amount = $studentFees[0]->early_offer_amount ?? 0;
                        if (!$early_offer_percentage) {
                            $early_offer -= $early_offer_amount;
                           
                        } else {
                            $early_offer -= $total_compulsory_fees * $early_offer_percentage / 100;
                            $early_offer = number_format($early_offer, 2, '.', '');
                           
                            
                        }
                   
                        $tempRow->total_price = number_format( $total_compulsory_fees + $early_offer, 2, '.', '');
                    }
                } else if ($row->is_fully_paid == 1 && $row->fee_paid_date <= $early_date){
                        $studentFees = DB::select("
                        SELECT early_offer, early_offer_amount
                        FROM student_fees
                        WHERE id = ?
                    ", [$row->student_fee_id]);

                    if (count($studentFees) > 0) {
                        $early_offer_percentage = $studentFees[0]->early_offer ?? 0;
                        $early_offer_amount = $studentFees[0]->early_offer_amount ?? 0;
                        if (!$early_offer_percentage) {
                            $early_offer -= $early_offer_amount;
                        
                        } else {
                            $early_offer -= $total_compulsory_fees * $early_offer_percentage / 100;
                            $early_offer = number_format($early_offer, 2, '.', '');
                        
                            
                        }
                        $tempRow->total_price = number_format( $total_compulsory_fees + $early_offer, 2, '.', '');
                    }
                }

                $totalAmount = $totalAmount + $tempRow->total_price;
            }

        $searchParm3=$searchParm;
        $searchParm3[]=Auth::user()->school_id;
        $totalFeePaidQuery = "
            SELECT  SUM(amount) AS total_fees
            FROM    student_fees_paids
            WHERE   student_fees_id IN (
                SELECT  id
                FROM    student_fees
                WHERE   session_year_id = ?   
                AND     status = ?             
                {$dateFilter}
                {$classFilter}
                {$feeFilter}
                AND deleted_at IS NULL
            )
            AND status = 1
            AND school_id = ?
        ";
        $totalFeePaidResult = DB::select($totalFeePaidQuery, $searchParm3);

        $dueChargesQuery = "
            SELECT sf.*
            FROM student_fees sf
            WHERE sf.school_id = ?
            AND sf.due_date < CURDATE()
            AND sf.deleted_at IS NULL
            AND sf.session_year_id = ?
            AND sf.status = ?
            AND NOT EXISTS (
                SELECT 1
                FROM student_fees_paids sfp
                WHERE sfp.student_fees_id = sf.id
                AND sfp.is_fully_paid IS NOT NULL
            )
            AND (sf.due_charges != 0 OR sf.due_charges_amount != 0)
            AND NOT (sf.due_charges != 0 AND sf.due_charges_amount != 0)
            {$dateFilter}
            {$feeFilter}
            {$classFilter}";

        $searchParm = array_merge([Auth::user()->school_id], $searchParm);
        $dueChargesQueryResult = DB::select($dueChargesQuery, $searchParm);
        $dueCharges = 0;
        foreach ($dueChargesQueryResult as $charges) {
            if ($charges->due_charges) {
                $totalCompulsoryFees = DB::table('student_fees_details')
                    ->where('student_fees_id', $charges->id)
                    ->sum(DB::raw('fees_type_amount * quantity'));
                $dueCharges += ($totalCompulsoryFees * ($charges->due_charges / 100));
            } else {
                $dueCharges += $charges->due_charges_amount;
            }
        }
        $totalFeesWithDue = $totalFeeResult[0]->total_fees + $dueCharges + $totalFeePaidResult[0]->total_fees;

        $fees_data = [
            'total_fees' => $totalAmount,
            //'total_fees' => $totalFeesWithDue,
            'total_fees_collected' => $totalFeePaidResult[0]->total_fees,
            'currency_symbol' => ($settings['currency_symbol'] ?? '')
        ];
      
        return response()->json([
            'error'   => false,
            'message' => "Fee Summary",
            'fees_data' => $fees_data
        ]);
    }

    public function feesPaidReceiptPDF($feesPaidId, $filename = null,$isPaid = null)
    {
        ResponseService::noFeatureThenRedirect('Fees Management');
        ResponseService::noPermissionThenRedirect('fees-paid');
        try {
           
            $feesPaid = $this->studentFeesPaid->builder()
                ->with(['student_fees', 'student_fees.class', 'student_fees.student_fees_details'])
                ->where('student_fees_id', $feesPaidId)
                ->first();

            $studentFee = $this->studentFees->findById($feesPaidId);
            // dd($studentFee);

            if($studentFee->student_id !== null){
                $student = $this->student->builder()
                ->with('user:id,first_name,last_name', 'class_section.class')
                ->find($studentFee->student_id);
                $studentUser = DB::select("SELECT * FROM users WHERE id = " . $student->user_id);
                $studentDetail = $studentUser[0];
                $parent = DB::select("SELECT * FROM users WHERE id = " . $student->guardian_id);
                $parentDetail = $parent[0];
                $studentFeesDetail = DB::select("SELECT * FROM student_fees_details WHERE student_fees_id = ? AND fees_type_name NOT IN ('Overdue Fees', 'Early Discount')", [$feesPaidId]);
                $e_invoice_school = DB::table('e_invoice')->where('school_id', Auth::user()->school_id)->first();
                $e_invoice_guardian = DB::table('e_invoice_guardian')->where('guardian_id', $student->guardian_id)->first();
                $studentFees = DB::select("SELECT * FROM student_fees WHERE id = " . $feesPaidId);
                $dueCharges = 0;
                $earlyoffer = 0;
                $earlyy = 0;
                $due_fees = 0;

            

                $totalDiscount=[];
                $totalTax=[];
                $sumDiscount = 0;
                $sumTax = 0;
                $i=0;
                $remarks = null;

                foreach($studentFeesDetail as $studentFeeDetail){

                    // Initialize array elements if not set
                    if (!isset($totalDiscount[$i])) {
                        $totalDiscount[$i] = 0;
                    }
                    if (!isset($totalTax[$i])) {
                        $totalTax[$i] = 0;
                    }

                    // calculate discount for each item
                    if($studentFeeDetail->discount > 0){
                        $itemDiscount = $studentFeeDetail->fees_type_amount * $studentFeeDetail->discount / 100;
                        $totalDiscount[$i] += $itemDiscount * $studentFeeDetail->quantity;
                        $sumDiscount += $totalDiscount[$i];
                    
                    }

                    // calculate tax for each item
                    if($studentFeeDetail->tax > 0){
                        $itemTax = ($studentFeeDetail->fees_type_amount - ($studentFeeDetail->fees_type_amount * $studentFeeDetail->discount / 100)) * $studentFeeDetail->tax / 100;
                        $totalTax[$i] += $itemTax * $studentFeeDetail->quantity;
                        $sumTax += $totalTax[$i];
                        
                    }

                    $i++;
                }

            
                if ($studentFees) {
                    $studentFees[0]->total_due_charges = 0;
                    $dueFee = DB::select('SELECT due_date FROM student_fees WHERE id= ? AND due_date < ?', [$studentFees[0]->id, date('Y-m-d')]);
                    $totalCompulsoryFees = DB::table('student_fees_details')
                        ->where('student_fees_id', $studentFees[0]->id)
                        ->whereNotIn('fees_type_name', ['Overdue Fees', 'Early Discount'])
                        ->sum(DB::raw('(fees_type_amount - (fees_type_amount * COALESCE(discount, 0) / 100) + ((fees_type_amount - (fees_type_amount * COALESCE(discount, 0) / 100)) * COALESCE(tax, 0) / 100)) * quantity'));
                
                    if(isset($studentFees[0]->tax_percentage) && $studentFees[0]->tax_percentage > 0){
                        $extraTax = ($totalCompulsoryFees * ($studentFees[0]->tax_percentage / 100));
                        $totalCompulsoryFees += $extraTax;
                    }

                    if (!isset($feesPaid) && $dueFee) {
                        if (!$studentFees[0]->due_charges) {
                            $dueCharges += $studentFees[0]->due_charges_amount;
                        } else {
                            $dueCharges += ($totalCompulsoryFees * ($studentFees[0]->due_charges / 100));
                        }

                        $studentFees[0]->total_due_charges = $dueCharges;
                        $due_fees = $dueCharges;
                    } else if (isset($feesPaid) && $dueFee) {
                        if ($feesPaid->date > $dueFee[0]->due_date) {
                            if (!$studentFees[0]->due_charges) {
                                $dueCharges += $studentFees[0]->due_charges_amount;
                            } else {
                                $dueCharges += ($totalCompulsoryFees * ($studentFees[0]->due_charges / 100));
                            }
                            $studentFees[0]->total_due_charges = $dueCharges;
                            $due_fees = $dueCharges;
                        }
                    }
                }
                if ($studentFees) {
                    $studentFees[0]->total_discount_charges = 0;
                    $student_early_discount = DB::table("student_fees_details")
                    ->select("fees_type_amount")
                    ->where("fees_type_name", "Early Discount")
                    ->where("student_fees_id", $studentFees[0]->id) 
                    ->first();
                    $earlyFee = DB::select('SELECT early_date FROM student_fees WHERE id= ? AND early_date >= ?', [$studentFees[0]->id, date('Y-m-d')]);
                    $totalCompulsoryFees = DB::table('student_fees_details')
                        ->where('student_fees_id', $studentFees[0]->id)
                        ->whereNotIn('fees_type_name', ['Overdue Fees', 'Early Discount'])
                        ->sum(DB::raw('(fees_type_amount - (fees_type_amount * COALESCE(discount, 0) / 100) + ((fees_type_amount - (fees_type_amount * COALESCE(discount, 0) / 100)) * COALESCE(tax, 0) / 100)) * quantity'));
                    
                    
                    if(isset($studentFees[0]->tax_percentage) && $studentFees[0]->tax_percentage > 0){
                        $extraTax = ($totalCompulsoryFees * ($studentFees[0]->tax_percentage / 100));
                        $totalCompulsoryFees += $extraTax;
                    }

                    if (!isset($feesPaid) && $earlyFee  && $earlyFee >= date('Y-m-d')) {
                        if (!$studentFees[0]->early_offer) {
                            $earlyoffer -= $studentFees[0]->early_offer_amount;
                        } else {
                            $earlyoffer -= ($totalCompulsoryFees * ($studentFees[0]->early_offer / 100));
                            // dd($earlyoffer);
                        }

                        $studentFees[0]->total_discount_charges = $earlyoffer;
                        $earlyy = $earlyoffer;
                    } else if (isset($feesPaid)) {
                        if ($feesPaid->date <= $studentFees[0]->early_date) {
                            if (!$studentFees[0]->early_offer) {
                                $earlyoffer -= $studentFees[0]->early_offer_amount;
                            } else {
                                $earlyoffer -= ($totalCompulsoryFees * ($studentFees[0]->early_offer / 100));
                            }
                            $studentFees[0]->total_discount_charges = $earlyoffer;
                            $earlyy = $earlyoffer;
                        }
                    }
                }

                $school = $this->cache->getSchoolSettings();
                $schoolSettings = DB::select("SELECT data FROM school_settings WHERE name = 'horizontal_logo' AND school_id = " . $student->school_id);
                $schoolLogo = '';
                if (COUNT($schoolSettings)) {
                    $schoolLogo = $schoolSettings[0]->data;
                }
                $dueQuery = DB::select('SELECT due_date FROM student_fees WHERE id= ?', [$studentFees[0]->id]);
                $dueDate = COUNT($dueQuery) ? $dueQuery[0]->due_date : '';
                $studentFeeEinvoice = DB::table('student_fees_einvoice')->where('student_fees_id',$studentFees[0]->id)->orderByDesc('id')->first();
                if(isset($studentFeeEinvoice) && $studentFeeEinvoice->status != 0){
                    $documentSummary = json_decode($studentFeeEinvoice->document_summary);
                    // dd($documentSummary);
                    $url = env('E_INVOICE_URL').'/'.$studentFeeEinvoice->uuid.'/share/'.$documentSummary[0]->longId;
                    // dd($url);
                    $studentFeeEinvoice->e_invoice_url = (new QRCode)->render($url);
                }

                //Table format tax type, tax rate and tax amount
                if(isset($studentFees[0]->tax_percentage) && $studentFees[0]->tax_percentage > 0){
                    $taxTypeJSON = json_decode(file_get_contents('assets/JSON/eInvoice/TaxTypes.json'),true);
                    foreach ($taxTypeJSON as $tax) {
                        if (isset($tax['Code']) && $tax['Code'] === $studentFees[0]->tax_type) {
                            $studentFees[0]->tax_type = $tax['Description'];
                            break;
                        }
                    }
                }

                // return view('student-fees.student_fees_receipt', compact('school', 'feesPaid', 'student','studentDetail', 'parentDetail', 'studentFeesDetail', 'studentFees','schoolLogo','due_fees', 'earlyy', 'dueDate', 'e_invoice_school', 'e_invoice_guardian','totalTax','totalDiscount','sumTax','sumDiscount','isPaid'));
                $pdf = Pdf::loadView('student-fees.student_fees_receipt', compact('school', 'feesPaid', 'student', 'studentDetail', 'parentDetail', 'studentFeesDetail', 'studentFees', 'schoolLogo', 'due_fees', 'earlyy', 'dueDate', 'e_invoice_school', 'e_invoice_guardian','totalTax','totalDiscount','sumTax','sumDiscount','isPaid','studentFeeEinvoice', 'remarks'));

                if ($filename === 'invoice') {
                    return $pdf->stream('student-fees-invoice.pdf');
                } else {
                    return $pdf->stream('student-fees-receipt.pdf');
                }
            }else{
                $consolidateInvoices = DB::table('student_fees_consolidate')
                ->where('reference_uid', $studentFee->uid)
                ->get();
            
                $studentFeeIds = $consolidateInvoices->pluck('student_fees_id')->toArray();
                
                $studentFee = DB::table('student_fees')
                ->where('id',$feesPaidId)
                ->get();

                $referenceStudentFees = DB::table('student_fees')
                ->wherein('id',$studentFeeIds)
                ->where('school_id',Auth::user()->school_id)
                ->get();

                $groupedByStudentFees = $referenceStudentFees->groupBy('student_id');
                // dd($studentFees);

                $school = $this->cache->getSchoolSettings();
                $schoolSettings = DB::select("SELECT data FROM school_settings WHERE name = 'horizontal_logo' AND school_id = " . Auth::user()->school_id);
                $schoolLogo = '';
                $studentFeeEinvoice = DB::table('student_fees_einvoice')->where('student_fees_id',$feesPaidId)->orderByDesc('id')->first();
                if (COUNT($schoolSettings)) {
                    $schoolLogo = $schoolSettings[0]->data;
                }
                $e_invoice_school = DB::table('e_invoice')->where('school_id', Auth::user()->school_id)->first();
 
                $studentFeesDetail = [];
                $tempRow = [];
                
                
                
                ///Loop each student
                foreach ($groupedByStudentFees as $studentId => $fees) {
                    $totalAmount = 0;
                    $formattedUids = [];
                    foreach ($fees as $fee) {
                        $formattedUids[] = 'R' . $fee->uid;
                        $formattedUidsString = implode(', ', $formattedUids);
                        ///Find Total Amount
                       $feesPaid = DB::table('student_fees_paids')
                       ->where('student_fees_id',$fee->id)
                       ->sum('amount');
                       $totalAmount += $feesPaid;
                    }
                    $tempRow['name'] = $formattedUidsString;
                    $tempRow['totalAmount'] = $totalAmount;
                  
                  

                }

                $studentFeeEinvoice = DB::table('student_fees_einvoice')->where('student_fees_id',$feesPaidId)->orderByDesc('id')->first();
                if(isset($studentFeeEinvoice) && $studentFeeEinvoice->status != 0){
                    $documentSummary = json_decode($studentFeeEinvoice->document_summary);
                    // dd($documentSummary);
                    $url = env('E_INVOICE_URL').'/'.$studentFeeEinvoice->uuid.'/share/'.$documentSummary[0]->longId;
                    // dd($url);
                    $studentFeeEinvoice->e_invoice_url = (new QRCode)->render($url);
                }
                
                $studentFeesDetail = $tempRow;
                $remarks = ''; // Initialize $remarks for consolidated invoice
                    
    
                   
                $pdf = Pdf::loadView('student-fees.consolidate_fees_receipt', compact('school','schoolLogo','e_invoice_school','studentFeeEinvoice','consolidateInvoices','studentFee','studentFeesDetail','remarks'));

                    return $pdf->stream('student-fees-invoice.pdf');
                }
               
            
        } catch (Throwable $e) {
            //ResponseService::errorRedirectResponse();

            ResponseService::logErrorResponse($e);
            ResponseService::errorResponse();
            return false;
        }
    }

    public function payCompulsoryFeesIndex($studentFeesID, $studentID)
    {
        ResponseService::noFeatureThenRedirect('Fees Management');
        //        ResponseService::noPermissionThenRedirect('fees-edit');
        $studentFees = $this->studentFees->findById(
            $studentFeesID, 
            ['*'], 
            ['student_fees_details' => function($query) {
                $query->whereNotIn('fees_type_name', ['Early Discount', 'Overdue Fees']);
            }]
        );
        $res = DB::select("
            SELECT      sf.id AS student_fees_id,
                        s.id AS student_id,
                        s.school_id,
                        CONCAT(u.first_name , u.last_name) AS full_name,
                        s.guardian_id,
                        (
                            SELECT  SUM( (fees_type_amount - (fees_type_amount * COALESCE(discount,0)/100) + ((fees_type_amount - (fees_type_amount * COALESCE(discount,0)/100)) * COALESCE(tax,0)/100) ) * quantity ) 
                            FROM    student_fees_details
                            WHERE   student_fees_id = sf.id
                            AND     fees_type_name NOT IN ('Early Discount', 'Overdue Fees')
                        )    AS total_compulsory_fees,
                        (
                            SELECT  SUM(fees_type_amount * discount / 100 *quantity) 
                            FROM    student_fees_details
                            WHERE   student_fees_id = sf.id
                            AND     fees_type_name NOT IN ('Early Discount', 'Overdue Fees')
                        )   AS sumDiscount,
                        (
                            SELECT  SUM((fees_type_amount - (fees_type_amount * discount / 100)) * tax / 100 * quantity) 
                            FROM    student_fees_details
                            WHERE   student_fees_id = sf.id
                            AND     fees_type_name NOT IN ('Early Discount', 'Overdue Fees')
                        )   AS sumTax,
                        c.name AS class_name,
                        sfp.is_fully_paid,
                        sfp.mode,
                        sfp.cheque_no,
                        sfp.date,
                        sf.tax_percentage,
                        sf.tax_type
            FROM        users u
            JOIN        students s
            ON          u.id = s.user_id
            JOIN        class_sections cs
            ON          cs.id = s.class_section_id
            JOIN        classes c
            ON          c.id = cs.class_id
            JOIN        student_fees sf
            ON          sf.student_id = s.id
            AND         sf.status = 'published'
            LEFT JOIN   student_fees_paids sfp
            ON          sfp.student_fees_id = sf.id
            WHERE       s.id = ?
            AND         sf.id = ?
            AND         sf.deleted_at IS NULL
            ORDER BY    sfp.is_fully_paid, sf.created_at desc
        ", [$studentID, $studentFeesID]);
        $student = $res[0];
        if ($student) {


            $currentDate = date('Y-m-d');
            $due_date = DB::select('SELECT due_date FROM student_fees WHERE id= ?', [$student->student_fees_id]);
            $due_date = $due_date[0]->due_date;
            $due_date = date("Y-m-d", strtotime($due_date));
            $early_date = DB::select('SELECT early_date FROM student_fees WHERE id= ?', [$student->student_fees_id]);
            $early_date = $early_date[0]->early_date;
            $early_date = date("Y-m-d", strtotime($early_date));
            $total_compulsory_fees = floatval(str_replace(',', '', $student->total_compulsory_fees));

            $extraTax = 0;
            if(isset($student->tax_percentage) && $student->tax_percentage > 0){
                $taxTypeJSON = json_decode(file_get_contents('assets/JSON/eInvoice/TaxTypes.json'),true);
                foreach ($taxTypeJSON as $tax) {
                    if (isset($tax['Code']) && $tax['Code'] === $student->tax_type) {
                        $student->tax_type = $tax['Description'];
                        break;
                    }
                }
                $extraTax = ($total_compulsory_fees * ($student->tax_percentage / 100));
                $total_compulsory_fees += $extraTax;
                $student->total_compulsory_fees = $total_compulsory_fees;
            }
            $earlyFee = 0;
            $dueFee = 0;
            $studentFeesCharges = DB::table('student_fees')->where('id',$student->student_fees_id)->first();
            if ($student->is_fully_paid == 0 && $currentDate > $due_date) {
                $student_overdue_fees = DB::table("student_fees_details")
                ->select("fees_type_amount")
                ->where("fees_type_name", "Overdue Fees")
                ->where("student_fees_id", $student->student_fees_id) 
                ->get();
                if ($student_overdue_fees->isNotEmpty()) {
                    $total_compulsory_fees += $student_overdue_fees->first()->fees_type_amount;
                    $student->due_charges = number_format($student_overdue_fees->first()->fees_type_amount, 2);
                    $student->total_compulsory_fees = $total_compulsory_fees;   
                } else {
                    if ($currentDate > $due_date) {
                        if (!$studentFeesCharges->due_charges) {
                            $dueFee = $studentFeesCharges->due_charges_amount;
                            $total_compulsory_fees += $dueFee;
                        } else {
                            $dueFee =($total_compulsory_fees * ($studentFeesCharges->due_charges / 100));
                            $total_compulsory_fees += $dueFee;
                        }
                        $student->due_charges = number_format($dueFee);
                        $student->total_compulsory_fees = $total_compulsory_fees;   
                    } 
                }
            }
            // Only apply the early discount if there are no overdue fees
            if ($student->is_fully_paid == 0 && $currentDate <= $early_date && $currentDate <= $due_date) {
                $student_early_discount = DB::table("student_fees_details")
                    ->select("fees_type_amount")
                    ->where("fees_type_name", "Early Discount")
                    ->where("student_fees_id", $student->student_fees_id) 
                    ->get();
                if ($student_early_discount->isNotEmpty()) {
                    $total_compulsory_fees += $student_early_discount->first()->fees_type_amount;
                    $student->early_offer = number_format($student_early_discount->first()->fees_type_amount, 2);
                    $student->total_compulsory_fees = $total_compulsory_fees;
                    
                } else {
                    if ($currentDate <= $early_date) {
                        if (!$studentFeesCharges->early_offer) {
                            $earlyFee = $studentFeesCharges->early_offer_amount;
                            $total_compulsory_fees -= $earlyFee;
                        } else {
                            $earlyFee = ($total_compulsory_fees * ($studentFeesCharges->early_offer / 100));
                            $total_compulsory_fees -= $earlyFee;
                        }
                        $student->early_offer = number_format($earlyFee);
                        $student->total_compulsory_fees = $total_compulsory_fees;
                    } 
                }
            }
        }
        $sumDiscount= $student->sumDiscount;
        $sumTax = $student->sumTax;

        if ($student->is_fully_paid) {
           ResponseService::successRedirectResponse(route('student-fees.paid.index'), 'Compulsory Fees Already Paid');
        }

        $currencySymbol = $this->cache->getSchoolSettings('currency_symbol');
        return view('student-fees.pay-compulsory', compact('studentFees', 'student', 'currencySymbol','sumDiscount','sumTax','extraTax'));
    }

    public function payCompulsoryFeesStore(Request $request)
    {
        ResponseService::noFeatureThenRedirect('Fees Management');
        ResponseService::noPermissionThenRedirect('fees-paid');

        $request->validate([
            'student_fees_id'            => 'required|numeric',
            'student_id'         => 'required|numeric',
            'school_id'         => 'required|numeric',
        ]);
     

        $res = DB::select("
            SELECT      sf.id AS student_fees_id,
                        s.id AS student_id,
                        s.school_id,
                        CONCAT(u.first_name , u.last_name) AS full_name,
                        s.guardian_id,
                        (
                           SELECT  SUM(((fees_type_amount - (fees_type_amount * COALESCE(discount,0) / 100)) + 
                            ((fees_type_amount - (fees_type_amount * COALESCE(discount,0) / 100)) * COALESCE(tax,0) /100)) * quantity)
                            FROM    student_fees_details
                            WHERE   student_fees_id = sf.id
                            AND     fees_type_name NOT IN ('Early Discount', 'Overdue Fees')
                        ) AS total_compulsory_fees,
                        c.name AS class_name,
                        sfp.is_fully_paid,
                        sfp.mode,
                        sfp.cheque_no,
                        sfp.date,
                        sf.tax_percentage
            FROM        users u
            JOIN        students s
            ON          u.id = s.user_id
            JOIN        class_sections cs
            ON          cs.id = s.class_section_id
            JOIN        classes c
            ON          c.id = cs.class_id
            JOIN        student_fees sf
            ON          sf.student_id = s.id
            AND         sf.status = 'published'
            LEFT JOIN   student_fees_paids sfp
            ON          sfp.student_fees_id = sf.id
            WHERE       s.id = ?
            AND         sf.id = ?
            AND         sf.deleted_at IS NULL
            ORDER BY    sfp.is_fully_paid, sf.created_at desc
        ", [$request->student_id, $request->student_fees_id]);
        $student = $res[0];
        if ($student) {
           
            $currentDate = date('Y-m-d');
            $due_date = DB::select('SELECT due_date FROM student_fees WHERE id= ?', [$student->student_fees_id]);
            $due_date = $due_date[0]->due_date;
            $due_date = date("Y-m-d", strtotime($due_date));
            $early_date = DB::select('SELECT early_date FROM student_fees WHERE id= ?', [$student->student_fees_id]);
            $early_date = $early_date[0]->early_date;
            $early_date = date("Y-m-d", strtotime($early_date));
            $total_compulsory_fees = floatval(str_replace(',', '', $student->total_compulsory_fees));
            
            

            if(isset($student->tax_percentage) && $student->tax_percentage > 0){
                $extraTax = ($total_compulsory_fees * ($student->tax_percentage / 100));
                $total_compulsory_fees += $extraTax;
                $student->total_compulsory_fees = $total_compulsory_fees;
            }
          
            if ($student->is_fully_paid == 0 && $currentDate > $due_date) {
                $student_overdue_fees = DB::table("student_fees_details")
                ->select("fees_type_amount")
                ->where("fees_type_name", "Overdue Fees")
                ->where("student_fees_id", $student->student_fees_id) 
                ->get();
                if ($student_overdue_fees->isNotEmpty()) {
                    $total_compulsory_fees += $student_overdue_fees->first()->fees_type_amount;
                    $student->due_charges = number_format($student_overdue_fees->first()->fees_type_amount, 2);
                    $student->total_compulsory_fees = $total_compulsory_fees;   
                }
            }
            
            // Only apply the early discount if there are no overdue fees
            if ($student->is_fully_paid == 0 && $currentDate <= $early_date && $currentDate < $due_date) {
                $student_early_discount = DB::table("student_fees_details")
                    ->select("fees_type_amount")
                    ->where("fees_type_name", "Early Discount")
                    ->where("student_fees_id", $student->student_fees_id) 
                    ->get();
            
                if ($student_early_discount->isNotEmpty()) {
                    $total_compulsory_fees += $student_early_discount->first()->fees_type_amount;
                    $student->early_offer = number_format($student_early_discount->first()->fees_type_amount, 2);
                    $student->total_compulsory_fees = $total_compulsory_fees;
                }
            }
        }


        try {
            DB::beginTransaction();
            $fees = $this->studentFees->findById($request->student_fees_id, ['*'], ['student_fees_details', 'student_fees_paid']);

            $feesPaid = $this->studentFeesPaid->builder()->where([
                'student_fees_id'    => $request->student_fees_id,
                'school_id' => $request->school_id
            ])->first();

            /*
            if (!empty($feesPaid) && $feesPaid->is_fully_paid) {
                ResponseService::errorResponse("Fees already Paid");
            }
            */
            // $dueCharges = 0;
            // $dueFee = DB::select('SELECT due_date FROM student_fees WHERE id= ? AND due_date < ?', [$fees->id, date('Y-m-d')]);
            // if ($dueFee) {
            //     $totalCompulsoryFees = DB::table('student_fees_details')
            //         ->where('student_fees_id', $fees->id)
            //         ->sum(DB::raw('fees_type_amount * quantity'));

            //     if (!$fees->due_charges) {
            //         $dueCharges += $fees->due_charges_amount;
            //     } else {
            //         $dueCharges += ($totalCompulsoryFees * ($fees->due_charges / 100));
            //     }
            // }
            // $earlyoffer = 0;
            // $earlyFee = DB::select('SELECT early_date FROM student_fees WHERE id= ? AND early_date >= ?', [$fees->id, date('Y-m-d')]);
            // if ($earlyFee) {
            //     $totalCompulsoryFees = DB::table('student_fees_details')
            //         ->where('student_fees_id', $fees->id)
            //         ->sum(DB::raw('fees_type_amount * quantity'));

            //     if (!$fees->early_charges) {
            //         $earlyoffer -= $fees->early_offer_amount;
            //     } else {
            //         $earlyoffer -= ($totalCompulsoryFees * ($fees->early_offer / 100));
            //     }
            // }

            // $sumDiscount = 0;
            // $sumTax = 0;
            // $i=0;


            // $studentFeesDetail = DB::select("SELECT * FROM student_fees_details WHERE student_fees_id = " .$fees->id);

            // foreach($studentFeesDetail as $studentFeeDetail){

            //     // Initialize array elements if not set
            //     if (!isset($totalDiscount[$i])) {
            //         $totalDiscount[$i] = 0;
            //     }
            //     if (!isset($totalTax[$i])) {
            //         $totalTax[$i] = 0;
            //     }

            //     // calculate discount for each item
            //     if($studentFeeDetail->discount > 0){
            //         $itemDiscount = $studentFeeDetail->fees_type_amount * $studentFeeDetail->discount / 100;
            //         $totalDiscount[$i] += $itemDiscount * $studentFeeDetail->quantity;
            //         $sumDiscount += $totalDiscount[$i];
                  
            //     }
            //     else{
            //         $totalDiscount[$i] += $studentFeeDetail->discount;
            //         $sumDiscount += $totalDiscount[$i];
                   
            //     }

            //     // calculate tax for each item
            //     if($studentFeeDetail->tax > 0){
            //         $itemTax = ($studentFeeDetail->fees_type_amount - ($studentFeeDetail->fees_type_amount * $studentFeeDetail->discount / 100)) * $studentFeeDetail->tax / 100;
            //         $totalTax[$i] += $itemTax * $studentFeeDetail->quantity;
            //         $sumTax += $totalTax[$i];
                    
            //     }else{
            //         $totalTax[$i] += $studentFeeDetail->tax;
            //         $sumTax += $totalTax[$i];

            //     }

            //     $i++;
            // }
            if (empty($feesPaid)) {
                $feesPaidResult = $this->studentFeesPaid->create([
                    'date'                => date('Y-m-d', strtotime($request->date)),
                    'school_id'           => $request->school_id,
                    'is_fully_paid'       => 1,
                    'student_fees_id'             => $request->student_fees_id,
                    'mode'         => $request->mode,
                    'cheque_no'    => $request->mode == 2 ? $request->cheque_no : null,
                    'amount' => (double)number_format(str_replace(',', '', $student->total_compulsory_fees), 2, '.', ''),
                    'due_charges'  => $dueCharges ?? null,
                    'status' => 1,
                    'remark'         => $request-> remark ?? null,
                ]);
                
                // Check if this is a package purchase
                $feesDetails = DB::table('student_fees_details')
                    ->where('student_fees_id', $request->student_fees_id)
                    ->first();
                
                if ($feesDetails && strpos($fees->name, 'Package Purchase') === 0) {
                    // Extract package name from fees_type_name
                    $packageName = $feesDetails->fees_type_name;
                    
                    // Get package details
                    $package = DB::table('subject_package')
                        ->where('name', $packageName)
                        ->first();
                    
                    if ($package) {
                        // Create purchase_package record
                        $purchasePackageId = DB::table('purchase_package')->insertGetId([
                            'package_id' => $package->id,
                            'student_id' => $request->student_id,
                            'date' => date('Y-m-d', strtotime($request->date)),
                            'total_sessions' => $package->total_sessions,
                            'school_id' => $request->school_id,
                            'created_at' => Carbon::now(),
                            'updated_at' => Carbon::now()
                        ]);
                        
                        // Create package_usage record
                        DB::table('package_usage')->insert([
                            'purchase_package_id' => $purchasePackageId,
                            'student_id' => $request->student_id,
                            'remaining_session' => $package->total_sessions,
                            'school_id' => $request->school_id,
                            'created_at' => Carbon::now(),
                            'updated_at' => Carbon::now()
                        ]);
                    }
                }
            } else {
                $feesPaid->date = date('Y-m-d', strtotime($request->date));
                $feesPaid->mode = $request->mode;
                $feesPaid->cheque_no = ($request->mode == 2 ? $request->cheque_no : null);
                $feesPaid->remark = ($request->remark ?? null);
                $feesPaid->save();
            }

            $students = DB::table('students')
                ->join('users', 'students.user_id', '=', 'users.id')
                ->whereIn('students.id', collect($request->student_id))
                ->whereNull('users.deleted_at')
                ->get(['students.user_id', 'students.guardian_id', 'users.first_name', 'users.last_name']);
            foreach ($students as $student) {
                $notifyUser = [];
                $studentName = $student->first_name . ' ' . $student->last_name;
                $notifyUser[] = $student->user_id;

                if ($student->guardian_id) {
                    $notifyUser[] = $student->guardian_id;
                    if ($student->guardian_id) {
                        $notifyUser[] = $student->guardian_id;
                    }
                    $title = 'Payment Receipt';
                    $body = 'We just received your payment. Please find your receipt no:'.('R'.sprintf('%08d', $fees->uid ?? '')).' in the app.';
                    $type = "Payment";
                    send_notification($notifyUser, $title, $body, $type);
                }
            }

            DB::commit();
            ResponseService::successResponse("Data Updated Successfully");
        } catch (Throwable $e) {
            DB::rollback();
            ResponseService::logErrorResponse($e, 'StudentFeesController -> compulsoryFeesPaidStore method ');
            ResponseService::errorResponse();
        }
    }

    public function setStudentFeeStatus(Request $request)
    {
        $validator = Validator::make($request->all(), [
            // 'class_id' => '',
            // 'session_year_id' => 'required',
            // 'student_fees_id' => 'required',
            'status' => 'required',
            'student_id' => ''
        ]);
        if ($validator->fails()) {
            return response()->json([
                'error'   => false,
                'message' => "Parameter Error"
            ]);
        }
        $parentIds = array();
        $studentFeeIds = $request->has('id') ? [$request->id] : $request->student_fees_id;
        $schoolId = Auth::user()->school_id;
        // if ($request->class_id && $request->student_id && $request->status == 'published') {
        //     foreach ($request->student_id as $student_id) {
        //         $studentFeeExists = DB::table('student_fees')->where('status', 'draft')->where('student_id', $student_id)->whereNull('deleted_at')->whereNull('uid')->orderBy('created_at_draft', 'asc')->get();
        //         if ($studentFeeExists) {
        //             foreach($studentFeeExists as $stdFee){
        //                 $studentFeeIds[] = $stdFee->id;
        //                 $getIds = DB::select("SELECT guardian_id FROM students WHERE id =" . $student_id);
        //                 $parentIds[] = $getIds[0]->guardian_id;
        //             }
        //         }
        //     }
        // } elseif (empty($request->student_id) && empty($request->id) && $request->status == 'published') {
        //     $data = DB::table('student_fees')
        //         ->join('students', 'student_fees.student_id', '=', 'students.id')
        //         ->where('student_fees.status', 'draft')
        //         ->where('student_fees.school_id', $schoolId)
        //         ->whereNull('student_fees.deleted_at')
        //         ->whereNull('student_fees.uid')
        //         ->orderBy('student_fees.created_at_draft', 'asc')
        //         ->select('students.guardian_id', 'student_fees.id as student_fee_id')
        //         ->get();

        //     foreach ($data as $row) {
        //         $parentIds[] = $row->guardian_id;
        //         $studentFeeIds[] = $row->student_fee_id;
        //     }
        // } else
        
        // if (isset($studentFeeIds) && $request->status == 'published') {
        //     $studentFeeExists = DB::table('student_fees')->where('status', 'draft')->wherein('id', $studentFeeIds)->whereNull('deleted_at')->whereNull('uid')->get();
        //     if (isset($studentFeeExists) && COUNT($studentFeeExists) > 0) {

        //         $studentFeeIds[] = $studentFeeExists->id;
        //         $getIds = DB::select("SELECT guardian_id FROM students WHERE id =" . $studentFeeExists->student_id);
        //         $parentIds[] = $getIds[0]->guardian_id;
        //     }
        // }

        $latestUID = DB::table('student_fees')->where('school_id', Auth::user()->school_id)->where('status', 'published')->whereNull('deleted_at')->select('uid')->orderBy('uid', 'desc')->value('uid');
        $uid = $latestUID ? $latestUID + 1 : 1;


        //if($request->status == 'draft'){
            $paidFeesExist = DB::table('student_fees')
            ->join('student_fees_paids', 'student_fees.id', '=', 'student_fees_paids.student_fees_id')
            // ->where('student_fees.session_year_id', $request->session_year_id)
            // ->when($request->class_id, function ($query) use ($request) {
            //     $query->whereIn('student_fees.class_id', $request->class_id);
            // })
            ->when($request->student_fees_id, function ($query) use ($request) {
                $query->whereIn('student_fees.id', $request->student_fees_id);
            })
            ->when($request->id, function ($query) use ($request) {
                $query->where('student_fees.id', $request->id);
            })
            // ->when($request->student_id, function ($query) use ($request) {
            //     $query->whereIn('student_fees.student_id', $request->student_id);
            // })
            ->exists();

            // If trying to change to draft and paid fees exist, prevent the update
            if ($request->status === 'draft' && $paidFeesExist) {
                return response()->json([
                    'error' => true,
                    'message' => 'Cannot change status to draft for fees that have already been paid'
                ]);
            }

            $consolidatedFeeExist = DB::table('student_fees_consolidate')
            ->join('student_fees', 'student_fees.uid', '=', 'student_fees_consolidate.reference_uid')
            // ->where('student_fees.session_year_id', $request->session_year_id)
            // ->when($request->class_id, function ($query) use ($request) {
            //     $query->whereIn('student_fees.class_id', $request->class_id);
            // })
            ->when($request->student_fees_id, function ($query) use ($request) {
                $query->whereIn('student_fees.id', $request->student_fees_id);
            })
            ->when($request->id, function ($query) use ($request) {
                $query->where('student_fees.id', $request->id);
            })
            // ->when($request->student_id, function ($query) use ($request) {
            //     $query->whereIn('student_fees.student_id', $request->student_id);
            // })
            ->exists();

            if($consolidatedFeeExist){
                return response()->json([
                    'error' => true,
                    'message' => 'Cannot change status for Consolidated Fees'
                ]);
            }

            // If trying to change to draft and paid fees exist, prevent the update
            if ($request->status === 'draft' && $paidFeesExist) {
                return response()->json([
                    'error' => true,
                    'message' => 'Cannot change status to draft for fees that have already been paid'
                ]);
            }

            // dd($paidFeesExist);
            if($request->status == "published"){
                foreach ($studentFeeIds as $studentFeeId) {
                    $studentFeeData = DB::table('student_fees')->where('id',$studentFeeId)->first();
                    if(isset($studentFeeData) && isset($studentFeeData->uid)){
                        continue;
                    }
                    while (DB::table('student_fees')
                        ->where('uid', $uid)
                        ->where('school_id', Auth::user()->school_id)
                        ->where('status', 'published')
                        ->exists()
                    ) {
                        $uid++;
                    }
                    DB::table('student_fees')->where('id', $studentFeeId)->update(["uid" => $uid,"created_at" => now()]);
                    $uid++;



                    if($studentFeeData){
                        $userId = DB::table('students')
                        ->where('id', $studentFeeData->student_id)
                        ->value('user_id');

                        if ($userId !== null){
                            $guardianId = DB::table('students')
                                ->where('user_id', $userId)
                                ->value('guardian_id');
                                
                            $notifyUser = [$userId, $guardianId];
                            $title = 'New Invoice Available';
                            $body = 'A new Invoice has been generated';
                            $type = 'Notification';
                            send_notification($notifyUser, $title, $body, $type);
                        }
                    }
                }
            }

            // foreach ($studentFeeIds as $studentFeeId) {
            //     while (DB::table('student_fees')
            //         ->where('uid', $uid)
            //         ->where('school_id', Auth::user()->school_id)
            //         ->where('status', 'published')
            //         ->exists()
            //     ) {
            //         $uid++;
            //     }
            //     DB::table('student_fees')->where('id', $studentFeeId)->update(["uid" => $uid,"created_at" => now()]);
            //     $uid++;
            // }

            $this->studentFees->builder()
                // ->where('session_year_id', $request->session_year_id)
                // ->when($request->class_id, function ($query) use ($request) {
                //     $query->whereIn('class_id', $request->class_id);
                // })
                ->when($request->student_fees_id, function ($query) use ($request) {
                    $query->wherein('id', $request->student_fees_id);
                })
                ->when($request->id, function ($query) use ($request) {
                    $query->where('id', $request->id);
                })
                // ->when($request->student_id, function ($query) use ($request) {
                //     $query->whereIn('student_id', $request->student_id);
                // })
                ->whereNotExists(function ($query) {
                    $query->select(DB::raw(1))
                        ->from('student_fees_paids')
                        ->whereRaw('student_fees_paids.student_fees_id = student_fees.id');
                })
                ->update(['status' => $request->status]);
        //}


        // $this->studentFees->builder()
        //     ->where("session_year_id", $request->session_year_id)
        //     ->when($request->class_id, function ($query) use ($request) {
        //         $query->whereIn("class_id", $request->class_id);
        //     })
        //     ->when($request->id, function ($query) use ($request) {
        //         $query->where("id", $request->id);
        //     })
        //     ->when($request->student_id, function ($query) use ($request) {
        //         $query->whereIn("student_id", $request->student_id);
        //     })->update(["status" => $request->status]);

        return response()->json([
            'error'   => false,
            'message' => "Status Set Successful"
        ]);
    }

    public function setupUID()
    {
        DB::table('student_fees')->update(['uid' => null]);

        $schools = DB::table('schools')->whereNull('deleted_at')->get();

        foreach ($schools as $school) {
            $studentFees = DB::table('student_fees')->where('school_id', $school->id)->whereNull('deleted_at')->where('status', 'published')->orderBy('id', 'asc')->get();
            $uid = 1;
            foreach ($studentFees as $studentFee) {
                DB::table('student_fees')
                    ->where('id', $studentFee->id)
                    ->update(['uid' => $uid]);
                $uid++;
            }
        }
        return response()->json(['message' => 'UID setup completed successfully']);
    }

    public function setStudentFeePendingStatus(Request $request)
    {
        // Validate the incoming request data
        $validator = Validator::make($request->all(), [
            'pending_status' => 'required|integer|in:1,2,3', 
            'student_fee_id' => 'required|exists:student_fees,id', 
        ]);

        // Check if validation fails
        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 400);
        }

        try {
            DB::beginTransaction();
            // Retrieve the validated data
            $data = $validator->validated();
            $studentFees = DB::table("student_fees as sf")
            ->join('student_fees_paids as sfp','sfp.student_fees_id','=','sf.id')
            ->select('sf.student_id','sfp.amount', 'sf.id as fees_id')
            ->where("sf.id",$data['student_fee_id'])
            ->first();
            
            if($data['pending_status'] == "1"){
                $updated = DB::table('student_fees_paids')
                    ->where('student_fees_id', $data['student_fee_id']) 
                    ->update(['status' => $data['pending_status']]);
                if ($updated) {
                    // Check if this is a package purchase
                    $feesDetails = DB::table('student_fees_details')
                        ->where('student_fees_id', $data['student_fee_id'])
                        ->first();
                    
                    $fees = DB::table('student_fees')->where('id', $data['student_fee_id'])->first();
                    
                    if ($feesDetails && $fees && strpos($fees->name, 'Package Purchase') === 0) {
                        // Extract package name from fees_type_name
                        $packageName = $feesDetails->fees_type_name;
                        
                        // Get package details
                        $package = DB::table('subject_package')
                            ->where('name', $packageName)
                            ->first();
                        
                        if ($package) {
                            // Create purchase_package record
                            $purchasePackageId = DB::table('purchase_package')->insertGetId([
                                'package_id' => $package->id,
                                'student_id' => $studentFees->student_id,
                                'date' => date('Y-m-d'),
                                'total_sessions' => $package->total_sessions,
                                'school_id' => Auth::user()->school_id,
                                'created_at' => Carbon::now(),
                                'updated_at' => Carbon::now()
                            ]);
                            
                            // Create package_usage record
                            DB::table('package_usage')->insert([
                                'purchase_package_id' => $purchasePackageId,
                                'student_id' => $studentFees->student_id,
                                'remaining_session' => $package->total_sessions,
                                'school_id' => Auth::user()->school_id,
                                'created_at' => Carbon::now(),
                                'updated_at' => Carbon::now()
                            ]);
                        }
                    }
                    
                    $title = 'Bank Transfer Successful';
                    $body = "Your bank transfer of RM " . $studentFees->amount . " has been successfully approved." ;
                }
            } else if ($data['pending_status'] == "3"){
                $studentFeePaid = DB::table('student_fees_paids')->where('student_fees_id',$data['student_fee_id'])->first();
                $data = [
                    'student_fees_id'   => $data['student_fee_id'],
                    'school_id'         => Auth::user()->school_id,
                    'mode'              => $studentFeePaid->mode,
                    'amount'            => $studentFeePaid->amount,
                    'due_charges'       => $studentFeePaid->due_charges,
                    'payment_date'      => $studentFeePaid->date,
                    'payment_detail'    => $studentFeePaid->payment_detail ?? '',
                    'remark'            => $request->remark
                ];
                $result = DB::table('student_fees_rejected')->insert($data);
                if($result) {
                    DB::table('student_fees_paids')->where('id',$studentFeePaid->id)->delete();
                    $title = 'Payment Rejected';
                    $body = "Your offline payment was rejected after review. Please resubmit with the correct details. Thank You" ;
                }
            }

            if($studentFees){
                $userId = DB::table('students')
                ->where('id', $studentFees->student_id)
                ->value('user_id');

                if ($userId !== null){
                    $guardianId = DB::table('students')
                    ->where('user_id', $userId)
                    ->value('guardian_id');
                    
                    $notifyUser = [$userId,$guardianId];
                    $type = "Bank Transfer";
                    send_notification($notifyUser, $title, $body, $type);
                    DB::commit();
                    ResponseService::successResponse('Status updated successfully!');
                }
            }
        } catch (\Throwable $th) {
            DB::rollback();
            ResponseService::errorResponse();
        }
    }
    
    public function feePaidDateEdit(Request $request)
    {
        $student_fees_id = $request->id;

        // Fetch invoice number and format it
        $invNo = DB::table('student_fees')->where('id', $student_fees_id)->value('uid');
        $formattedInvNo = str_pad($invNo, 9, "0", STR_PAD_LEFT);

        // Fetch the current date and remark for the fee payment
        $date = DB::table('student_fees_paids')->where('student_fees_id', $student_fees_id)->value('date');
        $remark = DB::table('student_fees_paids')->where('student_fees_id', $student_fees_id)->value('remark');

        return view('student-fees.fees_paid_date_edit', compact('student_fees_id', 'formattedInvNo', 'date', 'remark'));
    }

    public function feePaidDateUpdate(Request $request)
    {
        // remark
        $validator = Validator::make($request->all(), [
            'date' => 'required|date_format:d-m-Y', 
            'remark' => 'nullable|string|max:255', 
        ]);

        if ($validator->fails()) {
            // Return with previous data to retain the user input, including remarks
            return response()->json([
                'error' => $validator->errors(),
                'previousData' => $request->only(['date', 'remark'])
            ], 400);
        }

        $date = $request->input('date');
        $remark = $request->input('remark', null); // Fetch remark, default to null if not provided
        $formattedDate = Carbon::createFromFormat('d-m-Y', $date)->format('Y-m-d'); // Convert date to Y-m-d format

        try {
            // Begin database transaction
            DB::beginTransaction();

            $updated = DB::table('student_fees_paids')
                ->where('student_fees_id', $request->input('student_fees_id'))
                ->update([
                    'date' => $formattedDate,
                    'remark' => $remark, 
                    'updated_at' => now(),
                ]);

            DB::commit();

            // Redirect on success
            ResponseService::successRedirectResponse(route('student-fees.paid.index'), 'Data Update Successfully');
        } catch (\Throwable $th) {
            DB::rollback();

            // Return with previous data to retain the user input on failure
            return response()->json([
                'error' => 'An error occurred while updating data.',
                'previousData' => $request->only(['date', 'remark'])
            ], 500);
        }
    }


    // public function submitEInvoicing($id){
    //     $studentFeesSubmitted[] = DB::table('student_fees_einvoice')->where('student_fees_id',$id)->where('status',1)->first();
    //     ///Check Consolidated Student Fees
    //     $consolidates = DB::table('student_fees_consolidate')
    //         ->join('student_fees','student_fees.uid','=','student_fees_consolidate.reference_uid')
    //         ->where('student_fees.id',$id)
    //         ->get();
    //     if ($consolidates->isEmpty()) {
    //         $studentFees = $this->studentFees->builder()->where('id',$id)->first();
    //     } else {
    //         $consolidatedStudentFees =  $consolidates;
    //     }
    //     ///return error if one of the fees already submitted before
    //     foreach($studentFeesSubmitted as $studentFeeSubmitted){
    //         if($studentFeeSubmitted){
    //                 $inv_number =  $this->studentFees->builder()->where('id',$id)->select('uid')->first();
    //                 $inv_number = str_pad($inv_number, 8, '0', STR_PAD_LEFT);
    //                 ResponseService::errorResponse("E-Invoice already submitted and validated for INV".$inv_number);
    //         }
    //     }

    //     if($studentFeeSubmitted){
    //         $inv_number =  $this->studentFees->builder()->where('id',$id)->select('uid')->first();
    //         $inv_number = str_pad($inv_number, 8, '0', STR_PAD_LEFT);
    //         ResponseService::errorResponse("E-Invoice already submitted and validated for INV".$inv_number);
    //     }

    //     $studentFees = $this->studentFees->builder()->where('id',$id)->first();
    //     $studentFeeSubmitted = DB::table('student_fees_einvoice')->where('student_fees_id',$id)->where('status',1)->first();
    //     if($studentFeeSubmitted){
    //         ResponseService::errorResponse("E-Invoice already submitted and validated");
    //     }
    //     $student = $this->student->builder()->where('id',$studentFees->student_id)->first();
    //     $e_invoice = DB::table('e_invoice')->where('school_id',Auth::user()->school_id)->first();
    //     if(!$e_invoice || $e_invoice->status != 1){
    //         ResponseService::errorResponse("School's TIN number not verified");
    //     } else {
    //         if(isset($consolidatedStudentFees)){
    //             // dd($consolidateStudents);
    //             // dd($consolidates[0]);
    //             $e_invoice->tax_identification_number = "EI00000000010";

    //             // Only pass the first fees 
    //             // use the passed first fees to find others in formatEInvoice
    //             $data = [
    //                 'id'                    => $consolidatedStudentFees[0]->id,
    //                 'student_fees'          => $consolidatedStudentFees[0],
    //                 'e_invoice'             => $e_invoice
    //             ];
               
    //             $documents = $this->eInvoiceFormatService->formatEInvoice($data,'01');
    //         }

    //         if(isset($student)){
    //             $e_invoice_guardian = DB::table('e_invoice_guardian')->where('guardian_id',$student->guardian_id)->where('school_id',Auth::user()->school_id)->first();
    //             if($e_invoice_guardian && $e_invoice_guardian->status != 1){
    //                     ResponseService::errorResponse("TIN number not verified");
    //             } else {
    //                 $data = [
    //                     'id'                    => $id,
    //                     'student'               => $student,
    //                     'e_invoice_guardian'    => $e_invoice_guardian,
    //                     'student_fees'          => $studentFees,
    //                     'e_invoice'             => $e_invoice
    //                 ];
    //                 $documents = $this->eInvoiceFormatService->formatEInvoice($data,'01');
    //             }
    //         }
            
    //             $schoolSettings = $this->cache->getSchoolSettings();
    //             if($schoolSettings) {
    //                 $accessToken = '';
    //                 $clientId = '';
    //                 $client_secret = array();
    //                 if($schoolSettings['client_id']) {
    //                     $clientId = $schoolSettings['client_id'];
    //                 }
    //                 if($schoolSettings['client_secret_1']){
    //                     $client_secret[] = $schoolSettings['client_secret_1'];
    //                 }
    //                 if($schoolSettings['client_secret_2']){
    //                     $client_secret[] = $schoolSettings['client_secret_2'];
    //                 }
    //                 if(!empty($clientId) && count($client_secret) > 0) {
    //                     if(isset($consolidatedStudentFees)){
    //                         $accessTokenExist = DB::table('access_token_einvoice')->where('consolidate_id',$consolidatedStudentFees[0]->id)->orderBy('id','desc')->first();
    //                         if($accessTokenExist && $accessTokenExist->time_expired > now()){
    //                             $accessToken = $accessTokenExist->access_token; 
    //                         } else {
    //                             foreach ($client_secret as $secret){
    //                                 $url = 'https://api.myinvois.hasil.gov.my/connect/token';
    //                                 // dd($e_invoice->tax_identification_number);
    //                                 $headers = ['onbehalfof:'.$e_invoice->tax_identification_number];
    //                                 $fields = [
    //                                     'client_id' => $clientId,
    //                                     'client_secret' => $secret,
    //                                     'grant_type'    => 'client_credentials',
    //                                     'scope'         => 'InvoicingAPI'
    //                                 ];
    //                                 $encodedFields = http_build_query($fields);
    //                                 $ch = curl_init();
    //                                 curl_setopt($ch, CURLOPT_URL, $url);
    //                                 curl_setopt($ch, CURLOPT_POST, true);
    //                                 curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    //                                 curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    //                                 curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    //                                 curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
    //                                 curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    //                                 curl_setopt($ch,CURLOPT_POSTFIELDS, $encodedFields);
    //                                 $result = curl_exec($ch);
    //                                 if($result == false){
    //                                     ResponseService::errorResponse("Unable to connect to submit e-invoice");
    //                                     die('Curl failed: ' . curl_error($ch));
    //                                 }else {
    //                                     $data = json_decode($result);
    //                                     if(!isset($data->error)){
    //                                         if(!empty($data->access_token)){
    //                                             $accessToken = $data->access_token;
    //                                             DB::table('access_token_einvoice')->insert([
    //                                                 'access_token' => $accessToken,
    //                                                 'consolidate_id' => $consolidatedStudentFees[0]->id,
    //                                             ]);
    //                                             break;
    //                                         }
    //                                     }
    //                                 }
    //                                 curl_close($ch);
    //                             }
    //                         }  
    //                     }else{
    //                         $accessTokenExist = DB::table('access_token_einvoice')->where('guardian_id',$e_invoice_guardian->guardian_id)->orderBy('id','desc')->first();
    //                     if($accessTokenExist && $accessTokenExist->time_expired > now()){
    //                         $accessToken = $accessTokenExist->access_token; 
    //                     } else {
    //                             foreach ($client_secret as $secret){
    //                                 $url = 'https://api.myinvois.hasil.gov.my/connect/token';
    //                                 // dd($e_invoice->tax_identification_number);
    //                                 $headers = ['onbehalfof:'.$e_invoice_guardian->tax_identification_number];
    //                                 $fields = [
    //                                     'client_id' => $clientId,
    //                                     'client_secret' => $secret,
    //                                     'grant_type'    => 'client_credentials',
    //                                     'scope'         => 'InvoicingAPI'
    //                                 ];
    //                                 $encodedFields = http_build_query($fields);
    //                                 $ch = curl_init();
    //                                 curl_setopt($ch, CURLOPT_URL, $url);
    //                                 curl_setopt($ch, CURLOPT_POST, true);
    //                                 curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    //                                 curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    //                                 curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    //                                 curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
    //                                 curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    //                                 curl_setopt($ch,CURLOPT_POSTFIELDS, $encodedFields);
    //                                 $result = curl_exec($ch);
    //                                 if($result == false){
    //                                     ResponseService::errorResponse("Unable to connect to submit e-invoice");
    //                                     die('Curl failed: ' . curl_error($ch));
    //                                 }else {
    //                                     $data = json_decode($result);
    //                                     if(!isset($data->error)){
    //                                         if(!empty($data->access_token)){
    //                                             $accessToken = $data->access_token;
    //                                             DB::table('access_token_einvoice')->insert([
    //                                                 'access_token' => $accessToken,
    //                                                 'guardian_id' => $e_invoice_guardian->guardian_id,
    //                                             ]);
    //                                             break;
    //                                         }
    //                                     }
    //                                 }
    //                                 curl_close($ch);
    //                             }
    //                         }
    //                     }
                        
                        
    //                     // dd($accessToken);
    //                     if($accessToken){
    //                         // dd($documents);
    //                         $url = 'https://api.myinvois.hasil.gov.my/api/v1.0/documentsubmissions';
    //                         $headers = [
    //                             'authorization: Bearer '.$accessToken,
    //                             'Content-Type:application/json',
    //                         ];
    //                         $data = [
    //                             'documents' => $documents
    //                         ];
    //                         $ch = curl_init();
    //                         curl_setopt($ch, CURLOPT_URL, $url);
    //                         curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    //                         curl_setopt($ch, CURLOPT_POST, true);
    //                         curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    //                         curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    //                         curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
    //                         curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    //                         curl_setopt($ch,CURLOPT_POSTFIELDS, json_encode($data));
    //                         $result = curl_exec($ch);
    //                         $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    //                         // dd($result);
    //                         curl_close($ch);
    //                         $resultData = json_decode($result);
    //                         if($httpCode == 422){
    //                             ResponseService::errorResponse($resultData->error);
    //                         } else {
    //                             if($httpCode == 202){
    //                                 $data = [
    //                                     'student_fees_id' => $id,
    //                                     'submission_uid' => $resultData->submissionUid,
    //                                     'rejected_documents' => !empty($resultData->rejectedDocuments) ? json_encode($resultData->rejectedDocuments) : null,
    //                                 ];
    //                                 if(count($resultData->acceptedDocuments) > 0){
    //                                     foreach($resultData->acceptedDocuments as $document){
    //                                         if($document->uuid){
    //                                             $data['uuid'] = $document->uuid;
    //                                         }
    //                                         if($document->invoiceCodeNumber){
    //                                             $data['invoice_code_number'] = $document->invoiceCodeNumber;
    //                                         }
    //                                     }
    //                                 }

    //                                 if(count($resultData->rejectedDocuments) > 0){
    //                                     foreach($resultData->rejectedDocuments as $document){
    //                                         if(isset($document->error->details[0])){
    //                                             ResponseService::errorResponse($document->error->details[0]->message);
    //                                         }
    //                                     }
    //                                 }
    //                                 DB::table('student_fees_einvoice')->insert($data);
    //                                 // dd($resultData);
    //                                 ResponseService::successResponse("E-Invoice submitted successfully");
    //                             } else {
    //                                 // dd($resultData->error->details[0]->message);
    //                                 if(isset($resultData->error)){
    //                                     ResponseService::errorResponse($resultData->error->details[0]->message);
    //                                 }
    //                                 ResponseService::errorResponse($resultData->message);
    //                             }
    //                         } 
    //                     }
    //                 }
    //             }
    //         }
    // }   

    
    public function submitEInvoicing($id) {
        // Check if e-invoice already submitted
        $studentFeesSubmitted = DB::table('student_fees_einvoice')->where('student_fees_id',$id)->where('status',1)->first();
        if($studentFeesSubmitted) {
            $inv_number = $this->studentFees->builder()->where('id',$id)->select('uid')->first();
            $inv_number = str_pad($inv_number, 8, '0', STR_PAD_LEFT);
            ResponseService::errorResponse("E-Invoice already submitted and validated for INV".$inv_number);
        }
    
    
        // Get student fees data
        $studentFees = $this->studentFees->builder()->where('id',$id)->first();
        $studentFeesDetails = $this->studentFeesDetail->builder()->where('student_fees_id',$id)->get();
    
        $invoiceId = $studentFees->invoice_number ?? 'INV' . sprintf('%09d', $studentFees->uid ?? '');
       
        // Calculate monetary totals with rounding
        $lineExtensionAmount = 0;
        $taxExclusiveAmount = 0;
        $taxInclusiveAmount = 0;
        $totalTaxAmount = 0;

        $earlyoffer = 0;
        $dueCharges = 0;
                
        // Get student fees data for early discount or due charges
        $studentFeesData = DB::table('student_fees')->where('id', $id)->first();
        $feesPaid = DB::table('student_fees_paids')->where('student_fees_id', $id)->first();
                
        $totalCompulsoryFees = DB::table('student_fees_details')
            ->where('student_fees_id', $id)
            ->whereNotIn('fees_type_name', ['Overdue Fees', 'Early Discount'])
            ->sum(DB::raw('(fees_type_amount - (fees_type_amount * COALESCE(discount, 0) / 100) + ((fees_type_amount - (fees_type_amount * COALESCE(discount, 0) / 100)) * COALESCE(tax, 0) / 100)) * quantity'));
                
        // Apply tax if applicable
        if(isset($studentFeesData->tax_percentage) && $studentFeesData->tax_percentage > 0){
            $extraTax = ($totalCompulsoryFees * ($studentFeesData->tax_percentage / 100));
            $totalCompulsoryFees += $extraTax;
        }
                
        // Calculate due charges if applicable
        $currentDate = date('Y-m-d');
        $dueFee = DB::select('SELECT due_date FROM student_fees WHERE id= ? AND due_date < ?', [$id, $currentDate]);
                
        if (!empty($dueFee)) {
            if (!$studentFeesData->due_charges) {
                $dueCharges = $studentFeesData->due_charges_amount;
            } else {
                $dueCharges = ($totalCompulsoryFees * ($studentFeesData->due_charges / 100));
            }
        }
                
        // Calculate early discount if applicable
        $earlyFee = DB::select('SELECT early_date FROM student_fees WHERE id= ? AND early_date >= ?', [$id, $currentDate]);
                
        if (!empty($earlyFee)) {
            if (!$studentFeesData->early_offer) {
                $earlyoffer = -$studentFeesData->early_offer_amount;
            } else {
                $earlyoffer = -($totalCompulsoryFees * ($studentFeesData->early_offer / 100));
            }
        }
                
        $allowanceTotalAmount = round(abs($earlyoffer ?? 0), 2);
        $chargeTotalAmount = round(abs($dueCharges ?? 0), 2);

    
        foreach ($studentFeesDetails as $detail) {
            if (in_array($detail->fees_type_name, ['Early Discount', 'Overdue Fees'])) {
                $detail->fees_type_amount = 0;
            }
            
            $priceExtensionAmount = round(
                ($detail->fees_type_amount * $detail->quantity) - 
                (($detail->fees_type_amount * $detail->quantity) * ($detail->discount / 100)),
                2
            );
            
            $taxAmount = round($priceExtensionAmount * ($detail->tax / 100), 2);
    
            $lineExtensionAmount += $priceExtensionAmount;
            $taxExclusiveAmount += $priceExtensionAmount;
            $taxInclusiveAmount += ($priceExtensionAmount + $taxAmount);
            $totalTaxAmount += $taxAmount;
        }
    
        // Apply final rounding to all amounts
        $studentFees->line_extension_amount = round($lineExtensionAmount, 2);
        $studentFees->tax_exclusive_amount = round($taxExclusiveAmount, 2);
        $studentFees->tax_inclusive_amount = round($taxInclusiveAmount, 2);
        $studentFees->allowance_total_amount = $allowanceTotalAmount;
        $studentFees->charge_total_amount = $chargeTotalAmount;
        $studentFees->payable_rounding_amount = 0;
        $studentFees->payable_amount = round(
            $studentFees->total_amount ?? ($taxInclusiveAmount - $allowanceTotalAmount + $chargeTotalAmount),
            2
        );
        $studentFees->total_tax_amount = round($totalTaxAmount, 2);
        $studentFees->total_taxable_amount = round($taxExclusiveAmount, 2);
        $studentFees->total_tax_percent = ($taxExclusiveAmount > 0) 
            ? round(($totalTaxAmount / $taxExclusiveAmount) * 100, 2) 
            : 0;
    
        $student = $this->student->builder()->where('id',$studentFees->student_id)->first();
        $e_invoice = DB::table('e_invoice')->where('school_id',Auth::user()->school_id)->first();
        $school = DB::table('schools')->where('id',Auth::user()->school_id)->first();
    
        if(!$e_invoice || $e_invoice->status != 1) {
            ResponseService::errorResponse("School's TIN number not verified");
        }
    
        // Get guardian e-invoice data
        $e_invoice_guardian = DB::table('e_invoice_guardian')->where('guardian_id',$student->guardian_id)
            ->where('school_id',Auth::user()->school_id)->first();
        
        if($e_invoice_guardian && $e_invoice_guardian->status != 1) {
            ResponseService::errorResponse("TIN number not verified");
        }
    
        $supplierAddressLines = array_filter([
            trim(preg_replace('/\s+/', ' ', $school->address ?? '')),
            // $school->address2 ?? null,
            // $school->address3 ?? null
        ]);
    
        $customerAddressLines = array_filter([
            trim(preg_replace('/\s+/', ' ', $e_invoice_guardian->address ?? '')),
            // $e_invoice_guardian->address2 ?? null,
            // $e_invoice_guardian->address3 ?? null
        ]);
    
        // Prepare invoice data structure
        $invoiceData = [
            'invoice_type' => 'invoice',
            'id' => $invoiceId,
            'billing_references' => [
                'additional_document' => [
                    'id' => $studentFees->billing_reference_id ?? '',
                    'uuid' => $studentFees->billing_reference_uuid ?? ''
                ]
            ],
            'supplier' => [
                'address' => [
                    'city' => $e_invoice->city ?? '',
                    'postcode' => $e_invoice->postal_code ?? '',
                    'country_subentity_code' => $e_invoice->state ?? ''
                ],
                'address_line' => $supplierAddressLines,
                'country_code' => 'MYS',
                'legal_entity' => $school->name ?? '',
                'contact_phone' => $school->support_phone ?? '',
                'contact_email' => $school->support_email ?? '',
                'msic_code' => $e_invoice->company_msic_code ?? '',
                'party_identification' => [
                    'TIN' => $e_invoice->tax_identification_number ?? '',
                    ($e_invoice->id_type == 'NRIC' ? 'NRIC' : 
                     ($e_invoice->id_type == 'BRN' ? 'BRN' : 'ID')) 
                        => $e_invoice->registration_id_passport_number ?? ''
                ],
            ],
            'customer' => [
                'address' => [
                    'city' => $e_invoice_guardian->city ?? ($student->guardian->city ?? ''),
                    'postcode' => $e_invoice_guardian->postal_code ?? ($student->guardian->postcode ?? ''),
                    'country_subentity_code' => $e_invoice_guardian->state ?? ($student->guardian->state_code ?? '')
                ],
                'address_line' => $customerAddressLines,
                'country_code' => $e_invoice_guardian->country ?? ($student->guardian->country_code ?? 'MYS'),
                'legal_entity' => $e_invoice_guardian->name ?? ($student->guardian->name ?? ''),
                'contact_phone' => $e_invoice_guardian->contact_no ?? ($student->guardian->phone ?? ''),
                'contact_email' => $e_invoice_guardian->email ?? ($student->guardian->email ?? ''),
                'party_identification' => [
                    'TIN' => $e_invoice_guardian->tax_identification_number ?? '',
                    'NRIC' => $e_invoice_guardian->ic_no ?? ''
                ],
            ],
            'document_line' => $studentFeesDetails->map(function ($studentFeesDetail) use ($school) {
                $itemCode = DB::table('fees_type_master')
                    ->where('school_id', $school->id)
                    ->where('name', $studentFeesDetail->fees_type_name)
                    ->value('item_code');
    
                if (empty($itemCode)) {
                    $itemCode = DB::table('fees_type_master')
                        ->where('school_id', $school->id)
                        ->where('classification_code', $studentFeesDetail->classification_code)
                        ->value('item_code');
                }
    
                if (empty($itemCode)) {
                    return null;
                }
    
                $priceExtensionAmount = round(
                    ($studentFeesDetail->fees_type_amount * $studentFeesDetail->quantity) - 
                    (($studentFeesDetail->fees_type_amount * $studentFeesDetail->quantity) * ($studentFeesDetail->discount / 100)),
                    2
                );
                
                $priceExtensionAmountTrue = round($studentFeesDetail->quantity * $studentFeesDetail->fees_type_amount, 2);
                $taxTotalAmount = round($priceExtensionAmount * ($studentFeesDetail->tax / 100), 2);
    
                return [
                    'id' => $itemCode ?? '',
                    'quantity' => $studentFeesDetail->quantity ?? 1,
                    'unit' => !empty($studentFeesDetail->unit) ? explode(' - ', $studentFeesDetail->unit)[0] : '',
                    'line_amount' => $priceExtensionAmount ?? 0,
                    'item' => [
                        'description' => $studentFeesDetail->fees_type_name ?? '',
                        'classifications' => [
                            [
                                'code' => '010',
                                'type' => 'CLASS',
                            ]
                        ]
                    ],
                    'price' => [
                        'amount' => round($studentFeesDetail->fees_type_amount ?? 0, 2)
                    ],
                    'price_extension' => [
                        'amount' => $priceExtensionAmountTrue
                    ],
                    'tax_total' => [
                        'amount' => $taxTotalAmount
                    ],
                    'tax_sub_totals' => [
                        [
                            'taxable_amount' => $priceExtensionAmount,
                            'tax_amount' => $taxTotalAmount,
                            'percent' => (float) $studentFeesDetail->tax ?? 0,
                            'tax_scheme' => [
                                'id' => 'OTH'
                            ],
                            'tax_category' => [
                                'id' => '01',
                                'percent' => (float) $studentFeesDetail->tax ?? 0,
                                'tax_exemption_reason' => 'None'
                            ]
                        ]
                    ],

                    'allowance_charges' => array_filter([
                        $studentFeesDetail->discount > 0 ? [
                            'charge_indicator' => false,
                            'reason' => 'Discount',
                            'multiplier' => ($studentFeesDetail->discount/100),
                            'amount' => $studentFeesDetail->fees_type_amount * ($studentFeesDetail->discount / 100) * $studentFeesDetail->quantity,
                        ] : null,
                        // $studentFeesDetail->tax > 0 ? [
                        //     'charge_indicator' => true,
                        //     'reason' => 'Tax',
                        //     'multiplier' => ($studentFeesDetail->tax/100),
                        //     'amount' => $studentFeesDetail->fees_type_amount * ($studentFeesDetail->tax / 100)* $studentFeesDetail->quantity,
                        // ] : null,
                    ]),
                ];
            })->filter()->values()->toArray(),
    
            "legal_monetary_total"=> [
                'line_extension_amount' => $studentFees->line_extension_amount,
                'tax_exclusive_amount' => $studentFees->tax_exclusive_amount,
                'tax_inclusive_amount' => $studentFees->tax_inclusive_amount,
                'allowance_total_amount' => $studentFees->allowance_total_amount,
                'charge_total_amount' => $studentFees->charge_total_amount,
                'payable_rounding_amount' => $studentFees->payable_rounding_amount,
                'payable_amount' => $studentFees->payable_amount
            ],
    
            'allowance_charges' => array_filter([
                $studentFees->allowance_total_amount > 0 ? [
                    'charge_indicator' => false,
                    'reason' => 'Early payment discount',
                    'multiplier' =>round(($studentFees->allowance_total_amount)/$studentFees->tax_exclusive_amount,4),
                    'amount' => $studentFees->allowance_total_amount
                ] : null,
                $studentFees->charge_total_amount > 0 ? [
                    'charge_indicator' => true,
                    'reason' => 'Overdue Fees Charges',
                    'multiplier' => round(($studentFees->charge_total_amount)/$studentFees->tax_exclusive_amount, 4),
                    'amount' => $studentFees->charge_total_amount
                ] : null
            ]),
    
            $totalTaxAmount = $studentFees->tax_inclusive_amount * ($studentFees->tax_percentage / 100),
    
            'tax_total' => [
                'tax_amount' => $totalTaxAmount,
                'tax_sub_totals' => [
                    [
                        'taxable_amount' => $studentFees->tax_inclusive_amount,
                        'tax_amount' => $totalTaxAmount,
                        'percent' => $studentFees->tax_percentage,
                        'tax_category' => [
                            'id' => $studentFees->tax_type ?? '06',
                            'tax_exemption_reason' => 'None'
                        ],
                        'tax_scheme' => [
                            'id' => 'OTH'
                        ]
                    ]
                ]
            ]
        ];
    
        // Get school settings for API credentials
        $schoolSettings = $this->cache->getSchoolSettings();
        if(!$schoolSettings || !$schoolSettings['client_id'] || empty($schoolSettings['client_secret_1'])) {
            ResponseService::errorResponse("School API credentials not configured");
        }
    
        $schoolId = auth()->user()->school_id;
    
        // New submission logic
        $submitDocument = null;
        $invoice = null;
    
        try {
            $data = [
                'invoice_type' => 'invoice',
                'id' => $invoiceId,
                'supplier' => $invoiceData['supplier'], 
                'customer' => $invoiceData['customer'], 
                'document_line' => $invoiceData['document_line'], 
                'legal_monetary_total' => $invoiceData['legal_monetary_total'], 
                'allowance_charges' => $invoiceData['allowance_charges'], 
                'tax_total' => $invoiceData['tax_total'],
            ];
    
            $invoiceType = EInvoiceHelper::mapInvoiceTypeCode($data['invoice_type']);
            //\Log::info('Invoice Type:', [$invoiceType]);

            $invoice = EInvoiceHelper::createXmlDocument($invoiceType, $data);
            //\Log::info('Invoice:', [$invoice]);
    
            $documents = [];
            $document = MyInvoisHelper::getSubmitDocument($id, $invoice);
            $documents[] = $document;

            //This is how you extract the signatue value from the invoice xml
            $signatureValue = '';
            $dom = new DOMDocument();
            $dom->loadXML($invoice);
            $xpath = new DOMXPath($dom);
            $xpath->registerNamespace('ds', 'http://www.w3.org/2000/09/xmldsig#');
            $xpath->registerNamespace('cac', 'urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2');
            $xpath->registerNamespace('ext', 'urn:oasis:names:specification:ubl:schema:xsd:CommonExtensionComponents-2');
            $signatureValueNodes = $xpath->query('//ds:SignatureValue');
            if ($signatureValueNodes->length > 0) {
                $signatureValue = $signatureValueNodes->item(0)->nodeValue;
            }
            
            $submissionResult = EInvoiceHelper::submitDocument($schoolId, $documents);
            


            if ($submissionResult['success'] == true) {
                \Log::info('E-Invoice Submission Data: '. json_encode($data));
                //\Log::info('E-Invoice Submission Result: '. json_encode($submissionResult));
            }
    
        } catch (\Exception $exception) {
            $errorMessage = $exception->getMessage();
            $submitDocument = json_decode($errorMessage);
    
            if (json_last_error() !== JSON_ERROR_NONE) {
                $submitDocument = $errorMessage;
            }
    
            \Log::error('E-Invoice Submission Exception: ' . $errorMessage, ['exception' => $exception]);
            return ResponseService::errorResponse("An error occurred during e-invoice submission: " . $errorMessage);
        }
    
        if(isset($submissionResult['success']) && $submissionResult['success'] == true) {
            $resultData = $submissionResult['data'] ?? null;
    
            if ($resultData) {
                $dataToInsert = [
                    'student_fees_id' => $id,
                    'submission_uid' => null,
                    'rejected_documents' => null,
                    'uuid' => null,
                    'invoice_code_number' => null,
                ];
    
                if (is_object($resultData)) {
                    $dataToInsert['submission_uid'] = $resultData->submissionUid ?? null;
                    $dataToInsert['rejected_documents'] = !empty($resultData->rejectedDocuments) ? json_encode($resultData->rejectedDocuments) : null;
    
                    if(isset($resultData->acceptedDocuments) && is_array($resultData->acceptedDocuments) && count($resultData->acceptedDocuments) > 0) {
                        foreach($resultData->acceptedDocuments as $document) {
                            if(isset($document->uuid)) $dataToInsert['uuid'] = $document->uuid;
                            if(isset($document->invoiceCodeNumber)) $dataToInsert['invoice_code_number'] = $invoiceId;
                        }
                    }
                } elseif (is_array($resultData)) {
                    $dataToInsert['submission_uid'] = $resultData['submissionUid'] ?? null;
                    $dataToInsert['rejected_documents'] = !empty($resultData['rejectedDocuments']) ? json_encode($resultData['rejectedDocuments']) : null;
    
                    if(isset($resultData['acceptedDocuments']) && is_array($resultData['acceptedDocuments']) && count($resultData['acceptedDocuments']) > 0) {
                        foreach($resultData['acceptedDocuments'] as $document) {
                            if(isset($document['uuid'])) $dataToInsert['uuid'] = $document['uuid'];
                            if(isset($document['invoiceCodeNumber'])) $dataToInsert['invoice_code_number'] = $invoiceId;
                        }
                    }
                }
    
                try {
                    DB::table('student_fees_einvoice')->insert($dataToInsert);

                    DB::table('student_fees')->where('id', $id)->update(['sign_value' => $signatureValue]);
                    
                    // Call getEInvoiceStatus to immediately check and update the status
                    return $this->getEInvoiceStatus($id);
                    
                } catch (\Illuminate\Database\QueryException $ex) {
                    \Log::error('Database Insert Error: '. $ex->getMessage(), ['query' => $ex->getSql(), 'bindings' => $ex->getBindings()]);
                    return ResponseService::errorResponse("E-Invoice submitted, but failed to save e-invoice data to database. " . $ex->getMessage());
                }
    
            } else {
                \Log::warning('E-Invoice submission successful, but resultData is null or empty.');
                return ResponseService::errorResponse($submissionResult['message'] ?? "E-Invoice submission successful, but response data is missing or invalid.");
            }
        } else {
            $errorMessage = "Failed to submit e-invoice.";
            if (isset($submissionResult['message']) && !empty($submissionResult['message'])) {
                $errorMessage = $submissionResult['message'];
            }
            \Log::error('E-Invoice Submission Failed: ' . $errorMessage, $submissionResult);
            return ResponseService::errorResponse($errorMessage);
        } 
    }

    public function getEInvoiceStatus($id){
        try {
            $studentFeeEinvoice = DB::table('student_fees_einvoice')->where('student_fees_id', $id)->orderByDesc('id')->first();
            
            if (!$studentFeeEinvoice) {
                return ResponseService::errorResponse("No e-invoice submission found for this invoice");
            }
            
            // Check if submission_uid exists
            if (empty($studentFeeEinvoice->submission_uid)) {
                return ResponseService::errorResponse("No submission UID found for this e-invoice");
            }
            
            $studentFees = $this->studentFees->builder()->where('id', $id)->first();
            if (!$studentFees) {
                return ResponseService::errorResponse("Student fees record not found");
            }
            
            // Log the submission UID being used
            //\Log::info('Checking e-invoice status with submission UID: ' . $studentFeeEinvoice->submission_uid);
            
            // Make sure to pass the exact submission_uid from the database
            $result = EInvoiceHelper::getSubmission(Auth::user()->school_id, $studentFeeEinvoice->submission_uid);
            
            if ($result['success']) {
                $submissionData = $result['data'];
                $status = 0; // Default: pending
                
                // Log the submission data received
                //\Log::info('Submission data received: ' . json_encode($submissionData));
                
                if ($submissionData['overallStatus'] == 'Invalid') {
                    $status = 2; // Invalid
                } else if ($submissionData['overallStatus'] == 'Valid') {
                    $status = 1; // Valid
                }
                
                $updateData = [
                    'status' => $status,
                    'document_summary' => json_encode($submissionData['documentSummary'])
                ];
                
                if (!empty($submissionData['documentSummary'])) {
                    $documentSummary = $submissionData['documentSummary'][0];
                    if (isset($documentSummary['uuid'])) {
                        $updateData['uuid'] = $documentSummary['uuid'];
                    }
                    
                    if (isset($documentSummary['longId'])) {
                        $updateData['long_id'] = $documentSummary['longId'];
                    }
                    
                    if (isset($documentSummary['internalId'])) {
                        $updateData['internal_id'] = $documentSummary['internalId'];
                    }
                }
                
                if ($status == 2) {
                    $updateData['deleted_at'] = now();
                }
                
                DB::table('student_fees_einvoice')
                    ->where('id', $studentFeeEinvoice->id)
                    ->update($updateData);
                    
                return ResponseService::successResponse("E-Invoice status updated successfully", [
                    'status' => $status,
                    'status_text' => $submissionData['overallStatus']
                ]);
            } else {
                \Log::error('Error in e-invoice submission validation: ' . json_encode($result));
                return ResponseService::errorResponse("Failed to retrieve e-invoice status: " . ($result['message'] ?? "Unknown error"));
            }
        } catch (\Throwable $th) {
            \Log::error('Error in getting e-invoice status: ' . $th->getMessage(), [
                'exception' => $th,
                'stack_trace' => $th->getTraceAsString(),
            ]);
            return ResponseService::errorResponse("An error occurred while retrieving e-invoice status");
        }
    }

       
    // public function cancelEInvoicing($id,Request $request){
    //     $studentFeeEinvoice = DB::table('student_fees_einvoice')->where('student_fees_id',$id)->where('status',1)->first();
    //     $studentFees = $this->studentFees->builder()->where('id',$id)->first();
    //     $student = $this->student->builder()->where('id',$studentFees->student_id)->first();
    //     $e_invoice_guardian = DB::table('e_invoice_guardian')->where('guardian_id',$student->guardian_id)->where('school_id',Auth::user()->school_id)->first();

    //     $schoolSettings = $this->cache->getSchoolSettings();
    //     if($schoolSettings) {
    //         $accessToken = '';
    //         $clientId = '';
    //         $client_secret = array();
    //         if($schoolSettings['client_id']) {
    //             $clientId = $schoolSettings['client_id'];
    //         }
    //         if($schoolSettings['client_secret_1']){
    //             $client_secret[] = $schoolSettings['client_secret_1'];
    //         }
    //         if($schoolSettings['client_secret_2']){
    //             $client_secret[] = $schoolSettings['client_secret_2'];
    //         }
    //         if(!empty($clientId) && count($client_secret) > 0) {
    //             $accessTokenExist = DB::table('access_token_einvoice')->where('guardian_id',$e_invoice_guardian->guardian_id)->orderBy('id','desc')->first();
    //             if($accessTokenExist && $accessTokenExist->time_expired > now()){
    //                 $accessToken = $accessTokenExist->access_token; 
    //             } else {
    //                 foreach ($client_secret as $secret){
    //                     $url = 'https://api.myinvois.hasil.gov.my/connect/token';
    //                     $headers = ['onbehalfof:'.$e_invoice_guardian->tax_identification_number];
    //                     $fields = [
    //                         'client_id' => $clientId,
    //                         'client_secret' => $secret,
    //                         'grant_type'    => 'client_credentials',
    //                         'scope'         => 'InvoicingAPI'
    //                     ];
    //                     $encodedFields = http_build_query($fields);
    //                     $ch = curl_init();
    //                     curl_setopt($ch, CURLOPT_URL, $url);
    //                     curl_setopt($ch, CURLOPT_POST, true);
    //                     curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    //                     curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    //                     curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    //                     curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
    //                     curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    //                     curl_setopt($ch,CURLOPT_POSTFIELDS, $encodedFields);
    //                     $result = curl_exec($ch);
    //                     if($result == false){
    //                         ResponseService::errorResponse("Unable to connect to submit e-invoice");
    //                         die('Curl failed: ' . curl_error($ch));
    //                     }else {
    //                         $data = json_decode($result);
    //                         if(!isset($data->error)){
    //                             if(!empty($data->access_token)){
    //                                 $accessToken = $data->access_token;
    //                                 DB::table('access_token_einvoice')->insert([
    //                                     'access_token' => $accessToken,
    //                                     'guardian_id' => $e_invoice_guardian->guardian_id,
    //                                 ]);
    //                                 break;
    //                             }
    //                         }
    //                     }
    //                     curl_close($ch);
    //                 }
    //             }
    //             if($accessToken){
    //                 $url = 'https://api.myinvois.hasil.gov.my/api/v1.0/documents/state/'.$studentFeeEinvoice->uuid.'/state';
    //                 $headers = [
    //                     'authorization: Bearer '.$accessToken,
    //                     'Content-Type:application/json',
    //                 ];
    //                 $data = [
    //                     'status' => 'cancelled',
    //                     'reason' => $request->reason
    //                 ];
    //                 $encodedData = json_encode($data);
    //                 $ch = curl_init();
    //                 curl_setopt($ch, CURLOPT_URL, $url);
    //                 curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    //                 curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "PUT");
    //                 curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    //                 curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    //                 curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
    //                 curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    //                 curl_setopt($ch,CURLOPT_POSTFIELDS, $encodedData);
    //                 $result = curl_exec($ch);
    //                 $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    //                 curl_close($ch);
    //                 $resultData = json_decode($result);
    //                 if($httpCode == 200){
    //                     DB::table('student_fees_einvoice')->where('id',$studentFeeEinvoice->id)->update(['status'=> 3,'cancel_reason'=>$request->reason,'deleted_at'=>now()]);
    //                     ResponseService::successResponse("E-Invoice cancelled successfully");
    //                 } else {
    //                     // dd($resultData);
    //                     if(isset($resultData->error)){
    //                         ResponseService::errorResponse($resultData->error->details[0]->message);
    //                     }
    //                     if(isset($resultData->message)){
    //                         ResponseService::errorResponse($resultData->message);
    //                     }
    //                 }
    //             }
    //         }
    //     }
    // }

    public function cancelEInvoicing($id, Request $request) {
        // Validate request
        if (empty($request->reason)) {
            return ResponseService::errorResponse("Cancellation reason is required");
        }
        
        // Get e-invoice record
        $studentFeeEinvoice = DB::table('student_fees_einvoice')
            ->where('student_fees_id', $id)
            ->where('status', 1)
            ->first();
        
        if (!$studentFeeEinvoice) {
            return ResponseService::errorResponse("No valid e-invoice found for this invoice");
        }
        
        if (empty($studentFeeEinvoice->uuid)) {
            return ResponseService::errorResponse("E-invoice UUID not found");
        }
        
        // Call the helper method to cancel the document
        $result = EInvoiceHelper::cancelDocument(
            Auth::user()->school_id, 
            $studentFeeEinvoice->uuid, 
            $request->reason
        );
        
        if ($result['success']) {
            // Update the database record
            DB::table('student_fees_einvoice')
                ->where('id', $studentFeeEinvoice->id)
                ->update([
                    'status' => 3,
                    'cancel_reason' => $request->reason,
                    'deleted_at' => now()
                ]);
            
            return ResponseService::successResponse("E-Invoice cancelled successfully");
        } else {
            // Handle error
            $errorMessage = $result['message'] ?? "Failed to cancel e-invoice";
            if (isset($result['errors']) && is_object($result['errors'])) {
                if (isset($result['errors']->error) && isset($result['errors']->error->details[0]->message)) {
                    $errorMessage = $result['errors']->error->details[0]->message;
                } else if (isset($result['errors']->message)) {
                    $errorMessage = $result['errors']->message;
                }
            }
            
            return ResponseService::errorResponse($errorMessage);
        }
    }
    
    // public function submitMultipleEInvoice(Request $request){
    //     $studentFeesIds = $request->input('student_fees_id');
    //     $studentFees = [];
    //     $studentFeesSubmitted = [];
    //     $students = [];
    //     $e_invoice_guardian = "";
    //     $consolidatedStudentFees = [];
    //     $submitSuccessInvoice = [];
    //     $documents = [];
    //     $errors = [];
    //     $messages = [];
    //     $success = [];
    //     $increment = 0; $increase = 0;
    //     $errorString = '';
    //     $messageString = '';

    //     foreach($studentFeesIds as $studentFeeId){
       
    //         $studentFeesSubmitted[] = DB::table('student_fees_einvoice')->where('student_fees_id',$studentFeeId)->where('status',1)->first();
    //         ///Check Consolidated Student Fees
    //         $consolidates = DB::table('student_fees_consolidate')
    //         ->join('student_fees','student_fees.uid','=','student_fees_consolidate.reference_uid')
    //         ->where('student_fees.id',$studentFeeId)
    //         ->get();

    //         if ($consolidates->isEmpty()) {
    //             $studentFees[] = $this->studentFees->builder()->where('id',$studentFeeId)->first();
    //         } else {
    //             $consolidatedStudentFees[] =  $consolidates;
    //         }
    //     }


    //     ///return error if one of the fees already submitted before
    //     foreach($studentFeesSubmitted as $studentFeeSubmitted){
    //         if($studentFeeSubmitted){
    //                 $inv_number =  $this->studentFees->builder()->where('id',$studentFeeId)->select('uid')->first();
    //                 $submitSuccessInvoice[] = $inv_number->uid;
    //         }
    //     }

    //     if(COUNT($submitSuccessInvoice) > 0){
    //         return response()->json([
    //             'status' => 'error',
    //             'message' => 'E-Invoice already submitted and validated for INV: ' . implode(",", $submitSuccessInvoice),
    //         ], 400); 
    //     }

    //     // if(COUNT($consolidatedStudentFees) > 0){
    //     //     foreach($consolidatedStudentFees[0] as $consolidatedStudentFee){
    //     //         $student_fees = DB::table('student_fees')
    //     //         ->where('id',$consolidatedStudentFee->student_fees_id)
    //     //         ->first();

               
    //     //         $student = DB::table('students')
    //     //         ->join('users','users.id','=','students.user_id')
    //     //         ->where('students.id',$student_fees->student_id)
    //     //         ->where('students.school_id',Auth::user()->school_id)
    //     //         ->wherenull('students.deleted_at')
    //     //         ->first();

    //     //         if ($student) {
    //     //             $consolidateStudents[] = $student;
    //     //         }
    //     //     }
    //     // }
       

    //     if($studentFees > 0){
    //         foreach($studentFees as $studentFee){
    //             $student = $this->student->builder()->where('id',$studentFee->student_id)->first();
    //             if ($student) {
    //                 $students[] = [
    //                     'student' => $student,
    //                     'id' => $studentFee->id, 
    //                     'student_fees' =>$studentFee
    //                 ];
    //             }
    //         }
    //     }
        

    //     $e_invoice = DB::table('e_invoice')->where('school_id',Auth::user()->school_id)->first();
    //     if(!$e_invoice || $e_invoice->status != 1){
    //         ResponseService::errorResponse("School's TIN number not verified");
    //     } else {

    //         /// This only handle one consolidate 
    //         if(COUNT($consolidatedStudentFees) > 0){
    //             // dd($consolidateStudents);
    //             // dd($consolidates[0]);
    //             $e_invoice->tax_identification_number = "EI00000000010";
    //             // dd($consolidatedStudentFees);

    //             // Only pass the first fees 
    //             // use the passed first fees to find others in formatEInvoice
    //             $data = [
    //                 'id'                    => $consolidatedStudentFees[0][0]->id,
    //                 'student_fees'          => $consolidatedStudentFees[0][0],
    //                 'e_invoice'             => $e_invoice
    //             ];
               
    //             $documents[] = $this->eInvoiceFormatService->formatEInvoice($data,'01');
    //         }
    //         // dd($e_invoice_guardians);


    //         /// Handle Normal Fees
    //         foreach($students as $entry){
    //                 $student = $entry['student'];
    //                 $id = $entry['id'];
    //                 $studentFees = $entry['student_fees'];

    //                 $e_invoice_guardian = DB::table('e_invoice_guardian')->where('guardian_id',$student->guardian_id)->where('school_id',Auth::user()->school_id)->first();  
    //                 if($e_invoice_guardian && $e_invoice_guardian->status != 1){
    //                 ResponseService::errorResponse("TIN number not verified");
    //                 } else{ 
    //                     $data = [
    //                         'id'                    => $id,
    //                         'student'               => $student,
    //                         'e_invoice_guardian'    => $e_invoice_guardian,
    //                         'student_fees'          => $studentFees,
    //                         'e_invoice'             => $e_invoice
    //                     ];
    //                     $documents[] = $this->eInvoiceFormatService->formatEInvoice($data,'01');
    //                 }
    //         }

    //         if($documents){
    //             $schoolSettings = $this->cache->getSchoolSettings();
    //             if($schoolSettings) {
    //                 $accessToken = '';
    //                 $clientId = '';
    //                 $client_secret = array();
    //                 if($schoolSettings['client_id']) {
    //                     $clientId = $schoolSettings['client_id'];
    //                 }
    //                 if($schoolSettings['client_secret_1']){
    //                     $client_secret[] = $schoolSettings['client_secret_1'];
    //                 }
    //                 if($schoolSettings['client_secret_2']){
    //                     $client_secret[] = $schoolSettings['client_secret_2'];
    //                 }
    //                 if(!empty($clientId) && count($client_secret) > 0) {
    //                     foreach($documents as $document){
    //                         if(isset($consolidatedStudentFees[0][0]->uid))
    //                         {
    //                             $formattedUID = 'INV' . str_pad($consolidatedStudentFees[0][0]->uid, 8, '0', STR_PAD_LEFT);
    //                             if($document[$increase]['codeNumber'] == $formattedUID){
    //                                 $accessTokenExist = DB::table('access_token_einvoice')->where('consolidate_id',$consolidatedStudentFees[0][0]->id)->orderBy('id','desc')->first();
    //                                 if($accessTokenExist && $accessTokenExist->time_expired > now()){
    //                                     $accessToken = $accessTokenExist->access_token; 
    //                                 } else {
    //                                     foreach ($client_secret as $secret){
    //                                         $url = 'https://api.myinvois.hasil.gov.my/connect/token';
    //                                         // dd($e_invoice->tax_identification_number);
    //                                         $headers = ['onbehalfof:'.$e_invoice->tax_identification_number];
    //                                         $fields = [
    //                                             'client_id' => $clientId,
    //                                             'client_secret' => $secret,
    //                                             'grant_type'    => 'client_credentials',
    //                                             'scope'         => 'InvoicingAPI'
    //                                         ];
    //                                         $encodedFields = http_build_query($fields);
    //                                         $ch = curl_init();
    //                                         curl_setopt($ch, CURLOPT_URL, $url);
    //                                         curl_setopt($ch, CURLOPT_POST, true);
    //                                         curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    //                                         curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    //                                         curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    //                                         curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
    //                                         curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    //                                         curl_setopt($ch,CURLOPT_POSTFIELDS, $encodedFields);
    //                                         $result = curl_exec($ch);
    //                                         if($result == false){
    //                                             ResponseService::errorResponse("Unable to connect to submit e-invoice");
    //                                             die('Curl failed: ' . curl_error($ch));
    //                                         }else {
    //                                             $data = json_decode($result);
    //                                             if(!isset($data->error)){
    //                                                 if(!empty($data->access_token)){
    //                                                     $accessToken = $data->access_token;
    //                                                     DB::table('access_token_einvoice')->insert([
    //                                                         'access_token' => $accessToken,
    //                                                         'consolidate_id' => $consolidatedStudentFees[0][0]->id,
    //                                                     ]);
    //                                                     $increase++;
    //                                                     break;
    //                                                 }
    //                                             }
    //                                         }
                        
    //                                         curl_close($ch);
    //                                     }
    //                                 }  
    //                         }
    //                         }
                           
                            
    //                         else{
    //                             $accessTokenExist = DB::table('access_token_einvoice')->where('guardian_id',$e_invoice_guardian->guardian_id)->orderBy('id','desc')->first();
    //                         if($accessTokenExist && $accessTokenExist->time_expired > now()){
    //                             $accessToken = $accessTokenExist->access_token; 
    //                             $increase++;
    //                         } else {
    //                             foreach ($client_secret as $secret){
    //                                 $url = 'https://api.myinvois.hasil.gov.my/connect/token';
    //                                 // dd($e_invoice->tax_identification_number);
    //                                 $headers = ['onbehalfof:'.$e_invoice_guardian->tax_identification_number];
    //                                 $fields = [
    //                                     'client_id' => $clientId,
    //                                     'client_secret' => $secret,
    //                                     'grant_type'    => 'client_credentials',
    //                                     'scope'         => 'InvoicingAPI'
    //                                 ];
    //                                 $encodedFields = http_build_query($fields);
    //                                 $ch = curl_init();
    //                                 curl_setopt($ch, CURLOPT_URL, $url);
    //                                 curl_setopt($ch, CURLOPT_POST, true);
    //                                 curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    //                                 curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    //                                 curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    //                                 curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
    //                                 curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    //                                 curl_setopt($ch,CURLOPT_POSTFIELDS, $encodedFields);
    //                                 $result = curl_exec($ch);
    //                                 if($result == false){
    //                                     ResponseService::errorResponse("Unable to connect to submit e-invoice");
    //                                     die('Curl failed: ' . curl_error($ch));
    //                                 }else {
    //                                     $data = json_decode($result);
    //                                     if(!isset($data->error)){
    //                                         if(!empty($data->access_token)){
    //                                             $accessToken = $data->access_token;
    //                                             DB::table('access_token_einvoice')->insert([
    //                                                 'access_token' => $accessToken,
    //                                                 'guardian_id' => $e_invoice_guardian->guardian_id,
    //                                             ]);
    //                                             $increase++;
    //                                             break;
    //                                         }
    //                                     }
    //                                 }
    //                                 curl_close($ch);
    //                             }
    //                         } 
    //                     }     
    //                 }
                        
    //                 // dd($accessToken);
    //                 if($accessToken){
    //                     $url = 'https://api.myinvois.hasil.gov.my/api/v1.0/documentsubmissions';
    //                     $headers = [
    //                         'authorization: Bearer '.$accessToken,
    //                         'Content-Type:application/json',
    //                     ];
    //                     foreach ($documents as $submitdocument) {
    //                         $data = [
    //                             'documents' => $submitdocument
    //                         ];
    //                         $ch = curl_init();
    //                         curl_setopt($ch, CURLOPT_URL, $url);
    //                         curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    //                         curl_setopt($ch, CURLOPT_POST, true);
    //                         curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    //                         curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    //                         curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
    //                         curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    //                         curl_setopt($ch,CURLOPT_POSTFIELDS, json_encode($data));
    //                         $result = curl_exec($ch);
    //                         $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    //                         curl_close($ch);
    //                         $resultData = json_decode($result);
    //                         // dd($resultData);

    //                         if(!empty($resultData->error)){
    //                             $errors[] = $resultData->error;
    //                         }
    //                         if($httpCode !== 422 && $httpCode !== 202){
    //                             $messages[] = $resultData->message;
    //                         }
    //                         if($httpCode == 202)
    //                         {
    //                             $success[] = [
    //                                 'resultData' => $resultData,
    //                                 'id' => $studentFeesIds[$increment]
    //                             ];
    //                             $increment ++;
    //                         }
    //                     }

    //                     // dd( $success);
    //                     if(count($errors) > 0){
    //                         // dd($errors);
    //                         $errorString = implode(', ', $errors);
    //                     }
                        
    //                     if(count($messages) > 0){
    //                         $messageString = implode(', ', $messages);
    //                     }
                        
    //                     if($errorString != null){
    //                         /// If Error
    //                         ResponseService::errorResponse($errorString);
    //                     }else{
    //                         /// Other Errors
    //                         if($messageString != null){
    //                             ResponseService::errorResponse($messageString);
    //                         }
    //                         else {
    //                             ///Every Submit Success
    //                             foreach($success as $submitted){
    //                                 // dd($submitted);
    //                                 $data = [
    //                                     'student_fees_id' => $submitted['id'],
    //                                     'submission_uid' => $submitted['resultData']->submissionUid,
    //                                     'rejected_documents' => !empty($submitted['resultData']->rejectedDocuments) ? json_encode($submitted['resultData']->rejectedDocuments) : null,
    //                                 ];                           

    //                                 if(count($submitted['resultData']->acceptedDocuments) > 0){
    //                                     foreach($submitted['resultData']->acceptedDocuments as $document){
    //                                         if($document->uuid){
    //                                             $data['uuid'] = $document->uuid;
    //                                         }
    //                                         if($document->invoiceCodeNumber){
    //                                             $data['invoice_code_number'] = $document->invoiceCodeNumber;
    //                                         }
    //                                     }
    //                                 }
    //                                 if(count($submitted['resultData']->rejectedDocuments) > 0){
    //                                     foreach($submitted['resultData']->rejectedDocuments as $document){
    //                                         if(isset($document->error->details[0])){
    //                                             ResponseService::errorResponse($document->error->details[0]->message);
    //                                         }
    //                                     }
    //                                 }
    //                                     DB::table('student_fees_einvoice')->insert($data);
    //                                 }
    //                                 return response()->json([
    //                                     'success' => true,
    //                                     'message' => 'E-Invoice Submitted Successfully',
    //                                 ]);
    //                             }
    //                         }

    //                             //  if($httpCode == 422){
    //                             //     ResponseService::errorResponse($resultData->error,);
    //                             // } else {
    //                             //     if($httpCode == 202){
    //                             //         $data = [
    //                             //             'student_fees_id' => $id,
    //                             //             'submission_uid' => $resultData->submissionUid,
    //                             //             'rejected_documents' => !empty($resultData->rejectedDocuments) ? json_encode($resultData->rejectedDocuments) : null,
    //                             //         ];
    //                             //         if(count($resultData->acceptedDocuments) > 0){
    //                             //             foreach($resultData->acceptedDocuments as $document){
    //                             //                 if($document->uuid){
    //                             //                     $data['uuid'] = $document->uuid;
    //                             //                 }
    //                             //                 if($document->invoiceCodeNumber){
    //                             //                     $data['invoice_code_number'] = $document->invoiceCodeNumber;
    //                             //                 }
    //                             //             }
    //                             //         }
    //                             //         DB::table('student_fees_einvoice')->insert($data);
    //                             //         ResponseService::successResponse("E-Invoice submitted successfully");
    //                             //     } else {
    //                             //         // dd($resultData->error->details[0]->message);
    //                             //         if(isset($resultData->error)){
    //                             //             ResponseService::errorResponse($resultData->error->details[0]->message);
    //                             //         }
    //                             //         ResponseService::errorResponse($resultData->message);
    //                             //     }
    //                             // } 
    //                     }
    //                 }
    //             }
    //         }
    //     }

    // }

    // public function submitMultipleEInvoice(Request $request) {
    //     $studentFeesIds = $request->input('student_fees_id');
    //     $errors = [];
    //     $successfulSubmissions = [];
    
    //     foreach ($studentFeesIds as $id) {
    //         try {
    //             // Check if e-invoice already submitted (same as submitEInvoicing)
    //             $studentFeesSubmitted = DB::table('student_fees_einvoice')
    //                 ->where('student_fees_id', $id)
    //                 ->where('status', 1)
    //                 ->first();
                
    //             if ($studentFeesSubmitted) {
    //                 $inv_number = $this->studentFees->builder()->where('id', $id)->value('uid');
    //                 $inv_number = str_pad($inv_number, 8, '0', STR_PAD_LEFT);
    //                 throw new \Exception("E-Invoice already submitted and validated for INV" . $inv_number);
    //             }
    
    //             // Get student fees data (same as submitEInvoicing)
    //             $studentFees = $this->studentFees->builder()->where('id', $id)->first();
    //             $studentFeesDetails = $this->studentFeesDetail->builder()->where('student_fees_id', $id)->get();
    
    //             // --- START: Same calculation logic as submitEInvoicing ---
    //             $invoiceId = $studentFees->invoice_number ?? 'INV' . sprintf('%09d', $studentFees->uid ?? '');
    //             $lineExtensionAmount = 0;
    //             $taxExclusiveAmount = 0;
    //             $taxInclusiveAmount = 0;
    //             $totalTaxAmount = 0;
    //             $earlyoffer = 0;
    //             $dueCharges = 0;
    
    //             $studentFeesData = DB::table('student_fees')->where('id', $id)->first();
    //             $totalCompulsoryFees = DB::table('student_fees_details')
    //                 ->where('student_fees_id', $id)
    //                 ->whereNotIn('fees_type_name', ['Overdue Fees', 'Early Discount'])
    //                 ->sum(DB::raw('(fees_type_amount - (fees_type_amount * COALESCE(discount, 0) / 100)) + ((fees_type_amount - (fees_type_amount * COALESCE(discount, 0) / 100)) * COALESCE(tax, 0) / 100) * quantity'));
    
    //             // Apply tax if applicable
    //             if(isset($studentFeesData->tax_percentage) && $studentFeesData->tax_percentage > 0){
    //                 $extraTax = ($totalCompulsoryFees * ($studentFeesData->tax_percentage / 100));
    //                 $totalCompulsoryFees += $extraTax;
    //             }

                
    
    //             // Calculate due charges
    //             $currentDate = date('Y-m-d');
    //             $dueFee = DB::select('SELECT due_date FROM student_fees WHERE id= ? AND due_date < ?', [$id, $currentDate]);
    //             if (!empty($dueFee)) {
    //                 $dueCharges = $studentFeesData->due_charges 
    //                     ? ($totalCompulsoryFees * $studentFeesData->due_charges / 100)
    //                     : $studentFeesData->due_charges_amount;
    //             }
    
    //             // Calculate early discount
    //             $earlyFee = DB::select('SELECT early_date FROM student_fees WHERE id= ? AND early_date >= ?', [$id, $currentDate]);
    //             if (!empty($earlyFee)) {
    //                 $earlyoffer = $studentFeesData->early_offer 
    //                     ? -($totalCompulsoryFees * $studentFeesData->early_offer / 100)
    //                     : -$studentFeesData->early_offer_amount;
    //             }
    
    //             $allowanceTotalAmount = round(abs($earlyoffer), 2);
    //             $chargeTotalAmount = round(abs($dueCharges), 2);
    
    //             foreach ($studentFeesDetails as $detail) {
    //                 if (in_array($detail->fees_type_name, ['Early Discount', 'Overdue Fees'])) {
    //                     $detail->fees_type_amount = 0;
    //                 }
                    
    //                 $priceExtensionAmount = round(
    //                     ($detail->fees_type_amount * $detail->quantity) - 
    //                     (($detail->fees_type_amount * $detail->quantity) * ($detail->discount / 100)),
    //                     2
    //                 );
                    
    //                 $taxAmount = round($priceExtensionAmount * ($detail->tax / 100), 2);
            
    //                 $lineExtensionAmount += $priceExtensionAmount;
    //                 $taxExclusiveAmount += $priceExtensionAmount;
    //                 $taxInclusiveAmount += ($priceExtensionAmount + $taxAmount);
    //                 $totalTaxAmount += $taxAmount;
    //             }
    
    //             // Apply final rounding
    //             $studentFees->line_extension_amount = round($lineExtensionAmount, 2);
    //             $studentFees->tax_exclusive_amount = round($taxExclusiveAmount, 2);
    //             $studentFees->tax_inclusive_amount = round($taxInclusiveAmount, 2);
    //             $studentFees->allowance_total_amount = $allowanceTotalAmount;
    //             $studentFees->charge_total_amount = $chargeTotalAmount;
    //             $studentFees->payable_rounding_amount = 0;
    //             $studentFees->payable_amount = round(
    //                 $studentFees->total_amount ?? ($taxInclusiveAmount - $allowanceTotalAmount + $chargeTotalAmount),
    //                 2
    //             );
    //             $studentFees->total_tax_amount = round($totalTaxAmount, 2);
    //             $studentFees->total_taxable_amount = round($taxExclusiveAmount, 2);
    //             $studentFees->total_tax_percent = ($taxExclusiveAmount > 0) 
    //                 ? round(($totalTaxAmount / $taxExclusiveAmount) * 100, 2) 
    //                 : 0;
    //             // --- END: Same calculation logic as submitEInvoicing ---
    
    //             // Get student and school data
    //             $student = $this->student->builder()->where('id', $studentFees->student_id)->first();
    //             $e_invoice = DB::table('e_invoice')->where('school_id', Auth::user()->school_id)->first();
    //             $school = DB::table('schools')->where('id', Auth::user()->school_id)->first();
    
    //             // Verify TIN numbers
    //             if (!$e_invoice || $e_invoice->status != 1) {
    //                 throw new \Exception("School's TIN number not verified");
    //             }
    
    //             $e_invoice_guardian = DB::table('e_invoice_guardian')
    //                 ->where('guardian_id', $student->guardian_id)
    //                 ->where('school_id', Auth::user()->school_id)
    //                 ->first();
                
    //             if ($e_invoice_guardian && $e_invoice_guardian->status != 1) {
    //                 throw new \Exception("Guardian TIN number not verified");
    //             }
    
    //             // Prepare addresses
    //             $supplierAddressLines = array_filter([
    //                 trim(preg_replace('/\s+/', ' ', $school->address ?? '')),
    //             ]);
    
    //             $customerAddressLines = array_filter([
    //                 trim(preg_replace('/\s+/', ' ', $e_invoice_guardian->address ?? ($student->guardian->address ?? ''))),
    //             ]);
    
    //             // Prepare invoice data (same structure as submitEInvoicing)
    //             $invoiceData = [
    //                 'invoice_type' => 'invoice',
    //                 'id' => $invoiceId,
    //                 'billing_references' => [
    //                     'additional_document' => [
    //                         'id' => $studentFees->billing_reference_id ?? '',
    //                         'uuid' => $studentFees->billing_reference_uuid ?? ''
    //                     ]
    //                 ],
    //                 'supplier' => [
    //                     'address' => [
    //                         'city' => $e_invoice->city ?? '',
    //                         'postcode' => $e_invoice->postal_code ?? '',
    //                         'country_subentity_code' => $e_invoice->state ?? ''
    //                     ],
    //                     'address_line' => $supplierAddressLines,
    //                     'country_code' => 'MYS',
    //                     'legal_entity' => $school->name ?? '',
    //                     'contact_phone' => $school->support_phone ?? '',
    //                     'contact_email' => $school->support_email ?? '',
    //                     'msic_code' => $e_invoice->company_msic_code ?? '',
    //                     'party_identification' => [
    //                         'TIN' => $e_invoice->tax_identification_number ?? '',
    //                         ($e_invoice->id_type == 'NRIC' ? 'NRIC' : 
    //                          ($e_invoice->id_type == 'BRN' ? 'BRN' : 'ID')) 
    //                             => $e_invoice->registration_id_passport_number ?? ''
    //                     ],
    //                 ],
    //                 'customer' => [
    //                     'address' => [
    //                         'city' => $e_invoice_guardian->city ?? ($student->guardian->city ?? ''),
    //                         'postcode' => $e_invoice_guardian->postal_code ?? ($student->guardian->postcode ?? ''),
    //                         'country_subentity_code' => $e_invoice_guardian->state ?? ($student->guardian->state_code ?? '')
    //                     ],
    //                     'address_line' => $customerAddressLines,
    //                     'country_code' => $e_invoice_guardian->country ?? ($student->guardian->country_code ?? 'MYS'),
    //                     'legal_entity' => $e_invoice_guardian->name ?? ($student->guardian->name ?? ''),
    //                     'contact_phone' => $e_invoice_guardian->contact_no ?? ($student->guardian->phone ?? ''),
    //                     'contact_email' => $e_invoice_guardian->email ?? ($student->guardian->email ?? ''),
    //                     'party_identification' => [
    //                         'TIN' => $e_invoice_guardian->tax_identification_number ?? '',
    //                         'NRIC' => $e_invoice_guardian->ic_no ?? ''
    //                     ],
    //                 ],
    //                 'document_line' => $studentFeesDetails->map(function ($studentFeesDetail) use ($school) {
    //                     $itemCode = DB::table('fees_type_master')
    //                         ->where('school_id', $school->id)
    //                         ->where('name', $studentFeesDetail->fees_type_name)
    //                         ->value('item_code');
            
    //                     if (empty($itemCode)) {
    //                         $itemCode = DB::table('fees_type_master')
    //                             ->where('school_id', $school->id)
    //                             ->where('classification_code', $studentFeesDetail->classification_code)
    //                             ->value('item_code');
    //                     }
            
    //                     if (empty($itemCode)) {
    //                         return null;
    //                     }
            
    //                     $priceExtensionAmount = round(
    //                         ($studentFeesDetail->fees_type_amount * $studentFeesDetail->quantity) - 
    //                         (($studentFeesDetail->fees_type_amount * $studentFeesDetail->quantity) * ($studentFeesDetail->discount / 100)),
    //                         2
    //                     );
                        
    //                     $priceExtensionAmountTrue = round($studentFeesDetail->quantity * $studentFeesDetail->fees_type_amount, 2);
    //                     $taxTotalAmount = round($priceExtensionAmount * ($studentFeesDetail->tax / 100), 2);
            
    //                     return [
    //                         'id' => $itemCode ?? '',
    //                         'quantity' => $studentFeesDetail->quantity ?? 1,
    //                         'unit' => !empty($studentFeesDetail->unit) ? explode(' - ', $studentFeesDetail->unit)[0] : '',
    //                         'line_amount' => $priceExtensionAmount ?? 0,
    //                         'item' => [
    //                             'description' => $studentFeesDetail->fees_type_name ?? '',
    //                             'classifications' => [
    //                                 [
    //                                     'code' => '010',
    //                                     'type' => 'CLASS',
    //                                 ]
    //                             ]
    //                         ],
    //                         'price' => [
    //                             'amount' => round($studentFeesDetail->fees_type_amount ?? 0, 2)
    //                         ],
    //                         'price_extension' => [
    //                             'amount' => $priceExtensionAmountTrue
    //                         ],
    //                         'tax_total' => [
    //                             'amount' => $taxTotalAmount
    //                         ],
    //                         'tax_sub_totals' => [
    //                             [
    //                                 'taxable_amount' => $priceExtensionAmount,
    //                                 'tax_amount' => $taxTotalAmount,
    //                                 'percent' => (float) $studentFeesDetail->tax ?? 0,
    //                                 'tax_scheme' => [
    //                                     'id' => 'OTH'
    //                                 ],
    //                                 'tax_category' => [
    //                                     'id' => '01',
    //                                     'percent' => (float) $studentFeesDetail->tax ?? 0,
    //                                     'tax_exemption_reason' => 'None'
    //                                 ]
    //                             ]
    //                         ],
        
    //                         'allowance_charges' => array_filter([
    //                             $studentFeesDetail->discount > 0 ? [
    //                                 'charge_indicator' => false,
    //                                 'reason' => 'Discount',
    //                                 'multiplier' => ($studentFeesDetail->discount/100),
    //                                 'amount' => $studentFeesDetail->fees_type_amount * ($studentFeesDetail->discount / 100) * $studentFeesDetail->quantity,
    //                             ] : null,
    //                             // $studentFeesDetail->tax > 0 ? [
    //                             //     'charge_indicator' => true,
    //                             //     'reason' => 'Tax',
    //                             //     'multiplier' => ($studentFeesDetail->tax/100),
    //                             //     'amount' => $studentFeesDetail->fees_type_amount * ($studentFeesDetail->tax / 100)* $studentFeesDetail->quantity,
    //                             // ] : null,
    //                         ]),
    //                     ];
    //                 })->filter()->values()->toArray(),
            
    //                 "legal_monetary_total"=> [
    //                     'line_extension_amount' => $studentFees->line_extension_amount,
    //                     'tax_exclusive_amount' => $studentFees->tax_exclusive_amount,
    //                     'tax_inclusive_amount' => $studentFees->tax_inclusive_amount,
    //                     'allowance_total_amount' => $studentFees->allowance_total_amount,
    //                     'charge_total_amount' => $studentFees->charge_total_amount,
    //                     'payable_rounding_amount' => $studentFees->payable_rounding_amount,
    //                     'payable_amount' => $studentFees->payable_amount
    //                 ],
            
    //                 'allowance_charges' => array_filter([
    //                     $studentFees->allowance_total_amount > 0 ? [
    //                         'charge_indicator' => false,
    //                         'reason' => 'Early payment discount',
    //                         'multiplier' =>round(($studentFees->allowance_total_amount)/$studentFees->tax_exclusive_amount,4),
    //                         'amount' => $studentFees->allowance_total_amount
    //                     ] : null,
    //                     $studentFees->charge_total_amount > 0 ? [
    //                         'charge_indicator' => true,
    //                         'reason' => 'Overdue Fees Charges',
    //                         'multiplier' => round(($studentFees->charge_total_amount)/$studentFees->tax_exclusive_amount, 4),
    //                         'amount' => $studentFees->charge_total_amount
    //                     ] : null
    //                 ]),
            
    //                 $totalTaxAmount = $studentFees->tax_inclusive_amount * ($studentFees->tax_percentage / 100),
            
    //                 'tax_total' => [
    //                     'tax_amount' => $totalTaxAmount,
    //                     'tax_sub_totals' => [
    //                         [
    //                             'taxable_amount' => $studentFees->tax_inclusive_amount,
    //                             'tax_amount' => $totalTaxAmount,
    //                             'percent' => $studentFees->tax_percentage,
    //                             'tax_category' => [
    //                                 'id' => $studentFees->tax_type ?? '06',
    //                                 'tax_exemption_reason' => 'None'
    //                             ],
    //                             'tax_scheme' => [
    //                                 'id' => 'OTH'
    //                             ]
    //                         ]
    //                     ]
    //                 ]
    //             ];
    
    //             // Get school settings for API credentials
    //             $schoolSettings = $this->cache->getSchoolSettings();
    //             if (!$schoolSettings || !$schoolSettings['client_id'] || empty($schoolSettings['client_secret_1'])) {
    //                 throw new \Exception("School API credentials not configured");
    //             }
    
    //             // Generate XML and submit
    //             $invoiceType = EInvoiceHelper::mapInvoiceTypeCode($invoiceData['invoice_type']);
    //             $invoiceXml = EInvoiceHelper::createXmlDocument($invoiceType, $invoiceData);
    //             $documents = [MyInvoisHelper::getSubmitDocument($id, $invoiceXml)];
    //             $submissionResult = EInvoiceHelper::submitDocument(Auth::user()->school_id, $documents);
    
    //             if (!$submissionResult['success']) {
    //                 throw new \Exception($submissionResult['message'] ?? "E-Invoice submission failed");
    //             }
    
    //             // Extract signature value
    //             $signatureValue = '';
    //             $dom = new DOMDocument();
    //             $dom->loadXML($invoiceXml);
    //             $xpath = new DOMXPath($dom);
    //             $xpath->registerNamespace('ds', 'http://www.w3.org/2000/09/xmldsig#');
    //             $signatureValueNodes = $xpath->query('//ds:SignatureValue');
    //             if ($signatureValueNodes->length > 0) {
    //                 $signatureValue = $signatureValueNodes->item(0)->nodeValue;
    //             }
    
    //             // Process submission result
    //             $resultData = $submissionResult['data'] ?? null;
    //             $dataToInsert = [
    //                 'student_fees_id' => $id,
    //                 'submission_uid' => $resultData->submissionUid ?? null,
    //                 'rejected_documents' => !empty($resultData->rejectedDocuments) ? json_encode($resultData->rejectedDocuments) : null,
    //                 'uuid' => null,
    //                 'invoice_code_number' => null
    //             ];
    
    //             if (isset($resultData->acceptedDocuments)) {
    //                 foreach ($resultData->acceptedDocuments as $document) {
    //                     if (isset($document->uuid)) $dataToInsert['uuid'] = $document->uuid;
    //                     if (isset($document->invoiceCodeNumber)) $dataToInsert['invoice_code_number'] = $invoiceId;
    //                 }
    //             }
    
    //             // Save to database
    //             DB::table('student_fees_einvoice')->insert($dataToInsert);
    //             DB::table('student_fees')->where('id', $id)->update(['sign_value' => $signatureValue]);
                
    //             $successfulSubmissions[] = $id;
    
    //         } catch (\Exception $e) {
    //             \Log::error("E-Invoice submission failed for ID $id: " . $e->getMessage());
    //             $errors[] = "ID $id: " . $e->getMessage();
    //         }
    //     }
    
    //     // Return response
    //     if (!empty($errors)) {
    //         return response()->json([
    //             'status' => 'error',
    //             'message' => implode("; ", $errors),
    //             'successful_submissions' => $successfulSubmissions
    //         ], 400);
    //     }
    
    //     return response()->json([
    //         'success' => true,
    //         'message' => count($successfulSubmissions) . ' e-invoice(s) submitted successfully',
    //         'submitted_ids' => $successfulSubmissions
    //     ]);
    // }

    public function consolidateInvoice(Request $request){
        $studentFeesIds = $request->student_fees_id;
        $total_invoice_count = COUNT($studentFeesIds);
        $unpaidInv = []; $draftInv=[]; $submittedInv=[]; $tinInvalidInv=[];$repeatInv=[];
        $draftInvString = "";
        $submittedInvString = "";
        $unpaidInvString = "";
        $tinInvalidInvString = "";
        $repeatInvString = "";
        $session_year_id = $this->cache->getDefaultSessionYear()->id;
        // dd($total_invoice_count);


        //If no data passed
        if(!$studentFeesIds){
            // ResponseService::errorResponse("Please Select Two or More Invoice");
            return response()->json([
                'status' => 'error',
                'message' => 'Please Select Two or More Invoice',
            ], 400); 
        }

        //If only one Invoice Passed
        if($studentFeesIds < 1){
            // ResponseService::errorResponse("Please Select Two or More Invoice");
            return response()->json([
                'status' => 'error',
                'message' => 'Please Select Two or More Invoice',
            ], 400); 
        }

         ///Check If Contains Draft
         $student_fees = DB::table('student_fees')
         ->where('school_id',Auth::user()->school_id)
         ->whereIn('id',$studentFeesIds)
         ->wherenull('deleted_at')
         ->get();
 
         if ($student_fees && $student_fees->contains(fn($student_fee) => $student_fee->status === "draft")) {
            return response()->json([
                'status' => 'error',
                'message' => 'Cannot Consolidate Draft Student Fees',
            ], 400); 
         }

         ///Check If consolidated before
         $consolidatedInvoice = DB::table('student_fees_consolidate')
         ->join('student_fees','student_fees.uid','=','student_fees_consolidate.reference_uid')
         ->wherein('student_fees_consolidate.student_fees_id',$studentFeesIds)
         ->wherenull('student_fees.deleted_at')
         ->get();
    
 
         if(COUNT($consolidatedInvoice) > 0)
         {
             foreach($consolidatedInvoice as $invoice){
                 $uid = DB::table('student_fees')
                 ->where('id',$invoice->student_fees_id)
                 ->first()
                 ->uid;
                 $formattedUID = 'INV' . $uid;    
                 $repeatInv[] = $formattedUID;
 
             }
 
             $repeatInv = array_unique($repeatInv);

                // Combine all consolidated invoice IDs into a single string
                $repeatInvString = implode(', ', $repeatInv) . ' Has Already Been Consolidated Before';
             
         }

        ///If the Student Fees has Already been Submitted
        $student_fees_einvoice = DB::table('student_fees_einvoice')
        ->join('student_fees','student_fees.id','=','student_fees_einvoice.student_fees_id')
        ->whereIn('student_fees_id',$studentFeesIds)
        ->whereNull('student_fees_einvoice.deleted_at')
        ->get();

        if(COUNT($student_fees_einvoice) > 0){
            foreach($student_fees_einvoice as $student_fee_einvoice){
                /// 0 = pending 
                // if($student_fee_einvoice->status == 0){

                // }
                // /// 1 = valid 
                // if($student_fee_einvoice->status == 1){

                // }
                //  /// 2 = invalid 
                //  if($student_fee_einvoice->status == 2){

                //  }
                //  /// 3 = canceled
                //  if($student_fee_einvoice->status == 3){

                //  }

                $submittedInv[] = 'INV' .$student_fee_einvoice->uid;
            }

             // Ensure unique values (optional)
            $submittedInv = array_unique($submittedInv);

            // Combine all submitted invoices into a single string
            $submittedInvString = implode(', ', $submittedInv) . ' Has Already Submitted Before';
            // return ResponseService::errorResponse($submittedInvString);
        }

       
       
        ///Check if the Student Fees is paid
        $student_fees_paids = DB::table('student_fees_paids')
            ->whereIn('student_fees_id', $studentFeesIds) // Correct usage of whereIn
            ->where('school_id', Auth::user()->school_id)
            ->whereNull('deleted_at')
            ->get();
        
        $total_paid_invoice_count = COUNT($student_fees_paids);

        if($total_invoice_count != $total_paid_invoice_count){
        $unpaidStudentFees = DB::table('student_fees')
            ->whereNotIn('id', function ($query) {
                $query->select('student_fees_id')
                    ->from('student_fees_paids')
                    ->where('school_id', Auth::user()->school_id)
                    ->whereNull('deleted_at');
            })
            ->whereIn('id', $studentFeesIds) // Ensure we're checking only the relevant student_fees IDs
            ->get(['id', 'uid','status']);

            if(COUNT($unpaidStudentFees) > 0){
                foreach($unpaidStudentFees as $unpaidStudentFee){
                    if($unpaidStudentFee->status == 'published'){
                        $formattedUID = 'INV' .$unpaidStudentFee->uid;
                        $unpaidInv[] = $formattedUID;
                    }
                }

                $unpaidInvString = implode(',', $unpaidInv).' Has Not Been Paid';

                // return ResponseService::errorResponse($unpaidInvString);
            }
        }

        ///Check If every Tin Number is verified
        foreach($student_fees as $student_fee){
            $student = DB::table('students')
            ->where('id',$student_fee->student_id)
            ->first();
           
            if(isset($student)){
                $e_invoice_guardian = DB::table('e_invoice_guardian')->where('guardian_id',$student->guardian_id)->where('school_id',Auth::user()->school_id)->first();  
                if(isset($e_invoice_guardian) && $e_invoice_guardian->status != 1){
                    $formattedUID = 'INV' . $student_fee->uid;    
                    $tinInvalidInv[] = $formattedUID;
                }
            }
        }

        if(COUNT($tinInvalidInv)> 0){
            $tinInvalidInvString = implode(',', $tinInvalidInv).' Tin is not verified';
        }
        
       



        /// Consolidate store in student_fees table

        if($submittedInvString || $unpaidInvString || $tinInvalidInvString || $repeatInvString){
            $messages = array_filter([
                $submittedInvString,
                $unpaidInvString,
                $tinInvalidInvString,
                $repeatInvString
            ]);

            $errorMessage = implode(".\n", $messages) . '.';
        
           
            return response()->json([
                'status' => 'error',
                'message' => $errorMessage,
            ], 400);
        }
        
        
        /// Create Consolidate
        $latestUID = DB::table('student_fees')->where('school_id', Auth::user()->school_id)->where('status', 'published')->whereNull('deleted_at')->select('uid')->orderBy('uid', 'desc')->value('uid');
        $uid = $latestUID ? $latestUID + 1 : 1;

        foreach($studentFeesIds as $studentFeeId){
            $data[] = [
                'student_fees_id' => $studentFeeId,
                'reference_uid' => $uid,
                'session_year_id' => $session_year_id,
                'school_id' => Auth::user()->school_id
            ];
        }

        // dd($data);

        DB::table('student_fees_consolidate')->insert($data);

        $data = [
            'name' => 'Consolidated Invoice',
            'school_id' => Auth::user()->school_id,
            'session_year_id' => $session_year_id,
            'status' => 'published',
            'class_id' => null,
            'student_id'    => null,
            'uid' => $uid,
            'created_at'=>now(),
        ];

        DB::table('student_fees')->insert($data);

        return response()->json([
            'success' => true,
            'message' => 'Successfully Consolidated Invoice',
        ]);
    }
}
