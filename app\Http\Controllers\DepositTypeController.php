<?php

namespace App\Http\Controllers;

use App\Models\DepositType;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Http\Requests\DepositTypeRequest;

class DepositTypeController extends Controller
{
    public function index()
    {
        $request = request();
        $query = DepositType::query();
        
        if ($request->has('search')) {
            $search = $request->input('search');
            $query->where('name', 'LIKE', "%{$search}%")
                  ->orWhere('description', 'LIKE', "%{$search}%");
        }

        if ($request->has('show_deleted') && $request->show_deleted) {
            $query->withTrashed();
        }

        $depositTypes = $query->leftJoin('student_deposits', 'deposit_types.id', '=', 'student_deposits.deposit_type_id')
            ->select('deposit_types.*')
            ->selectRaw('SUM(CASE WHEN student_deposits.status = "active" THEN student_deposits.amount ELSE 0 END) as collected_amount')
            ->selectRaw('SUM(CASE WHEN student_deposits.status = "refunded" THEN student_deposits.amount ELSE 0 END) as refunded_amount')
            ->groupBy('deposit_types.id', 'deposit_types.name', 'deposit_types.description', 'deposit_types.school_id', 'deposit_types.created_at', 'deposit_types.updated_at', 'deposit_types.deleted_at')
            ->withCasts(['collected_amount' => 'float', 'refunded_amount' => 'float'])
            ->withCount(['studentDeposits' => function($query) {
                $query->where('status', 'active');
            }])
            ->orderBy('deposit_types.name')
            ->paginate(10);

        return view('deposit-types.index', compact('depositTypes'));
    }

    public function create()
    {
        return view('deposit-types.create');
    }

    public function store(DepositTypeRequest $request)
    {
        $data = $request->validated();
        $data['school_id'] = auth()->user()->school_id;
        
        $depositType = DepositType::create($data);

        return redirect()
            ->route('deposit-types.index')
            ->with('success', 'Deposit type created successfully');
    }

    public function edit(DepositType $depositType)
    {
        return view('deposit-types.edit', compact('depositType'));
    }

    public function update(DepositTypeRequest $request, DepositType $depositType)
    {
        $depositType->update($request->validated());

        return redirect()
            ->route('deposit-types.index')
            ->with('success', 'Deposit type updated successfully');
    }

    public function destroy(DepositType $depositType)
    {
        if ($depositType->getActiveDepositsCount() > 0) {
            return redirect()
                ->route('deposit-types.index')
                ->with('error', 'Cannot delete deposit type with active deposits');
        }

        $depositType->delete();

        return redirect()
            ->route('deposit-types.index')
            ->with('success', 'Deposit type deleted successfully');
    }

    public function restore($id)
    {
        $depositType = DepositType::withTrashed()->findOrFail($id);
        $depositType->restore();

        return redirect()
            ->route('deposit-types.index')
            ->with('success', 'Deposit type restored successfully');
    }
}