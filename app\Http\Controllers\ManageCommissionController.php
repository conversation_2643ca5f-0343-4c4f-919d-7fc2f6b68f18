<?php

namespace App\Http\Controllers;

use App\Repositories\Attendance\AttendanceInterface;
use App\Repositories\ClassSection\ClassSectionInterface;
use App\Repositories\Student\StudentInterface;
use App\Services\CachingService;
use App\Services\ResponseService;
use App\Models\Subject;
use App\Models\Attendances;
use App\Models\User;
use App\Models\SubjectAttendance;
use App\Services\BootstrapTableService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Throwable;

class ManageCommissionController extends Controller {

    private AttendanceInterface $attendance;
    private ClassSectionInterface $classSection;
    private StudentInterface $student;
    private CachingService $cache;

    public function __construct(AttendanceInterface $attendance, ClassSectionInterface $classSection, StudentInterface $student, CachingService $cachingService) {
        $this->attendance = $attendance;
        $this->classSection = $classSection;
        $this->student = $student;
        $this->cache = $cachingService;
    }


    public function scan() {
        $schoolId = Auth::user()->school_id;
        $subjects = DB::table('subjects')
                ->select('id','name')
                ->where('school_id', $schoolId)
                ->get();
            
            $staffs = DB::table('staffs as s')
            ->join('users as u', 's.user_id', '=', 'u.id')
            ->where('u.school_id', $schoolId)
            ->whereNull('u.deleted_at')
            ->select('u.id', DB::raw("CONCAT(u.first_name, ' ', u.last_name) AS full_name"))
            ->get();

        return view('commission.manage_commission', compact( 'subjects','staffs'));
    }


    public function showAttendance(Request $request)    
    {
        // Get request parameters
        $offset = $request->input('offset', 0);
        $limit = $request->input('limit', 9999);
        $sort = $request->input('sort', 'id');
        $order = $request->input('order', 'DESC');
        $search = $request->input('search');
        $subject_id = $request->input('subject_id');
        $teacher_id = $request->input('teacher_id');
        $schoolId = Auth::user()->school_id;
        $start_date = Carbon::createFromFormat('d-m-Y', $request->input('start_date'))->startOfDay();
        $end_date = Carbon::createFromFormat('d-m-Y', $request->input('end_date'))->endOfDay();
        
        $query = SubjectAttendance::query()
            ->join('users', 'subject_attendances.user_id', '=', 'users.id')
            ->leftJoin('subjects', 'subject_attendances.subjects_id', '=', 'subjects.id')
            ->leftJoin('users as t', 'subject_attendances.teacher_id', '=', 't.id')  // Changed this join
            ->select(
                'subject_attendances.*',
                'users.first_name as student_first_name',
                'users.last_name as student_last_name',
                DB::raw('COALESCE(subjects.name, "All Day") as subject_name'),
                'subject_attendances.fees_per_section',  
                'subjects.commission as section_default_fee', 
                DB::raw('COALESCE(CONCAT(t.first_name, " ", t.last_name), "No Teacher Assigned") as teacher_name')
            )
            ->where('subject_attendances.school_id', $schoolId)
            ->whereNull('users.deleted_at');

        // Apply search filters
        if ($search) {
            $searchTerms = explode(' ', $search);
            
            $query->where(function ($q) use ($searchTerms) {
                foreach ($searchTerms as $term) {
                    $q->where(function ($q) use ($term) {
                        $q->where('users.first_name', 'LIKE', "%$term%")
                        ->orWhere('users.last_name', 'LIKE', "%$term%")
                        ->orWhere('t.first_name', 'LIKE', "%$term%")
                        ->orWhere('t.last_name', 'LIKE', "%$term%");
                    });
                }
            });
        }

        // Apply subject filters
        if ($subject_id == '0') {
            $query->whereNull('subject_attendances.subjects_id');
        } elseif ($subject_id) {
            $query->where('subject_attendances.subjects_id', $subject_id);
        }

        if ($teacher_id == '0') {
            $query->whereNull('subject_attendances.teacher_id');
        } elseif ($teacher_id) {
            $query->where('subject_attendances.teacher_id', $teacher_id);
        }
        
        // Apply date range filter if provided
        if ($start_date && $end_date) {
            $query->whereBetween('subject_attendances.date', [$start_date, $end_date]);
        }
        
        // Get the total count of records (after applying filters)
        $total = $query->count();
        
        $query->groupBy('subject_attendances.id');
        
        // Apply sorting, offset, and limit
        $query->orderBy($sort, $order)->skip($offset)->take($limit);
        
        // Get the records
        $res = $query->get();
        
        // Prepare the response data
        $bulkData = [];
        $bulkData['total'] = $total;
        $rows = [];
        $no = 1;

        foreach ($res as $row) {
            $operate = BootstrapTableService::editButton(route('managecommission.updateCommission', $row->id));
            
            $tempRow = $row->toArray();
            $tempRow['no'] = $no++;
            $tempRow['student_name'] = $row->student_first_name . ' ' . $row->student_last_name;
            $tempRow['subject_name'] = $row->subject_name; 
            $tempRow['operate'] = $operate;
            $tempRow['clock_in'] = $row->clock_in ? Carbon::parse($row->clock_in)->format('H:i:s') : '-';
            $tempRow['clock_out'] = $row->clock_out ? Carbon::parse($row->clock_out)->format('H:i:s') : '-';
            $tempRow['teacher_name'] = $row->teacher_name;
            $tempRow['fees_per_section'] = $row->fees_per_section ?? $row->section_default_fee ?? '0.00';

            // Default commission amount and type
            $tempRow['commission_amount'] = '0.00';
            $tempRow['commission_type'] = '-';

            // Check if commission data exists
            if ($row->commission_typee !== null) {
                $tempRow['commission_amount'] = $row->commission_amountt ?? '0.00';
                $tempRow['commission_type'] = $row->commission_typee == 0 ? 'Percentage' : 'Fixed Amount';
            }
            
            if ($row->subjects_id && $row->teacher_id) {
                $commission = DB::table('user_group_details')
                    ->join('user_groups', 'user_groups.id', '=', 'user_group_details.group_id')
                    ->where('user_group_details.subject_id', $row->subjects_id)
                    ->where('user_group_details.teacher_id', $row->teacher_id)
                    ->select('commission_type', 'commission_amount', 'exclude_absent_students')
                    ->first();

                if ($commission) {
                    $commissionAmount = $commission->commission_amount ?? '0.00';
                    $commissionType = $commission->commission_type === null ? '-' : ($commission->commission_type == 0 ? 'Percentage' : 'Fixed Amount');
                    // If "Exclude Absent Students" is checked and student is absent, set commission amount to 0 but keep type
                    if ($commission->exclude_absent_students == 1 && $row->status == 0) {
                        $tempRow['commission_amount'] = '0.00';
                    } else {
                        $tempRow['commission_amount'] = $commissionAmount;
                    }
                    
                    // Always display commission type
                    $tempRow['commission_type'] = $commissionType;
                }
            }
            
            // Calculate total commission amount based on commission type
            if ($tempRow['commission_type'] == 'Percentage') {
                $tempRow['total'] = number_format(($tempRow['fees_per_section'] * ($tempRow['commission_amount'] / 100)), 2);
            } else {
                $tempRow['total'] = number_format($tempRow['commission_amount'], 2);
            }

            // Determine status based on type
            $tempRow['status'] = match ($row->status) {
                1 => 'Present',
                2 => 'Late',
                3 => 'Replacement',
                default => 'Absent',
            };

            $rows[] = $tempRow;
        }

        $bulkData['rows'] = $rows;

        return response()->json($bulkData);
    }

    public function updateCommission(Request $request)
    {
        $request->validate([
            'id' => 'required|exists:subject_attendances,id',
            'date' => 'required|date',
            'clock_in' => 'nullable|date_format:H:i',
            'clock_out' => 'nullable|date_format:H:i',
            'status' => 'required',
            'remark_picture' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
        ]);
    
        try {
            DB::beginTransaction();
            
            // Find the record
            $subjectAttendance = SubjectAttendance::findOrFail($request->id);
            
            // Update other fields
            $subjectAttendance->date = $request->date;
    
            // Set commission_amountt based on status
            if ($request->status == 0) {
                $subjectAttendance->commission_amountt = 0; // Set to 0 if status is 0
            } else {
                $subjectAttendance->commission_amountt = $request->commission_amount; // Otherwise, use the submitted value
            }
    
            $subjectAttendance->fees_per_section = $request->fees_per_section;
            $subjectAttendance->commission_typee = $request->edit_commission_type;

            // Handle clock_in
            if (!empty($request->clock_in)) {
                $clockInDateTime = Carbon::createFromFormat('Y-m-d H:i', $request->date . ' ' . $request->clock_in);
                $subjectAttendance->clock_in = $clockInDateTime;
            } else {
                $subjectAttendance->clock_in = null; // or whatever default behavior you need
            }
    
            // Handle clock_out
            if (!empty($request->clock_out)) {
                $clockOutDateTime = Carbon::createFromFormat('Y-m-d H:i', $request->date . ' ' . $request->clock_out);
                $subjectAttendance->clock_out = $clockOutDateTime;
            } else {
                $subjectAttendance->clock_out = null; // or whatever default behavior you need
            }
    
            // Handle status
            $subjectAttendance->status = $request->status;
    
            // Handle remark_picture only if a new file is uploaded
            if ($request->hasFile('remark_picture')) {
                $file = $request->file('remark_picture');
                $path = $file->store('remark_pictures', 'public');
                $subjectAttendance->remark_picture = $path;
            }
    
            // Calculate total time if both clock_in and clock_out are provided
            if ($subjectAttendance->clock_in && $subjectAttendance->clock_out) {
                $interval = $subjectAttendance->clock_out->diff($subjectAttendance->clock_in);
                $subjectAttendance->total_time = $interval->format('%H:%I:%S');
            } else {
                $subjectAttendance->total_time = null; // or set to default value
            }
    
            $subjectAttendance->save();
    
            // Handle attendance update logic...
            $oldAttendance = DB::select("SELECT * FROM attendances WHERE subject_attendance_id = ?", [$request->id]);
            if ($oldAttendance) {
                $type = $request->status == 0 ? 0 : 1;
                $oldAttendanceData = [
                    'type' => $type,
                    'date' => $request->date,
                    'updated_at' => now(),
                ];
                DB::table('attendances')
                    ->where('subject_attendance_id', $request->id)
                    ->update($oldAttendanceData);
            }
    
            DB::commit();
            ResponseService::successResponse('Data Updated Successfully');
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "Leave Controller -> Update Method");
            ResponseService::errorResponse();
        }
    }
    
    // public function updateAttendance(Request $request)
    // {
    //     $data = $request->json()->all();
        
    //     foreach ($data as $item) {
    //         SubjectAttendance::where('id', $item['id'])
    //             ->update([
    //                 'commission_typee' => $item['commission_typee'] ?? null,
    //                 'commission_amountt' => $item['commission_amountt'] ?? null,
    //                 'fees_per_section' => $item['fees_per_section'] ?? null,
    //                 'store' => $item['store'] ?? null,
    //             ]);
    //     }

    //     return response()->json(['success' => true]);
    // }

    public function perMonthIndex()
    {
        $schoolId = Auth::user()->school_id;
        $subjects = DB::table('subjects')
            ->select('id', 'name')
            ->where('school_id', $schoolId)
            ->get();
            
        $staffs = DB::table('staffs as s')
            ->join('users as u', 's.user_id', '=', 'u.id')
            ->where('u.school_id', $schoolId)
            ->whereNull('u.deleted_at')
            ->select('u.id', DB::raw("CONCAT(u.first_name, ' ', u.last_name) AS full_name"))
            ->get();

        $startDate = Carbon::now()->startOfYear()->format('d-m-Y');
        $endDate = Carbon::now()->endOfYear()->format('d-m-Y');

        return view('commission.manage_commission_per_month', compact('subjects', 'staffs', 'startDate', 'endDate'));
    }

    public function perMonthShow(Request $request)
    {
        try {
            $offset = request('offset', 0);
            $limit = request('limit', 9999);
            $sort = request('sort', 'id');
            $order = request('order', 'DESC');
            $search = request('search', '');
            $subject_id = $request->input('filter_subject');
            $teacher_id = $request->input('filter_teacher');
            $start_date = $request->input('filter_start_date');
            $end_date = $request->input('filter_end_date');

            $query = DB::table('subject_attendances as sa')
                ->join('users as u', 'sa.teacher_id', '=', 'u.id')
                ->leftJoin('subjects as s', 's.id', '=', 'sa.subjects_id')  // Change to LEFT JOIN
                ->join('users as su', 'sa.user_id', '=', 'su.id')
                ->select(
                    'sa.id',
                    DB::raw('CONCAT(u.first_name, " ", COALESCE(u.last_name, "")) as teacher_name'),
                    DB::raw('CONCAT(su.first_name, " ", COALESCE(su.last_name, "")) as student_name'),
                    's.name as subject_name',
                    'sa.commission_typee',
                    'sa.commission_amountt',
                    'sa.fees_per_month',  
                    's.commission_month as month_default_fee', 
                    'sa.status',
                    'sa.date'
                )
                ->where('sa.school_id', Auth::user()->school_id)
                ->whereNull('u.deleted_at');

            // Apply filters
            if ($search) {
                $query->where(function($q) use ($search) {
                    $q->where('u.first_name', 'LIKE', "%$search%")
                      ->orWhere('u.last_name', 'LIKE', "%$search%")
                      ->orWhere('su.first_name', 'LIKE', "%$search%")
                      ->orWhere('su.last_name', 'LIKE', "%$search%")
                      ->orWhere('s.name', 'LIKE', "%$search%");
                });
            }

            if ($subject_id) {
                $query->where('sa.subjects_id', $subject_id);
            }
            if ($teacher_id) {
                $query->where('sa.teacher_id', $teacher_id);
            }
            if ($start_date && $end_date) {
                $query->whereBetween('sa.date', [$start_date, $end_date]);
            }

            $total = $query->count();

            $rows = $query->orderBy('su.first_name')
                ->orderBy('sa.date')
                ->offset($offset)
                ->limit($limit)
                ->get();
                
            // More specific grouping key
            $groupedData = [];
            foreach ($rows as $row) {
                $formattedDate = Carbon::parse($row->date)->format('d/m/y');
                $currentMonthYear = Carbon::parse($row->date)->format('m/Y');
                
                // Create a more unique grouping key
                $groupKey = implode('_', [
                    $row->student_name,
                    $currentMonthYear,
                    $row->teacher_name,
                    $row->subject_name ?? 'no_subject',  // Handle null subjects
                    $row->fees_per_month ?? 'no_fees'    // Add fees_per_month to grouping key
                ]);

                if (!isset($groupedData[$groupKey])) {
                    $groupedData[$groupKey] = [
                        'dates' => [],
                        'data' => $row,
                        'attendance_count' => 0,
                        'total_fees' => 0
                    ];
                }

                // Store the date and count occurrences
                if (!isset($groupedData[$groupKey]['dates'][$formattedDate])) {
                    $groupedData[$groupKey]['dates'][$formattedDate] = 1;
                    $groupedData[$groupKey]['attendance_count']++;
                    $groupedData[$groupKey]['total_fees'] += $row->fees_per_month;
                } else {
                    $groupedData[$groupKey]['dates'][$formattedDate]++;
                }
            }

            $formattedRows = [];
            $no = $offset + 1;

            foreach ($groupedData as $group) {
                $row = $group['data'];
                $dates = $group['dates'];

                $tempRow = [
                    'no' => $no++,
                    'id' => $row->id,
                    'student_name' => '<div class="student-name">' . $row->student_name . '</div>',
                    'dates' => $this->formatDatesHtml($dates),
                    'teacher_name' => $row->teacher_name,
                    'subject_name' => $row->subject_name ?? 'No Subject',
                    'fees_per_month' => $row->fees_per_month !== null 
                        ? number_format($row->fees_per_month, 2) 
                        : null,
                    'attendance_count' => $group['attendance_count'],
                    'commission_type' => $row->commission_typee == 0 ? 'Percentage' : 'Fixed Amount',
                    'commission_amount' => number_format($row->commission_amountt ?? 0, 2),
                    'total' => $this->calculateTotal($row, $group['attendance_count']),
                    'operate' => BootstrapTableService::editButton(route('managecommission.per-month.update', $row->id))
                ];

                $formattedRows[] = $tempRow;
            }

            return response()->json([
                'total' => $total,
                'rows' => $formattedRows
            ]);

        } catch (\Exception $e) {
            \Log::error('Error in perMonthShow:', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'error' => 'An error occurred while fetching the data',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    private function formatDatesHtml(array $dates): string 
    {
        $dateLines = [];
        foreach ($dates as $date => $count) {
            $dateLines[] = '<div class="attendance-date">' . $date . 
                ($count > 1 ? '<span class="attendance-count">×' . $count . '</span>' : '') . '</div>';
        }
        return '<div class="attendance-dates">' . implode('', $dateLines) . '</div>';
    }

    private function calculateTotal($row, $attendanceCount): string
    {
        if ($row->fees_per_month === null || floatval($row->fees_per_month) == 0) {
            return number_format(0, 2);
        }

        $amount = $row->commission_typee == 0 
            ? ($row->fees_per_month * ($row->commission_amountt / 100)) 
            : $row->commission_amountt;
        return number_format($amount, 2);
    }

    public function perMonthUpdate(Request $request, $id)
    {
        try {
            DB::beginTransaction();
            
            $validator = Validator::make($request->all(), [
                'fees_per_month' => 'required|numeric|min:0',
                'commission_type' => 'required|in:0,1',
                'commission_amount' => 'required|numeric|min:0',
            ]);

            if ($validator->fails()) {
                return ResponseService::errorResponse($validator->errors()->first());
            }

            // Get the reference record
            $reference = SubjectAttendance::findOrFail($id);
            $date = Carbon::parse($reference->date);
            
            // Update all records for this student+teacher+subject in the same month
            SubjectAttendance::where('user_id', $reference->user_id)
                ->where('teacher_id', $reference->teacher_id)
                ->where('subjects_id', $reference->subjects_id)
                ->whereYear('date', $date->year)
                ->whereMonth('date', $date->month)
                ->update([
                    'fees_per_month' => $request->fees_per_month,
                    'commission_typee' => $request->commission_type,
                    'commission_amountt' => $request->commission_amount,
                ]);
            
            DB::commit();
            return ResponseService::successResponse('Data Updated Successfully');
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e);
            return ResponseService::errorResponse();
        }
    }
}
