<?php

namespace App\Http\Controllers;

use App\Repositories\CertificateTemplate\CertificateTemplateInterface;
use App\Repositories\ClassSection\ClassSectionInterface;
use App\Repositories\Exam\ExamInterface;
use App\Repositories\SessionYear\SessionYearInterface;
use App\Repositories\User\UserInterface;
use App\Services\BootstrapTableService;
use App\Services\CachingService;
use App\Services\ResponseService;
use Carbon\Carbon;
use DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Throwable;
use Illuminate\Support\Facades\Mail;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Str;

class CertificateTemplateController extends Controller
{
    /**
     * Display a listing of the resource.
     */

    private CertificateTemplateInterface $certificateTemplate;
    private CachingService $cache;
    private UserInterface $user;
    private ClassSectionInterface $classSection;
    private ExamInterface $exam;
    private SessionYearInterface $sessionYear;

    public function __construct(CertificateTemplateInterface $certificateTemplate, CachingService $cache, UserInterface $user, ClassSectionInterface $classSection, ExamInterface $exam, SessionYearInterface $sessionYear)
    {
        $this->certificateTemplate = $certificateTemplate;
        $this->cache = $cache;
        $this->user = $user;
        $this->classSection = $classSection;
        $this->exam = $exam;
        $this->sessionYear = $sessionYear;

    }

    public function index()
    {
        //
        ResponseService::noFeatureThenRedirect('ID Card - Certificate Generation');
        ResponseService::noAnyPermissionThenRedirect(['certificate-create','certificate-list']);

        return view('certificate.template');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
        ResponseService::noFeatureThenRedirect('ID Card - Certificate Generation');
        ResponseService::noPermissionThenSendJson('certificate-create');

        $request->validate([
            'name' => 'required',
            'page_layout' => 'required',
            'height' => 'required',
            'width' => 'required',
            'user_image_shape' => 'required',
            'image_size' => 'required',
            'description' => 'required',
            'type' => 'required',
        ]);

        try {
            DB::beginTransaction();

            $page_layout = 'A4 Landscape';
            if ($request->height == 210 && $request->width == 297) {
                // A4 Landscape
                $page_layout = 'A4 Landscape';
            } else if($request->height == 297 && $request->width == 210) {
                // A4 Portrait
                $page_layout = 'A4 Portrait';
            } else {
                // Custom
                $page_layout = 'Custom';
            }

            $data = [
                'name' => $request->name,
                'page_layout' => $page_layout,
                'height' => $request->height,
                'width' => $request->width,
                'user_image_shape' => $request->user_image_shape,
                'image_size' => $request->image_size,
                'description' => $request->description,
                'type' => $request->type,
            ];
            if ($request->hasFile('background_image')) {
                $data['background_image'] = $request->file('background_image')->store('certificate','public');
            }
            $this->certificateTemplate->create($data);
            DB::commit();
            ResponseService::successResponse('Data Stored Successfully');
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "Certificate Template Controller -> Store Method");
            ResponseService::errorResponse();
        }

    }

    /**
     * Display the specified resource.
     */
    public function show($id)
    {
        //
        ResponseService::noFeatureThenRedirect('ID Card - Certificate Generation');
        ResponseService::noPermissionThenSendJson('certificate-list');
        $offset = request('offset', 0);
        $limit = request('limit', 10);
        $sort = request('sort', 'id');
        $order = request('order', 'DESC');
        $search = request('search');

        $sql = $this->certificateTemplate->builder()
            ->when($search, function ($query) use ($search) {
                $query->where(function ($query) use ($search) {
                    $query->where('id', 'LIKE', "%$search%")
                    ->orwhere('name', 'LIKE', "%$search%")
                    ->orwhere('type', 'LIKE', "%$search%");
                });
            });

        $total = $sql->count();

        $sql->orderBy($sort, $order)->skip($offset)->take($limit);
        $res = $sql->get();

        $bulkData = array();
        $bulkData['total'] = $total;
        $rows = array();
        $no = 1;
        foreach ($res as $row) {
            $operate = BootstrapTableService::button('fa fa-edit',route('certificate-template.edit',$row->id),[ 'btn-gradient-primary'],['title' => trans('edit')]);
            $operate .= BootstrapTableService::button('fa fa-table-layout',route('certificate-template.design',$row->id),[ 'btn-gradient-info'],['title' => trans('layout')]);
            $operate .= BootstrapTableService::deleteButton(route('certificate-template.destroy', $row->id));
            $tempRow = $row->toArray();
            $tempRow['no'] = $no++;
            $tempRow['operate'] = $operate;
            $rows[] = $tempRow;
        }
        $bulkData['rows'] = $rows;
        return response()->json($bulkData);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($id)
    {
        //
        ResponseService::noFeatureThenRedirect('ID Card - Certificate Generation');
        ResponseService::noPermissionThenRedirect('certificate-edit');
        $certificateTemplate = $this->certificateTemplate->findById($id);

        return view('certificate.edit-template',compact('certificateTemplate'));

    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        //
        ResponseService::noFeatureThenSendJson('ID Card - Certificate Generation');
        ResponseService::noPermissionThenSendJson('certificate-edit');

        $request->validate([
            'name' => 'required',
            'page_layout' => 'required',
            'height' => 'required',
            'width' => 'required',
            'user_image_shape' => 'required',
            'image_size' => 'required',
            'description' => 'required',
            'type' => 'required',
        ]);

        try {
            DB::beginTransaction();

            $page_layout = 'A4 Landscape';
            if ($request->height == 210 && $request->width == 297) {
                // A4 Landscape
                $page_layout = 'A4 Landscape';
            } else if($request->height == 297 && $request->width == 210) {
                // A4 Portrait
                $page_layout = 'A4 Portrait';
            } else {
                // Custom
                $page_layout = 'Custom';
            }
            $data = [
                'name' => $request->name,
                'page_layout' => $page_layout,
                'height' => $request->height,
                'width' => $request->width,
                'user_image_shape' => $request->user_image_shape,
                'image_size' => $request->image_size,
                'description' => $request->description,
                'type' => $request->type,
            ];

            if ($request->hasFile('background_image')) {
                $certificateTemplate = $this->certificateTemplate->findById($id);
                if ($certificateTemplate->background_image) {
                    if (Storage::disk('public')->exists($certificateTemplate->getRawOriginal('background_image'))) {
                        Storage::disk('public')->delete($certificateTemplate->getRawOriginal('background_image'));
                    }    
                }
                $data['background_image'] = $request->file('background_image')->store('certificate','public');
            }

            $this->certificateTemplate->update($id, $data);
            DB::commit();
            ResponseService::successResponse('Data Stored Successfully');
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "Certificate Template Controller -> Store Method");
            ResponseService::errorResponse();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        //
        ResponseService::noFeatureThenRedirect('ID Card - Certificate Generation');
        ResponseService::noPermissionThenSendJson('certificate-delete');
        try {
            DB::beginTransaction();
            $this->certificateTemplate->deleteById($id);
            DB::commit();
            ResponseService::successResponse('Data Deleted Successfully');
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "Certificate Template Controller -> Destroy Method");
            ResponseService::errorResponse();
        }
    }

    public function design($id)
    {
        //
        ResponseService::noFeatureThenRedirect('ID Card - Certificate Generation');
        ResponseService::noPermissionThenRedirect('certificate-edit');
        try {
            $certificateTemplate = $this->certificateTemplate->findById($id);
            $settings = $this->cache->getSchoolSettings();

            $style = json_decode($certificateTemplate->style, true);

            if (!isset($style['description'])) {
                $style['description'] = 'style="position:absolute; left: 145px;top: 255px"';
            }

            if (!isset($style['title'])) {
                $style['title'] = 'style="position:absolute; left: 145px;top: 290px"';
            }

            if (!isset($style['issue_date'])) {
                $style['issue_date'] = 'style="position:absolute; left: 100px;top: 100px"';
            }

            if (!isset($style['signature'])) {
                $style['signature'] = 'style="position:absolute; left: 150px;top: 150px"';
            }

            if (!isset($style['school_name'])) {
                $style['school_name'] = 'style="position:absolute; left: 480px;top: 60px"';
            }

            if (!isset($style['school_address'])) {
                $style['school_address'] = 'style="position:absolute; left: 125px;top: 85px"';
            }

            if (!isset($style['school_mobile'])) {
                $style['school_mobile'] = 'style="position:absolute; left: 125px;top: 130px"';
            }

            if (!isset($style['school_email'])) {
                $style['school_email'] = 'style="position:absolute; left: 125px;top: 175px"';
            }

            if (!isset($style['school_logo'])) {
                $style['school_logo'] = 'style="position:absolute; left: 525px;top: 75px"';
            }

            if (!isset($style['user_image'])) {
                $style['user_image'] = 'style="position:absolute; left: 525px;top: 125px"';
            }

            $height = $certificateTemplate->height * 3.7795275591;
            $width = $certificateTemplate->width * 3.7795275591;

            $layout = [
                'height' => $height.'px',
                'width' => $width.'px'
            ];
            
            
            return view('certificate.design',compact('certificateTemplate','settings','style','layout'));
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e, "Certificate Template Controller -> Design Method");
            ResponseService::errorResponse();
        }
    }

    public function design_store(Request $request, $id)
    {
        //
        ResponseService::noFeatureThenRedirect('ID Card - Certificate Generation');
        ResponseService::noPermissionThenRedirect('certificate-edit');
        try {
            
            $fields = '';
            if ($request->school_data) {
                $fields = implode(",",$request->school_data);    
            }
            

            $style = array();
            foreach ($request->style as $key => $value) {
                $style[$key] = $value;
            }
            $value = [
                'style' => $style,
                'fields' => $fields
            ];
            $this->certificateTemplate->update($id, $value);
            ResponseService::successResponse('Data Updated Successfully');
            
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e, "Certificate Template Controller -> Design Store Method");
            ResponseService::errorResponse();
        }
    }

    public function certificate()
    {
        ResponseService::noFeatureThenRedirect('ID Card - Certificate Generation');
        ResponseService::noPermissionThenRedirect('certificate-edit');
        try {
            $classSections = $this->classSection->builder()->with('class.stream','section','medium')->get()->pluck('full_name','id');

            $exams = $this->exam->builder()->with('class.medium')->where('publish', 1)->get()->append(['prefix_name']);

            $certificateTemplates = $this->certificateTemplate->builder()->whereNotNull('style')->where('type' ,'Student')->pluck('name','id');

            $sessionYears = $this->sessionYear->builder()->pluck('name','id');

            return view('certificate.student-list', compact('classSections','exams','certificateTemplates','sessionYears'));
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e, "Certificate Template Controller -> Certificate Store Method");
            ResponseService::errorResponse();
        }
    }

    public function certificate_generate(Request $request)
    {
        ResponseService::noFeatureThenRedirect('ID Card - Certificate Generation');
        ResponseService::noPermissionThenRedirect('certificate-list');

        $request->validate([
            'certificate_template_id' => 'required',
            'user_id' => 'required'
        ],[
            'certificate_template_id.required' => 'The certificate template field is required',
            'user_id.required' => 'Please select at least one record.'
        ]);

        try {
            $certificateTemplate = $this->certificateTemplate->findById($request->certificate_template_id);

            $height = $certificateTemplate->height * 3.7795275591;
            $width = $certificateTemplate->width * 3.7795275591;

            $layout = [
                'height' => $height.'px',
                'width' => $width.'px'
            ];

            $style = json_decode($certificateTemplate->style, true);
            $settings = $this->cache->getSchoolSettings();
            
            $user_id = explode(",",$request->user_id);

            $users = $this->user->builder()->with(['student' => function($q) use($request) {
                $q->with('class_section.class.stream','class_section.section','class_section.medium','guardian')
                ->when($request->exam_id, function($q) use($request) {
                    $q->with(['exam_result' => function($q) use($request) {
                        $q->where('exam_id', $request->exam_id)->with('exam:id,name');
                    }]);
                });
            }])->whereIn('id',$user_id)->get();

            $user_data = array();
            foreach ($users as $key => $user) {
                $user_data[] = [
                    'image' => $user->image ?? 'assets/dummy_logo.jpg', 
                    'description' => $this->replacePlaceholders($certificateTemplate->description, $user, $request->exam_id)
                ];

                \Log::info('Created user_data entry: ' . json_encode($user_data[count($user_data)-1]));

                // Send email if guardian email exists(bye)
                // if ($user->student && $user->student->guardian && $user->student->guardian->email) {
                //     $this->sendCertificateEmail($user, $user_data[$key], $certificateTemplate, $layout, $style, $settings);
                // }
            }

            $users = $user_data;
            
            // Return web view as normal
            return view('certificate.certificate-pdf', compact('certificateTemplate','layout','users','style','settings'));
                    
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e, "Certificate Template Controller -> Certificate Generate Store Method");
            ResponseService::errorResponse();
        }
    }
    
    private function sendCertificateEmail($user, $userData, $certificateTemplate, $layout, $style, $settings)
    {
        try {
            \Log::info('Preparing certificate email for user ID: ' . $user->id);

            // Keep original image
            $processedUser = [
                'image' => $userData['image'],
                'description' => $userData['description']
            ];

            // Use the same scaling factor as certificate_generate()
            $heightInPoints = $certificateTemplate->height * (72 / 25.4);
            $widthInPoints = $certificateTemplate->width * (72 / 25.4);
            $heightInPixels = $certificateTemplate->height * (95.8 / 25.4);
            $widthInPixels = $certificateTemplate->width * (95.8 / 25.4);

            $updatedLayout = [
                'height' => $heightInPixels . 'px',
                'width' => $widthInPixels . 'px'
            ];
    
            $html = view('certificate.certificate-pdf', [
                'certificateTemplate' => $certificateTemplate,
                'layout' => $updatedLayout,
                'users' => [$processedUser],
                'style' => $style,
                'settings' => $settings
            ])->render();

            // Prepare email data
            $emailData = [
                'student_name' => $user->full_name,
                'guardian_name' => $user->student->guardian->full_name,
                'school_name' => $settings['school_name'],
                'email_body' => "Dear " . $user->student->guardian->full_name . ",\n\n" .
                                "Please find attached the document for " . $user->full_name . 
                                " from " . $settings['school_name'] . "."
            ];

            // Send Email with PDF Attachment
            \Mail::send('certificate.email', $emailData, function($message) use ($user, $settings, $html, $widthInPoints, $heightInPoints) {
                $message->to($user->student->guardian->email)
                        ->subject('Document from ' . $settings['school_name']);
                        
                $customPaper = array(0, 0, $widthInPoints, $heightInPoints);
                
                $pdf = \PDF::loadHTML($html)
                        ->setPaper($customPaper, 'portrait')
                        ->setOptions([
                            'enable_php' => true,
                            'enable_remote' => true,
                            'images' => true,
                            'margin-top' => '0mm',
                            'margin-right' => '0mm',
                            'margin-bottom' => '0mm',
                            'margin-left' => '0mm',
                            'disable-smart-shrinking' => true,
                            'dpi' => 96,
                            'page-size' => 'custom',
                            'orientation' => 'Portrait'
                        ]);
                
                $filename = str_replace(' ', '_', $user->full_name) . '_Document.pdf';
                $message->attachData($pdf->output(), $filename);
            });

            \Log::info('Email sent successfully to: ' . $user->student->guardian->email);
        } catch (\Exception $e) {
            \Log::error('Email failed for user ID: ' . $user->id . ' Error: ' . $e->getMessage());
        }
    }

    public function certificate_email($id)
    {
        ResponseService::noFeatureThenRedirect('ID Card - Certificate Generation');
        ResponseService::noPermissionThenRedirect('certificate-edit');
        try {
            $request = request();
            
            // Get user with all relationships like in certificate_generate
            $user = $this->user->builder()
                ->with(['student' => function($q) use($request) {
                    $q->with('class_section.class.stream','class_section.section','class_section.medium','guardian')
                    ->when($request->exam_id, function($q) use($request) {
                        $q->with(['exam_result' => function($q) use($request) {
                            $q->where('exam_id', $request->exam_id)->with('exam:id,name');
                        }]);
                    });
                }])
                ->where('id', $id)
                ->first();

            if (!$user) {
                return ResponseService::errorResponse('User not found');
            }

            if ($user->student && $user->student->guardian && $user->student->guardian->email) {
                $certificateTemplate = $this->certificateTemplate->findById($request->certificate_template_id);
                
                if (!$certificateTemplate) {
                    return ResponseService::errorResponse('Certificate template not found');
                }

                $height = $certificateTemplate->height * 3.7795275591;
                $width = $certificateTemplate->width * 3.7795275591;
                $layout = [
                    'height' => $height.'px',
                    'width' => $width.'px'
                ];

                $userData = [
                    'image' => $user->image ?? 'assets/dummy_logo.jpg',
                    'description' => $this->replacePlaceholders($certificateTemplate->description, $user, $request->exam_id)
                ];

                $style = json_decode($certificateTemplate->style, true);
                $settings = $this->cache->getSchoolSettings();

                $this->sendCertificateEmail($user, $userData, $certificateTemplate, $layout, $style, $settings);
                
                return ResponseService::successResponse('Email Sent Successfully');
            } else {
                return ResponseService::errorResponse('Guardian email not found');
            }
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e, "Certificate Template Controller -> Send Email Method");
            return ResponseService::errorResponse();
        }
    }

    public function sendBulkEmails(Request $request)
    {
        ResponseService::noFeatureThenRedirect('ID Card - Certificate Generation');
        ResponseService::noPermissionThenRedirect('certificate-edit');
        
        try {
            $user_ids = explode(",", $request->user_ids);
            $certificateTemplate = $this->certificateTemplate->findById($request->certificate_template_id);
            
            if (!$certificateTemplate) {
                return ResponseService::errorResponse('Certificate template not found');
            }

            $successCount = 0;
            $failCount = 0;

            foreach ($user_ids as $id) {
                $user = $this->user->builder()
                    ->with(['student' => function($q) use($request) {
                        $q->with('class_section.class.stream','class_section.section','class_section.medium','guardian')
                        ->when($request->exam_id, function($q) use($request) {
                            $q->with(['exam_result' => function($q) use($request) {
                                $q->where('exam_id', $request->exam_id)->with('exam:id,name');
                            }]);
                        });
                    }])
                    ->where('id', $id)
                    ->first();

                if ($user && $user->student && $user->student->guardian && $user->student->guardian->email) {
                    $height = $certificateTemplate->height * 3.7795275591;
                    $width = $certificateTemplate->width * 3.7795275591;
                    $layout = [
                        'height' => $height.'px',
                        'width' => $width.'px'
                    ];

                    $userData = [
                        'image' => $user->image ?? 'assets/dummy_logo.jpg',
                        'description' => $this->replacePlaceholders($certificateTemplate->description, $user, $request->exam_id)
                    ];

                    $style = json_decode($certificateTemplate->style, true);
                    $settings = $this->cache->getSchoolSettings();

                    try {
                        $this->sendCertificateEmail($user, $userData, $certificateTemplate, $layout, $style, $settings);
                        $successCount++;
                    } catch (Throwable $e) {
                        \Log::error("Failed to send email to user {$id}: " . $e->getMessage());
                        $failCount++;
                    }
                } else {
                    $failCount++;
                }
            }

            if ($failCount === 0) {
                return ResponseService::successResponse("Successfully sent {$successCount} emails");
            } else {
                return ResponseService::successResponse("Sent {$successCount} emails, failed to send {$failCount} emails");
            }

        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e, "Certificate Template Controller -> Send Bulk Emails Method");
            return ResponseService::errorResponse();
        }
    }

    private function replacePlaceholders($templateContent, $user, $exam_id = null)
    {
        $settings = $this->cache->getSchoolSettings();
        $sessionYear = $this->cache->getDefaultSessionYear();
        // Define the placeholders and their replacements
        $placeholders = [
            '{full_name}' => $user->full_name,
            '{first_name}' => $user->first_name,
            '{last_name}' => $user->last_name,
            '{ic_no_2}' => $user->student->ic_no_2 ?? '',    
            '{class_section}' => $user->student->class_section->full_name,
            '{student_mobile}' => $user->mobile,
            '{dob}' => date($settings['date_format'],strtotime($user->dob)),
            '{roll_no}' => $user->student->roll_number,
            '{admission_no}' => $user->student->admission_no,
            '{current_address}' => $user->current_address,
            '{permanent_address}' => $user->permanent_address,
            '{gender}' => $user->gender,
            '{admission_date}' => date($settings['date_format'],strtotime($user->student->admission_date)),
            '{guardian_name}' => $user->student->guardian->full_name,
            '{guardian_mobile}' => $user->student->guardian->mobile,
            '{guardian_email}' => $user->student->guardian->email,
            '{session_year}' => $sessionYear->name,
            // Add more placeholders as needed
        ];

        $exam_data = array();

        if ($exam_id && count($user->student->exam_result)) {
            $result = $user->student->exam_result[0];
            $exam_data = [
                '{exam}' => $result->exam->name,
                '{total_marks}' => $result->total_marks,
                '{obtain_marks}' => $result->obtained_marks,
                '{grade}' => $result->grade,
            ];
        }

        $placeholders = array_merge($placeholders, $exam_data);

        // Replace the placeholders in the template content
        foreach ($placeholders as $placeholder => $replacement) {
            $templateContent = str_replace($placeholder, $replacement, $templateContent);
        }

        return $templateContent;
    }

    public function staff_certificate()
    {
        ResponseService::noFeatureThenRedirect('ID Card - Certificate Generation');
        ResponseService::noPermissionThenRedirect('certificate-edit');
        try {
            $certificateTemplates = $this->certificateTemplate->builder()->whereNotNull('style')->where('type' ,'Staff')->pluck('name','id');

            return view('certificate.staff-list', compact('certificateTemplates'));
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e, "Certificate Template Controller -> Staff Certificate Store Method");
            ResponseService::errorResponse();
        }
    }

    public function staff_generate_certificate(Request $request)
    {
        ResponseService::noFeatureThenRedirect('ID Card - Certificate Generation');
        ResponseService::noPermissionThenRedirect('certificate-list');

        $request->validate([
            'certificate_template_id' => 'required',
            'user_id' => 'required'
        ],[
            'certificate_template_id.required' => 'The certificate template field is required',
            'user_id.required' => 'Please select at least one record.'
        ]);

        try {
            
            $certificateTemplate = $this->certificateTemplate->findById($request->certificate_template_id);

            $height = $certificateTemplate->height * 3.7795275591;
            $width = $certificateTemplate->width * 3.7795275591;

            $layout = [
                'height' => $height.'px',
                'width' => $width.'px'
            ];
            
            $user_id = explode(",",$request->user_id);

            $users = $this->user->builder()->with('staff','roles')->whereIn('id',$user_id)->get();
            $user_data = array();
            foreach ($users as $key => $user) {
                $user_data[] = [
                    'image' => $user->image,
                    'description' => $this->replaceSatffPlaceholders($certificateTemplate->description, $user)
                ];
            }
            $users = $user_data;
            $style = json_decode($certificateTemplate->style, true);
            $settings = $this->cache->getSchoolSettings();
            

            return view('certificate.certificate-pdf',compact('certificateTemplate','layout','users','style','settings'));
                        
        } catch (Throwable $e) {
            ResponseService::logErrorResponse($e, "Certificate Template Controller -> Certificate Generate Store Method");
            ResponseService::errorResponse();
        }
    }

    

    private function replaceSatffPlaceholders($templateContent, $user)
    {
        $settings = $this->cache->getSchoolSettings();
        $sessionYear = $this->cache->getDefaultSessionYear();

        $today_date = Carbon::now();
        $joining_date = Carbon::parse($user->created_at);

        $experience = $joining_date->diffInMonths($today_date);
        $experience = $experience / 12;

        // Define the placeholders and their replacements
        $placeholders = [
            '{full_name}' => $user->full_name,
            '{first_name}' => $user->first_name,
            '{last_name}' => $user->last_name,
            '{mobile}' => $user->mobile,
            '{dob}' => date($settings['date_format'],strtotime($user->dob)),
            '{current_address}' => $user->current_address,
            '{permanent_address}' => $user->permanent_address,
            '{gender}' => $user->gender,
            '{email}' => $user->email,
            '{joining_date}' => date($settings['date_format'],strtotime($user->created_at)),
            '{role}' => implode(',',$user->roles->pluck('name')->toArray()),
            '{qualification}' => $user->staff->qualification,
            '{session_year}' => $sessionYear->name,
            '{experience}' => number_format($experience, 1),
            // Add more placeholders as needed
        ];

        // Replace the placeholders in the template content
        foreach ($placeholders as $placeholder => $replacement) {
            $templateContent = str_replace($placeholder, $replacement, $templateContent);
        }

        return $templateContent;
    }

    
}
