<?php

namespace App\Exports;

use App\Models\Students;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Illuminate\Support\Facades\DB;

class StudentFeeTypesExport implements FromQuery, WithMapping, WithHeadings, ShouldAutoSize
{
    private $class_id;
    private $session_year_id;
    private $search;

    public function __construct(int $class_id, int $session_year_id, ?string $search = null)
    {
        $this->class_id = $class_id;
        $this->session_year_id = $session_year_id;
    }


    public function query()
    {
        
        return DB::table('student_fee_types')
            ->join('students', 'student_fee_types.student_id', '=', 'students.id')
            ->join('users', 'students.user_id', '=', 'users.id')
            ->select(
                'student_fee_types.id',
                'student_fee_types.item_code',
                DB::raw("CONCAT(users.first_name, ' ', users.last_name) as full_name"),
                'users.rfid_id',
                'student_fee_types.classification_code',
                'student_fee_types.fees_type_name',
                'student_fee_types.unit',
                'student_fee_types.quantity',
                'student_fee_types.discount',
                'student_fee_types.tax',
                'student_fee_types.unit_price',
                DB::raw("(student_fee_types.unit_price * (1 - student_fee_types.discount / 100) * (1 + student_fee_types.tax / 100)) * student_fee_types.quantity as total_price"),                
                'student_fee_types.remarks'
            )
            ->where('students.session_year_id', $this->session_year_id)
            ->where('student_fee_types.class_id', $this->class_id)
            ->where('users.status', '1')
            ->orderBy('student_fee_types.id', 'DESC');
    }

    public function map($row): array
    {
        return [
            $row->id,
            $row->item_code,
            $row->full_name,
            $row->rfid_id,
            $row->classification_code,
            $row->fees_type_name,
            $row->unit,
            $row->quantity,
            $row->discount,
            $row->tax,
            'RM ' . number_format($row->unit_price, 2),
            'RM ' . number_format($row->total_price, 2),
            $row->remarks
        ];
    }

    public function headings(): array
    {
        return [
            'No.',
            'Item Code',
            'Student',
            'RFID',
            'Classification Code',
            'Fees Type',
            'UOM',
            'Quantity',
            'Discount (%)',
            'Tax (%)',
            'Unit Price',
            'Total Price',
            'Remarks'
        ];
    }
}
